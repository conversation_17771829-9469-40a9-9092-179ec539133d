{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\n\n/**\r\n * Merges two arrays.\r\n * @param  {*} a\r\n * @param  {*} b\r\n * @return {*}\r\n */\nexport default function mergeArrays(a, b) {\n  var merged = a.slice();\n  for (var _iterator = _createForOfIteratorHelperLoose(b), _step; !(_step = _iterator()).done;) {\n    var element = _step.value;\n    if (a.indexOf(element) < 0) {\n      merged.push(element);\n    }\n  }\n  return merged.sort(function (a, b) {\n    return a - b;\n  }); // ES6 version, requires Set polyfill.\n  // let merged = new Set(a)\n  // for (const element of b) {\n  // \tmerged.add(i)\n  // }\n  // return Array.from(merged).sort((a, b) => a - b)\n}", "map": {"version": 3, "names": ["mergeArrays", "a", "b", "merged", "slice", "_iterator", "_createForOfIteratorHelperLoose", "_step", "done", "element", "value", "indexOf", "push", "sort"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\helpers\\mergeArrays.js"], "sourcesContent": ["/**\r\n * Merges two arrays.\r\n * @param  {*} a\r\n * @param  {*} b\r\n * @return {*}\r\n */\r\nexport default function mergeArrays(a, b) {\r\n\tconst merged = a.slice()\r\n\r\n\tfor (const element of b) {\r\n\t\tif (a.indexOf(element) < 0) {\r\n\t\t\tmerged.push(element)\r\n\t\t}\r\n\t}\r\n\r\n\treturn merged.sort((a, b) => a - b)\r\n\r\n\t// ES6 version, requires Set polyfill.\r\n\t// let merged = new Set(a)\r\n\t// for (const element of b) {\r\n\t// \tmerged.add(i)\r\n\t// }\r\n\t// return Array.from(merged).sort((a, b) => a - b)\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,WAATA,CAAqBC,CAArB,EAAwBC,CAAxB,EAA2B;EACzC,IAAMC,MAAM,GAAGF,CAAC,CAACG,KAAF,EAAf;EAEA,SAAAC,SAAA,GAAAC,+BAAA,CAAsBJ,CAAtB,GAAAK,KAAA,IAAAA,KAAA,GAAAF,SAAA,IAAAG,IAAA,GAAyB;IAAA,IAAdC,OAAc,GAAAF,KAAA,CAAAG,KAAA;IACxB,IAAIT,CAAC,CAACU,OAAF,CAAUF,OAAV,IAAqB,CAAzB,EAA4B;MAC3BN,MAAM,CAACS,IAAP,CAAYH,OAAZ;IACA;EACD;EAED,OAAON,MAAM,CAACU,IAAP,CAAY,UAACZ,CAAD,EAAIC,CAAJ;IAAA,OAAUD,CAAC,GAAGC,CAAd;EAAA,CAAZ,CAAP,CATyC,CAWzC;EACA;EACA;EACA;EACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}