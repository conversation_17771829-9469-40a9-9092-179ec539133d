{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { AsyncAction } from './AsyncAction';\nimport { immediateProvider } from './immediateProvider';\nvar AsapAction = function (_super) {\n  __extends(AsapAction, _super);\n  function AsapAction(scheduler, work) {\n    var _this = _super.call(this, scheduler, work) || this;\n    _this.scheduler = scheduler;\n    _this.work = work;\n    return _this;\n  }\n  AsapAction.prototype.requestAsyncId = function (scheduler, id, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (delay !== null && delay > 0) {\n      return _super.prototype.requestAsyncId.call(this, scheduler, id, delay);\n    }\n    scheduler.actions.push(this);\n    return scheduler._scheduled || (scheduler._scheduled = immediateProvider.setImmediate(scheduler.flush.bind(scheduler, undefined)));\n  };\n  AsapAction.prototype.recycleAsyncId = function (scheduler, id, delay) {\n    var _a;\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (delay != null ? delay > 0 : this.delay > 0) {\n      return _super.prototype.recycleAsyncId.call(this, scheduler, id, delay);\n    }\n    var actions = scheduler.actions;\n    if (id != null && ((_a = actions[actions.length - 1]) === null || _a === void 0 ? void 0 : _a.id) !== id) {\n      immediateProvider.clearImmediate(id);\n      if (scheduler._scheduled === id) {\n        scheduler._scheduled = undefined;\n      }\n    }\n    return undefined;\n  };\n  return AsapAction;\n}(AsyncAction);\nexport { AsapAction };", "map": {"version": 3, "names": ["AsyncAction", "immediate<PERSON>rovider", "AsapAction", "_super", "__extends", "scheduler", "work", "_this", "call", "prototype", "requestAsyncId", "id", "delay", "actions", "push", "_scheduled", "setImmediate", "flush", "bind", "undefined", "recycleAsyncId", "_a", "length", "clearImmediate"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\scheduler\\AsapAction.ts"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nimport { AsapScheduler } from './AsapScheduler';\nimport { SchedulerAction } from '../types';\nimport { immediateProvider } from './immediateProvider';\nimport { TimerHandle } from './timerHandle';\n\nexport class AsapAction<T> extends AsyncAction<T> {\n  constructor(protected scheduler: AsapScheduler, protected work: (this: SchedulerAction<T>, state?: T) => void) {\n    super(scheduler, work);\n  }\n\n  protected requestAsyncId(scheduler: AsapScheduler, id?: TimerHandle, delay: number = 0): TimerHandle {\n    // If delay is greater than 0, request as an async action.\n    if (delay !== null && delay > 0) {\n      return super.requestAsyncId(scheduler, id, delay);\n    }\n    // Push the action to the end of the scheduler queue.\n    scheduler.actions.push(this);\n    // If a microtask has already been scheduled, don't schedule another\n    // one. If a microtask hasn't been scheduled yet, schedule one now. Return\n    // the current scheduled microtask id.\n    return scheduler._scheduled || (scheduler._scheduled = immediateProvider.setImmediate(scheduler.flush.bind(scheduler, undefined)));\n  }\n\n  protected recycleAsyncId(scheduler: AsapScheduler, id?: TimerHandle, delay: number = 0): TimerHandle | undefined {\n    // If delay exists and is greater than 0, or if the delay is null (the\n    // action wasn't rescheduled) but was originally scheduled as an async\n    // action, then recycle as an async action.\n    if (delay != null ? delay > 0 : this.delay > 0) {\n      return super.recycleAsyncId(scheduler, id, delay);\n    }\n    // If the scheduler queue has no remaining actions with the same async id,\n    // cancel the requested microtask and set the scheduled flag to undefined\n    // so the next AsapAction will request its own.\n    const { actions } = scheduler;\n    if (id != null && actions[actions.length - 1]?.id !== id) {\n      immediateProvider.clearImmediate(id);\n      if (scheduler._scheduled === id) {\n        scheduler._scheduled = undefined;\n      }\n    }\n    // Return undefined so the action knows to request a new async id if it's rescheduled.\n    return undefined;\n  }\n}\n"], "mappings": ";AAAA,SAASA,WAAW,QAAQ,eAAe;AAG3C,SAASC,iBAAiB,QAAQ,qBAAqB;AAGvD,IAAAC,UAAA,aAAAC,MAAA;EAAmCC,SAAA,CAAAF,UAAA,EAAAC,MAAA;EACjC,SAAAD,WAAsBG,SAAwB,EAAYC,IAAmD;IAA7G,IAAAC,KAAA,GACEJ,MAAA,CAAAK,IAAA,OAAMH,SAAS,EAAEC,IAAI,CAAC;IADFC,KAAA,CAAAF,SAAS,GAATA,SAAS;IAA2BE,KAAA,CAAAD,IAAI,GAAJA,IAAI;;EAE9D;EAEUJ,UAAA,CAAAO,SAAA,CAAAC,cAAc,GAAxB,UAAyBL,SAAwB,EAAEM,EAAgB,EAAEC,KAAiB;IAAjB,IAAAA,KAAA;MAAAA,KAAA,IAAiB;IAAA;IAEpF,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,GAAG,CAAC,EAAE;MAC/B,OAAOT,MAAA,CAAAM,SAAA,CAAMC,cAAc,CAAAF,IAAA,OAACH,SAAS,EAAEM,EAAE,EAAEC,KAAK,CAAC;;IAGnDP,SAAS,CAACQ,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC;IAI5B,OAAOT,SAAS,CAACU,UAAU,KAAKV,SAAS,CAACU,UAAU,GAAGd,iBAAiB,CAACe,YAAY,CAACX,SAAS,CAACY,KAAK,CAACC,IAAI,CAACb,SAAS,EAAEc,SAAS,CAAC,CAAC,CAAC;EACpI,CAAC;EAESjB,UAAA,CAAAO,SAAA,CAAAW,cAAc,GAAxB,UAAyBf,SAAwB,EAAEM,EAAgB,EAAEC,KAAiB;;IAAjB,IAAAA,KAAA;MAAAA,KAAA,IAAiB;IAAA;IAIpF,IAAIA,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,GAAG,CAAC,EAAE;MAC9C,OAAOT,MAAA,CAAAM,SAAA,CAAMW,cAAc,CAAAZ,IAAA,OAACH,SAAS,EAAEM,EAAE,EAAEC,KAAK,CAAC;;IAK3C,IAAAC,OAAO,GAAKR,SAAS,CAAAQ,OAAd;IACf,IAAIF,EAAE,IAAI,IAAI,IAAI,EAAAU,EAAA,GAAAR,OAAO,CAACA,OAAO,CAACS,MAAM,GAAG,CAAC,CAAC,cAAAD,EAAA,uBAAAA,EAAA,CAAEV,EAAE,MAAKA,EAAE,EAAE;MACxDV,iBAAiB,CAACsB,cAAc,CAACZ,EAAE,CAAC;MACpC,IAAIN,SAAS,CAACU,UAAU,KAAKJ,EAAE,EAAE;QAC/BN,SAAS,CAACU,UAAU,GAAGI,SAAS;;;IAIpC,OAAOA,SAAS;EAClB,CAAC;EACH,OAAAjB,UAAC;AAAD,CAAC,CAtCkCF,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}