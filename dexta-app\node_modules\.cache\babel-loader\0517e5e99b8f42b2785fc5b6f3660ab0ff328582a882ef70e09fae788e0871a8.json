{"ast": null, "code": "var ElementType = require(\"domelementtype\"),\n  getOuterHTML = require(\"dom-serializer\"),\n  isTag = ElementType.isTag;\nmodule.exports = {\n  getInnerHTML: getInnerHTML,\n  getOuterHTML: getOuterHTML,\n  getText: getText\n};\nfunction getInnerHTML(elem, opts) {\n  return elem.children ? elem.children.map(function (elem) {\n    return getOuterHTML(elem, opts);\n  }).join(\"\") : \"\";\n}\nfunction getText(elem) {\n  if (Array.isArray(elem)) return elem.map(getText).join(\"\");\n  if (isTag(elem)) return elem.name === \"br\" ? \"\\n\" : getText(elem.children);\n  if (elem.type === ElementType.CDATA) return getText(elem.children);\n  if (elem.type === ElementType.Text) return elem.data;\n  return \"\";\n}", "map": {"version": 3, "names": ["ElementType", "require", "getOuterHTML", "isTag", "module", "exports", "getInnerHTML", "getText", "elem", "opts", "children", "map", "join", "Array", "isArray", "name", "type", "CDATA", "Text", "data"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/domutils/lib/stringify.js"], "sourcesContent": ["var ElementType = require(\"domelementtype\"),\n    getOuterHTML = require(\"dom-serializer\"),\n    isTag = ElementType.isTag;\n\nmodule.exports = {\n\tgetInnerHTML: getInnerHTML,\n\tgetOuterHTML: getOuterHTML,\n\tgetText: getText\n};\n\nfunction getInnerHTML(elem, opts){\n\treturn elem.children ? elem.children.map(function(elem){\n\t\treturn getOuterHTML(elem, opts);\n\t}).join(\"\") : \"\";\n}\n\nfunction getText(elem){\n\tif(Array.isArray(elem)) return elem.map(getText).join(\"\");\n\tif(isTag(elem)) return elem.name === \"br\" ? \"\\n\" : getText(elem.children);\n\tif(elem.type === ElementType.CDATA) return getText(elem.children);\n\tif(elem.type === ElementType.Text) return elem.data;\n\treturn \"\";\n}\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,gBAAgB,CAAC;EACvCC,YAAY,GAAGD,OAAO,CAAC,gBAAgB,CAAC;EACxCE,KAAK,GAAGH,WAAW,CAACG,KAAK;AAE7BC,MAAM,CAACC,OAAO,GAAG;EAChBC,YAAY,EAAEA,YAAY;EAC1BJ,YAAY,EAAEA,YAAY;EAC1BK,OAAO,EAAEA;AACV,CAAC;AAED,SAASD,YAAYA,CAACE,IAAI,EAAEC,IAAI,EAAC;EAChC,OAAOD,IAAI,CAACE,QAAQ,GAAGF,IAAI,CAACE,QAAQ,CAACC,GAAG,CAAC,UAASH,IAAI,EAAC;IACtD,OAAON,YAAY,CAACM,IAAI,EAAEC,IAAI,CAAC;EAChC,CAAC,CAAC,CAACG,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE;AACjB;AAEA,SAASL,OAAOA,CAACC,IAAI,EAAC;EACrB,IAAGK,KAAK,CAACC,OAAO,CAACN,IAAI,CAAC,EAAE,OAAOA,IAAI,CAACG,GAAG,CAACJ,OAAO,CAAC,CAACK,IAAI,CAAC,EAAE,CAAC;EACzD,IAAGT,KAAK,CAACK,IAAI,CAAC,EAAE,OAAOA,IAAI,CAACO,IAAI,KAAK,IAAI,GAAG,IAAI,GAAGR,OAAO,CAACC,IAAI,CAACE,QAAQ,CAAC;EACzE,IAAGF,IAAI,CAACQ,IAAI,KAAKhB,WAAW,CAACiB,KAAK,EAAE,OAAOV,OAAO,CAACC,IAAI,CAACE,QAAQ,CAAC;EACjE,IAAGF,IAAI,CAACQ,IAAI,KAAKhB,WAAW,CAACkB,IAAI,EAAE,OAAOV,IAAI,CAACW,IAAI;EACnD,OAAO,EAAE;AACV"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}