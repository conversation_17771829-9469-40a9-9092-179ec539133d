{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { throttle } from './throttle';\nimport { timer } from '../observable/timer';\nexport function throttleTime(duration, scheduler, config) {\n  if (scheduler === void 0) {\n    scheduler = asyncScheduler;\n  }\n  var duration$ = timer(duration, scheduler);\n  return throttle(function () {\n    return duration$;\n  }, config);\n}", "map": {"version": 3, "names": ["asyncScheduler", "throttle", "timer", "throttleTime", "duration", "scheduler", "config", "duration$"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\throttleTime.ts"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { throttle, ThrottleConfig } from './throttle';\nimport { MonoTypeOperatorFunction, SchedulerLike } from '../types';\nimport { timer } from '../observable/timer';\n\n/**\n * Emits a value from the source Observable, then ignores subsequent source\n * values for `duration` milliseconds, then repeats this process.\n *\n * <span class=\"informal\">Lets a value pass, then ignores source values for the\n * next `duration` milliseconds.</span>\n *\n * ![](throttleTime.png)\n *\n * `throttleTime` emits the source Observable values on the output Observable\n * when its internal timer is disabled, and ignores source values when the timer\n * is enabled. Initially, the timer is disabled. As soon as the first source\n * value arrives, it is forwarded to the output Observable, and then the timer\n * is enabled. After `duration` milliseconds (or the time unit determined\n * internally by the optional `scheduler`) has passed, the timer is disabled,\n * and this process repeats for the next source value. Optionally takes a\n * {@link SchedulerLike} for managing timers.\n *\n * ## Examples\n *\n * ### Limit click rate\n *\n * Emit clicks at a rate of at most one click per second\n *\n * ```ts\n * import { fromEvent, throttleTime } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(throttleTime(1000));\n *\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link auditTime}\n * @see {@link debounceTime}\n * @see {@link delay}\n * @see {@link sampleTime}\n * @see {@link throttle}\n *\n * @param duration Time to wait before emitting another value after\n * emitting the last value, measured in milliseconds or the time unit determined\n * internally by the optional `scheduler`.\n * @param scheduler The {@link SchedulerLike} to use for\n * managing the timers that handle the throttling. Defaults to {@link asyncScheduler}.\n * @param config A configuration object to define `leading` and\n * `trailing` behavior. Defaults to `{ leading: true, trailing: false }`.\n * @return A function that returns an Observable that performs the throttle\n * operation to limit the rate of emissions from the source.\n */\nexport function throttleTime<T>(\n  duration: number,\n  scheduler: SchedulerLike = asyncScheduler,\n  config?: ThrottleConfig\n): MonoTypeOperatorFunction<T> {\n  const duration$ = timer(duration, scheduler);\n  return throttle(() => duration$, config);\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,oBAAoB;AACnD,SAASC,QAAQ,QAAwB,YAAY;AAErD,SAASC,KAAK,QAAQ,qBAAqB;AAmD3C,OAAM,SAAUC,YAAYA,CAC1BC,QAAgB,EAChBC,SAAyC,EACzCC,MAAuB;EADvB,IAAAD,SAAA;IAAAA,SAAA,GAAAL,cAAyC;EAAA;EAGzC,IAAMO,SAAS,GAAGL,KAAK,CAACE,QAAQ,EAAEC,SAAS,CAAC;EAC5C,OAAOJ,QAAQ,CAAC;IAAM,OAAAM,SAAS;EAAT,CAAS,EAAED,MAAM,CAAC;AAC1C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}