{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { Subscription } from '../Subscription';\nexport var animationFrameProvider = {\n  schedule: function (callback) {\n    var request = requestAnimationFrame;\n    var cancel = cancelAnimationFrame;\n    var delegate = animationFrameProvider.delegate;\n    if (delegate) {\n      request = delegate.requestAnimationFrame;\n      cancel = delegate.cancelAnimationFrame;\n    }\n    var handle = request(function (timestamp) {\n      cancel = undefined;\n      callback(timestamp);\n    });\n    return new Subscription(function () {\n      return cancel === null || cancel === void 0 ? void 0 : cancel(handle);\n    });\n  },\n  requestAnimationFrame: function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    var delegate = animationFrameProvider.delegate;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.requestAnimationFrame) || requestAnimationFrame).apply(void 0, __spreadArray([], __read(args)));\n  },\n  cancelAnimationFrame: function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    var delegate = animationFrameProvider.delegate;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.cancelAnimationFrame) || cancelAnimationFrame).apply(void 0, __spreadArray([], __read(args)));\n  },\n  delegate: undefined\n};", "map": {"version": 3, "names": ["Subscription", "animationFrameProvider", "schedule", "callback", "request", "requestAnimationFrame", "cancel", "cancelAnimationFrame", "delegate", "handle", "timestamp", "undefined", "args", "_i", "arguments", "length", "apply", "__spread<PERSON><PERSON>y", "__read"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\scheduler\\animationFrameProvider.ts"], "sourcesContent": ["import { Subscription } from '../Subscription';\n\ninterface AnimationFrameProvider {\n  schedule(callback: FrameRequestCallback): Subscription;\n  requestAnimationFrame: typeof requestAnimationFrame;\n  cancelAnimationFrame: typeof cancelAnimationFrame;\n  delegate:\n    | {\n        requestAnimationFrame: typeof requestAnimationFrame;\n        cancelAnimationFrame: typeof cancelAnimationFrame;\n      }\n    | undefined;\n}\n\nexport const animationFrameProvider: AnimationFrameProvider = {\n  // When accessing the delegate, use the variable rather than `this` so that\n  // the functions can be called without being bound to the provider.\n  schedule(callback) {\n    let request = requestAnimationFrame;\n    let cancel: typeof cancelAnimationFrame | undefined = cancelAnimationFrame;\n    const { delegate } = animationFrameProvider;\n    if (delegate) {\n      request = delegate.requestAnimationFrame;\n      cancel = delegate.cancelAnimationFrame;\n    }\n    const handle = request((timestamp) => {\n      // Clear the cancel function. The request has been fulfilled, so\n      // attempting to cancel the request upon unsubscription would be\n      // pointless.\n      cancel = undefined;\n      callback(timestamp);\n    });\n    return new Subscription(() => cancel?.(handle));\n  },\n  requestAnimationFrame(...args) {\n    const { delegate } = animationFrameProvider;\n    return (delegate?.requestAnimationFrame || requestAnimationFrame)(...args);\n  },\n  cancelAnimationFrame(...args) {\n    const { delegate } = animationFrameProvider;\n    return (delegate?.cancelAnimationFrame || cancelAnimationFrame)(...args);\n  },\n  delegate: undefined,\n};\n"], "mappings": ";AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAc9C,OAAO,IAAMC,sBAAsB,GAA2B;EAG5DC,QAAQ,EAAR,SAAAA,CAASC,QAAQ;IACf,IAAIC,OAAO,GAAGC,qBAAqB;IACnC,IAAIC,MAAM,GAA4CC,oBAAoB;IAClE,IAAAC,QAAQ,GAAKP,sBAAsB,CAAAO,QAA3B;IAChB,IAAIA,QAAQ,EAAE;MACZJ,OAAO,GAAGI,QAAQ,CAACH,qBAAqB;MACxCC,MAAM,GAAGE,QAAQ,CAACD,oBAAoB;;IAExC,IAAME,MAAM,GAAGL,OAAO,CAAC,UAACM,SAAS;MAI/BJ,MAAM,GAAGK,SAAS;MAClBR,QAAQ,CAACO,SAAS,CAAC;IACrB,CAAC,CAAC;IACF,OAAO,IAAIV,YAAY,CAAC;MAAM,OAAAM,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAGG,MAAM,CAAC;IAAhB,CAAgB,CAAC;EACjD,CAAC;EACDJ,qBAAqB,WAAAA,CAAA;IAAC,IAAAO,IAAA;SAAA,IAAAC,EAAA,IAAO,EAAPA,EAAA,GAAAC,SAAA,CAAAC,MAAO,EAAPF,EAAA,EAAO;MAAPD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;IACZ,IAAAL,QAAQ,GAAKP,sBAAsB,CAAAO,QAA3B;IAChB,OAAO,CAAC,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEH,qBAAqB,KAAIA,qBAAqB,EAACW,KAAA,SAAAC,aAAA,KAAAC,MAAA,CAAIN,IAAI;EAC3E,CAAC;EACDL,oBAAoB,WAAAA,CAAA;IAAC,IAAAK,IAAA;SAAA,IAAAC,EAAA,IAAO,EAAPA,EAAA,GAAAC,SAAA,CAAAC,MAAO,EAAPF,EAAA,EAAO;MAAPD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;IACX,IAAAL,QAAQ,GAAKP,sBAAsB,CAAAO,QAA3B;IAChB,OAAO,CAAC,CAAAA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAED,oBAAoB,KAAIA,oBAAoB,EAACS,KAAA,SAAAC,aAAA,KAAAC,MAAA,CAAIN,IAAI;EACzE,CAAC;EACDJ,QAAQ,EAAEG;CACX"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}