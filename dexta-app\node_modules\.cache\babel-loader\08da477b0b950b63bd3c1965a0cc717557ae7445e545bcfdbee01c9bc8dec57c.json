{"ast": null, "code": "var isArray = Array.isArray;\nvar getPrototypeOf = Object.getPrototypeOf,\n  objectProto = Object.prototype,\n  getKeys = Object.keys;\nexport function argsArgArrayOrObject(args) {\n  if (args.length === 1) {\n    var first_1 = args[0];\n    if (isArray(first_1)) {\n      return {\n        args: first_1,\n        keys: null\n      };\n    }\n    if (isPOJO(first_1)) {\n      var keys = getKeys(first_1);\n      return {\n        args: keys.map(function (key) {\n          return first_1[key];\n        }),\n        keys: keys\n      };\n    }\n  }\n  return {\n    args: args,\n    keys: null\n  };\n}\nfunction isPOJO(obj) {\n  return obj && typeof obj === 'object' && getPrototypeOf(obj) === objectProto;\n}", "map": {"version": 3, "names": ["isArray", "Array", "getPrototypeOf", "Object", "objectProto", "prototype", "get<PERSON><PERSON><PERSON>", "keys", "argsArgArrayOrObject", "args", "length", "first_1", "isPOJO", "map", "key", "obj"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\util\\argsArgArrayOrObject.ts"], "sourcesContent": ["const { isArray } = Array;\nconst { getPrototypeOf, prototype: objectProto, keys: getKeys } = Object;\n\n/**\n * Used in functions where either a list of arguments, a single array of arguments, or a\n * dictionary of arguments can be returned. Returns an object with an `args` property with\n * the arguments in an array, if it is a dictionary, it will also return the `keys` in another\n * property.\n */\nexport function argsArgArrayOrObject<T, O extends Record<string, T>>(args: T[] | [O] | [T[]]): { args: T[]; keys: string[] | null } {\n  if (args.length === 1) {\n    const first = args[0];\n    if (isArray(first)) {\n      return { args: first, keys: null };\n    }\n    if (isPOJO(first)) {\n      const keys = getKeys(first);\n      return {\n        args: keys.map((key) => first[key]),\n        keys,\n      };\n    }\n  }\n\n  return { args: args as T[], keys: null };\n}\n\nfunction isPOJO(obj: any): obj is object {\n  return obj && typeof obj === 'object' && getPrototypeOf(obj) === objectProto;\n}\n"], "mappings": "AAAQ,IAAAA,OAAO,GAAKC,KAAK,CAAAD,OAAV;AACP,IAAAE,cAAc,GAA4CC,MAAM,CAAAD,cAAlD;EAAaE,WAAW,GAAoBD,MAAM,CAAAE,SAA1B;EAAQC,OAAO,GAAKH,MAAM,CAAAI,IAAX;AAQ7D,OAAM,SAAUC,oBAAoBA,CAAiCC,IAAuB;EAC1F,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;IACrB,IAAMC,OAAK,GAAGF,IAAI,CAAC,CAAC,CAAC;IACrB,IAAIT,OAAO,CAACW,OAAK,CAAC,EAAE;MAClB,OAAO;QAAEF,IAAI,EAAEE,OAAK;QAAEJ,IAAI,EAAE;MAAI,CAAE;;IAEpC,IAAIK,MAAM,CAACD,OAAK,CAAC,EAAE;MACjB,IAAMJ,IAAI,GAAGD,OAAO,CAACK,OAAK,CAAC;MAC3B,OAAO;QACLF,IAAI,EAAEF,IAAI,CAACM,GAAG,CAAC,UAACC,GAAG;UAAK,OAAAH,OAAK,CAACG,GAAG,CAAC;QAAV,CAAU,CAAC;QACnCP,IAAI,EAAAA;OACL;;;EAIL,OAAO;IAAEE,IAAI,EAAEA,IAAW;IAAEF,IAAI,EAAE;EAAI,CAAE;AAC1C;AAEA,SAASK,MAAMA,CAACG,GAAQ;EACtB,OAAOA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIb,cAAc,CAACa,GAAG,CAAC,KAAKX,WAAW;AAC9E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}