{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"elementType\", \"externalSlotProps\", \"ownerState\"];\nimport { unstable_useForkRef as useForkRef } from '@mui/utils';\nimport appendOwnerState from './appendOwnerState';\nimport mergeSlotProps from './mergeSlotProps';\nimport resolveComponentProps from './resolveComponentProps';\n\n/**\n * Builds the props to be passed into the slot of an unstyled component.\n * It merges the internal props of the component with the ones supplied by the user, allowing to customize the behavior.\n * If the slot component is not a host component, it also merges in the `ownerState`.\n *\n * @param parameters.getSlotProps - A function that returns the props to be passed to the slot component.\n */\nexport default function useSlotProps(parameters) {\n  var _parameters$additiona;\n  const {\n      elementType,\n      externalSlotProps,\n      ownerState\n    } = parameters,\n    rest = _objectWithoutPropertiesLoose(parameters, _excluded);\n  const resolvedComponentsProps = resolveComponentProps(externalSlotProps, ownerState);\n  const {\n    props: mergedProps,\n    internalRef\n  } = mergeSlotProps(_extends({}, rest, {\n    externalSlotProps: resolvedComponentsProps\n  }));\n  const ref = useForkRef(internalRef, useForkRef(resolvedComponentsProps == null ? void 0 : resolvedComponentsProps.ref, (_parameters$additiona = parameters.additionalProps) == null ? void 0 : _parameters$additiona.ref));\n  const props = appendOwnerState(elementType, _extends({}, mergedProps, {\n    ref\n  }), ownerState);\n  return props;\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "unstable_useForkRef", "useForkRef", "appendOwnerState", "mergeSlotProps", "resolveComponentProps", "useSlotProps", "parameters", "_parameters$additiona", "elementType", "externalSlotProps", "ownerState", "rest", "resolvedComponentsProps", "props", "mergedProps", "internalRef", "ref", "additionalProps"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/utils/useSlotProps.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"elementType\", \"externalSlotProps\", \"ownerState\"];\nimport { unstable_useForkRef as useForkRef } from '@mui/utils';\nimport appendOwnerState from './appendOwnerState';\nimport mergeSlotProps from './mergeSlotProps';\nimport resolveComponentProps from './resolveComponentProps';\n\n/**\n * Builds the props to be passed into the slot of an unstyled component.\n * It merges the internal props of the component with the ones supplied by the user, allowing to customize the behavior.\n * If the slot component is not a host component, it also merges in the `ownerState`.\n *\n * @param parameters.getSlotProps - A function that returns the props to be passed to the slot component.\n */\nexport default function useSlotProps(parameters) {\n  var _parameters$additiona;\n\n  const {\n    elementType,\n    externalSlotProps,\n    ownerState\n  } = parameters,\n        rest = _objectWithoutPropertiesLoose(parameters, _excluded);\n\n  const resolvedComponentsProps = resolveComponentProps(externalSlotProps, ownerState);\n  const {\n    props: mergedProps,\n    internalRef\n  } = mergeSlotProps(_extends({}, rest, {\n    externalSlotProps: resolvedComponentsProps\n  }));\n  const ref = useForkRef(internalRef, useForkRef(resolvedComponentsProps == null ? void 0 : resolvedComponentsProps.ref, (_parameters$additiona = parameters.additionalProps) == null ? void 0 : _parameters$additiona.ref));\n  const props = appendOwnerState(elementType, _extends({}, mergedProps, {\n    ref\n  }), ownerState);\n  return props;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,aAAa,EAAE,mBAAmB,EAAE,YAAY,CAAC;AACpE,SAASC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAC9D,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,qBAAqB,MAAM,yBAAyB;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,YAAYA,CAACC,UAAU,EAAE;EAC/C,IAAIC,qBAAqB;EAEzB,MAAM;MACJC,WAAW;MACXC,iBAAiB;MACjBC;IACF,CAAC,GAAGJ,UAAU;IACRK,IAAI,GAAGb,6BAA6B,CAACQ,UAAU,EAAEP,SAAS,CAAC;EAEjE,MAAMa,uBAAuB,GAAGR,qBAAqB,CAACK,iBAAiB,EAAEC,UAAU,CAAC;EACpF,MAAM;IACJG,KAAK,EAAEC,WAAW;IAClBC;EACF,CAAC,GAAGZ,cAAc,CAACN,QAAQ,CAAC,CAAC,CAAC,EAAEc,IAAI,EAAE;IACpCF,iBAAiB,EAAEG;EACrB,CAAC,CAAC,CAAC;EACH,MAAMI,GAAG,GAAGf,UAAU,CAACc,WAAW,EAAEd,UAAU,CAACW,uBAAuB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,uBAAuB,CAACI,GAAG,EAAE,CAACT,qBAAqB,GAAGD,UAAU,CAACW,eAAe,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGV,qBAAqB,CAACS,GAAG,CAAC,CAAC;EAC1N,MAAMH,KAAK,GAAGX,gBAAgB,CAACM,WAAW,EAAEX,QAAQ,CAAC,CAAC,CAAC,EAAEiB,WAAW,EAAE;IACpEE;EACF,CAAC,CAAC,EAAEN,UAAU,CAAC;EACf,OAAOG,KAAK;AACd"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}