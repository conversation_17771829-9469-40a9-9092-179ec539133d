{"ast": null, "code": "import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getMenuUnstyledUtilityClass(slot) {\n  return generateUtilityClass('MuiMenuUnstyled', slot);\n}\nconst menuUnstyledClasses = generateUtilityClasses('MuiMenuUnstyled', ['root', 'listbox', 'expanded']);\nexport default menuUnstyledClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getMenuUnstyledUtilityClass", "slot", "menuUnstyledClasses"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/MenuUnstyled/menuUnstyledClasses.js"], "sourcesContent": ["import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getMenuUnstyledUtilityClass(slot) {\n  return generateUtilityClass('MuiMenuUnstyled', slot);\n}\nconst menuUnstyledClasses = generateUtilityClasses('MuiMenuUnstyled', ['root', 'listbox', 'expanded']);\nexport default menuUnstyledClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,yBAAyB;AAC1D,OAAOC,sBAAsB,MAAM,2BAA2B;AAC9D,OAAO,SAASC,2BAA2BA,CAACC,IAAI,EAAE;EAChD,OAAOH,oBAAoB,CAAC,iBAAiB,EAAEG,IAAI,CAAC;AACtD;AACA,MAAMC,mBAAmB,GAAGH,sBAAsB,CAAC,iBAAiB,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;AACtG,eAAeG,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}