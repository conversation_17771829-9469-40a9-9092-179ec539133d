{"ast": null, "code": "//Types of elements found in the DOM\nmodule.exports = {\n  Text: \"text\",\n  //Text\n  Directive: \"directive\",\n  //<? ... ?>\n  Comment: \"comment\",\n  //<!-- ... -->\n  Script: \"script\",\n  //<script> tags\n  Style: \"style\",\n  //<style> tags\n  Tag: \"tag\",\n  //Any tag\n  CDATA: \"cdata\",\n  //<![CDATA[ ... ]]>\n  Doctype: \"doctype\",\n  isTag: function (elem) {\n    return elem.type === \"tag\" || elem.type === \"script\" || elem.type === \"style\";\n  }\n};", "map": {"version": 3, "names": ["module", "exports", "Text", "Directive", "Comment", "<PERSON><PERSON><PERSON>", "Style", "Tag", "CDATA", "Doctype", "isTag", "elem", "type"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/domutils/node_modules/domelementtype/index.js"], "sourcesContent": ["//Types of elements found in the DOM\nmodule.exports = {\n\tText: \"text\", //Text\n\tDirective: \"directive\", //<? ... ?>\n\tComment: \"comment\", //<!-- ... -->\n\tScript: \"script\", //<script> tags\n\tStyle: \"style\", //<style> tags\n\tTag: \"tag\", //Any tag\n\tCDATA: \"cdata\", //<![CDATA[ ... ]]>\n\tDoctype: \"doctype\",\n\n\tisTag: function(elem){\n\t\treturn elem.type === \"tag\" || elem.type === \"script\" || elem.type === \"style\";\n\t}\n};\n"], "mappings": "AAAA;AACAA,MAAM,CAACC,OAAO,GAAG;EAChBC,IAAI,EAAE,MAAM;EAAE;EACdC,SAAS,EAAE,WAAW;EAAE;EACxBC,OAAO,EAAE,SAAS;EAAE;EACpBC,MAAM,EAAE,QAAQ;EAAE;EAClBC,KAAK,EAAE,OAAO;EAAE;EAChBC,GAAG,EAAE,KAAK;EAAE;EACZC,KAAK,EAAE,OAAO;EAAE;EAChBC,OAAO,EAAE,SAAS;EAElBC,KAAK,EAAE,SAAAA,CAASC,IAAI,EAAC;IACpB,OAAOA,IAAI,CAACC,IAAI,KAAK,KAAK,IAAID,IAAI,CAACC,IAAI,KAAK,QAAQ,IAAID,IAAI,CAACC,IAAI,KAAK,OAAO;EAC9E;AACD,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}