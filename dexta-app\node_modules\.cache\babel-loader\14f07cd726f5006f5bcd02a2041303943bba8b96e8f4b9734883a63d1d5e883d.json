{"ast": null, "code": "export function createInvalidObservableTypeError(input) {\n  return new TypeError(\"You provided \" + (input !== null && typeof input === 'object' ? 'an invalid object' : \"'\" + input + \"'\") + \" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.\");\n}", "map": {"version": 3, "names": ["createInvalidObservableTypeError", "input", "TypeError"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\util\\throwUnobservableError.ts"], "sourcesContent": ["/**\n * Creates the TypeError to throw if an invalid object is passed to `from` or `scheduled`.\n * @param input The object that was passed.\n */\nexport function createInvalidObservableTypeError(input: any) {\n  // TODO: We should create error codes that can be looked up, so this can be less verbose.\n  return new TypeError(\n    `You provided ${\n      input !== null && typeof input === 'object' ? 'an invalid object' : `'${input}'`\n    } where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`\n  );\n}\n"], "mappings": "AAIA,OAAM,SAAUA,gCAAgCA,CAACC,KAAU;EAEzD,OAAO,IAAIC,SAAS,CAClB,mBACED,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,GAAG,mBAAmB,GAAG,MAAIA,KAAK,MAAG,8HACwC,CAC3H;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}