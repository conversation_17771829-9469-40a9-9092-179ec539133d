{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport isHostComponent from './isHostComponent';\n/**\n * Type of the ownerState based on the type of an element it applies to.\n * This resolves to the provided OwnerState for React components and `undefined` for host components.\n * Falls back to `OwnerState | undefined` when the exact type can't be determined in development time.\n */\n\n/**\n * Appends the ownerState object to the props, merging with the existing one if necessary.\n *\n * @param elementType Type of the element that owns the `existingProps`. If the element is a DOM node, `ownerState` is not applied.\n * @param otherProps Props of the element.\n * @param ownerState\n */\nexport default function appendOwnerState(elementType, otherProps = {}, ownerState) {\n  if (isHostComponent(elementType)) {\n    return otherProps;\n  }\n  return _extends({}, otherProps, {\n    ownerState: _extends({}, otherProps.ownerState, ownerState)\n  });\n}", "map": {"version": 3, "names": ["_extends", "isHostComponent", "appendOwnerState", "elementType", "otherProps", "ownerState"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/utils/appendOwnerState.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport isHostComponent from './isHostComponent';\n/**\n * Type of the ownerState based on the type of an element it applies to.\n * This resolves to the provided OwnerState for React components and `undefined` for host components.\n * Falls back to `OwnerState | undefined` when the exact type can't be determined in development time.\n */\n\n/**\n * Appends the ownerState object to the props, merging with the existing one if necessary.\n *\n * @param elementType Type of the element that owns the `existingProps`. If the element is a DOM node, `ownerState` is not applied.\n * @param otherProps Props of the element.\n * @param ownerState\n */\nexport default function appendOwnerState(elementType, otherProps = {}, ownerState) {\n  if (isHostComponent(elementType)) {\n    return otherProps;\n  }\n\n  return _extends({}, otherProps, {\n    ownerState: _extends({}, otherProps.ownerState, ownerState)\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,gBAAgBA,CAACC,WAAW,EAAEC,UAAU,GAAG,CAAC,CAAC,EAAEC,UAAU,EAAE;EACjF,IAAIJ,eAAe,CAACE,WAAW,CAAC,EAAE;IAChC,OAAOC,UAAU;EACnB;EAEA,OAAOJ,QAAQ,CAAC,CAAC,CAAC,EAAEI,UAAU,EAAE;IAC9BC,UAAU,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAEI,UAAU,CAACC,UAAU,EAAEA,UAAU;EAC5D,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}