{"ast": null, "code": "import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getOptionUnstyledUtilityClass(slot) {\n  return generateUtilityClass('MuiOptionUnstyled', slot);\n}\nconst optionUnstyledClasses = generateUtilityClasses('MuiOptionUnstyled', ['root', 'disabled', 'selected', 'highlighted']);\nexport default optionUnstyledClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getOptionUnstyledUtilityClass", "slot", "optionUnstyledClasses"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/OptionUnstyled/optionUnstyledClasses.js"], "sourcesContent": ["import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getOptionUnstyledUtilityClass(slot) {\n  return generateUtilityClass('MuiOptionUnstyled', slot);\n}\nconst optionUnstyledClasses = generateUtilityClasses('MuiOptionUnstyled', ['root', 'disabled', 'selected', 'highlighted']);\nexport default optionUnstyledClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,yBAAyB;AAC1D,OAAOC,sBAAsB,MAAM,2BAA2B;AAC9D,OAAO,SAASC,6BAA6BA,CAACC,IAAI,EAAE;EAClD,OAAOH,oBAAoB,CAAC,mBAAmB,EAAEG,IAAI,CAAC;AACxD;AACA,MAAMC,qBAAqB,GAAGH,sBAAsB,CAAC,mBAAmB,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC;AAC1H,eAAeG,qBAAqB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}