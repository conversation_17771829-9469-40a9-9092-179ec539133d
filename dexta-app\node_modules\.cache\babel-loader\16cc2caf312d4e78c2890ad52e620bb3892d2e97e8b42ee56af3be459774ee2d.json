{"ast": null, "code": "import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getTabsListUnstyledUtilityClass(slot) {\n  return generateUtilityClass('TabsListUnstyled', slot);\n}\nconst tabsListUnstyledClasses = generateUtilityClasses('TabsListUnstyled', ['root', 'horizontal', 'vertical']);\nexport default tabsListUnstyledClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTabsListUnstyledUtilityClass", "slot", "tabsListUnstyledClasses"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/TabsListUnstyled/tabsListUnstyledClasses.js"], "sourcesContent": ["import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getTabsListUnstyledUtilityClass(slot) {\n  return generateUtilityClass('TabsListUnstyled', slot);\n}\nconst tabsListUnstyledClasses = generateUtilityClasses('TabsListUnstyled', ['root', 'horizontal', 'vertical']);\nexport default tabsListUnstyledClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,yBAAyB;AAC1D,OAAOC,sBAAsB,MAAM,2BAA2B;AAC9D,OAAO,SAASC,+BAA+BA,CAACC,IAAI,EAAE;EACpD,OAAOH,oBAAoB,CAAC,kBAAkB,EAAEG,IAAI,CAAC;AACvD;AACA,MAAMC,uBAAuB,GAAGH,sBAAsB,CAAC,kBAAkB,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;AAC9G,eAAeG,uBAAuB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}