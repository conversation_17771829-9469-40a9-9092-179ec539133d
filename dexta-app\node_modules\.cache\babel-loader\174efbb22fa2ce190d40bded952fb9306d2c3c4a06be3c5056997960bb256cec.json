{"ast": null, "code": "import { getCountryCallingCode } from 'libphonenumber-js/core';\nexport function getInputValuePrefix(_ref) {\n  var country = _ref.country,\n    international = _ref.international,\n    withCountryCallingCode = _ref.withCountryCallingCode,\n    metadata = _ref.metadata;\n  return country && international && !withCountryCallingCode ? \"+\".concat(getCountryCallingCode(country, metadata)) : '';\n}\nexport function removeInputValuePrefix(value, prefix) {\n  if (prefix) {\n    value = value.slice(prefix.length);\n    if (value[0] === ' ') {\n      value = value.slice(1);\n    }\n  }\n  return value;\n}", "map": {"version": 3, "names": ["getCountryCallingCode", "getInputValuePrefix", "_ref", "country", "international", "withCountryCallingCode", "metadata", "concat", "removeInputValuePrefix", "value", "prefix", "slice", "length"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\react-phone-number-input\\source\\helpers\\inputValuePrefix.js"], "sourcesContent": ["import { getCountryCallingCode } from 'libphonenumber-js/core'\r\n\r\nexport function getInputValuePrefix({\r\n\tcountry,\r\n\tinternational,\r\n\twithCountryCallingCode,\r\n\tmetadata\r\n}) {\r\n\treturn country && international && !withCountryCallingCode ?\r\n\t\t`+${getCountryCallingCode(country, metadata)}` :\r\n\t\t''\r\n}\r\n\r\nexport function removeInputValuePrefix(value, prefix) {\r\n\tif (prefix) {\r\n\t\tvalue = value.slice(prefix.length)\r\n\t\tif (value[0] === ' ') {\r\n\t\t\tvalue = value.slice(1)\r\n\t\t}\r\n\t}\r\n\treturn value\r\n}"], "mappings": "AAAA,SAASA,qBAAT,QAAsC,wBAAtC;AAEA,OAAO,SAASC,mBAATA,CAAAC,IAAA,EAKJ;EAAA,IAJFC,OAIE,GAAAD,IAAA,CAJFC,OAIE;IAHFC,aAGE,GAAAF,IAAA,CAHFE,aAGE;IAFFC,sBAEE,GAAAH,IAAA,CAFFG,sBAEE;IADFC,QACE,GAAAJ,IAAA,CADFI,QACE;EACF,OAAOH,OAAO,IAAIC,aAAX,IAA4B,CAACC,sBAA7B,OAAAE,MAAA,CACFP,qBAAqB,CAACG,OAAD,EAAUG,QAAV,CADnB,IAEN,EAFD;AAGA;AAED,OAAO,SAASE,sBAATA,CAAgCC,KAAhC,EAAuCC,MAAvC,EAA+C;EACrD,IAAIA,MAAJ,EAAY;IACXD,KAAK,GAAGA,KAAK,CAACE,KAAN,CAAYD,MAAM,CAACE,MAAnB,CAAR;IACA,IAAIH,KAAK,CAAC,CAAD,CAAL,KAAa,GAAjB,EAAsB;MACrBA,KAAK,GAAGA,KAAK,CAACE,KAAN,CAAY,CAAZ,CAAR;IACA;EACD;EACD,OAAOF,KAAP;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}