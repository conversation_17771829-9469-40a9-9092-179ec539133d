{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"badgeContent\", \"component\", \"children\", \"components\", \"componentsProps\", \"invisible\", \"max\", \"showZero\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '../composeClasses';\nimport useBadge from './useBadge';\nimport { getBadgeUnstyledUtilityClass } from './badgeUnstyledClasses';\nimport { useSlotProps } from '../utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    invisible\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    badge: ['badge', invisible && 'invisible']\n  };\n  return composeClasses(slots, getBadgeUnstyledUtilityClass, undefined);\n};\n/**\n *\n * Demos:\n *\n * - [Unstyled badge](https://mui.com/base/react-badge/)\n *\n * API:\n *\n * - [BadgeUnstyled API](https://mui.com/base/api/badge-unstyled/)\n */\n\nconst BadgeUnstyled = /*#__PURE__*/React.forwardRef(function BadgeUnstyled(props, ref) {\n  const {\n      component,\n      children,\n      components = {},\n      componentsProps = {},\n      max: maxProp = 99,\n      showZero = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    badgeContent,\n    max,\n    displayValue,\n    invisible\n  } = useBadge(_extends({}, props, {\n    max: maxProp\n  }));\n  const ownerState = _extends({}, props, {\n    badgeContent,\n    invisible,\n    max,\n    showZero\n  });\n  const classes = useUtilityClasses(ownerState);\n  const Root = component || components.Root || 'span';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref\n    },\n    ownerState,\n    className: classes.root\n  });\n  const Badge = components.Badge || 'span';\n  const badgeProps = useSlotProps({\n    elementType: Badge,\n    externalSlotProps: componentsProps.badge,\n    ownerState,\n    className: classes.badge\n  });\n  return /*#__PURE__*/_jsxs(Root, _extends({}, rootProps, {\n    children: [children, /*#__PURE__*/_jsx(Badge, _extends({}, badgeProps, {\n      children: displayValue\n    }))]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? BadgeUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * The content rendered within the badge.\n   */\n  badgeContent: PropTypes.node,\n  /**\n   * The badge will be added relative to this node.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the Badge.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Badge: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The props used for each slot inside the Badge.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    badge: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * If `true`, the badge is invisible.\n   * @default false\n   */\n  invisible: PropTypes.bool,\n  /**\n   * Max count to show.\n   * @default 99\n   */\n  max: PropTypes.number,\n  /**\n   * Controls whether the badge is hidden when `badgeContent` is zero.\n   * @default false\n   */\n  showZero: PropTypes.bool\n} : void 0;\nexport default BadgeUnstyled;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "composeClasses", "useBadge", "getBadgeUnstyledUtilityClass", "useSlotProps", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "invisible", "slots", "root", "badge", "undefined", "BadgeUnstyled", "forwardRef", "props", "ref", "component", "children", "components", "componentsProps", "max", "maxProp", "showZero", "other", "badgeContent", "displayValue", "classes", "Root", "rootProps", "elementType", "externalSlotProps", "externalForwardedProps", "additionalProps", "className", "Badge", "badgeProps", "process", "env", "NODE_ENV", "propTypes", "node", "shape", "oneOfType", "func", "object", "bool", "number"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/BadgeUnstyled/BadgeUnstyled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"badgeContent\", \"component\", \"children\", \"components\", \"componentsProps\", \"invisible\", \"max\", \"showZero\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '../composeClasses';\nimport useBadge from './useBadge';\nimport { getBadgeUnstyledUtilityClass } from './badgeUnstyledClasses';\nimport { useSlotProps } from '../utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\n\nconst useUtilityClasses = ownerState => {\n  const {\n    invisible\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    badge: ['badge', invisible && 'invisible']\n  };\n  return composeClasses(slots, getBadgeUnstyledUtilityClass, undefined);\n};\n/**\n *\n * Demos:\n *\n * - [Unstyled badge](https://mui.com/base/react-badge/)\n *\n * API:\n *\n * - [BadgeUnstyled API](https://mui.com/base/api/badge-unstyled/)\n */\n\n\nconst BadgeUnstyled = /*#__PURE__*/React.forwardRef(function BadgeUnstyled(props, ref) {\n  const {\n    component,\n    children,\n    components = {},\n    componentsProps = {},\n    max: maxProp = 99,\n    showZero = false\n  } = props,\n        other = _objectWithoutPropertiesLoose(props, _excluded);\n\n  const {\n    badgeContent,\n    max,\n    displayValue,\n    invisible\n  } = useBadge(_extends({}, props, {\n    max: maxProp\n  }));\n\n  const ownerState = _extends({}, props, {\n    badgeContent,\n    invisible,\n    max,\n    showZero\n  });\n\n  const classes = useUtilityClasses(ownerState);\n  const Root = component || components.Root || 'span';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref\n    },\n    ownerState,\n    className: classes.root\n  });\n  const Badge = components.Badge || 'span';\n  const badgeProps = useSlotProps({\n    elementType: Badge,\n    externalSlotProps: componentsProps.badge,\n    ownerState,\n    className: classes.badge\n  });\n  return /*#__PURE__*/_jsxs(Root, _extends({}, rootProps, {\n    children: [children, /*#__PURE__*/_jsx(Badge, _extends({}, badgeProps, {\n      children: displayValue\n    }))]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? BadgeUnstyled.propTypes\n/* remove-proptypes */\n= {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * The content rendered within the badge.\n   */\n  badgeContent: PropTypes.node,\n\n  /**\n   * The badge will be added relative to this node.\n   */\n  children: PropTypes.node,\n\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n\n  /**\n   * The components used for each slot inside the Badge.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Badge: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n\n  /**\n   * The props used for each slot inside the Badge.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    badge: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n\n  /**\n   * If `true`, the badge is invisible.\n   * @default false\n   */\n  invisible: PropTypes.bool,\n\n  /**\n   * Max count to show.\n   * @default 99\n   */\n  max: PropTypes.number,\n\n  /**\n   * Controls whether the badge is hidden when `badgeContent` is zero.\n   * @default false\n   */\n  showZero: PropTypes.bool\n} : void 0;\nexport default BadgeUnstyled;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,cAAc,EAAE,WAAW,EAAE,UAAU,EAAE,YAAY,EAAE,iBAAiB,EAAE,WAAW,EAAE,KAAK,EAAE,UAAU,CAAC;AAC5H,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,4BAA4B,QAAQ,wBAAwB;AACrE,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAEjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,KAAK,EAAE,CAAC,OAAO,EAAEH,SAAS,IAAI,WAAW;EAC3C,CAAC;EACD,OAAOV,cAAc,CAACW,KAAK,EAAET,4BAA4B,EAAEY,SAAS,CAAC;AACvE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,MAAMC,aAAa,GAAG,aAAajB,KAAK,CAACkB,UAAU,CAAC,SAASD,aAAaA,CAACE,KAAK,EAAEC,GAAG,EAAE;EACrF,MAAM;MACJC,SAAS;MACTC,QAAQ;MACRC,UAAU,GAAG,CAAC,CAAC;MACfC,eAAe,GAAG,CAAC,CAAC;MACpBC,GAAG,EAAEC,OAAO,GAAG,EAAE;MACjBC,QAAQ,GAAG;IACb,CAAC,GAAGR,KAAK;IACHS,KAAK,GAAG9B,6BAA6B,CAACqB,KAAK,EAAEpB,SAAS,CAAC;EAE7D,MAAM;IACJ8B,YAAY;IACZJ,GAAG;IACHK,YAAY;IACZlB;EACF,CAAC,GAAGT,QAAQ,CAACN,QAAQ,CAAC,CAAC,CAAC,EAAEsB,KAAK,EAAE;IAC/BM,GAAG,EAAEC;EACP,CAAC,CAAC,CAAC;EAEH,MAAMf,UAAU,GAAGd,QAAQ,CAAC,CAAC,CAAC,EAAEsB,KAAK,EAAE;IACrCU,YAAY;IACZjB,SAAS;IACTa,GAAG;IACHE;EACF,CAAC,CAAC;EAEF,MAAMI,OAAO,GAAGrB,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMqB,IAAI,GAAGX,SAAS,IAAIE,UAAU,CAACS,IAAI,IAAI,MAAM;EACnD,MAAMC,SAAS,GAAG5B,YAAY,CAAC;IAC7B6B,WAAW,EAAEF,IAAI;IACjBG,iBAAiB,EAAEX,eAAe,CAACV,IAAI;IACvCsB,sBAAsB,EAAER,KAAK;IAC7BS,eAAe,EAAE;MACfjB;IACF,CAAC;IACDT,UAAU;IACV2B,SAAS,EAAEP,OAAO,CAACjB;EACrB,CAAC,CAAC;EACF,MAAMyB,KAAK,GAAGhB,UAAU,CAACgB,KAAK,IAAI,MAAM;EACxC,MAAMC,UAAU,GAAGnC,YAAY,CAAC;IAC9B6B,WAAW,EAAEK,KAAK;IAClBJ,iBAAiB,EAAEX,eAAe,CAACT,KAAK;IACxCJ,UAAU;IACV2B,SAAS,EAAEP,OAAO,CAAChB;EACrB,CAAC,CAAC;EACF,OAAO,aAAaN,KAAK,CAACuB,IAAI,EAAEnC,QAAQ,CAAC,CAAC,CAAC,EAAEoC,SAAS,EAAE;IACtDX,QAAQ,EAAE,CAACA,QAAQ,EAAE,aAAaf,IAAI,CAACgC,KAAK,EAAE1C,QAAQ,CAAC,CAAC,CAAC,EAAE2C,UAAU,EAAE;MACrElB,QAAQ,EAAEQ;IACZ,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG1B,aAAa,CAAC2B;AACtD,yBACE;EACA;EACA;EACA;EACA;;EAEA;AACF;AACA;EACEf,YAAY,EAAE5B,SAAS,CAAC4C,IAAI;EAE5B;AACF;AACA;EACEvB,QAAQ,EAAErB,SAAS,CAAC4C,IAAI;EAExB;AACF;AACA;AACA;EACExB,SAAS,EAAEpB,SAAS,CAACiC,WAAW;EAEhC;AACF;AACA;AACA;AACA;EACEX,UAAU,EAAEtB,SAAS,CAAC6C,KAAK,CAAC;IAC1BP,KAAK,EAAEtC,SAAS,CAACiC,WAAW;IAC5BF,IAAI,EAAE/B,SAAS,CAACiC;EAClB,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACEV,eAAe,EAAEvB,SAAS,CAAC6C,KAAK,CAAC;IAC/B/B,KAAK,EAAEd,SAAS,CAAC8C,SAAS,CAAC,CAAC9C,SAAS,CAAC+C,IAAI,EAAE/C,SAAS,CAACgD,MAAM,CAAC,CAAC;IAC9DnC,IAAI,EAAEb,SAAS,CAAC8C,SAAS,CAAC,CAAC9C,SAAS,CAAC+C,IAAI,EAAE/C,SAAS,CAACgD,MAAM,CAAC;EAC9D,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACErC,SAAS,EAAEX,SAAS,CAACiD,IAAI;EAEzB;AACF;AACA;AACA;EACEzB,GAAG,EAAExB,SAAS,CAACkD,MAAM;EAErB;AACF;AACA;AACA;EACExB,QAAQ,EAAE1B,SAAS,CAACiD;AACtB,CAAC,GAAG,KAAK,CAAC;AACV,eAAejC,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}