{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useId as useId, unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { MenuUnstyledContext } from '../MenuUnstyled';\nimport { useButton } from '../ButtonUnstyled';\nexport default function useMenuItem(props) {\n  var _itemState$disabled;\n  const {\n    disabled = false,\n    ref,\n    label\n  } = props;\n  const id = useId();\n  const menuContext = React.useContext(MenuUnstyledContext);\n  const itemRef = React.useRef(null);\n  const handleRef = useForkRef(itemRef, ref);\n  if (menuContext === null) {\n    throw new Error('MenuItemUnstyled must be used within a MenuUnstyled');\n  }\n  const {\n    registerItem,\n    unregisterItem,\n    open\n  } = menuContext;\n  React.useEffect(() => {\n    if (id === undefined) {\n      return undefined;\n    }\n    registerItem(id, {\n      disabled,\n      id,\n      ref: itemRef,\n      label\n    });\n    return () => unregisterItem(id);\n  }, [id, registerItem, unregisterItem, disabled, ref, label]);\n  const {\n    getRootProps: getButtonProps,\n    focusVisible\n  } = useButton({\n    disabled,\n    focusableWhenDisabled: true,\n    ref: handleRef\n  }); // Ensure the menu item is focused when highlighted\n\n  const [focusRequested, requestFocus] = React.useState(false);\n  const focusIfRequested = React.useCallback(() => {\n    if (focusRequested && itemRef.current != null) {\n      itemRef.current.focus();\n      requestFocus(false);\n    }\n  }, [focusRequested]);\n  React.useEffect(() => {\n    focusIfRequested();\n  });\n  React.useDebugValue({\n    id,\n    disabled,\n    label\n  });\n  const itemState = menuContext.getItemState(id != null ? id : '');\n  const {\n    highlighted\n  } = itemState != null ? itemState : {\n    highlighted: false\n  };\n  React.useEffect(() => {\n    requestFocus(highlighted && open);\n  }, [highlighted, open]);\n  if (id === undefined) {\n    return {\n      getRootProps: other => _extends({}, other, getButtonProps(other), {\n        role: 'menuitem'\n      }),\n      disabled: false,\n      focusVisible\n    };\n  }\n  return {\n    getRootProps: other => {\n      const optionProps = menuContext.getItemProps(id, other);\n      return _extends({}, other, getButtonProps(other), {\n        tabIndex: optionProps.tabIndex,\n        id: optionProps.id,\n        role: 'menuitem'\n      });\n    },\n    disabled: (_itemState$disabled = itemState == null ? void 0 : itemState.disabled) != null ? _itemState$disabled : false,\n    focusVisible\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_useId", "useId", "unstable_useForkRef", "useForkRef", "MenuUnstyledContext", "useButton", "useMenuItem", "props", "_itemState$disabled", "disabled", "ref", "label", "id", "menuContext", "useContext", "itemRef", "useRef", "handleRef", "Error", "registerItem", "unregisterItem", "open", "useEffect", "undefined", "getRootProps", "getButtonProps", "focusVisible", "focusableWhenDisabled", "focusRequested", "requestFocus", "useState", "focusIfRequested", "useCallback", "current", "focus", "useDebugValue", "itemState", "getItemState", "highlighted", "other", "role", "optionProps", "getItemProps", "tabIndex"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/MenuItemUnstyled/useMenuItem.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useId as useId, unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { MenuUnstyledContext } from '../MenuUnstyled';\nimport { useButton } from '../ButtonUnstyled';\nexport default function useMenuItem(props) {\n  var _itemState$disabled;\n\n  const {\n    disabled = false,\n    ref,\n    label\n  } = props;\n  const id = useId();\n  const menuContext = React.useContext(MenuUnstyledContext);\n  const itemRef = React.useRef(null);\n  const handleRef = useForkRef(itemRef, ref);\n\n  if (menuContext === null) {\n    throw new Error('MenuItemUnstyled must be used within a MenuUnstyled');\n  }\n\n  const {\n    registerItem,\n    unregisterItem,\n    open\n  } = menuContext;\n  React.useEffect(() => {\n    if (id === undefined) {\n      return undefined;\n    }\n\n    registerItem(id, {\n      disabled,\n      id,\n      ref: itemRef,\n      label\n    });\n    return () => unregisterItem(id);\n  }, [id, registerItem, unregisterItem, disabled, ref, label]);\n  const {\n    getRootProps: getButtonProps,\n    focusVisible\n  } = useButton({\n    disabled,\n    focusableWhenDisabled: true,\n    ref: handleRef\n  }); // Ensure the menu item is focused when highlighted\n\n  const [focusRequested, requestFocus] = React.useState(false);\n  const focusIfRequested = React.useCallback(() => {\n    if (focusRequested && itemRef.current != null) {\n      itemRef.current.focus();\n      requestFocus(false);\n    }\n  }, [focusRequested]);\n  React.useEffect(() => {\n    focusIfRequested();\n  });\n  React.useDebugValue({\n    id,\n    disabled,\n    label\n  });\n  const itemState = menuContext.getItemState(id != null ? id : '');\n  const {\n    highlighted\n  } = itemState != null ? itemState : {\n    highlighted: false\n  };\n  React.useEffect(() => {\n    requestFocus(highlighted && open);\n  }, [highlighted, open]);\n\n  if (id === undefined) {\n    return {\n      getRootProps: other => _extends({}, other, getButtonProps(other), {\n        role: 'menuitem'\n      }),\n      disabled: false,\n      focusVisible\n    };\n  }\n\n  return {\n    getRootProps: other => {\n      const optionProps = menuContext.getItemProps(id, other);\n      return _extends({}, other, getButtonProps(other), {\n        tabIndex: optionProps.tabIndex,\n        id: optionProps.id,\n        role: 'menuitem'\n      });\n    },\n    disabled: (_itemState$disabled = itemState == null ? void 0 : itemState.disabled) != null ? _itemState$disabled : false,\n    focusVisible\n  };\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,IAAIC,KAAK,EAAEC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AACvF,SAASC,mBAAmB,QAAQ,iBAAiB;AACrD,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,eAAe,SAASC,WAAWA,CAACC,KAAK,EAAE;EACzC,IAAIC,mBAAmB;EAEvB,MAAM;IACJC,QAAQ,GAAG,KAAK;IAChBC,GAAG;IACHC;EACF,CAAC,GAAGJ,KAAK;EACT,MAAMK,EAAE,GAAGX,KAAK,CAAC,CAAC;EAClB,MAAMY,WAAW,GAAGd,KAAK,CAACe,UAAU,CAACV,mBAAmB,CAAC;EACzD,MAAMW,OAAO,GAAGhB,KAAK,CAACiB,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMC,SAAS,GAAGd,UAAU,CAACY,OAAO,EAAEL,GAAG,CAAC;EAE1C,IAAIG,WAAW,KAAK,IAAI,EAAE;IACxB,MAAM,IAAIK,KAAK,CAAC,qDAAqD,CAAC;EACxE;EAEA,MAAM;IACJC,YAAY;IACZC,cAAc;IACdC;EACF,CAAC,GAAGR,WAAW;EACfd,KAAK,CAACuB,SAAS,CAAC,MAAM;IACpB,IAAIV,EAAE,KAAKW,SAAS,EAAE;MACpB,OAAOA,SAAS;IAClB;IAEAJ,YAAY,CAACP,EAAE,EAAE;MACfH,QAAQ;MACRG,EAAE;MACFF,GAAG,EAAEK,OAAO;MACZJ;IACF,CAAC,CAAC;IACF,OAAO,MAAMS,cAAc,CAACR,EAAE,CAAC;EACjC,CAAC,EAAE,CAACA,EAAE,EAAEO,YAAY,EAAEC,cAAc,EAAEX,QAAQ,EAAEC,GAAG,EAAEC,KAAK,CAAC,CAAC;EAC5D,MAAM;IACJa,YAAY,EAAEC,cAAc;IAC5BC;EACF,CAAC,GAAGrB,SAAS,CAAC;IACZI,QAAQ;IACRkB,qBAAqB,EAAE,IAAI;IAC3BjB,GAAG,EAAEO;EACP,CAAC,CAAC,CAAC,CAAC;;EAEJ,MAAM,CAACW,cAAc,EAAEC,YAAY,CAAC,GAAG9B,KAAK,CAAC+B,QAAQ,CAAC,KAAK,CAAC;EAC5D,MAAMC,gBAAgB,GAAGhC,KAAK,CAACiC,WAAW,CAAC,MAAM;IAC/C,IAAIJ,cAAc,IAAIb,OAAO,CAACkB,OAAO,IAAI,IAAI,EAAE;MAC7ClB,OAAO,CAACkB,OAAO,CAACC,KAAK,CAAC,CAAC;MACvBL,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACD,cAAc,CAAC,CAAC;EACpB7B,KAAK,CAACuB,SAAS,CAAC,MAAM;IACpBS,gBAAgB,CAAC,CAAC;EACpB,CAAC,CAAC;EACFhC,KAAK,CAACoC,aAAa,CAAC;IAClBvB,EAAE;IACFH,QAAQ;IACRE;EACF,CAAC,CAAC;EACF,MAAMyB,SAAS,GAAGvB,WAAW,CAACwB,YAAY,CAACzB,EAAE,IAAI,IAAI,GAAGA,EAAE,GAAG,EAAE,CAAC;EAChE,MAAM;IACJ0B;EACF,CAAC,GAAGF,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAG;IAClCE,WAAW,EAAE;EACf,CAAC;EACDvC,KAAK,CAACuB,SAAS,CAAC,MAAM;IACpBO,YAAY,CAACS,WAAW,IAAIjB,IAAI,CAAC;EACnC,CAAC,EAAE,CAACiB,WAAW,EAAEjB,IAAI,CAAC,CAAC;EAEvB,IAAIT,EAAE,KAAKW,SAAS,EAAE;IACpB,OAAO;MACLC,YAAY,EAAEe,KAAK,IAAIzC,QAAQ,CAAC,CAAC,CAAC,EAAEyC,KAAK,EAAEd,cAAc,CAACc,KAAK,CAAC,EAAE;QAChEC,IAAI,EAAE;MACR,CAAC,CAAC;MACF/B,QAAQ,EAAE,KAAK;MACfiB;IACF,CAAC;EACH;EAEA,OAAO;IACLF,YAAY,EAAEe,KAAK,IAAI;MACrB,MAAME,WAAW,GAAG5B,WAAW,CAAC6B,YAAY,CAAC9B,EAAE,EAAE2B,KAAK,CAAC;MACvD,OAAOzC,QAAQ,CAAC,CAAC,CAAC,EAAEyC,KAAK,EAAEd,cAAc,CAACc,KAAK,CAAC,EAAE;QAChDI,QAAQ,EAAEF,WAAW,CAACE,QAAQ;QAC9B/B,EAAE,EAAE6B,WAAW,CAAC7B,EAAE;QAClB4B,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC;IACD/B,QAAQ,EAAE,CAACD,mBAAmB,GAAG4B,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC3B,QAAQ,KAAK,IAAI,GAAGD,mBAAmB,GAAG,KAAK;IACvHkB;EACF,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}