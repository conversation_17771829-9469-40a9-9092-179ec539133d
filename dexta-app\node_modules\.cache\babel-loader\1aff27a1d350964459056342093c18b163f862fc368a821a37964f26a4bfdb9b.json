{"ast": null, "code": "let nextUniqueId = 0;\nexport function getNextUniqueId() {\n  return nextUniqueId++;\n}", "map": {"version": 3, "names": ["nextUniqueId", "getNextUniqueId"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\dnd-core\\src\\utils\\getNextUniqueId.ts"], "sourcesContent": ["let nextUniqueId = 0\n\nexport function getNextUniqueId(): number {\n\treturn nextUniqueId++\n}\n"], "mappings": "AAAA,IAAIA,YAAY,GAAG,CAAC;AAEpB,OAAO,SAASC,eAAeA,CAAA,EAAW;EACzC,OAAOD,YAAY,EAAE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}