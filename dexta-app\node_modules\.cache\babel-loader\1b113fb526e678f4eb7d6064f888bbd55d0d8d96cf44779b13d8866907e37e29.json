{"ast": null, "code": "var isArray = Array.isArray;\nexport function argsOrArgArray(args) {\n  return args.length === 1 && isArray(args[0]) ? args[0] : args;\n}", "map": {"version": 3, "names": ["isArray", "Array", "argsOrArgArray", "args", "length"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\util\\argsOrArgArray.ts"], "sourcesContent": ["const { isArray } = Array;\n\n/**\n * Used in operators and functions that accept either a list of arguments, or an array of arguments\n * as a single argument.\n */\nexport function argsOrArgArray<T>(args: (T | T[])[]): T[] {\n  return args.length === 1 && isArray(args[0]) ? args[0] : (args as T[]);\n}\n"], "mappings": "AAAQ,IAAAA,OAAO,GAAKC,KAAK,CAAAD,OAAV;AAMf,OAAM,SAAUE,cAAcA,CAAIC,IAAiB;EACjD,OAAOA,IAAI,CAACC,MAAM,KAAK,CAAC,IAAIJ,OAAO,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAIA,IAAY;AACxE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}