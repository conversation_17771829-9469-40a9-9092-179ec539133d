{"ast": null, "code": "export function isReadOnly(element) {\n  return element.hasAttribute('readonly');\n} // Gets <input/> selection bounds\n\nexport function getSelection(element) {\n  // If no selection, return nothing\n  if (element.selectionStart === element.selectionEnd) {\n    return;\n  }\n  return {\n    start: element.selectionStart,\n    end: element.selectionEnd\n  };\n} // Key codes\n\nexport var Keys = {\n  Backspace: 8,\n  Delete: 46\n}; // Finds out the operation to be intercepted and performed\n// based on the key down event `keyCode`.\n\nexport function getOperation(event) {\n  switch (event.keyCode) {\n    case Keys.Backspace:\n      return 'Backspace';\n    case Keys.Delete:\n      return 'Delete';\n  }\n} // Gets <input/> caret position\n\nexport function getCaretPosition(element) {\n  return element.selectionStart;\n} // Sets <input/> caret position\n\nexport function setCaretPosition(element, caret_position) {\n  // Sanity check\n  if (caret_position === undefined) {\n    return;\n  } // Set caret position.\n  // There has been an issue with caret positioning on Android devices.\n  // https://github.com/catamphetamine/input-format/issues/2\n  // I was revisiting this issue and looked for similar issues in other libraries.\n  // For example, there's [`text-mask`](https://github.com/text-mask/text-mask) library.\n  // They've had exactly the same issue when the caret seemingly refused to be repositioned programmatically.\n  // The symptoms were the same: whenever the caret passed through a non-digit character of a mask (a whitespace, a bracket, a dash, etc), it looked as if it placed itself one character before its correct position.\n  // https://github.com/text-mask/text-mask/issues/300\n  // They seem to have found a basic fix for it: calling `input.setSelectionRange()` in a timeout rather than instantly for Android devices.\n  // https://github.com/text-mask/text-mask/pull/400/files\n  // I've implemented the same workaround here.\n\n  if (isAndroid()) {\n    setTimeout(function () {\n      return element.setSelectionRange(caret_position, caret_position);\n    }, 0);\n  } else {\n    element.setSelectionRange(caret_position, caret_position);\n  }\n}\nfunction isAndroid() {\n  // `navigator` is not defined when running mocha tests.\n  if (typeof navigator !== 'undefined') {\n    return ANDROID_USER_AGENT_REG_EXP.test(navigator.userAgent);\n  }\n}\nvar ANDROID_USER_AGENT_REG_EXP = /Android/i;", "map": {"version": 3, "names": ["isReadOnly", "element", "hasAttribute", "getSelection", "selectionStart", "selectionEnd", "start", "end", "Keys", "Backspace", "Delete", "getOperation", "event", "keyCode", "getCaretPosition", "setCaretPosition", "caret_position", "undefined", "isAndroid", "setTimeout", "setSelectionRange", "navigator", "ANDROID_USER_AGENT_REG_EXP", "test", "userAgent"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\input-format\\source\\dom.js"], "sourcesContent": ["export function isReadOnly(element)\r\n{\r\n\treturn element.hasAttribute('readonly')\r\n}\r\n\r\n// Gets <input/> selection bounds\r\nexport function getSelection(element)\r\n{\r\n\t// If no selection, return nothing\r\n\tif (element.selectionStart === element.selectionEnd)\r\n\t{\r\n\t\treturn\r\n\t}\r\n\r\n\treturn { start: element.selectionStart, end: element.selectionEnd }\r\n}\r\n\r\n// Key codes\r\nexport const Keys =\r\n{\r\n\tBackspace : 8,\r\n\tDelete    : 46\r\n}\r\n\r\n// Finds out the operation to be intercepted and performed\r\n// based on the key down event `keyCode`.\r\nexport function getOperation(event)\r\n{\r\n\tswitch (event.keyCode)\r\n\t{\r\n\t\tcase Keys.Backspace:\r\n\t\t\treturn 'Backspace'\r\n\r\n\t\tcase Keys.Delete:\r\n\t\t\treturn 'Delete'\r\n\t}\r\n}\r\n\r\n// Gets <input/> caret position\r\nexport function getCaretPosition(element)\r\n{\r\n\treturn element.selectionStart\r\n}\r\n\r\n// Sets <input/> caret position\r\nexport function setCaretPosition(element, caret_position)\r\n{\r\n\t// Sanity check\r\n\tif (caret_position === undefined)\r\n\t{\r\n\t\treturn\r\n\t}\r\n\r\n\t// Set caret position.\r\n\t// There has been an issue with caret positioning on Android devices.\r\n\t// https://github.com/catamphetamine/input-format/issues/2\r\n\t// I was revisiting this issue and looked for similar issues in other libraries.\r\n\t// For example, there's [`text-mask`](https://github.com/text-mask/text-mask) library.\r\n\t// They've had exactly the same issue when the caret seemingly refused to be repositioned programmatically.\r\n\t// The symptoms were the same: whenever the caret passed through a non-digit character of a mask (a whitespace, a bracket, a dash, etc), it looked as if it placed itself one character before its correct position.\r\n\t// https://github.com/text-mask/text-mask/issues/300\r\n\t// They seem to have found a basic fix for it: calling `input.setSelectionRange()` in a timeout rather than instantly for Android devices.\r\n\t// https://github.com/text-mask/text-mask/pull/400/files\r\n\t// I've implemented the same workaround here.\r\n\tif (isAndroid()) {\r\n      setTimeout(() => element.setSelectionRange(caret_position, caret_position), 0)\r\n\t} else {\r\n\t\telement.setSelectionRange(caret_position, caret_position)\r\n\t}\r\n}\r\n\r\nfunction isAndroid() {\r\n\t// `navigator` is not defined when running mocha tests.\r\n\tif (typeof navigator !== 'undefined') {\r\n\t\treturn ANDROID_USER_AGENT_REG_EXP.test(navigator.userAgent)\r\n\t}\r\n}\r\n\r\nconst ANDROID_USER_AGENT_REG_EXP = /Android/i"], "mappings": "AAAA,OAAO,SAASA,UAATA,CAAoBC,OAApB,EACP;EACC,OAAOA,OAAO,CAACC,YAAR,CAAqB,UAArB,CAAP;AACA,C,CAED;;AACA,OAAO,SAASC,YAATA,CAAsBF,OAAtB,EACP;EACC;EACA,IAAIA,OAAO,CAACG,cAAR,KAA2BH,OAAO,CAACI,YAAvC,EACA;IACC;EACA;EAED,OAAO;IAAEC,KAAK,EAAEL,OAAO,CAACG,cAAjB;IAAiCG,GAAG,EAAEN,OAAO,CAACI;EAA9C,CAAP;AACA,C,CAED;;AACA,OAAO,IAAMG,IAAI,GACjB;EACCC,SAAS,EAAG,CADb;EAECC,MAAM,EAAM;AAFb,CADO,C,CAMP;AACA;;AACA,OAAO,SAASC,YAATA,CAAsBC,KAAtB,EACP;EACC,QAAQA,KAAK,CAACC,OAAd;IAEC,KAAKL,IAAI,CAACC,SAAV;MACC,OAAO,WAAP;IAED,KAAKD,IAAI,CAACE,MAAV;MACC,OAAO,QAAP;EANF;AAQA,C,CAED;;AACA,OAAO,SAASI,gBAATA,CAA0Bb,OAA1B,EACP;EACC,OAAOA,OAAO,CAACG,cAAf;AACA,C,CAED;;AACA,OAAO,SAASW,gBAATA,CAA0Bd,OAA1B,EAAmCe,cAAnC,EACP;EACC;EACA,IAAIA,cAAc,KAAKC,SAAvB,EACA;IACC;EACA,CALF,CAOC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,IAAIC,SAAS,EAAb,EAAiB;IACZC,UAAU,CAAC;MAAA,OAAMlB,OAAO,CAACmB,iBAAR,CAA0BJ,cAA1B,EAA0CA,cAA1C,CAAN;IAAA,CAAD,EAAkE,CAAlE,CAAV;EACJ,CAFD,MAEO;IACNf,OAAO,CAACmB,iBAAR,CAA0BJ,cAA1B,EAA0CA,cAA1C;EACA;AACD;AAED,SAASE,SAATA,CAAA,EAAqB;EACpB;EACA,IAAI,OAAOG,SAAP,KAAqB,WAAzB,EAAsC;IACrC,OAAOC,0BAA0B,CAACC,IAA3B,CAAgCF,SAAS,CAACG,SAA1C,CAAP;EACA;AACD;AAED,IAAMF,0BAA0B,GAAG,UAAnC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}