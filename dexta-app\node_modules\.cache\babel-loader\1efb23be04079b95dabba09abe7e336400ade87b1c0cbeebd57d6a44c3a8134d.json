{"ast": null, "code": "// Deprecated. Import from 'metadata.js' directly instead.\nexport { getCountryCallingCode as default } from './metadata.js';", "map": {"version": 3, "names": ["getCountryCallingCode", "default"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\getCountryCallingCode.js"], "sourcesContent": ["// Deprecated. Import from 'metadata.js' directly instead.\r\nexport { getCountryCallingCode as default } from './metadata.js'"], "mappings": "AAAA;AACA,SAASA,qBAAqB,IAAIC,OAAlC,QAAiD,eAAjD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}