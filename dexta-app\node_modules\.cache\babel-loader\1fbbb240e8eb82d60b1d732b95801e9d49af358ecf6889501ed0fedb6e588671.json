{"ast": null, "code": "import { invariant } from '@react-dnd/invariant';\nimport { matchesType } from '../../utils/matchesType.js';\nimport { HOVER } from './types.js';\nexport function createHover(manager) {\n  return function hover(targetIdsArg, {\n    clientOffset\n  } = {}) {\n    verifyTargetIdsIsArray(targetIdsArg);\n    const targetIds = targetIdsArg.slice(0);\n    const monitor = manager.getMonitor();\n    const registry = manager.getRegistry();\n    const draggedItemType = monitor.getItemType();\n    removeNonMatchingTargetIds(targetIds, registry, draggedItemType);\n    checkInvariants(targetIds, monitor, registry);\n    hoverAllTargets(targetIds, monitor, registry);\n    return {\n      type: HOVER,\n      payload: {\n        targetIds,\n        clientOffset: clientOffset || null\n      }\n    };\n  };\n}\nfunction verifyTargetIdsIsArray(targetIdsArg) {\n  invariant(Array.isArray(targetIdsArg), 'Expected targetIds to be an array.');\n}\nfunction checkInvariants(targetIds, monitor, registry) {\n  invariant(monitor.isDragging(), 'Cannot call hover while not dragging.');\n  invariant(!monitor.didDrop(), 'Cannot call hover after drop.');\n  for (let i = 0; i < targetIds.length; i++) {\n    const targetId = targetIds[i];\n    invariant(targetIds.lastIndexOf(targetId) === i, 'Expected targetIds to be unique in the passed array.');\n    const target = registry.getTarget(targetId);\n    invariant(target, 'Expected targetIds to be registered.');\n  }\n}\nfunction removeNonMatchingTargetIds(targetIds, registry, draggedItemType) {\n  // Remove those targetIds that don't match the targetType.  This\n  // fixes shallow isOver which would only be non-shallow because of\n  // non-matching targets.\n  for (let i = targetIds.length - 1; i >= 0; i--) {\n    const targetId = targetIds[i];\n    const targetType = registry.getTargetType(targetId);\n    if (!matchesType(targetType, draggedItemType)) {\n      targetIds.splice(i, 1);\n    }\n  }\n}\nfunction hoverAllTargets(targetIds, monitor, registry) {\n  // Finally call hover on all matching targets.\n  targetIds.forEach(function (targetId) {\n    const target = registry.getTarget(targetId);\n    target.hover(monitor, targetId);\n  });\n}", "map": {"version": 3, "names": ["invariant", "matchesType", "HOVER", "createHover", "manager", "hover", "targetIdsArg", "clientOffset", "verifyTargetIdsIsArray", "targetIds", "slice", "monitor", "getMonitor", "registry", "getRegistry", "draggedItemType", "getItemType", "removeNonMatchingTargetIds", "checkInvariants", "hoverAllTargets", "type", "payload", "Array", "isArray", "isDragging", "didDrop", "i", "length", "targetId", "lastIndexOf", "target", "get<PERSON><PERSON><PERSON>", "targetType", "getTargetType", "splice", "for<PERSON>ach"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\dnd-core\\src\\actions\\dragDrop\\hover.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\n\nimport type {\n\tAction,\n\tDragDropManager,\n\tDragDropMonitor,\n\tHandlerRegistry,\n\tHoverOptions,\n\tHoverPayload,\n\tIdentifier,\n} from '../../interfaces.js'\nimport { matchesType } from '../../utils/matchesType.js'\nimport { HOVER } from './types.js'\n\nexport function createHover(manager: DragDropManager) {\n\treturn function hover(\n\t\ttargetIdsArg: string[],\n\t\t{ clientOffset }: HoverOptions = {},\n\t): Action<HoverPayload> {\n\t\tverifyTargetIdsIsArray(targetIdsArg)\n\t\tconst targetIds = targetIdsArg.slice(0)\n\t\tconst monitor = manager.getMonitor()\n\t\tconst registry = manager.getRegistry()\n\t\tconst draggedItemType = monitor.getItemType()\n\t\tremoveNonMatchingTargetIds(targetIds, registry, draggedItemType)\n\t\tcheckInvariants(targetIds, monitor, registry)\n\t\thoverAllTargets(targetIds, monitor, registry)\n\n\t\treturn {\n\t\t\ttype: HOVER,\n\t\t\tpayload: {\n\t\t\t\ttargetIds,\n\t\t\t\tclientOffset: clientOffset || null,\n\t\t\t},\n\t\t}\n\t}\n}\n\nfunction verifyTargetIdsIsArray(targetIdsArg: string[]) {\n\tinvariant(Array.isArray(targetIdsArg), 'Expected targetIds to be an array.')\n}\n\nfunction checkInvariants(\n\ttargetIds: string[],\n\tmonitor: DragDropMonitor,\n\tregistry: HandlerRegistry,\n) {\n\tinvariant(monitor.isDragging(), 'Cannot call hover while not dragging.')\n\tinvariant(!monitor.didDrop(), 'Cannot call hover after drop.')\n\tfor (let i = 0; i < targetIds.length; i++) {\n\t\tconst targetId = targetIds[i] as string\n\t\tinvariant(\n\t\t\ttargetIds.lastIndexOf(targetId) === i,\n\t\t\t'Expected targetIds to be unique in the passed array.',\n\t\t)\n\n\t\tconst target = registry.getTarget(targetId)\n\t\tinvariant(target, 'Expected targetIds to be registered.')\n\t}\n}\n\nfunction removeNonMatchingTargetIds(\n\ttargetIds: string[],\n\tregistry: HandlerRegistry,\n\tdraggedItemType: Identifier | null,\n) {\n\t// Remove those targetIds that don't match the targetType.  This\n\t// fixes shallow isOver which would only be non-shallow because of\n\t// non-matching targets.\n\tfor (let i = targetIds.length - 1; i >= 0; i--) {\n\t\tconst targetId = targetIds[i] as string\n\t\tconst targetType = registry.getTargetType(targetId)\n\t\tif (!matchesType(targetType, draggedItemType)) {\n\t\t\ttargetIds.splice(i, 1)\n\t\t}\n\t}\n}\n\nfunction hoverAllTargets(\n\ttargetIds: string[],\n\tmonitor: DragDropMonitor,\n\tregistry: HandlerRegistry,\n) {\n\t// Finally call hover on all matching targets.\n\ttargetIds.forEach(function (targetId) {\n\t\tconst target = registry.getTarget(targetId)\n\t\ttarget.hover(monitor, targetId)\n\t})\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB;AAWhD,SAASC,WAAW,QAAQ,4BAA4B;AACxD,SAASC,KAAK,QAAQ,YAAY;AAElC,OAAO,SAASC,WAAWA,CAACC,OAAwB,EAAE;EACrD,OAAO,SAASC,KAAKA,CACpBC,YAAsB,EACtB;IAAEC;EAAY,CAAgB,GAAG,EAAE,EACZ;IACvBC,sBAAsB,CAACF,YAAY,CAAC;IACpC,MAAMG,SAAS,GAAGH,YAAY,CAACI,KAAK,CAAC,CAAC,CAAC;IACvC,MAAMC,OAAO,GAAGP,OAAO,CAACQ,UAAU,EAAE;IACpC,MAAMC,QAAQ,GAAGT,OAAO,CAACU,WAAW,EAAE;IACtC,MAAMC,eAAe,GAAGJ,OAAO,CAACK,WAAW,EAAE;IAC7CC,0BAA0B,CAACR,SAAS,EAAEI,QAAQ,EAAEE,eAAe,CAAC;IAChEG,eAAe,CAACT,SAAS,EAAEE,OAAO,EAAEE,QAAQ,CAAC;IAC7CM,eAAe,CAACV,SAAS,EAAEE,OAAO,EAAEE,QAAQ,CAAC;IAE7C,OAAO;MACNO,IAAI,EAAElB,KAAK;MACXmB,OAAO,EAAE;QACRZ,SAAS;QACTF,YAAY,EAAEA,YAAY,IAAI;;KAE/B;GACD;;AAGF,SAASC,sBAAsBA,CAACF,YAAsB,EAAE;EACvDN,SAAS,CAACsB,KAAK,CAACC,OAAO,CAACjB,YAAY,CAAC,EAAE,oCAAoC,CAAC;;AAG7E,SAASY,eAAeA,CACvBT,SAAmB,EACnBE,OAAwB,EACxBE,QAAyB,EACxB;EACDb,SAAS,CAACW,OAAO,CAACa,UAAU,EAAE,EAAE,uCAAuC,CAAC;EACxExB,SAAS,CAAC,CAACW,OAAO,CAACc,OAAO,EAAE,EAAE,+BAA+B,CAAC;EAC9D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,SAAS,CAACkB,MAAM,EAAED,CAAC,EAAE,EAAE;IAC1C,MAAME,QAAQ,GAAGnB,SAAS,CAACiB,CAAC,CAAC;IAC7B1B,SAAS,CACRS,SAAS,CAACoB,WAAW,CAACD,QAAQ,CAAC,KAAKF,CAAC,EACrC,sDAAsD,CACtD;IAED,MAAMI,MAAM,GAAGjB,QAAQ,CAACkB,SAAS,CAACH,QAAQ,CAAC;IAC3C5B,SAAS,CAAC8B,MAAM,EAAE,sCAAsC,CAAC;;;AAI3D,SAASb,0BAA0BA,CAClCR,SAAmB,EACnBI,QAAyB,EACzBE,eAAkC,EACjC;EACD;EACA;EACA;EACA,KAAK,IAAIW,CAAC,GAAGjB,SAAS,CAACkB,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC/C,MAAME,QAAQ,GAAGnB,SAAS,CAACiB,CAAC,CAAC;IAC7B,MAAMM,UAAU,GAAGnB,QAAQ,CAACoB,aAAa,CAACL,QAAQ,CAAC;IACnD,IAAI,CAAC3B,WAAW,CAAC+B,UAAU,EAAEjB,eAAe,CAAC,EAAE;MAC9CN,SAAS,CAACyB,MAAM,CAACR,CAAC,EAAE,CAAC,CAAC;;;;AAKzB,SAASP,eAAeA,CACvBV,SAAmB,EACnBE,OAAwB,EACxBE,QAAyB,EACxB;EACD;EACAJ,SAAS,CAAC0B,OAAO,CAAC,UAAUP,QAAQ,EAAE;IACrC,MAAME,MAAM,GAAGjB,QAAQ,CAACkB,SAAS,CAACH,QAAQ,CAAC;IAC3CE,MAAM,CAACzB,KAAK,CAACM,OAAO,EAAEiB,QAAQ,CAAC;GAC/B,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}