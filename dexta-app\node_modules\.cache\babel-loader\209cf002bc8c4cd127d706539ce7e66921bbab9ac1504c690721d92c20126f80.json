{"ast": null, "code": "import { __asyncGenerator, __await, __generator } from \"tslib\";\nimport { isFunction } from './isFunction';\nexport function readableStreamLikeToAsyncGenerator(readableStream) {\n  return __asyncGenerator(this, arguments, function readableStreamLikeToAsyncGenerator_1() {\n    var reader, _a, value, done;\n    return __generator(this, function (_b) {\n      switch (_b.label) {\n        case 0:\n          reader = readableStream.getReader();\n          _b.label = 1;\n        case 1:\n          _b.trys.push([1,, 9, 10]);\n          _b.label = 2;\n        case 2:\n          if (!true) return [3, 8];\n          return [4, __await(reader.read())];\n        case 3:\n          _a = _b.sent(), value = _a.value, done = _a.done;\n          if (!done) return [3, 5];\n          return [4, __await(void 0)];\n        case 4:\n          return [2, _b.sent()];\n        case 5:\n          return [4, __await(value)];\n        case 6:\n          return [4, _b.sent()];\n        case 7:\n          _b.sent();\n          return [3, 2];\n        case 8:\n          return [3, 10];\n        case 9:\n          reader.releaseLock();\n          return [7];\n        case 10:\n          return [2];\n      }\n    });\n  });\n}\nexport function isReadableStreamLike(obj) {\n  return isFunction(obj === null || obj === void 0 ? void 0 : obj.getReader);\n}", "map": {"version": 3, "names": ["isFunction", "readableStreamLikeToAsyncGenerator", "readableStream", "reader", "<PERSON><PERSON><PERSON><PERSON>", "__await", "read", "_a", "_b", "sent", "value", "done", "releaseLock", "isReadableStreamLike", "obj"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\util\\isReadableStreamLike.ts"], "sourcesContent": ["import { ReadableStreamLike } from '../types';\nimport { isFunction } from './isFunction';\n\nexport async function* readableStreamLikeToAsyncGenerator<T>(readableStream: ReadableStreamLike<T>): AsyncGenerator<T> {\n  const reader = readableStream.getReader();\n  try {\n    while (true) {\n      const { value, done } = await reader.read();\n      if (done) {\n        return;\n      }\n      yield value!;\n    }\n  } finally {\n    reader.releaseLock();\n  }\n}\n\nexport function isReadableStreamLike<T>(obj: any): obj is ReadableStreamLike<T> {\n  // We don't want to use instanceof checks because they would return\n  // false for instances from another Realm, like an <iframe>.\n  return isFunction(obj?.getReader);\n}\n"], "mappings": ";AACA,SAASA,UAAU,QAAQ,cAAc;AAEzC,OAAM,SAAiBC,kCAAkCA,CAAIC,cAAqC;;;;;;UAC1FC,MAAM,GAAGD,cAAc,CAACE,SAAS,EAAE;;;;;;eAEhC,IAAI;UACe,WAAAC,OAAA,CAAMF,MAAM,CAACG,IAAI,EAAE;;UAArCC,EAAA,GAAkBC,EAAA,CAAAC,IAAA,EAAmB,EAAnCC,KAAK,GAAAH,EAAA,CAAAG,KAAA,EAAEC,IAAI,GAAAJ,EAAA,CAAAI,IAAA;eACfA,IAAI,EAAJ;;;UACF,WAAAH,EAAA,CAAAC,IAAA;;6BAEIC,KAAM;;UAAZ,WAAAF,EAAA,CAAAC,IAAA;;UAAAD,EAAA,CAAAC,IAAA,EAAY;;;;;UAGdN,MAAM,CAACS,WAAW,EAAE;;;;;;;;AAIxB,OAAM,SAAUC,oBAAoBA,CAAIC,GAAQ;EAG9C,OAAOd,UAAU,CAACc,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEV,SAAS,CAAC;AACnC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}