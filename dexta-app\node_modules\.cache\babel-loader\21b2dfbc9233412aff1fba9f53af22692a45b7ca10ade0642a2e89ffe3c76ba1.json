{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { AsyncScheduler } from './AsyncScheduler';\nvar AsapScheduler = function (_super) {\n  __extends(AsapScheduler, _super);\n  function AsapScheduler() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  AsapScheduler.prototype.flush = function (action) {\n    this._active = true;\n    var flushId = this._scheduled;\n    this._scheduled = undefined;\n    var actions = this.actions;\n    var error;\n    action = action || actions.shift();\n    do {\n      if (error = action.execute(action.state, action.delay)) {\n        break;\n      }\n    } while ((action = actions[0]) && action.id === flushId && actions.shift());\n    this._active = false;\n    if (error) {\n      while ((action = actions[0]) && action.id === flushId && actions.shift()) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  };\n  return AsapScheduler;\n}(AsyncScheduler);\nexport { AsapScheduler };", "map": {"version": 3, "names": ["AsyncScheduler", "AsapScheduler", "_super", "__extends", "prototype", "flush", "action", "_active", "flushId", "_scheduled", "undefined", "actions", "error", "shift", "execute", "state", "delay", "id", "unsubscribe"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\scheduler\\AsapScheduler.ts"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nimport { AsyncScheduler } from './AsyncScheduler';\n\nexport class AsapScheduler extends AsyncScheduler {\n  public flush(action?: AsyncAction<any>): void {\n    this._active = true;\n    // The async id that effects a call to flush is stored in _scheduled.\n    // Before executing an action, it's necessary to check the action's async\n    // id to determine whether it's supposed to be executed in the current\n    // flush.\n    // Previous implementations of this method used a count to determine this,\n    // but that was unsound, as actions that are unsubscribed - i.e. cancelled -\n    // are removed from the actions array and that can shift actions that are\n    // scheduled to be executed in a subsequent flush into positions at which\n    // they are executed within the current flush.\n    const flushId = this._scheduled;\n    this._scheduled = undefined;\n\n    const { actions } = this;\n    let error: any;\n    action = action || actions.shift()!;\n\n    do {\n      if ((error = action.execute(action.state, action.delay))) {\n        break;\n      }\n    } while ((action = actions[0]) && action.id === flushId && actions.shift());\n\n    this._active = false;\n\n    if (error) {\n      while ((action = actions[0]) && action.id === flushId && actions.shift()) {\n        action.unsubscribe();\n      }\n      throw error;\n    }\n  }\n}\n"], "mappings": ";AACA,SAASA,cAAc,QAAQ,kBAAkB;AAEjD,IAAAC,aAAA,aAAAC,MAAA;EAAmCC,SAAA,CAAAF,aAAA,EAAAC,MAAA;EAAnC,SAAAD,cAAA;;EAkCA;EAjCSA,aAAA,CAAAG,SAAA,CAAAC,KAAK,GAAZ,UAAaC,MAAyB;IACpC,IAAI,CAACC,OAAO,GAAG,IAAI;IAUnB,IAAMC,OAAO,GAAG,IAAI,CAACC,UAAU;IAC/B,IAAI,CAACA,UAAU,GAAGC,SAAS;IAEnB,IAAAC,OAAO,GAAK,IAAI,CAAAA,OAAT;IACf,IAAIC,KAAU;IACdN,MAAM,GAAGA,MAAM,IAAIK,OAAO,CAACE,KAAK,EAAG;IAEnC,GAAG;MACD,IAAKD,KAAK,GAAGN,MAAM,CAACQ,OAAO,CAACR,MAAM,CAACS,KAAK,EAAET,MAAM,CAACU,KAAK,CAAC,EAAG;QACxD;;KAEH,QAAQ,CAACV,MAAM,GAAGK,OAAO,CAAC,CAAC,CAAC,KAAKL,MAAM,CAACW,EAAE,KAAKT,OAAO,IAAIG,OAAO,CAACE,KAAK,EAAE;IAE1E,IAAI,CAACN,OAAO,GAAG,KAAK;IAEpB,IAAIK,KAAK,EAAE;MACT,OAAO,CAACN,MAAM,GAAGK,OAAO,CAAC,CAAC,CAAC,KAAKL,MAAM,CAACW,EAAE,KAAKT,OAAO,IAAIG,OAAO,CAACE,KAAK,EAAE,EAAE;QACxEP,MAAM,CAACY,WAAW,EAAE;;MAEtB,MAAMN,KAAK;;EAEf,CAAC;EACH,OAAAX,aAAC;AAAD,CAAC,CAlCkCD,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}