{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"component\", \"components\", \"componentsProps\", \"disabled\", \"value\", \"label\"];\nimport React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_useForkRef as useForkRef } from '@mui/utils';\nimport composeClasses from '../composeClasses';\nimport { SelectUnstyledContext } from '../SelectUnstyled/SelectUnstyledContext';\nimport { getOptionUnstyledUtilityClass } from './optionUnstyledClasses';\nimport { useSlotProps } from '../utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction useUtilityClasses(ownerState) {\n  const {\n    disabled,\n    highlighted,\n    selected\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', highlighted && 'highlighted', selected && 'selected']\n  };\n  return composeClasses(slots, getOptionUnstyledUtilityClass, {});\n}\n/**\n * An unstyled option to be used within a SelectUnstyled.\n */\n\nconst OptionUnstyled = /*#__PURE__*/React.forwardRef(function OptionUnstyled(props, ref) {\n  const {\n      children,\n      component,\n      components = {},\n      componentsProps = {},\n      disabled,\n      value,\n      label\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const selectContext = React.useContext(SelectUnstyledContext);\n  if (!selectContext) {\n    throw new Error('OptionUnstyled must be used within a SelectUnstyled');\n  }\n  const Root = component || components.Root || 'li';\n  const selectOption = {\n    value,\n    label: label || children,\n    disabled\n  };\n  const optionState = selectContext.getOptionState(selectOption);\n  const optionProps = selectContext.getOptionProps(selectOption);\n  const listboxRef = selectContext.listboxRef;\n  const ownerState = _extends({}, props, optionState);\n  const optionRef = React.useRef(null);\n  const handleRef = useForkRef(ref, optionRef);\n  React.useEffect(() => {\n    // Scroll to the currently highlighted option\n    if (optionState.highlighted) {\n      if (!listboxRef.current || !optionRef.current) {\n        return;\n      }\n      const listboxClientRect = listboxRef.current.getBoundingClientRect();\n      const optionClientRect = optionRef.current.getBoundingClientRect();\n      if (optionClientRect.top < listboxClientRect.top) {\n        listboxRef.current.scrollTop -= listboxClientRect.top - optionClientRect.top;\n      } else if (optionClientRect.bottom > listboxClientRect.bottom) {\n        listboxRef.current.scrollTop += optionClientRect.bottom - listboxClientRect.bottom;\n      }\n    }\n  }, [optionState.highlighted, listboxRef]);\n  const classes = useUtilityClasses(ownerState);\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: _extends({}, optionProps, {\n      ref: handleRef\n    }),\n    className: classes.root,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? OptionUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the OptionUnstyled.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The props used for each slot inside the OptionUnstyled.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * If `true`, the option will be disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * A text representation of the option's content.\n   * Used for keyboard text navigation matching.\n   */\n  label: PropTypes.string,\n  /**\n   * The value of the option.\n   */\n  value: PropTypes.any.isRequired\n} : void 0;\n/**\n * An unstyled option to be used within a SelectUnstyled.\n *\n * Demos:\n *\n * - [Unstyled select](https://mui.com/base/react-select/)\n *\n * API:\n *\n * - [OptionUnstyled API](https://mui.com/base/api/option-unstyled/)\n */\n\nexport default /*#__PURE__*/React.memo(OptionUnstyled);", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "unstable_useForkRef", "useForkRef", "composeClasses", "SelectUnstyledContext", "getOptionUnstyledUtilityClass", "useSlotProps", "jsx", "_jsx", "useUtilityClasses", "ownerState", "disabled", "highlighted", "selected", "slots", "root", "OptionUnstyled", "forwardRef", "props", "ref", "children", "component", "components", "componentsProps", "value", "label", "other", "selectContext", "useContext", "Error", "Root", "selectOption", "optionState", "getOptionState", "optionProps", "getOptionProps", "listboxRef", "optionRef", "useRef", "handleRef", "useEffect", "current", "listboxClientRect", "getBoundingClientRect", "optionClientRect", "top", "scrollTop", "bottom", "classes", "rootProps", "elementType", "externalSlotProps", "externalForwardedProps", "additionalProps", "className", "process", "env", "NODE_ENV", "propTypes", "node", "shape", "oneOfType", "func", "object", "bool", "string", "any", "isRequired", "memo"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/OptionUnstyled/OptionUnstyled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"component\", \"components\", \"componentsProps\", \"disabled\", \"value\", \"label\"];\nimport React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_useForkRef as useForkRef } from '@mui/utils';\nimport composeClasses from '../composeClasses';\nimport { SelectUnstyledContext } from '../SelectUnstyled/SelectUnstyledContext';\nimport { getOptionUnstyledUtilityClass } from './optionUnstyledClasses';\nimport { useSlotProps } from '../utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n\nfunction useUtilityClasses(ownerState) {\n  const {\n    disabled,\n    highlighted,\n    selected\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', highlighted && 'highlighted', selected && 'selected']\n  };\n  return composeClasses(slots, getOptionUnstyledUtilityClass, {});\n}\n/**\n * An unstyled option to be used within a SelectUnstyled.\n */\n\n\nconst OptionUnstyled = /*#__PURE__*/React.forwardRef(function OptionUnstyled(props, ref) {\n  const {\n    children,\n    component,\n    components = {},\n    componentsProps = {},\n    disabled,\n    value,\n    label\n  } = props,\n        other = _objectWithoutPropertiesLoose(props, _excluded);\n\n  const selectContext = React.useContext(SelectUnstyledContext);\n\n  if (!selectContext) {\n    throw new Error('OptionUnstyled must be used within a SelectUnstyled');\n  }\n\n  const Root = component || components.Root || 'li';\n  const selectOption = {\n    value,\n    label: label || children,\n    disabled\n  };\n  const optionState = selectContext.getOptionState(selectOption);\n  const optionProps = selectContext.getOptionProps(selectOption);\n  const listboxRef = selectContext.listboxRef;\n\n  const ownerState = _extends({}, props, optionState);\n\n  const optionRef = React.useRef(null);\n  const handleRef = useForkRef(ref, optionRef);\n  React.useEffect(() => {\n    // Scroll to the currently highlighted option\n    if (optionState.highlighted) {\n      if (!listboxRef.current || !optionRef.current) {\n        return;\n      }\n\n      const listboxClientRect = listboxRef.current.getBoundingClientRect();\n      const optionClientRect = optionRef.current.getBoundingClientRect();\n\n      if (optionClientRect.top < listboxClientRect.top) {\n        listboxRef.current.scrollTop -= listboxClientRect.top - optionClientRect.top;\n      } else if (optionClientRect.bottom > listboxClientRect.bottom) {\n        listboxRef.current.scrollTop += optionClientRect.bottom - listboxClientRect.bottom;\n      }\n    }\n  }, [optionState.highlighted, listboxRef]);\n  const classes = useUtilityClasses(ownerState);\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: _extends({}, optionProps, {\n      ref: handleRef\n    }),\n    className: classes.root,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? OptionUnstyled.propTypes\n/* remove-proptypes */\n= {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n\n  /**\n   * The components used for each slot inside the OptionUnstyled.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n\n  /**\n   * The props used for each slot inside the OptionUnstyled.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n\n  /**\n   * If `true`, the option will be disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n\n  /**\n   * A text representation of the option's content.\n   * Used for keyboard text navigation matching.\n   */\n  label: PropTypes.string,\n\n  /**\n   * The value of the option.\n   */\n  value: PropTypes.any.isRequired\n} : void 0;\n/**\n * An unstyled option to be used within a SelectUnstyled.\n *\n * Demos:\n *\n * - [Unstyled select](https://mui.com/base/react-select/)\n *\n * API:\n *\n * - [OptionUnstyled API](https://mui.com/base/api/option-unstyled/)\n */\n\nexport default /*#__PURE__*/React.memo(OptionUnstyled);"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC;AAC1G,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAC9D,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,6BAA6B,QAAQ,yBAAyB;AACvE,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAE/C,SAASC,iBAAiBA,CAACC,UAAU,EAAE;EACrC,MAAM;IACJC,QAAQ;IACRC,WAAW;IACXC;EACF,CAAC,GAAGH,UAAU;EACd,MAAMI,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEJ,QAAQ,IAAI,UAAU,EAAEC,WAAW,IAAI,aAAa,EAAEC,QAAQ,IAAI,UAAU;EAC7F,CAAC;EACD,OAAOV,cAAc,CAACW,KAAK,EAAET,6BAA6B,EAAE,CAAC,CAAC,CAAC;AACjE;AACA;AACA;AACA;;AAGA,MAAMW,cAAc,GAAG,aAAajB,KAAK,CAACkB,UAAU,CAAC,SAASD,cAAcA,CAACE,KAAK,EAAEC,GAAG,EAAE;EACvF,MAAM;MACJC,QAAQ;MACRC,SAAS;MACTC,UAAU,GAAG,CAAC,CAAC;MACfC,eAAe,GAAG,CAAC,CAAC;MACpBZ,QAAQ;MACRa,KAAK;MACLC;IACF,CAAC,GAAGP,KAAK;IACHQ,KAAK,GAAG7B,6BAA6B,CAACqB,KAAK,EAAEpB,SAAS,CAAC;EAE7D,MAAM6B,aAAa,GAAG5B,KAAK,CAAC6B,UAAU,CAACxB,qBAAqB,CAAC;EAE7D,IAAI,CAACuB,aAAa,EAAE;IAClB,MAAM,IAAIE,KAAK,CAAC,qDAAqD,CAAC;EACxE;EAEA,MAAMC,IAAI,GAAGT,SAAS,IAAIC,UAAU,CAACQ,IAAI,IAAI,IAAI;EACjD,MAAMC,YAAY,GAAG;IACnBP,KAAK;IACLC,KAAK,EAAEA,KAAK,IAAIL,QAAQ;IACxBT;EACF,CAAC;EACD,MAAMqB,WAAW,GAAGL,aAAa,CAACM,cAAc,CAACF,YAAY,CAAC;EAC9D,MAAMG,WAAW,GAAGP,aAAa,CAACQ,cAAc,CAACJ,YAAY,CAAC;EAC9D,MAAMK,UAAU,GAAGT,aAAa,CAACS,UAAU;EAE3C,MAAM1B,UAAU,GAAGd,QAAQ,CAAC,CAAC,CAAC,EAAEsB,KAAK,EAAEc,WAAW,CAAC;EAEnD,MAAMK,SAAS,GAAGtC,KAAK,CAACuC,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMC,SAAS,GAAGrC,UAAU,CAACiB,GAAG,EAAEkB,SAAS,CAAC;EAC5CtC,KAAK,CAACyC,SAAS,CAAC,MAAM;IACpB;IACA,IAAIR,WAAW,CAACpB,WAAW,EAAE;MAC3B,IAAI,CAACwB,UAAU,CAACK,OAAO,IAAI,CAACJ,SAAS,CAACI,OAAO,EAAE;QAC7C;MACF;MAEA,MAAMC,iBAAiB,GAAGN,UAAU,CAACK,OAAO,CAACE,qBAAqB,CAAC,CAAC;MACpE,MAAMC,gBAAgB,GAAGP,SAAS,CAACI,OAAO,CAACE,qBAAqB,CAAC,CAAC;MAElE,IAAIC,gBAAgB,CAACC,GAAG,GAAGH,iBAAiB,CAACG,GAAG,EAAE;QAChDT,UAAU,CAACK,OAAO,CAACK,SAAS,IAAIJ,iBAAiB,CAACG,GAAG,GAAGD,gBAAgB,CAACC,GAAG;MAC9E,CAAC,MAAM,IAAID,gBAAgB,CAACG,MAAM,GAAGL,iBAAiB,CAACK,MAAM,EAAE;QAC7DX,UAAU,CAACK,OAAO,CAACK,SAAS,IAAIF,gBAAgB,CAACG,MAAM,GAAGL,iBAAiB,CAACK,MAAM;MACpF;IACF;EACF,CAAC,EAAE,CAACf,WAAW,CAACpB,WAAW,EAAEwB,UAAU,CAAC,CAAC;EACzC,MAAMY,OAAO,GAAGvC,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMuC,SAAS,GAAG3C,YAAY,CAAC;IAC7B4C,WAAW,EAAEpB,IAAI;IACjBqB,iBAAiB,EAAE5B,eAAe,CAACR,IAAI;IACvCqC,sBAAsB,EAAE1B,KAAK;IAC7B2B,eAAe,EAAEzD,QAAQ,CAAC,CAAC,CAAC,EAAEsC,WAAW,EAAE;MACzCf,GAAG,EAAEoB;IACP,CAAC,CAAC;IACFe,SAAS,EAAEN,OAAO,CAACjC,IAAI;IACvBL;EACF,CAAC,CAAC;EACF,OAAO,aAAaF,IAAI,CAACsB,IAAI,EAAElC,QAAQ,CAAC,CAAC,CAAC,EAAEqD,SAAS,EAAE;IACrD7B,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFmC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzC,cAAc,CAAC0C;AACvD,yBACE;EACA;EACA;EACA;EACA;;EAEA;AACF;AACA;EACEtC,QAAQ,EAAEpB,SAAS,CAAC2D,IAAI;EAExB;AACF;AACA;AACA;EACEtC,SAAS,EAAErB,SAAS,CAACkD,WAAW;EAEhC;AACF;AACA;AACA;AACA;EACE5B,UAAU,EAAEtB,SAAS,CAAC4D,KAAK,CAAC;IAC1B9B,IAAI,EAAE9B,SAAS,CAACkD;EAClB,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACE3B,eAAe,EAAEvB,SAAS,CAAC4D,KAAK,CAAC;IAC/B7C,IAAI,EAAEf,SAAS,CAAC6D,SAAS,CAAC,CAAC7D,SAAS,CAAC8D,IAAI,EAAE9D,SAAS,CAAC+D,MAAM,CAAC;EAC9D,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACEpD,QAAQ,EAAEX,SAAS,CAACgE,IAAI;EAExB;AACF;AACA;AACA;EACEvC,KAAK,EAAEzB,SAAS,CAACiE,MAAM;EAEvB;AACF;AACA;EACEzC,KAAK,EAAExB,SAAS,CAACkE,GAAG,CAACC;AACvB,CAAC,GAAG,KAAK,CAAC;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,eAAe,aAAapE,KAAK,CAACqE,IAAI,CAACpD,cAAc,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}