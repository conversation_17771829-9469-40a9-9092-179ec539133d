{"ast": null, "code": "// Generated by CoffeeScript 1.12.2\n(function () {\n  var getNanoSeconds, hrtime, loadTime, moduleLoadTime, nodeLoadTime, upTime;\n  if (typeof performance !== \"undefined\" && performance !== null && performance.now) {\n    module.exports = function () {\n      return performance.now();\n    };\n  } else if (typeof process !== \"undefined\" && process !== null && process.hrtime) {\n    module.exports = function () {\n      return (getNanoSeconds() - nodeLoadTime) / 1e6;\n    };\n    hrtime = process.hrtime;\n    getNanoSeconds = function () {\n      var hr;\n      hr = hrtime();\n      return hr[0] * 1e9 + hr[1];\n    };\n    moduleLoadTime = getNanoSeconds();\n    upTime = process.uptime() * 1e9;\n    nodeLoadTime = moduleLoadTime - upTime;\n  } else if (Date.now) {\n    module.exports = function () {\n      return Date.now() - loadTime;\n    };\n    loadTime = Date.now();\n  } else {\n    module.exports = function () {\n      return new Date().getTime() - loadTime;\n    };\n    loadTime = new Date().getTime();\n  }\n}).call(this);", "map": {"version": 3, "names": ["getNanoSeconds", "hrtime", "loadTime", "moduleLoadTime", "nodeLoadTime", "upTime", "performance", "now", "module", "exports", "process", "hr", "uptime", "Date", "getTime"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\performance-now\\src\\performance-now.coffee"], "sourcesContent": ["if performance? and performance.now\n  module.exports = -> performance.now()\nelse if process? and process.hrtime\n  module.exports = -> (getNanoSeconds() - nodeLoadTime) / 1e6\n  hrtime = process.hrtime\n  getNanoSeconds = ->\n    hr = hrtime()\n    hr[0] * 1e9 + hr[1]\n  moduleLoadTime = getNanoSeconds()\n  upTime = process.uptime() * 1e9\n  nodeLoadTime = moduleLoadTime - upTime\nelse if Date.now\n  module.exports = -> Date.now() - loadTime\n  loadTime = Date.now()\nelse\n  module.exports = -> new Date().getTime() - loadTime\n  loadTime = new Date().getTime()\n"], "mappings": ";AAAA;EAAA,IAAAA,cAAA,EAAAC,MAAA,EAAAC,QAAA,EAAAC,cAAA,EAAAC,YAAA,EAAAC,MAAA;EAAA,IAAG,OAAAC,WAAA,oBAAAA,WAAA,aAAiBA,WAAW,CAACC,GAAhC;IACEC,MAAM,CAACC,OAAP,GAAiB;aAAGH,WAAW,CAACC,GAAZ;IAAH;GADnB,MAEK,IAAG,OAAAG,OAAA,oBAAAA,OAAA,aAAaA,OAAO,CAACT,MAAxB;IACHO,MAAM,CAACC,OAAP,GAAiB;aAAG,CAACT,cAAA,KAAmBI,YAApB,IAAoC;IAAvC;IACjBH,MAAA,GAASS,OAAO,CAACT,MAAA;IACjBD,cAAA,GAAiB,SAAAA,CAAA;MACf,IAAAW,EAAA;MAAAA,EAAA,GAAKV,MAAA;aACLU,EAAG,GAAH,GAAQ,GAAR,GAAcA,EAAG;IAFF;IAGjBR,cAAA,GAAiBH,cAAA;IACjBK,MAAA,GAASK,OAAO,CAACE,MAAR,KAAmB;IAC5BR,YAAA,GAAeD,cAAA,GAAiBE,MAAA;GAR7B,MASA,IAAGQ,IAAI,CAACN,GAAR;IACHC,MAAM,CAACC,OAAP,GAAiB;aAAGI,IAAI,CAACN,GAAL,KAAaL,QAAA;IAAhB;IACjBA,QAAA,GAAWW,IAAI,CAACN,GAAL;GAFR;IAIHC,MAAM,CAACC,OAAP,GAAiB;aAAO,IAAAI,IAAA,EAAM,CAACC,OAAP,EAAJ,GAAuBZ,QAAA;IAA1B;IACjBA,QAAA,GAAe,IAAAW,IAAA,EAAM,CAACC,OAAP"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}