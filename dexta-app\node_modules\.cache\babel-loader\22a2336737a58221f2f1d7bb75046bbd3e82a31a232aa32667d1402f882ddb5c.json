{"ast": null, "code": "import { trimAfterFirstMatch } from './util.js'; // Regular expression of characters typically used to start a second phone number for the purposes\n// of parsing. This allows us to strip off parts of the number that are actually the start of\n// another number, such as for: (************* x302/x2303 -> the second extension here makes this\n// actually two phone numbers, (************* x302 and (************* x2303. We remove the second\n// extension so that the first number is parsed correctly.\n//\n// Matches a slash (\\ or /) followed by a space followed by an `x`.\n//\n\nvar SECOND_NUMBER_START_PATTERN = /[\\\\/] *x/;\nexport default function parsePreCandidate(candidate) {\n  // Check for extra numbers at the end.\n  // TODO: This is the place to start when trying to support extraction of multiple phone number\n  // from split notations (+41 79 123 45 67 / 68).\n  return trimAfterFirstMatch(SECOND_NUMBER_START_PATTERN, candidate);\n}", "map": {"version": 3, "names": ["trimAfterFirstMatch", "SECOND_NUMBER_START_PATTERN", "parsePreCandidate", "candidate"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\findNumbers\\parsePreCandidate.js"], "sourcesContent": ["import { trimAfterFirstMatch } from './util.js'\r\n\r\n// Regular expression of characters typically used to start a second phone number for the purposes\r\n// of parsing. This allows us to strip off parts of the number that are actually the start of\r\n// another number, such as for: (************* x302/x2303 -> the second extension here makes this\r\n// actually two phone numbers, (************* x302 and (************* x2303. We remove the second\r\n// extension so that the first number is parsed correctly.\r\n//\r\n// Matches a slash (\\ or /) followed by a space followed by an `x`.\r\n//\r\nconst SECOND_NUMBER_START_PATTERN = /[\\\\/] *x/\r\n\r\nexport default function parsePreCandidate(candidate)\r\n{\r\n\t// Check for extra numbers at the end.\r\n\t// TODO: This is the place to start when trying to support extraction of multiple phone number\r\n\t// from split notations (+41 79 123 45 67 / 68).\r\n\treturn trimAfterFirstMatch(SECOND_NUMBER_START_PATTERN, candidate)\r\n}"], "mappings": "AAAA,SAASA,mBAAT,QAAoC,WAApC,C,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAMC,2BAA2B,GAAG,UAApC;AAEA,eAAe,SAASC,iBAATA,CAA2BC,SAA3B,EACf;EACC;EACA;EACA;EACA,OAAOH,mBAAmB,CAACC,2BAAD,EAA8BE,SAA9B,CAA1B;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}