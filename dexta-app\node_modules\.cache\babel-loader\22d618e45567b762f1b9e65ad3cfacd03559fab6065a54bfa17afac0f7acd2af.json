{"ast": null, "code": "import { not } from '../util/not';\nimport { filter } from './filter';\nexport function partition(predicate, thisArg) {\n  return function (source) {\n    return [filter(predicate, thisArg)(source), filter(not(predicate, thisArg))(source)];\n  };\n}", "map": {"version": 3, "names": ["not", "filter", "partition", "predicate", "thisArg", "source"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\partition.ts"], "sourcesContent": ["import { not } from '../util/not';\nimport { filter } from './filter';\nimport { Observable } from '../Observable';\nimport { UnaryFunction } from '../types';\n\n/**\n * Splits the source Observable into two, one with values that satisfy a\n * predicate, and another with values that don't satisfy the predicate.\n *\n * <span class=\"informal\">It's like {@link filter}, but returns two Observables:\n * one like the output of {@link filter}, and the other with values that did not\n * pass the condition.</span>\n *\n * ![](partition.png)\n *\n * `partition` outputs an array with two Observables that partition the values\n * from the source Observable through the given `predicate` function. The first\n * Observable in that array emits source values for which the predicate argument\n * returns true. The second Observable emits source values for which the\n * predicate returns false. The first behaves like {@link filter} and the second\n * behaves like {@link filter} with the predicate negated.\n *\n * ## Example\n *\n * Partition click events into those on DIV elements and those elsewhere\n *\n * ```ts\n * import { fromEvent } from 'rxjs';\n * import { partition } from 'rxjs/operators';\n *\n * const div = document.createElement('div');\n * div.style.cssText = 'width: 200px; height: 200px; background: #09c;';\n * document.body.appendChild(div);\n *\n * const clicks = fromEvent(document, 'click');\n * const [clicksOnDivs, clicksElsewhere] = clicks.pipe(partition(ev => (<HTMLElement>ev.target).tagName === 'DIV'));\n *\n * clicksOnDivs.subscribe(x => console.log('DIV clicked: ', x));\n * clicksElsewhere.subscribe(x => console.log('Other clicked: ', x));\n * ```\n *\n * @see {@link filter}\n *\n * @param predicate A function that evaluates each value emitted by the source\n * Observable. If it returns `true`, the value is emitted on the first Observable\n * in the returned array, if `false` the value is emitted on the second Observable\n * in the array. The `index` parameter is the number `i` for the i-th source\n * emission that has happened since the subscription, starting from the number `0`.\n * @param thisArg An optional argument to determine the value of `this` in the\n * `predicate` function.\n * @return A function that returns an array with two Observables: one with\n * values that passed the predicate, and another with values that did not pass\n * the predicate.\n * @deprecated Replaced with the {@link partition} static creation function. Will be removed in v8.\n */\nexport function partition<T>(\n  predicate: (value: T, index: number) => boolean,\n  thisArg?: any\n): UnaryFunction<Observable<T>, [Observable<T>, Observable<T>]> {\n  return (source: Observable<T>) =>\n    [filter(predicate, thisArg)(source), filter(not(predicate, thisArg))(source)] as [Observable<T>, Observable<T>];\n}\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,aAAa;AACjC,SAASC,MAAM,QAAQ,UAAU;AAsDjC,OAAM,SAAUC,SAASA,CACvBC,SAA+C,EAC/CC,OAAa;EAEb,OAAO,UAACC,MAAqB;IAC3B,QAACJ,MAAM,CAACE,SAAS,EAAEC,OAAO,CAAC,CAACC,MAAM,CAAC,EAAEJ,MAAM,CAACD,GAAG,CAACG,SAAS,EAAEC,OAAO,CAAC,CAAC,CAACC,MAAM,CAAC,CAAmC;EAA/G,CAA+G;AACnH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}