{"ast": null, "code": "import { isFunction } from './isFunction';\nexport function hasLift(source) {\n  return isFunction(source === null || source === void 0 ? void 0 : source.lift);\n}\nexport function operate(init) {\n  return function (source) {\n    if (hasLift(source)) {\n      return source.lift(function (liftedSource) {\n        try {\n          return init(liftedSource, this);\n        } catch (err) {\n          this.error(err);\n        }\n      });\n    }\n    throw new TypeError('Unable to lift unknown Observable type');\n  };\n}", "map": {"version": 3, "names": ["isFunction", "hasLift", "source", "lift", "operate", "init", "liftedSource", "err", "error", "TypeError"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\util\\lift.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { OperatorFunction } from '../types';\nimport { isFunction } from './isFunction';\n\n/**\n * Used to determine if an object is an Observable with a lift function.\n */\nexport function hasLift(source: any): source is { lift: InstanceType<typeof Observable>['lift'] } {\n  return isFunction(source?.lift);\n}\n\n/**\n * Creates an `OperatorFunction`. Used to define operators throughout the library in a concise way.\n * @param init The logic to connect the liftedSource to the subscriber at the moment of subscription.\n */\nexport function operate<T, R>(\n  init: (liftedSource: Observable<T>, subscriber: Subscriber<R>) => (() => void) | void\n): OperatorFunction<T, R> {\n  return (source: Observable<T>) => {\n    if (hasLift(source)) {\n      return source.lift(function (this: Subscriber<R>, liftedSource: Observable<T>) {\n        try {\n          return init(liftedSource, this);\n        } catch (err) {\n          this.error(err);\n        }\n      });\n    }\n    throw new TypeError('Unable to lift unknown Observable type');\n  };\n}\n"], "mappings": "AAGA,SAASA,UAAU,QAAQ,cAAc;AAKzC,OAAM,SAAUC,OAAOA,CAACC,MAAW;EACjC,OAAOF,UAAU,CAACE,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEC,IAAI,CAAC;AACjC;AAMA,OAAM,SAAUC,OAAOA,CACrBC,IAAqF;EAErF,OAAO,UAACH,MAAqB;IAC3B,IAAID,OAAO,CAACC,MAAM,CAAC,EAAE;MACnB,OAAOA,MAAM,CAACC,IAAI,CAAC,UAA+BG,YAA2B;QAC3E,IAAI;UACF,OAAOD,IAAI,CAACC,YAAY,EAAE,IAAI,CAAC;SAChC,CAAC,OAAOC,GAAG,EAAE;UACZ,IAAI,CAACC,KAAK,CAACD,GAAG,CAAC;;MAEnB,CAAC,CAAC;;IAEJ,MAAM,IAAIE,SAAS,CAAC,wCAAwC,CAAC;EAC/D,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}