{"ast": null, "code": "import { useTabContext, getPanelId, getTabId } from '../TabsUnstyled';\nconst useTabPanel = parameters => {\n  const {\n    value\n  } = parameters;\n  const context = useTabContext();\n  if (context === null) {\n    throw new Error('No TabContext provided');\n  }\n  const hidden = value !== context.value;\n  const id = getPanelId(context, value);\n  const tabId = getTabId(context, value);\n  const getRootProps = () => {\n    return {\n      'aria-labelledby': tabId != null ? tabId : undefined,\n      hidden,\n      id: id != null ? id : undefined\n    };\n  };\n  return {\n    hidden,\n    getRootProps\n  };\n};\nexport default useTabPanel;", "map": {"version": 3, "names": ["useTabContext", "getPanelId", "getTabId", "useTabPanel", "parameters", "value", "context", "Error", "hidden", "id", "tabId", "getRootProps", "undefined"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/TabPanelUnstyled/useTabPanel.js"], "sourcesContent": ["import { useTabContext, getPanelId, getTabId } from '../TabsUnstyled';\n\nconst useTabPanel = parameters => {\n  const {\n    value\n  } = parameters;\n  const context = useTabContext();\n\n  if (context === null) {\n    throw new Error('No TabContext provided');\n  }\n\n  const hidden = value !== context.value;\n  const id = getPanelId(context, value);\n  const tabId = getTabId(context, value);\n\n  const getRootProps = () => {\n    return {\n      'aria-labelledby': tabId != null ? tabId : undefined,\n      hidden,\n      id: id != null ? id : undefined\n    };\n  };\n\n  return {\n    hidden,\n    getRootProps\n  };\n};\n\nexport default useTabPanel;"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,iBAAiB;AAErE,MAAMC,WAAW,GAAGC,UAAU,IAAI;EAChC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,OAAO,GAAGN,aAAa,CAAC,CAAC;EAE/B,IAAIM,OAAO,KAAK,IAAI,EAAE;IACpB,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;EAC3C;EAEA,MAAMC,MAAM,GAAGH,KAAK,KAAKC,OAAO,CAACD,KAAK;EACtC,MAAMI,EAAE,GAAGR,UAAU,CAACK,OAAO,EAAED,KAAK,CAAC;EACrC,MAAMK,KAAK,GAAGR,QAAQ,CAACI,OAAO,EAAED,KAAK,CAAC;EAEtC,MAAMM,YAAY,GAAGA,CAAA,KAAM;IACzB,OAAO;MACL,iBAAiB,EAAED,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGE,SAAS;MACpDJ,MAAM;MACNC,EAAE,EAAEA,EAAE,IAAI,IAAI,GAAGA,EAAE,GAAGG;IACxB,CAAC;EACH,CAAC;EAED,OAAO;IACLJ,MAAM;IACNG;EACF,CAAC;AACH,CAAC;AAED,eAAeR,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}