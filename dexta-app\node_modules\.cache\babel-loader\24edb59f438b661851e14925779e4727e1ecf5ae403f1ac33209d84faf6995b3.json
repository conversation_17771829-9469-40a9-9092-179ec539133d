{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"action\", \"children\", \"value\", \"disabled\", \"onChange\", \"onClick\", \"onFocus\", \"component\", \"components\", \"componentsProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_useForkRef as useForkRef } from '@mui/utils';\nimport composeClasses from '../composeClasses';\nimport { getTabUnstyledUtilityClass } from './tabUnstyledClasses';\nimport useTab from './useTab';\nimport { useSlotProps } from '../utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    selected,\n    disabled\n  } = ownerState;\n  const slots = {\n    root: ['root', selected && 'selected', disabled && 'disabled']\n  };\n  return composeClasses(slots, getTabUnstyledUtilityClass, {});\n};\n/**\n *\n * Demos:\n *\n * - [Unstyled tabs](https://mui.com/base/react-tabs/)\n *\n * API:\n *\n * - [TabUnstyled API](https://mui.com/base/api/tab-unstyled/)\n */\n\nconst TabUnstyled = /*#__PURE__*/React.forwardRef(function TabUnstyled(props, ref) {\n  var _ref;\n  const {\n      action,\n      children,\n      disabled = false,\n      component,\n      components = {},\n      componentsProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const tabRef = React.useRef();\n  const handleRef = useForkRef(tabRef, ref);\n  const {\n    active,\n    focusVisible,\n    setFocusVisible,\n    selected,\n    getRootProps\n  } = useTab(_extends({}, props, {\n    ref: handleRef\n  }));\n  React.useImperativeHandle(action, () => ({\n    focusVisible: () => {\n      setFocusVisible(true);\n      tabRef.current.focus();\n    }\n  }), [setFocusVisible]);\n  const ownerState = _extends({}, props, {\n    active,\n    focusVisible,\n    disabled,\n    selected\n  });\n  const classes = useUtilityClasses(ownerState);\n  const TabRoot = (_ref = component != null ? component : components.Root) != null ? _ref : 'button';\n  const tabRootProps = useSlotProps({\n    elementType: TabRoot,\n    getSlotProps: getRootProps,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(TabRoot, _extends({}, tabRootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TabUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * A ref for imperative actions. It currently only supports `focusVisible()` action.\n   */\n  action: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: PropTypes.shape({\n      focusVisible: PropTypes.func.isRequired\n    })\n  })]),\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the Tab.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The props used for each slot inside the Tab.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Callback invoked when new value is being set.\n   */\n  onChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * You can provide your own value. Otherwise, we fall back to the child position index.\n   */\n  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string])\n} : void 0;\nexport default TabUnstyled;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "unstable_useForkRef", "useForkRef", "composeClasses", "getTabUnstyledUtilityClass", "useTab", "useSlotProps", "jsx", "_jsx", "useUtilityClasses", "ownerState", "selected", "disabled", "slots", "root", "TabUnstyled", "forwardRef", "props", "ref", "_ref", "action", "children", "component", "components", "componentsProps", "other", "tabRef", "useRef", "handleRef", "active", "focusVisible", "setFocusVisible", "getRootProps", "useImperativeHandle", "current", "focus", "classes", "TabRoot", "Root", "tabRootProps", "elementType", "getSlotProps", "externalSlotProps", "externalForwardedProps", "additionalProps", "className", "process", "env", "NODE_ENV", "propTypes", "oneOfType", "func", "shape", "isRequired", "node", "object", "bool", "onChange", "onClick", "onFocus", "value", "number", "string"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/TabUnstyled/TabUnstyled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"action\", \"children\", \"value\", \"disabled\", \"onChange\", \"onClick\", \"onFocus\", \"component\", \"components\", \"componentsProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_useForkRef as useForkRef } from '@mui/utils';\nimport composeClasses from '../composeClasses';\nimport { getTabUnstyledUtilityClass } from './tabUnstyledClasses';\nimport useTab from './useTab';\nimport { useSlotProps } from '../utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n\nconst useUtilityClasses = ownerState => {\n  const {\n    selected,\n    disabled\n  } = ownerState;\n  const slots = {\n    root: ['root', selected && 'selected', disabled && 'disabled']\n  };\n  return composeClasses(slots, getTabUnstyledUtilityClass, {});\n};\n/**\n *\n * Demos:\n *\n * - [Unstyled tabs](https://mui.com/base/react-tabs/)\n *\n * API:\n *\n * - [TabUnstyled API](https://mui.com/base/api/tab-unstyled/)\n */\n\n\nconst TabUnstyled = /*#__PURE__*/React.forwardRef(function TabUnstyled(props, ref) {\n  var _ref;\n\n  const {\n    action,\n    children,\n    disabled = false,\n    component,\n    components = {},\n    componentsProps = {}\n  } = props,\n        other = _objectWithoutPropertiesLoose(props, _excluded);\n\n  const tabRef = React.useRef();\n  const handleRef = useForkRef(tabRef, ref);\n  const {\n    active,\n    focusVisible,\n    setFocusVisible,\n    selected,\n    getRootProps\n  } = useTab(_extends({}, props, {\n    ref: handleRef\n  }));\n  React.useImperativeHandle(action, () => ({\n    focusVisible: () => {\n      setFocusVisible(true);\n      tabRef.current.focus();\n    }\n  }), [setFocusVisible]);\n\n  const ownerState = _extends({}, props, {\n    active,\n    focusVisible,\n    disabled,\n    selected\n  });\n\n  const classes = useUtilityClasses(ownerState);\n  const TabRoot = (_ref = component != null ? component : components.Root) != null ? _ref : 'button';\n  const tabRootProps = useSlotProps({\n    elementType: TabRoot,\n    getSlotProps: getRootProps,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(TabRoot, _extends({}, tabRootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TabUnstyled.propTypes\n/* remove-proptypes */\n= {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * A ref for imperative actions. It currently only supports `focusVisible()` action.\n   */\n  action: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: PropTypes.shape({\n      focusVisible: PropTypes.func.isRequired\n    })\n  })]),\n\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n\n  /**\n   * The components used for each slot inside the Tab.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n\n  /**\n   * The props used for each slot inside the Tab.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n\n  /**\n   * Callback invoked when new value is being set.\n   */\n  onChange: PropTypes.func,\n\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n\n  /**\n   * You can provide your own value. Otherwise, we fall back to the child position index.\n   */\n  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string])\n} : void 0;\nexport default TabUnstyled;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,CAAC;AAC7I,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAC9D,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,SAASC,0BAA0B,QAAQ,sBAAsB;AACjE,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAE/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,QAAQ;IACRC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEH,QAAQ,IAAI,UAAU,EAAEC,QAAQ,IAAI,UAAU;EAC/D,CAAC;EACD,OAAOT,cAAc,CAACU,KAAK,EAAET,0BAA0B,EAAE,CAAC,CAAC,CAAC;AAC9D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,MAAMW,WAAW,GAAG,aAAahB,KAAK,CAACiB,UAAU,CAAC,SAASD,WAAWA,CAACE,KAAK,EAAEC,GAAG,EAAE;EACjF,IAAIC,IAAI;EAER,MAAM;MACJC,MAAM;MACNC,QAAQ;MACRT,QAAQ,GAAG,KAAK;MAChBU,SAAS;MACTC,UAAU,GAAG,CAAC,CAAC;MACfC,eAAe,GAAG,CAAC;IACrB,CAAC,GAAGP,KAAK;IACHQ,KAAK,GAAG5B,6BAA6B,CAACoB,KAAK,EAAEnB,SAAS,CAAC;EAE7D,MAAM4B,MAAM,GAAG3B,KAAK,CAAC4B,MAAM,CAAC,CAAC;EAC7B,MAAMC,SAAS,GAAG1B,UAAU,CAACwB,MAAM,EAAER,GAAG,CAAC;EACzC,MAAM;IACJW,MAAM;IACNC,YAAY;IACZC,eAAe;IACfpB,QAAQ;IACRqB;EACF,CAAC,GAAG3B,MAAM,CAACT,QAAQ,CAAC,CAAC,CAAC,EAAEqB,KAAK,EAAE;IAC7BC,GAAG,EAAEU;EACP,CAAC,CAAC,CAAC;EACH7B,KAAK,CAACkC,mBAAmB,CAACb,MAAM,EAAE,OAAO;IACvCU,YAAY,EAAEA,CAAA,KAAM;MAClBC,eAAe,CAAC,IAAI,CAAC;MACrBL,MAAM,CAACQ,OAAO,CAACC,KAAK,CAAC,CAAC;IACxB;EACF,CAAC,CAAC,EAAE,CAACJ,eAAe,CAAC,CAAC;EAEtB,MAAMrB,UAAU,GAAGd,QAAQ,CAAC,CAAC,CAAC,EAAEqB,KAAK,EAAE;IACrCY,MAAM;IACNC,YAAY;IACZlB,QAAQ;IACRD;EACF,CAAC,CAAC;EAEF,MAAMyB,OAAO,GAAG3B,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM2B,OAAO,GAAG,CAAClB,IAAI,GAAGG,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAGC,UAAU,CAACe,IAAI,KAAK,IAAI,GAAGnB,IAAI,GAAG,QAAQ;EAClG,MAAMoB,YAAY,GAAGjC,YAAY,CAAC;IAChCkC,WAAW,EAAEH,OAAO;IACpBI,YAAY,EAAET,YAAY;IAC1BU,iBAAiB,EAAElB,eAAe,CAACV,IAAI;IACvC6B,sBAAsB,EAAElB,KAAK;IAC7BmB,eAAe,EAAE;MACf1B;IACF,CAAC;IACDR,UAAU;IACVmC,SAAS,EAAET,OAAO,CAACtB;EACrB,CAAC,CAAC;EACF,OAAO,aAAaN,IAAI,CAAC6B,OAAO,EAAEzC,QAAQ,CAAC,CAAC,CAAC,EAAE2C,YAAY,EAAE;IAC3DlB,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFyB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGjC,WAAW,CAACkC;AACpD,yBACE;EACA;EACA;EACA;EACA;;EAEA;AACF;AACA;EACE7B,MAAM,EAAEpB,SAAS,CAACkD,SAAS,CAAC,CAAClD,SAAS,CAACmD,IAAI,EAAEnD,SAAS,CAACoD,KAAK,CAAC;IAC3DlB,OAAO,EAAElC,SAAS,CAACoD,KAAK,CAAC;MACvBtB,YAAY,EAAE9B,SAAS,CAACmD,IAAI,CAACE;IAC/B,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;EAEJ;AACF;AACA;EACEhC,QAAQ,EAAErB,SAAS,CAACsD,IAAI;EAExB;AACF;AACA;AACA;EACEhC,SAAS,EAAEtB,SAAS,CAACwC,WAAW;EAEhC;AACF;AACA;AACA;AACA;EACEjB,UAAU,EAAEvB,SAAS,CAACoD,KAAK,CAAC;IAC1Bd,IAAI,EAAEtC,SAAS,CAACwC;EAClB,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACEhB,eAAe,EAAExB,SAAS,CAACoD,KAAK,CAAC;IAC/BtC,IAAI,EAAEd,SAAS,CAACkD,SAAS,CAAC,CAAClD,SAAS,CAACmD,IAAI,EAAEnD,SAAS,CAACuD,MAAM,CAAC;EAC9D,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACE3C,QAAQ,EAAEZ,SAAS,CAACwD,IAAI;EAExB;AACF;AACA;EACEC,QAAQ,EAAEzD,SAAS,CAACmD,IAAI;EAExB;AACF;AACA;EACEO,OAAO,EAAE1D,SAAS,CAACmD,IAAI;EAEvB;AACF;AACA;EACEQ,OAAO,EAAE3D,SAAS,CAACmD,IAAI;EAEvB;AACF;AACA;EACES,KAAK,EAAE5D,SAAS,CAACkD,SAAS,CAAC,CAAClD,SAAS,CAAC6D,MAAM,EAAE7D,SAAS,CAAC8D,MAAM,CAAC;AACjE,CAAC,GAAG,KAAK,CAAC;AACV,eAAe/C,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}