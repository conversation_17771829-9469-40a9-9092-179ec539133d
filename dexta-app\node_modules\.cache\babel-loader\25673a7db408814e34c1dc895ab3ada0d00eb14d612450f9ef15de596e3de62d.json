{"ast": null, "code": "/**\n * If `componentProps` is a function, calls it with the provided `ownerState`.\n * Otherwise, just returns `componentProps`.\n */\nexport default function resolveComponentProps(componentProps, ownerState) {\n  if (typeof componentProps === 'function') {\n    return componentProps(ownerState);\n  }\n  return componentProps;\n}", "map": {"version": 3, "names": ["resolveComponentProps", "componentProps", "ownerState"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/utils/resolveComponentProps.js"], "sourcesContent": ["/**\n * If `componentProps` is a function, calls it with the provided `ownerState`.\n * Otherwise, just returns `componentProps`.\n */\nexport default function resolveComponentProps(componentProps, ownerState) {\n  if (typeof componentProps === 'function') {\n    return componentProps(ownerState);\n  }\n\n  return componentProps;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA,eAAe,SAASA,qBAAqBA,CAACC,cAAc,EAAEC,UAAU,EAAE;EACxE,IAAI,OAAOD,cAAc,KAAK,UAAU,EAAE;IACxC,OAAOA,cAAc,CAACC,UAAU,CAAC;EACnC;EAEA,OAAOD,cAAc;AACvB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}