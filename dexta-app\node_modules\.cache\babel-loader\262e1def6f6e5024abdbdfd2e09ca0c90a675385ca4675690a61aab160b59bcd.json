{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"component\", \"components\", \"disabled\", \"componentsProps\"];\nimport React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '../composeClasses';\nimport { getOptionGroupUnstyledUtilityClass } from './optionGroupUnstyledClasses';\nimport { useSlotProps } from '../utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction useUtilityClasses(disabled) {\n  const slots = {\n    root: ['root', disabled && 'disabled'],\n    label: ['label'],\n    list: ['list']\n  };\n  return composeClasses(slots, getOptionGroupUnstyledUtilityClass, {});\n}\n/**\n * An unstyled option group to be used within a SelectUnstyled.\n *\n * Demos:\n *\n * - [Unstyled select](https://mui.com/base/react-select/)\n *\n * API:\n *\n * - [OptionGroupUnstyled API](https://mui.com/base/api/option-group-unstyled/)\n */\n\nconst OptionGroupUnstyled = /*#__PURE__*/React.forwardRef(function OptionGroupUnstyled(props, ref) {\n  const {\n      component,\n      components = {},\n      disabled = false,\n      componentsProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const Root = component || (components == null ? void 0 : components.Root) || 'li';\n  const Label = (components == null ? void 0 : components.Label) || 'span';\n  const List = (components == null ? void 0 : components.List) || 'ul';\n  const classes = useUtilityClasses(disabled);\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref\n    },\n    ownerState: props,\n    className: classes.root\n  });\n  const labelProps = useSlotProps({\n    elementType: Label,\n    externalSlotProps: componentsProps.label,\n    ownerState: props,\n    className: classes.label\n  });\n  const listProps = useSlotProps({\n    elementType: List,\n    externalSlotProps: componentsProps.list,\n    ownerState: props,\n    className: classes.list\n  });\n  return /*#__PURE__*/_jsxs(Root, _extends({}, rootProps, {\n    children: [/*#__PURE__*/_jsx(Label, _extends({}, labelProps, {\n      children: props.label\n    })), /*#__PURE__*/_jsx(List, _extends({}, listProps, {\n      children: props.children\n    }))]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? OptionGroupUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the OptionGroupUnstyled.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Label: PropTypes.elementType,\n    List: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The props used for each slot inside the Input.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    label: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    list: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * If `true` all the options in the group will be disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * The human-readable description of the group.\n   */\n  label: PropTypes.node\n} : void 0;\nexport default OptionGroupUnstyled;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "composeClasses", "getOptionGroupUnstyledUtilityClass", "useSlotProps", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "disabled", "slots", "root", "label", "list", "OptionGroupUnstyled", "forwardRef", "props", "ref", "component", "components", "componentsProps", "other", "Root", "Label", "List", "classes", "rootProps", "elementType", "externalSlotProps", "externalForwardedProps", "additionalProps", "ownerState", "className", "labelProps", "listProps", "children", "process", "env", "NODE_ENV", "propTypes", "node", "shape", "oneOfType", "func", "object", "bool"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/OptionGroupUnstyled/OptionGroupUnstyled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"component\", \"components\", \"disabled\", \"componentsProps\"];\nimport React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '../composeClasses';\nimport { getOptionGroupUnstyledUtilityClass } from './optionGroupUnstyledClasses';\nimport { useSlotProps } from '../utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\n\nfunction useUtilityClasses(disabled) {\n  const slots = {\n    root: ['root', disabled && 'disabled'],\n    label: ['label'],\n    list: ['list']\n  };\n  return composeClasses(slots, getOptionGroupUnstyledUtilityClass, {});\n}\n/**\n * An unstyled option group to be used within a SelectUnstyled.\n *\n * Demos:\n *\n * - [Unstyled select](https://mui.com/base/react-select/)\n *\n * API:\n *\n * - [OptionGroupUnstyled API](https://mui.com/base/api/option-group-unstyled/)\n */\n\n\nconst OptionGroupUnstyled = /*#__PURE__*/React.forwardRef(function OptionGroupUnstyled(props, ref) {\n  const {\n    component,\n    components = {},\n    disabled = false,\n    componentsProps = {}\n  } = props,\n        other = _objectWithoutPropertiesLoose(props, _excluded);\n\n  const Root = component || (components == null ? void 0 : components.Root) || 'li';\n  const Label = (components == null ? void 0 : components.Label) || 'span';\n  const List = (components == null ? void 0 : components.List) || 'ul';\n  const classes = useUtilityClasses(disabled);\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref\n    },\n    ownerState: props,\n    className: classes.root\n  });\n  const labelProps = useSlotProps({\n    elementType: Label,\n    externalSlotProps: componentsProps.label,\n    ownerState: props,\n    className: classes.label\n  });\n  const listProps = useSlotProps({\n    elementType: List,\n    externalSlotProps: componentsProps.list,\n    ownerState: props,\n    className: classes.list\n  });\n  return /*#__PURE__*/_jsxs(Root, _extends({}, rootProps, {\n    children: [/*#__PURE__*/_jsx(Label, _extends({}, labelProps, {\n      children: props.label\n    })), /*#__PURE__*/_jsx(List, _extends({}, listProps, {\n      children: props.children\n    }))]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? OptionGroupUnstyled.propTypes\n/* remove-proptypes */\n= {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n\n  /**\n   * The components used for each slot inside the OptionGroupUnstyled.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Label: PropTypes.elementType,\n    List: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n\n  /**\n   * The props used for each slot inside the Input.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    label: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    list: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n\n  /**\n   * If `true` all the options in the group will be disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n\n  /**\n   * The human-readable description of the group.\n   */\n  label: PropTypes.node\n} : void 0;\nexport default OptionGroupUnstyled;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,iBAAiB,CAAC;AAC5E,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,SAASC,kCAAkC,QAAQ,8BAA8B;AACjF,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAEjD,SAASC,iBAAiBA,CAACC,QAAQ,EAAE;EACnC,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,QAAQ,IAAI,UAAU,CAAC;IACtCG,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOZ,cAAc,CAACS,KAAK,EAAER,kCAAkC,EAAE,CAAC,CAAC,CAAC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,MAAMY,mBAAmB,GAAG,aAAaf,KAAK,CAACgB,UAAU,CAAC,SAASD,mBAAmBA,CAACE,KAAK,EAAEC,GAAG,EAAE;EACjG,MAAM;MACJC,SAAS;MACTC,UAAU,GAAG,CAAC,CAAC;MACfV,QAAQ,GAAG,KAAK;MAChBW,eAAe,GAAG,CAAC;IACrB,CAAC,GAAGJ,KAAK;IACHK,KAAK,GAAGxB,6BAA6B,CAACmB,KAAK,EAAElB,SAAS,CAAC;EAE7D,MAAMwB,IAAI,GAAGJ,SAAS,KAAKC,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACG,IAAI,CAAC,IAAI,IAAI;EACjF,MAAMC,KAAK,GAAG,CAACJ,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACI,KAAK,KAAK,MAAM;EACxE,MAAMC,IAAI,GAAG,CAACL,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACK,IAAI,KAAK,IAAI;EACpE,MAAMC,OAAO,GAAGjB,iBAAiB,CAACC,QAAQ,CAAC;EAC3C,MAAMiB,SAAS,GAAGvB,YAAY,CAAC;IAC7BwB,WAAW,EAAEL,IAAI;IACjBM,iBAAiB,EAAER,eAAe,CAACT,IAAI;IACvCkB,sBAAsB,EAAER,KAAK;IAC7BS,eAAe,EAAE;MACfb;IACF,CAAC;IACDc,UAAU,EAAEf,KAAK;IACjBgB,SAAS,EAAEP,OAAO,CAACd;EACrB,CAAC,CAAC;EACF,MAAMsB,UAAU,GAAG9B,YAAY,CAAC;IAC9BwB,WAAW,EAAEJ,KAAK;IAClBK,iBAAiB,EAAER,eAAe,CAACR,KAAK;IACxCmB,UAAU,EAAEf,KAAK;IACjBgB,SAAS,EAAEP,OAAO,CAACb;EACrB,CAAC,CAAC;EACF,MAAMsB,SAAS,GAAG/B,YAAY,CAAC;IAC7BwB,WAAW,EAAEH,IAAI;IACjBI,iBAAiB,EAAER,eAAe,CAACP,IAAI;IACvCkB,UAAU,EAAEf,KAAK;IACjBgB,SAAS,EAAEP,OAAO,CAACZ;EACrB,CAAC,CAAC;EACF,OAAO,aAAaN,KAAK,CAACe,IAAI,EAAE1B,QAAQ,CAAC,CAAC,CAAC,EAAE8B,SAAS,EAAE;IACtDS,QAAQ,EAAE,CAAC,aAAa9B,IAAI,CAACkB,KAAK,EAAE3B,QAAQ,CAAC,CAAC,CAAC,EAAEqC,UAAU,EAAE;MAC3DE,QAAQ,EAAEnB,KAAK,CAACJ;IAClB,CAAC,CAAC,CAAC,EAAE,aAAaP,IAAI,CAACmB,IAAI,EAAE5B,QAAQ,CAAC,CAAC,CAAC,EAAEsC,SAAS,EAAE;MACnDC,QAAQ,EAAEnB,KAAK,CAACmB;IAClB,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxB,mBAAmB,CAACyB;AAC5D,yBACE;EACA;EACA;EACA;EACA;;EAEA;AACF;AACA;EACEJ,QAAQ,EAAEnC,SAAS,CAACwC,IAAI;EAExB;AACF;AACA;AACA;EACEtB,SAAS,EAAElB,SAAS,CAAC2B,WAAW;EAEhC;AACF;AACA;AACA;AACA;EACER,UAAU,EAAEnB,SAAS,CAACyC,KAAK,CAAC;IAC1BlB,KAAK,EAAEvB,SAAS,CAAC2B,WAAW;IAC5BH,IAAI,EAAExB,SAAS,CAAC2B,WAAW;IAC3BL,IAAI,EAAEtB,SAAS,CAAC2B;EAClB,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACEP,eAAe,EAAEpB,SAAS,CAACyC,KAAK,CAAC;IAC/B7B,KAAK,EAAEZ,SAAS,CAAC0C,SAAS,CAAC,CAAC1C,SAAS,CAAC2C,IAAI,EAAE3C,SAAS,CAAC4C,MAAM,CAAC,CAAC;IAC9D/B,IAAI,EAAEb,SAAS,CAAC0C,SAAS,CAAC,CAAC1C,SAAS,CAAC2C,IAAI,EAAE3C,SAAS,CAAC4C,MAAM,CAAC,CAAC;IAC7DjC,IAAI,EAAEX,SAAS,CAAC0C,SAAS,CAAC,CAAC1C,SAAS,CAAC2C,IAAI,EAAE3C,SAAS,CAAC4C,MAAM,CAAC;EAC9D,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACEnC,QAAQ,EAAET,SAAS,CAAC6C,IAAI;EAExB;AACF;AACA;EACEjC,KAAK,EAAEZ,SAAS,CAACwC;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,eAAe1B,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}