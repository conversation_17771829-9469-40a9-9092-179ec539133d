{"ast": null, "code": "import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getPopperUnstyledUtilityClass(slot) {\n  return generateUtilityClass('MuiPopperUnstyled', slot);\n}\nconst popperUnstyledClasses = generateUtilityClasses('MuiPopperUnstyled', ['root']);\nexport default popperUnstyledClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getPopperUnstyledUtilityClass", "slot", "popperUnstyledClasses"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/PopperUnstyled/popperUnstyledClasses.js"], "sourcesContent": ["import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getPopperUnstyledUtilityClass(slot) {\n  return generateUtilityClass('MuiPopperUnstyled', slot);\n}\nconst popperUnstyledClasses = generateUtilityClasses('MuiPopperUnstyled', ['root']);\nexport default popperUnstyledClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,yBAAyB;AAC1D,OAAOC,sBAAsB,MAAM,2BAA2B;AAC9D,OAAO,SAASC,6BAA6BA,CAACC,IAAI,EAAE;EAClD,OAAOH,oBAAoB,CAAC,mBAAmB,EAAEG,IAAI,CAAC;AACxD;AACA,MAAMC,qBAAqB,GAAGH,sBAAsB,CAAC,mBAAmB,EAAE,CAAC,MAAM,CAAC,CAAC;AACnF,eAAeG,qBAAqB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}