{"ast": null, "code": "export var OPUS_HEAD = new Uint8Array([\n// O, p, u, s\n0x4f, 0x70, 0x75, 0x73,\n// H, e, a, d\n0x48, 0x65, 0x61, 0x64]); // https://wiki.xiph.org/OggOpus\n// https://vfrmaniac.fushizen.eu/contents/opus_in_isobmff.html\n// https://opus-codec.org/docs/opusfile_api-0.7/structOpusHead.html\n\nexport var parseOpusHead = function parseOpusHead(bytes) {\n  var view = new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);\n  var version = view.getUint8(0); // version 0, from mp4, does not use littleEndian.\n\n  var littleEndian = version !== 0;\n  var config = {\n    version: version,\n    channels: view.getUint8(1),\n    preSkip: view.getUint16(2, littleEndian),\n    sampleRate: view.getUint32(4, littleEndian),\n    outputGain: view.getUint16(8, littleEndian),\n    channelMappingFamily: view.getUint8(10)\n  };\n  if (config.channelMappingFamily > 0 && bytes.length > 10) {\n    config.streamCount = view.getUint8(11);\n    config.twoChannelStreamCount = view.getUint8(12);\n    config.channelMapping = [];\n    for (var c = 0; c < config.channels; c++) {\n      config.channelMapping.push(view.getUint8(13 + c));\n    }\n  }\n  return config;\n};\nexport var setOpusHead = function setOpusHead(config) {\n  var size = config.channelMappingFamily <= 0 ? 11 : 12 + config.channels;\n  var view = new DataView(new ArrayBuffer(size));\n  var littleEndian = config.version !== 0;\n  view.setUint8(0, config.version);\n  view.setUint8(1, config.channels);\n  view.setUint16(2, config.preSkip, littleEndian);\n  view.setUint32(4, config.sampleRate, littleEndian);\n  view.setUint16(8, config.outputGain, littleEndian);\n  view.setUint8(10, config.channelMappingFamily);\n  if (config.channelMappingFamily > 0) {\n    view.setUint8(11, config.streamCount);\n    config.channelMapping.foreach(function (cm, i) {\n      view.setUint8(12 + i, cm);\n    });\n  }\n  return new Uint8Array(view.buffer);\n};", "map": {"version": 3, "names": ["OPUS_HEAD", "Uint8Array", "parseOpusHead", "bytes", "view", "DataView", "buffer", "byteOffset", "byteLength", "version", "getUint8", "littleEndian", "config", "channels", "preSkip", "getUint16", "sampleRate", "getUint32", "outputGain", "channelMappingFamily", "length", "streamCount", "twoChannelStreamCount", "channelMapping", "c", "push", "setOpusHead", "size", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setUint8", "setUint16", "setUint32", "foreach", "cm", "i"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@videojs/vhs-utils/es/opus-helpers.js"], "sourcesContent": ["export var OPUS_HEAD = new Uint8Array([// O, p, u, s\n0x4f, 0x70, 0x75, 0x73, // H, e, a, d\n0x48, 0x65, 0x61, 0x64]); // https://wiki.xiph.org/OggOpus\n// https://vfrmaniac.fushizen.eu/contents/opus_in_isobmff.html\n// https://opus-codec.org/docs/opusfile_api-0.7/structOpusHead.html\n\nexport var parseOpusHead = function parseOpusHead(bytes) {\n  var view = new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);\n  var version = view.getUint8(0); // version 0, from mp4, does not use littleEndian.\n\n  var littleEndian = version !== 0;\n  var config = {\n    version: version,\n    channels: view.getUint8(1),\n    preSkip: view.getUint16(2, littleEndian),\n    sampleRate: view.getUint32(4, littleEndian),\n    outputGain: view.getUint16(8, littleEndian),\n    channelMappingFamily: view.getUint8(10)\n  };\n\n  if (config.channelMappingFamily > 0 && bytes.length > 10) {\n    config.streamCount = view.getUint8(11);\n    config.twoChannelStreamCount = view.getUint8(12);\n    config.channelMapping = [];\n\n    for (var c = 0; c < config.channels; c++) {\n      config.channelMapping.push(view.getUint8(13 + c));\n    }\n  }\n\n  return config;\n};\nexport var setOpusHead = function setOpusHead(config) {\n  var size = config.channelMappingFamily <= 0 ? 11 : 12 + config.channels;\n  var view = new DataView(new ArrayBuffer(size));\n  var littleEndian = config.version !== 0;\n  view.setUint8(0, config.version);\n  view.setUint8(1, config.channels);\n  view.setUint16(2, config.preSkip, littleEndian);\n  view.setUint32(4, config.sampleRate, littleEndian);\n  view.setUint16(8, config.outputGain, littleEndian);\n  view.setUint8(10, config.channelMappingFamily);\n\n  if (config.channelMappingFamily > 0) {\n    view.setUint8(11, config.streamCount);\n    config.channelMapping.foreach(function (cm, i) {\n      view.setUint8(12 + i, cm);\n    });\n  }\n\n  return new Uint8Array(view.buffer);\n};"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG,IAAIC,UAAU,CAAC;AAAC;AACvC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAAE;AACxB,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1B;AACA;;AAEA,OAAO,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;EACvD,IAAIC,IAAI,GAAG,IAAIC,QAAQ,CAACF,KAAK,CAACG,MAAM,EAAEH,KAAK,CAACI,UAAU,EAAEJ,KAAK,CAACK,UAAU,CAAC;EACzE,IAAIC,OAAO,GAAGL,IAAI,CAACM,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEhC,IAAIC,YAAY,GAAGF,OAAO,KAAK,CAAC;EAChC,IAAIG,MAAM,GAAG;IACXH,OAAO,EAAEA,OAAO;IAChBI,QAAQ,EAAET,IAAI,CAACM,QAAQ,CAAC,CAAC,CAAC;IAC1BI,OAAO,EAAEV,IAAI,CAACW,SAAS,CAAC,CAAC,EAAEJ,YAAY,CAAC;IACxCK,UAAU,EAAEZ,IAAI,CAACa,SAAS,CAAC,CAAC,EAAEN,YAAY,CAAC;IAC3CO,UAAU,EAAEd,IAAI,CAACW,SAAS,CAAC,CAAC,EAAEJ,YAAY,CAAC;IAC3CQ,oBAAoB,EAAEf,IAAI,CAACM,QAAQ,CAAC,EAAE;EACxC,CAAC;EAED,IAAIE,MAAM,CAACO,oBAAoB,GAAG,CAAC,IAAIhB,KAAK,CAACiB,MAAM,GAAG,EAAE,EAAE;IACxDR,MAAM,CAACS,WAAW,GAAGjB,IAAI,CAACM,QAAQ,CAAC,EAAE,CAAC;IACtCE,MAAM,CAACU,qBAAqB,GAAGlB,IAAI,CAACM,QAAQ,CAAC,EAAE,CAAC;IAChDE,MAAM,CAACW,cAAc,GAAG,EAAE;IAE1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,MAAM,CAACC,QAAQ,EAAEW,CAAC,EAAE,EAAE;MACxCZ,MAAM,CAACW,cAAc,CAACE,IAAI,CAACrB,IAAI,CAACM,QAAQ,CAAC,EAAE,GAAGc,CAAC,CAAC,CAAC;IACnD;EACF;EAEA,OAAOZ,MAAM;AACf,CAAC;AACD,OAAO,IAAIc,WAAW,GAAG,SAASA,WAAWA,CAACd,MAAM,EAAE;EACpD,IAAIe,IAAI,GAAGf,MAAM,CAACO,oBAAoB,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAGP,MAAM,CAACC,QAAQ;EACvE,IAAIT,IAAI,GAAG,IAAIC,QAAQ,CAAC,IAAIuB,WAAW,CAACD,IAAI,CAAC,CAAC;EAC9C,IAAIhB,YAAY,GAAGC,MAAM,CAACH,OAAO,KAAK,CAAC;EACvCL,IAAI,CAACyB,QAAQ,CAAC,CAAC,EAAEjB,MAAM,CAACH,OAAO,CAAC;EAChCL,IAAI,CAACyB,QAAQ,CAAC,CAAC,EAAEjB,MAAM,CAACC,QAAQ,CAAC;EACjCT,IAAI,CAAC0B,SAAS,CAAC,CAAC,EAAElB,MAAM,CAACE,OAAO,EAAEH,YAAY,CAAC;EAC/CP,IAAI,CAAC2B,SAAS,CAAC,CAAC,EAAEnB,MAAM,CAACI,UAAU,EAAEL,YAAY,CAAC;EAClDP,IAAI,CAAC0B,SAAS,CAAC,CAAC,EAAElB,MAAM,CAACM,UAAU,EAAEP,YAAY,CAAC;EAClDP,IAAI,CAACyB,QAAQ,CAAC,EAAE,EAAEjB,MAAM,CAACO,oBAAoB,CAAC;EAE9C,IAAIP,MAAM,CAACO,oBAAoB,GAAG,CAAC,EAAE;IACnCf,IAAI,CAACyB,QAAQ,CAAC,EAAE,EAAEjB,MAAM,CAACS,WAAW,CAAC;IACrCT,MAAM,CAACW,cAAc,CAACS,OAAO,CAAC,UAAUC,EAAE,EAAEC,CAAC,EAAE;MAC7C9B,IAAI,CAACyB,QAAQ,CAAC,EAAE,GAAGK,CAAC,EAAED,EAAE,CAAC;IAC3B,CAAC,CAAC;EACJ;EAEA,OAAO,IAAIhC,UAAU,CAACG,IAAI,CAACE,MAAM,CAAC;AACpC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}