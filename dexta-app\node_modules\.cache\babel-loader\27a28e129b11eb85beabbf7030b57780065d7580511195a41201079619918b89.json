{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { identity } from '../util/identity';\nimport { timer } from '../observable/timer';\nimport { innerFrom } from '../observable/innerFrom';\nexport function retry(configOrCount) {\n  if (configOrCount === void 0) {\n    configOrCount = Infinity;\n  }\n  var config;\n  if (configOrCount && typeof configOrCount === 'object') {\n    config = configOrCount;\n  } else {\n    config = {\n      count: configOrCount\n    };\n  }\n  var _a = config.count,\n    count = _a === void 0 ? Infinity : _a,\n    delay = config.delay,\n    _b = config.resetOnSuccess,\n    resetOnSuccess = _b === void 0 ? false : _b;\n  return count <= 0 ? identity : operate(function (source, subscriber) {\n    var soFar = 0;\n    var innerSub;\n    var subscribeForRetry = function () {\n      var syncUnsub = false;\n      innerSub = source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n        if (resetOnSuccess) {\n          soFar = 0;\n        }\n        subscriber.next(value);\n      }, undefined, function (err) {\n        if (soFar++ < count) {\n          var resub_1 = function () {\n            if (innerSub) {\n              innerSub.unsubscribe();\n              innerSub = null;\n              subscribeForRetry();\n            } else {\n              syncUnsub = true;\n            }\n          };\n          if (delay != null) {\n            var notifier = typeof delay === 'number' ? timer(delay) : innerFrom(delay(err, soFar));\n            var notifierSubscriber_1 = createOperatorSubscriber(subscriber, function () {\n              notifierSubscriber_1.unsubscribe();\n              resub_1();\n            }, function () {\n              subscriber.complete();\n            });\n            notifier.subscribe(notifierSubscriber_1);\n          } else {\n            resub_1();\n          }\n        } else {\n          subscriber.error(err);\n        }\n      }));\n      if (syncUnsub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        subscribeForRetry();\n      }\n    };\n    subscribeForRetry();\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "identity", "timer", "innerFrom", "retry", "config<PERSON>r<PERSON>ount", "Infinity", "config", "count", "_a", "delay", "_b", "resetOnSuccess", "source", "subscriber", "soFar", "innerSub", "subscribeForRetry", "syncUnsub", "subscribe", "value", "next", "undefined", "err", "resub_1", "unsubscribe", "notifier", "notifierSubscriber_1", "complete", "error"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\retry.ts"], "sourcesContent": ["import { MonoTypeOperatorFunction, ObservableInput } from '../types';\nimport { operate } from '../util/lift';\nimport { Subscription } from '../Subscription';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { identity } from '../util/identity';\nimport { timer } from '../observable/timer';\nimport { innerFrom } from '../observable/innerFrom';\n\n/**\n * The {@link retry} operator configuration object. `retry` either accepts a `number`\n * or an object described by this interface.\n */\nexport interface RetryConfig {\n  /**\n   * The maximum number of times to retry. If `count` is omitted, `retry` will try to\n   * resubscribe on errors infinite number of times.\n   */\n  count?: number;\n  /**\n   * The number of milliseconds to delay before retrying, OR a function to\n   * return a notifier for delaying. If a function is given, that function should\n   * return a notifier that, when it emits will retry the source. If the notifier\n   * completes _without_ emitting, the resulting observable will complete without error,\n   * if the notifier errors, the error will be pushed to the result.\n   */\n  delay?: number | ((error: any, retryCount: number) => ObservableInput<any>);\n  /**\n   * Whether or not to reset the retry counter when the retried subscription\n   * emits its first value.\n   */\n  resetOnSuccess?: boolean;\n}\n\nexport function retry<T>(count?: number): MonoTypeOperatorFunction<T>;\nexport function retry<T>(config: RetryConfig): MonoTypeOperatorFunction<T>;\n\n/**\n * Returns an Observable that mirrors the source Observable with the exception of an `error`.\n *\n * If the source Observable calls `error`, this method will resubscribe to the source Observable for a maximum of\n * `count` resubscriptions rather than propagating the `error` call.\n *\n * ![](retry.png)\n *\n * The number of retries is determined by the `count` parameter. It can be set either by passing a number to\n * `retry` function or by setting `count` property when `retry` is configured using {@link RetryConfig}. If\n * `count` is omitted, `retry` will try to resubscribe on errors infinite number of times.\n *\n * Any and all items emitted by the source Observable will be emitted by the resulting Observable, even those\n * emitted during failed subscriptions. For example, if an Observable fails at first but emits `[1, 2]` then\n * succeeds the second time and emits: `[1, 2, 3, 4, 5, complete]` then the complete stream of emissions and\n * notifications would be: `[1, 2, 1, 2, 3, 4, 5, complete]`.\n *\n * ## Example\n *\n * ```ts\n * import { interval, mergeMap, throwError, of, retry } from 'rxjs';\n *\n * const source = interval(1000);\n * const result = source.pipe(\n *   mergeMap(val => val > 5 ? throwError(() => 'Error!') : of(val)),\n *   retry(2) // retry 2 times on error\n * );\n *\n * result.subscribe({\n *   next: value => console.log(value),\n *   error: err => console.log(`${ err }: Retried 2 times then quit!`)\n * });\n *\n * // Output:\n * // 0..1..2..3..4..5..\n * // 0..1..2..3..4..5..\n * // 0..1..2..3..4..5..\n * // 'Error!: Retried 2 times then quit!'\n * ```\n *\n * @see {@link retryWhen}\n *\n * @param configOrCount Either number of retry attempts before failing or a\n * {@link RetryConfig} object.\n * @return A function that returns an Observable that will resubscribe to the\n * source stream when the source stream errors, at most `count` times.\n */\nexport function retry<T>(configOrCount: number | RetryConfig = Infinity): MonoTypeOperatorFunction<T> {\n  let config: RetryConfig;\n  if (configOrCount && typeof configOrCount === 'object') {\n    config = configOrCount;\n  } else {\n    config = {\n      count: configOrCount as number,\n    };\n  }\n  const { count = Infinity, delay, resetOnSuccess: resetOnSuccess = false } = config;\n\n  return count <= 0\n    ? identity\n    : operate((source, subscriber) => {\n        let soFar = 0;\n        let innerSub: Subscription | null;\n        const subscribeForRetry = () => {\n          let syncUnsub = false;\n          innerSub = source.subscribe(\n            createOperatorSubscriber(\n              subscriber,\n              (value) => {\n                // If we're resetting on success\n                if (resetOnSuccess) {\n                  soFar = 0;\n                }\n                subscriber.next(value);\n              },\n              // Completions are passed through to consumer.\n              undefined,\n              (err) => {\n                if (soFar++ < count) {\n                  // We are still under our retry count\n                  const resub = () => {\n                    if (innerSub) {\n                      innerSub.unsubscribe();\n                      innerSub = null;\n                      subscribeForRetry();\n                    } else {\n                      syncUnsub = true;\n                    }\n                  };\n\n                  if (delay != null) {\n                    // The user specified a retry delay.\n                    // They gave us a number, use a timer, otherwise, it's a function,\n                    // and we're going to call it to get a notifier.\n                    const notifier = typeof delay === 'number' ? timer(delay) : innerFrom(delay(err, soFar));\n                    const notifierSubscriber = createOperatorSubscriber(\n                      subscriber,\n                      () => {\n                        // After we get the first notification, we\n                        // unsubscribe from the notifier, because we don't want anymore\n                        // and we resubscribe to the source.\n                        notifierSubscriber.unsubscribe();\n                        resub();\n                      },\n                      () => {\n                        // The notifier completed without emitting.\n                        // The author is telling us they want to complete.\n                        subscriber.complete();\n                      }\n                    );\n                    notifier.subscribe(notifierSubscriber);\n                  } else {\n                    // There was no notifier given. Just resub immediately.\n                    resub();\n                  }\n                } else {\n                  // We're past our maximum number of retries.\n                  // Just send along the error.\n                  subscriber.error(err);\n                }\n              }\n            )\n          );\n          if (syncUnsub) {\n            innerSub.unsubscribe();\n            innerSub = null;\n            subscribeForRetry();\n          }\n        };\n        subscribeForRetry();\n      });\n}\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,cAAc;AAEtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,KAAK,QAAQ,qBAAqB;AAC3C,SAASC,SAAS,QAAQ,yBAAyB;AA6EnD,OAAM,SAAUC,KAAKA,CAAIC,aAA8C;EAA9C,IAAAA,aAAA;IAAAA,aAAA,GAAAC,QAA8C;EAAA;EACrE,IAAIC,MAAmB;EACvB,IAAIF,aAAa,IAAI,OAAOA,aAAa,KAAK,QAAQ,EAAE;IACtDE,MAAM,GAAGF,aAAa;GACvB,MAAM;IACLE,MAAM,GAAG;MACPC,KAAK,EAAEH;KACR;;EAEK,IAAAI,EAAA,GAAoEF,MAAM,CAAAC,KAA1D;IAAhBA,KAAK,GAAAC,EAAA,cAAGH,QAAQ,GAAAG,EAAA;IAAEC,KAAK,GAA6CH,MAAM,CAAAG,KAAnD;IAAEC,EAAA,GAA2CJ,MAAM,CAAAK,cAAX;IAAtBA,cAAc,GAAAD,EAAA,cAAG,KAAK,GAAAA,EAAA;EAEvE,OAAOH,KAAK,IAAI,CAAC,GACbP,QAAQ,GACRF,OAAO,CAAC,UAACc,MAAM,EAAEC,UAAU;IACzB,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,QAA6B;IACjC,IAAMC,iBAAiB,GAAG,SAAAA,CAAA;MACxB,IAAIC,SAAS,GAAG,KAAK;MACrBF,QAAQ,GAAGH,MAAM,CAACM,SAAS,CACzBnB,wBAAwB,CACtBc,UAAU,EACV,UAACM,KAAK;QAEJ,IAAIR,cAAc,EAAE;UAClBG,KAAK,GAAG,CAAC;;QAEXD,UAAU,CAACO,IAAI,CAACD,KAAK,CAAC;MACxB,CAAC,EAEDE,SAAS,EACT,UAACC,GAAG;QACF,IAAIR,KAAK,EAAE,GAAGP,KAAK,EAAE;UAEnB,IAAMgB,OAAK,GAAG,SAAAA,CAAA;YACZ,IAAIR,QAAQ,EAAE;cACZA,QAAQ,CAACS,WAAW,EAAE;cACtBT,QAAQ,GAAG,IAAI;cACfC,iBAAiB,EAAE;aACpB,MAAM;cACLC,SAAS,GAAG,IAAI;;UAEpB,CAAC;UAED,IAAIR,KAAK,IAAI,IAAI,EAAE;YAIjB,IAAMgB,QAAQ,GAAG,OAAOhB,KAAK,KAAK,QAAQ,GAAGR,KAAK,CAACQ,KAAK,CAAC,GAAGP,SAAS,CAACO,KAAK,CAACa,GAAG,EAAER,KAAK,CAAC,CAAC;YACxF,IAAMY,oBAAkB,GAAG3B,wBAAwB,CACjDc,UAAU,EACV;cAIEa,oBAAkB,CAACF,WAAW,EAAE;cAChCD,OAAK,EAAE;YACT,CAAC,EACD;cAGEV,UAAU,CAACc,QAAQ,EAAE;YACvB,CAAC,CACF;YACDF,QAAQ,CAACP,SAAS,CAACQ,oBAAkB,CAAC;WACvC,MAAM;YAELH,OAAK,EAAE;;SAEV,MAAM;UAGLV,UAAU,CAACe,KAAK,CAACN,GAAG,CAAC;;MAEzB,CAAC,CACF,CACF;MACD,IAAIL,SAAS,EAAE;QACbF,QAAQ,CAACS,WAAW,EAAE;QACtBT,QAAQ,GAAG,IAAI;QACfC,iBAAiB,EAAE;;IAEvB,CAAC;IACDA,iBAAiB,EAAE;EACrB,CAAC,CAAC;AACR"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}