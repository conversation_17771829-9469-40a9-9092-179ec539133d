{"ast": null, "code": "import { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function take(count) {\n  return count <= 0 ? function () {\n    return EMPTY;\n  } : operate(function (source, subscriber) {\n    var seen = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      if (++seen <= count) {\n        subscriber.next(value);\n        if (count <= seen) {\n          subscriber.complete();\n        }\n      }\n    }));\n  });\n}", "map": {"version": 3, "names": ["EMPTY", "operate", "createOperatorSubscriber", "take", "count", "source", "subscriber", "seen", "subscribe", "value", "next", "complete"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\take.ts"], "sourcesContent": ["import { MonoTypeOperatorFunction } from '../types';\nimport { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/**\n * Emits only the first `count` values emitted by the source Observable.\n *\n * <span class=\"informal\">Takes the first `count` values from the source, then\n * completes.</span>\n *\n * ![](take.png)\n *\n * `take` returns an Observable that emits only the first `count` values emitted\n * by the source Observable. If the source emits fewer than `count` values then\n * all of its values are emitted. After that, it completes, regardless if the\n * source completes.\n *\n * ## Example\n *\n * Take the first 5 seconds of an infinite 1-second interval Observable\n *\n * ```ts\n * import { interval, take } from 'rxjs';\n *\n * const intervalCount = interval(1000);\n * const takeFive = intervalCount.pipe(take(5));\n * takeFive.subscribe(x => console.log(x));\n *\n * // Logs:\n * // 0\n * // 1\n * // 2\n * // 3\n * // 4\n * ```\n *\n * @see {@link takeLast}\n * @see {@link takeUntil}\n * @see {@link takeWhile}\n * @see {@link skip}\n *\n * @param count The maximum number of `next` values to emit.\n * @return A function that returns an Observable that emits only the first\n * `count` values emitted by the source Observable, or all of the values from\n * the source if the source emits fewer than `count` values.\n */\nexport function take<T>(count: number): MonoTypeOperatorFunction<T> {\n  return count <= 0\n    ? // If we are taking no values, that's empty.\n      () => EMPTY\n    : operate((source, subscriber) => {\n        let seen = 0;\n        source.subscribe(\n          createOperatorSubscriber(subscriber, (value) => {\n            // Increment the number of values we have seen,\n            // then check it against the allowed count to see\n            // if we are still letting values through.\n            if (++seen <= count) {\n              subscriber.next(value);\n              // If we have met or passed our allowed count,\n              // we need to complete. We have to do <= here,\n              // because re-entrant code will increment `seen` twice.\n              if (count <= seen) {\n                subscriber.complete();\n              }\n            }\n          })\n        );\n      });\n}\n"], "mappings": "AACA,SAASA,KAAK,QAAQ,qBAAqB;AAC3C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AA4C/D,OAAM,SAAUC,IAAIA,CAAIC,KAAa;EACnC,OAAOA,KAAK,IAAI,CAAC,GAEb;IAAM,OAAAJ,KAAK;EAAL,CAAK,GACXC,OAAO,CAAC,UAACI,MAAM,EAAEC,UAAU;IACzB,IAAIC,IAAI,GAAG,CAAC;IACZF,MAAM,CAACG,SAAS,CACdN,wBAAwB,CAACI,UAAU,EAAE,UAACG,KAAK;MAIzC,IAAI,EAAEF,IAAI,IAAIH,KAAK,EAAE;QACnBE,UAAU,CAACI,IAAI,CAACD,KAAK,CAAC;QAItB,IAAIL,KAAK,IAAIG,IAAI,EAAE;UACjBD,UAAU,CAACK,QAAQ,EAAE;;;IAG3B,CAAC,CAAC,CACH;EACH,CAAC,CAAC;AACR"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}