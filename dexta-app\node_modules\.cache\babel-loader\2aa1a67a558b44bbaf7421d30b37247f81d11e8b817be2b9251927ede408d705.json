{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function refCount() {\n  return operate(function (source, subscriber) {\n    var connection = null;\n    source._refCount++;\n    var refCounter = createOperatorSubscriber(subscriber, undefined, undefined, undefined, function () {\n      if (!source || source._refCount <= 0 || 0 < --source._refCount) {\n        connection = null;\n        return;\n      }\n      var sharedConnection = source._connection;\n      var conn = connection;\n      connection = null;\n      if (sharedConnection && (!conn || sharedConnection === conn)) {\n        sharedConnection.unsubscribe();\n      }\n      subscriber.unsubscribe();\n    });\n    source.subscribe(refCounter);\n    if (!refCounter.closed) {\n      connection = source.connect();\n    }\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "refCount", "source", "subscriber", "connection", "_refCount", "refCounter", "undefined", "sharedConnection", "_connection", "conn", "unsubscribe", "subscribe", "closed", "connect"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\refCount.ts"], "sourcesContent": ["import { ConnectableObservable } from '../observable/ConnectableObservable';\nimport { Subscription } from '../Subscription';\nimport { MonoTypeOperatorFunction } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/**\n * Make a {@link ConnectableObservable} behave like a ordinary observable and automates the way\n * you can connect to it.\n *\n * Internally it counts the subscriptions to the observable and subscribes (only once) to the source if\n * the number of subscriptions is larger than 0. If the number of subscriptions is smaller than 1, it\n * unsubscribes from the source. This way you can make sure that everything before the *published*\n * refCount has only a single subscription independently of the number of subscribers to the target\n * observable.\n *\n * Note that using the {@link share} operator is exactly the same as using the `multicast(() => new Subject())` operator\n * (making the observable hot) and the *refCount* operator in a sequence.\n *\n * ![](refCount.png)\n *\n * ## Example\n *\n * In the following example there are two intervals turned into connectable observables\n * by using the *publish* operator. The first one uses the *refCount* operator, the\n * second one does not use it. You will notice that a connectable observable does nothing\n * until you call its connect function.\n *\n * ```ts\n * import { interval, tap, publish, refCount } from 'rxjs';\n *\n * // Turn the interval observable into a ConnectableObservable (hot)\n * const refCountInterval = interval(400).pipe(\n *   tap(num => console.log(`refCount ${ num }`)),\n *   publish(),\n *   refCount()\n * );\n *\n * const publishedInterval = interval(400).pipe(\n *   tap(num => console.log(`publish ${ num }`)),\n *   publish()\n * );\n *\n * refCountInterval.subscribe();\n * refCountInterval.subscribe();\n * // 'refCount 0' -----> 'refCount 1' -----> etc\n * // All subscriptions will receive the same value and the tap (and\n * // every other operator) before the `publish` operator will be executed\n * // only once per event independently of the number of subscriptions.\n *\n * publishedInterval.subscribe();\n * // Nothing happens until you call .connect() on the observable.\n * ```\n *\n * @return A function that returns an Observable that automates the connection\n * to ConnectableObservable.\n * @see {@link ConnectableObservable}\n * @see {@link share}\n * @see {@link publish}\n * @deprecated Replaced with the {@link share} operator. How `share` is used\n * will depend on the connectable observable you created just prior to the\n * `refCount` operator.\n * Details: https://rxjs.dev/deprecations/multicasting\n */\nexport function refCount<T>(): MonoTypeOperatorFunction<T> {\n  return operate((source, subscriber) => {\n    let connection: Subscription | null = null;\n\n    (source as any)._refCount++;\n\n    const refCounter = createOperatorSubscriber(subscriber, undefined, undefined, undefined, () => {\n      if (!source || (source as any)._refCount <= 0 || 0 < --(source as any)._refCount) {\n        connection = null;\n        return;\n      }\n\n      ///\n      // Compare the local RefCountSubscriber's connection Subscription to the\n      // connection Subscription on the shared ConnectableObservable. In cases\n      // where the ConnectableObservable source synchronously emits values, and\n      // the RefCountSubscriber's downstream Observers synchronously unsubscribe,\n      // execution continues to here before the RefCountOperator has a chance to\n      // supply the RefCountSubscriber with the shared connection Subscription.\n      // For example:\n      // ```\n      // range(0, 10).pipe(\n      //   publish(),\n      //   refCount(),\n      //   take(5),\n      // )\n      // .subscribe();\n      // ```\n      // In order to account for this case, RefCountSubscriber should only dispose\n      // the ConnectableObservable's shared connection Subscription if the\n      // connection Subscription exists, *and* either:\n      //   a. RefCountSubscriber doesn't have a reference to the shared connection\n      //      Subscription yet, or,\n      //   b. RefCountSubscriber's connection Subscription reference is identical\n      //      to the shared connection Subscription\n      ///\n\n      const sharedConnection = (source as any)._connection;\n      const conn = connection;\n      connection = null;\n\n      if (sharedConnection && (!conn || sharedConnection === conn)) {\n        sharedConnection.unsubscribe();\n      }\n\n      subscriber.unsubscribe();\n    });\n\n    source.subscribe(refCounter);\n\n    if (!refCounter.closed) {\n      connection = (source as ConnectableObservable<T>).connect();\n    }\n  });\n}\n"], "mappings": "AAGA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AA4D/D,OAAM,SAAUC,QAAQA,CAAA;EACtB,OAAOF,OAAO,CAAC,UAACG,MAAM,EAAEC,UAAU;IAChC,IAAIC,UAAU,GAAwB,IAAI;IAEzCF,MAAc,CAACG,SAAS,EAAE;IAE3B,IAAMC,UAAU,GAAGN,wBAAwB,CAACG,UAAU,EAAEI,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE;MACvF,IAAI,CAACL,MAAM,IAAKA,MAAc,CAACG,SAAS,IAAI,CAAC,IAAI,CAAC,GAAG,EAAGH,MAAc,CAACG,SAAS,EAAE;QAChFD,UAAU,GAAG,IAAI;QACjB;;MA4BF,IAAMI,gBAAgB,GAAIN,MAAc,CAACO,WAAW;MACpD,IAAMC,IAAI,GAAGN,UAAU;MACvBA,UAAU,GAAG,IAAI;MAEjB,IAAII,gBAAgB,KAAK,CAACE,IAAI,IAAIF,gBAAgB,KAAKE,IAAI,CAAC,EAAE;QAC5DF,gBAAgB,CAACG,WAAW,EAAE;;MAGhCR,UAAU,CAACQ,WAAW,EAAE;IAC1B,CAAC,CAAC;IAEFT,MAAM,CAACU,SAAS,CAACN,UAAU,CAAC;IAE5B,IAAI,CAACA,UAAU,CAACO,MAAM,EAAE;MACtBT,UAAU,GAAIF,MAAmC,CAACY,OAAO,EAAE;;EAE/D,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}