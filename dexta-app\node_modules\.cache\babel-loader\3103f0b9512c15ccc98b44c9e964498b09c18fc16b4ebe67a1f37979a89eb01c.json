{"ast": null, "code": "import { count_occurences } from './helpers.js';\nexport default function closeBraces(retained_template, template) {\n  var placeholder = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'x';\n  var empty_placeholder = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : ' ';\n  var cut_before = retained_template.length;\n  var opening_braces = count_occurences('(', retained_template);\n  var closing_braces = count_occurences(')', retained_template);\n  var dangling_braces = opening_braces - closing_braces;\n  while (dangling_braces > 0 && cut_before < template.length) {\n    retained_template += template[cut_before].replace(placeholder, empty_placeholder);\n    if (template[cut_before] === ')') {\n      dangling_braces--;\n    }\n    cut_before++;\n  }\n  return retained_template;\n}", "map": {"version": 3, "names": ["count_occurences", "closeBraces", "retained_template", "template", "placeholder", "arguments", "length", "undefined", "empty_placeholder", "cut_before", "opening_braces", "closing_braces", "dangling_braces", "replace"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\input-format\\source\\closeBraces.js"], "sourcesContent": ["import { count_occurences } from './helpers.js'\r\n\r\nexport default function closeBraces(retained_template, template, placeholder = 'x', empty_placeholder = ' ')\r\n{\r\n\tlet cut_before = retained_template.length\r\n\r\n\tconst opening_braces = count_occurences('(', retained_template)\r\n\tconst closing_braces = count_occurences(')', retained_template)\r\n\r\n\tlet dangling_braces = opening_braces - closing_braces\r\n\r\n\twhile (dangling_braces > 0 && cut_before < template.length)\r\n\t{\r\n\t\tretained_template += template[cut_before].replace(placeholder, empty_placeholder)\r\n\r\n\t\tif (template[cut_before] === ')')\r\n\t\t{\r\n\t\t\tdangling_braces--\r\n\t\t}\r\n\r\n\t\tcut_before++\r\n\t}\r\n\r\n\treturn retained_template\r\n}\r\n"], "mappings": "AAAA,SAASA,gBAAT,QAAiC,cAAjC;AAEA,eAAe,SAASC,WAATA,CAAqBC,iBAArB,EAAwCC,QAAxC,EACf;EAAA,IADiEC,WACjE,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAD+E,GAC/E;EAAA,IADoFG,iBACpF,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MADwG,GACxG;EACC,IAAII,UAAU,GAAGP,iBAAiB,CAACI,MAAnC;EAEA,IAAMI,cAAc,GAAGV,gBAAgB,CAAC,GAAD,EAAME,iBAAN,CAAvC;EACA,IAAMS,cAAc,GAAGX,gBAAgB,CAAC,GAAD,EAAME,iBAAN,CAAvC;EAEA,IAAIU,eAAe,GAAGF,cAAc,GAAGC,cAAvC;EAEA,OAAOC,eAAe,GAAG,CAAlB,IAAuBH,UAAU,GAAGN,QAAQ,CAACG,MAApD,EACA;IACCJ,iBAAiB,IAAIC,QAAQ,CAACM,UAAD,CAAR,CAAqBI,OAArB,CAA6BT,WAA7B,EAA0CI,iBAA1C,CAArB;IAEA,IAAIL,QAAQ,CAACM,UAAD,CAAR,KAAyB,GAA7B,EACA;MACCG,eAAe;IACf;IAEDH,UAAU;EACV;EAED,OAAOP,iBAAP;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}