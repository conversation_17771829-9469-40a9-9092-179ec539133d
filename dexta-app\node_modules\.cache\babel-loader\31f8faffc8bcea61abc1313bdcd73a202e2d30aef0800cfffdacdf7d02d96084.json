{"ast": null, "code": "import { Observable } from '../Observable';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function scheduleAsyncIterable(input, scheduler) {\n  if (!input) {\n    throw new Error('Iterable cannot be null');\n  }\n  return new Observable(function (subscriber) {\n    executeSchedule(subscriber, scheduler, function () {\n      var iterator = input[Symbol.asyncIterator]();\n      executeSchedule(subscriber, scheduler, function () {\n        iterator.next().then(function (result) {\n          if (result.done) {\n            subscriber.complete();\n          } else {\n            subscriber.next(result.value);\n          }\n        });\n      }, 0, true);\n    });\n  });\n}", "map": {"version": 3, "names": ["Observable", "executeSchedule", "scheduleAsyncIterable", "input", "scheduler", "Error", "subscriber", "iterator", "Symbol", "asyncIterator", "next", "then", "result", "done", "complete", "value"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\scheduled\\scheduleAsyncIterable.ts"], "sourcesContent": ["import { SchedulerLike } from '../types';\nimport { Observable } from '../Observable';\nimport { executeSchedule } from '../util/executeSchedule';\n\nexport function scheduleAsyncIterable<T>(input: AsyncIterable<T>, scheduler: SchedulerLike) {\n  if (!input) {\n    throw new Error('Iterable cannot be null');\n  }\n  return new Observable<T>((subscriber) => {\n    executeSchedule(subscriber, scheduler, () => {\n      const iterator = input[Symbol.asyncIterator]();\n      executeSchedule(\n        subscriber,\n        scheduler,\n        () => {\n          iterator.next().then((result) => {\n            if (result.done) {\n              // This will remove the subscriptions from\n              // the parent subscription.\n              subscriber.complete();\n            } else {\n              subscriber.next(result.value);\n            }\n          });\n        },\n        0,\n        true\n      );\n    });\n  });\n}\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,eAAe,QAAQ,yBAAyB;AAEzD,OAAM,SAAUC,qBAAqBA,CAAIC,KAAuB,EAAEC,SAAwB;EACxF,IAAI,CAACD,KAAK,EAAE;IACV,MAAM,IAAIE,KAAK,CAAC,yBAAyB,CAAC;;EAE5C,OAAO,IAAIL,UAAU,CAAI,UAACM,UAAU;IAClCL,eAAe,CAACK,UAAU,EAAEF,SAAS,EAAE;MACrC,IAAMG,QAAQ,GAAGJ,KAAK,CAACK,MAAM,CAACC,aAAa,CAAC,EAAE;MAC9CR,eAAe,CACbK,UAAU,EACVF,SAAS,EACT;QACEG,QAAQ,CAACG,IAAI,EAAE,CAACC,IAAI,CAAC,UAACC,MAAM;UAC1B,IAAIA,MAAM,CAACC,IAAI,EAAE;YAGfP,UAAU,CAACQ,QAAQ,EAAE;WACtB,MAAM;YACLR,UAAU,CAACI,IAAI,CAACE,MAAM,CAACG,KAAK,CAAC;;QAEjC,CAAC,CAAC;MACJ,CAAC,EACD,CAAC,EACD,IAAI,CACL;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}