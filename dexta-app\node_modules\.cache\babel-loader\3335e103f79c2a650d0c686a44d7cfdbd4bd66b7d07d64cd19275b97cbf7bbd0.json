{"ast": null, "code": "const defaultOptionStringifier = option => {\n  const {\n    label,\n    value\n  } = option;\n  if (typeof label === 'string') {\n    return label;\n  }\n  if (typeof value === 'string') {\n    return value;\n  } // Fallback string representation\n\n  return String(option);\n};\nexport default defaultOptionStringifier;", "map": {"version": 3, "names": ["defaultOptionStringifier", "option", "label", "value", "String"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/SelectUnstyled/defaultOptionStringifier.js"], "sourcesContent": ["const defaultOptionStringifier = option => {\n  const {\n    label,\n    value\n  } = option;\n\n  if (typeof label === 'string') {\n    return label;\n  }\n\n  if (typeof value === 'string') {\n    return value;\n  } // Fallback string representation\n\n\n  return String(option);\n};\n\nexport default defaultOptionStringifier;"], "mappings": "AAAA,MAAMA,wBAAwB,GAAGC,MAAM,IAAI;EACzC,MAAM;IACJC,KAAK;IACLC;EACF,CAAC,GAAGF,MAAM;EAEV,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOA,KAAK;EACd;EAEA,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAOA,KAAK;EACd,CAAC,CAAC;;EAGF,OAAOC,MAAM,CAACH,MAAM,CAAC;AACvB,CAAC;AAED,eAAeD,wBAAwB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}