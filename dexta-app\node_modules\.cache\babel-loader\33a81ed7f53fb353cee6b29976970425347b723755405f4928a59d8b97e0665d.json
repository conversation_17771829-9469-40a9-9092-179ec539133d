{"ast": null, "code": "import generateUtilityClasses from '../generateUtilityClasses';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getBadgeUnstyledUtilityClass(slot) {\n  return generateUtilityClass('BaseBadge', slot);\n}\nconst badgeUnstyledClasses = generateUtilityClasses('BaseBadge', ['root', 'badge', 'invisible']);\nexport default badgeUnstyledClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getBadgeUnstyledUtilityClass", "slot", "badgeUnstyledClasses"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/BadgeUnstyled/badgeUnstyledClasses.js"], "sourcesContent": ["import generateUtilityClasses from '../generateUtilityClasses';\nimport generateUtilityClass from '../generateUtilityClass';\nexport function getBadgeUnstyledUtilityClass(slot) {\n  return generateUtilityClass('BaseBadge', slot);\n}\nconst badgeUnstyledClasses = generateUtilityClasses('BaseBadge', ['root', 'badge', 'invisible']);\nexport default badgeUnstyledClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,2BAA2B;AAC9D,OAAOC,oBAAoB,MAAM,yBAAyB;AAC1D,OAAO,SAASC,4BAA4BA,CAACC,IAAI,EAAE;EACjD,OAAOF,oBAAoB,CAAC,WAAW,EAAEE,IAAI,CAAC;AAChD;AACA,MAAMC,oBAAoB,GAAGJ,sBAAsB,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;AAChG,eAAeI,oBAAoB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}