{"ast": null, "code": "var ActionTypes; // split declaration and export due to https://github.com/codesandbox/codesandbox-client/issues/6435\n\n(function (ActionTypes) {\n  ActionTypes[\"blur\"] = \"blur\";\n  ActionTypes[\"focus\"] = \"focus\";\n  ActionTypes[\"keyDown\"] = \"keyDown\";\n  ActionTypes[\"optionClick\"] = \"optionClick\";\n  ActionTypes[\"optionHover\"] = \"optionHover\";\n  ActionTypes[\"optionsChange\"] = \"optionsChange\";\n  ActionTypes[\"setValue\"] = \"setValue\";\n  ActionTypes[\"setHighlight\"] = \"setHighlight\";\n  ActionTypes[\"textNavigation\"] = \"textNagivation\";\n})(ActionTypes || (ActionTypes = {}));\nexport { ActionTypes };", "map": {"version": 3, "names": ["ActionTypes"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/ListboxUnstyled/useListbox.types.js"], "sourcesContent": ["var ActionTypes; // split declaration and export due to https://github.com/codesandbox/codesandbox-client/issues/6435\n\n(function (ActionTypes) {\n  ActionTypes[\"blur\"] = \"blur\";\n  ActionTypes[\"focus\"] = \"focus\";\n  ActionTypes[\"keyDown\"] = \"keyDown\";\n  ActionTypes[\"optionClick\"] = \"optionClick\";\n  ActionTypes[\"optionHover\"] = \"optionHover\";\n  ActionTypes[\"optionsChange\"] = \"optionsChange\";\n  ActionTypes[\"setValue\"] = \"setValue\";\n  ActionTypes[\"setHighlight\"] = \"setHighlight\";\n  ActionTypes[\"textNavigation\"] = \"textNagivation\";\n})(ActionTypes || (ActionTypes = {}));\n\nexport { ActionTypes };"], "mappings": "AAAA,IAAIA,WAAW,CAAC,CAAC;;AAEjB,CAAC,UAAUA,WAAW,EAAE;EACtBA,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM;EAC5BA,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;EAC9BA,WAAW,CAAC,SAAS,CAAC,GAAG,SAAS;EAClCA,WAAW,CAAC,aAAa,CAAC,GAAG,aAAa;EAC1CA,WAAW,CAAC,aAAa,CAAC,GAAG,aAAa;EAC1CA,WAAW,CAAC,eAAe,CAAC,GAAG,eAAe;EAC9CA,WAAW,CAAC,UAAU,CAAC,GAAG,UAAU;EACpCA,WAAW,CAAC,cAAc,CAAC,GAAG,cAAc;EAC5CA,WAAW,CAAC,gBAAgB,CAAC,GAAG,gBAAgB;AAClD,CAAC,EAAEA,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;AAErC,SAASA,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}