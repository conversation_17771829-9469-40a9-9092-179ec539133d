{"ast": null, "code": "import { createIdentifier as e } from \"@univerjs/core\";\nconst t = e(\"telemetry.service\");\nexport { t as ITelemetryService };", "map": {"version": 3, "names": ["createIdentifier", "e", "t", "ITelemetryService"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@univerjs/telemetry/lib/es/index.js"], "sourcesContent": ["import { createIdentifier as e } from \"@univerjs/core\";\nconst t = e(\"telemetry.service\");\nexport {\n  t as ITelemetryService\n};\n"], "mappings": "AAAA,SAASA,gBAAgB,IAAIC,CAAC,QAAQ,gBAAgB;AACtD,MAAMC,CAAC,GAAGD,CAAC,CAAC,mBAAmB,CAAC;AAChC,SACEC,CAAC,IAAIC,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}