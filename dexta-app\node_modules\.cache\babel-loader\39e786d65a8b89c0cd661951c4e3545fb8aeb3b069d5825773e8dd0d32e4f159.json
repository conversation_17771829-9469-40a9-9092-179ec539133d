{"ast": null, "code": "import { config } from '../config';\nimport { timeoutProvider } from '../scheduler/timeoutProvider';\nexport function reportUnhandledError(err) {\n  timeoutProvider.setTimeout(function () {\n    var onUnhandledError = config.onUnhandledError;\n    if (onUnhandledError) {\n      onUnhandledError(err);\n    } else {\n      throw err;\n    }\n  });\n}", "map": {"version": 3, "names": ["config", "timeout<PERSON>rovider", "reportUnhandledError", "err", "setTimeout", "onUnhandledError"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\util\\reportUnhandledError.ts"], "sourcesContent": ["import { config } from '../config';\nimport { timeoutProvider } from '../scheduler/timeoutProvider';\n\n/**\n * Handles an error on another job either with the user-configured {@link onUnhandledError},\n * or by throwing it on that new job so it can be picked up by `window.onerror`, `process.on('error')`, etc.\n *\n * This should be called whenever there is an error that is out-of-band with the subscription\n * or when an error hits a terminal boundary of the subscription and no error handler was provided.\n *\n * @param err the error to report\n */\nexport function reportUnhandledError(err: any) {\n  timeoutProvider.setTimeout(() => {\n    const { onUnhandledError } = config;\n    if (onUnhandledError) {\n      // Execute the user-configured error handler.\n      onUnhandledError(err);\n    } else {\n      // Throw so it is picked up by the runtime's uncaught error mechanism.\n      throw err;\n    }\n  });\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,WAAW;AAClC,SAASC,eAAe,QAAQ,8BAA8B;AAW9D,OAAM,SAAUC,oBAAoBA,CAACC,GAAQ;EAC3CF,eAAe,CAACG,UAAU,CAAC;IACjB,IAAAC,gBAAgB,GAAKL,MAAM,CAAAK,gBAAX;IACxB,IAAIA,gBAAgB,EAAE;MAEpBA,gBAAgB,CAACF,GAAG,CAAC;KACtB,MAAM;MAEL,MAAMA,GAAG;;EAEb,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}