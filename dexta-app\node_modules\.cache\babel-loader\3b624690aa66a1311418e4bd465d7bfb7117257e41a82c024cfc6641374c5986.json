{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { Subscriber } from '../Subscriber';\nexport function createOperatorSubscriber(destination, onNext, onComplete, onError, onFinalize) {\n  return new OperatorSubscriber(destination, onNext, onComplete, onError, onFinalize);\n}\nvar OperatorSubscriber = function (_super) {\n  __extends(OperatorSubscriber, _super);\n  function OperatorSubscriber(destination, onNext, onComplete, onError, onFinalize, shouldUnsubscribe) {\n    var _this = _super.call(this, destination) || this;\n    _this.onFinalize = onFinalize;\n    _this.shouldUnsubscribe = shouldUnsubscribe;\n    _this._next = onNext ? function (value) {\n      try {\n        onNext(value);\n      } catch (err) {\n        destination.error(err);\n      }\n    } : _super.prototype._next;\n    _this._error = onError ? function (err) {\n      try {\n        onError(err);\n      } catch (err) {\n        destination.error(err);\n      } finally {\n        this.unsubscribe();\n      }\n    } : _super.prototype._error;\n    _this._complete = onComplete ? function () {\n      try {\n        onComplete();\n      } catch (err) {\n        destination.error(err);\n      } finally {\n        this.unsubscribe();\n      }\n    } : _super.prototype._complete;\n    return _this;\n  }\n  OperatorSubscriber.prototype.unsubscribe = function () {\n    var _a;\n    if (!this.shouldUnsubscribe || this.shouldUnsubscribe()) {\n      var closed_1 = this.closed;\n      _super.prototype.unsubscribe.call(this);\n      !closed_1 && ((_a = this.onFinalize) === null || _a === void 0 ? void 0 : _a.call(this));\n    }\n  };\n  return OperatorSubscriber;\n}(Subscriber);\nexport { OperatorSubscriber };", "map": {"version": 3, "names": ["Subscriber", "createOperatorSubscriber", "destination", "onNext", "onComplete", "onError", "onFinalize", "OperatorSubscriber", "_super", "__extends", "shouldUnsubscribe", "_this", "call", "_next", "value", "err", "error", "prototype", "_error", "unsubscribe", "_complete", "closed_1", "closed", "_a"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\OperatorSubscriber.ts"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\n\n/**\n * Creates an instance of an `OperatorSubscriber`.\n * @param destination The downstream subscriber.\n * @param onNext Handles next values, only called if this subscriber is not stopped or closed. Any\n * error that occurs in this function is caught and sent to the `error` method of this subscriber.\n * @param onError Handles errors from the subscription, any errors that occur in this handler are caught\n * and send to the `destination` error handler.\n * @param onComplete Handles completion notification from the subscription. Any errors that occur in\n * this handler are sent to the `destination` error handler.\n * @param onFinalize Additional teardown logic here. This will only be called on teardown if the\n * subscriber itself is not already closed. This is called after all other teardown logic is executed.\n */\nexport function createOperatorSubscriber<T>(\n  destination: Subscriber<any>,\n  onNext?: (value: T) => void,\n  onComplete?: () => void,\n  onError?: (err: any) => void,\n  onFinalize?: () => void\n): Subscriber<T> {\n  return new OperatorSubscriber(destination, onNext, onComplete, onError, onFinalize);\n}\n\n/**\n * A generic helper for allowing operators to be created with a Subscriber and\n * use closures to capture necessary state from the operator function itself.\n */\nexport class OperatorSubscriber<T> extends Subscriber<T> {\n  /**\n   * Creates an instance of an `OperatorSubscriber`.\n   * @param destination The downstream subscriber.\n   * @param onNext Handles next values, only called if this subscriber is not stopped or closed. Any\n   * error that occurs in this function is caught and sent to the `error` method of this subscriber.\n   * @param onError Handles errors from the subscription, any errors that occur in this handler are caught\n   * and send to the `destination` error handler.\n   * @param onComplete Handles completion notification from the subscription. Any errors that occur in\n   * this handler are sent to the `destination` error handler.\n   * @param onFinalize Additional finalization logic here. This will only be called on finalization if the\n   * subscriber itself is not already closed. This is called after all other finalization logic is executed.\n   * @param shouldUnsubscribe An optional check to see if an unsubscribe call should truly unsubscribe.\n   * NOTE: This currently **ONLY** exists to support the strange behavior of {@link groupBy}, where unsubscription\n   * to the resulting observable does not actually disconnect from the source if there are active subscriptions\n   * to any grouped observable. (DO NOT EXPOSE OR USE EXTERNALLY!!!)\n   */\n  constructor(\n    destination: Subscriber<any>,\n    onNext?: (value: T) => void,\n    onComplete?: () => void,\n    onError?: (err: any) => void,\n    private onFinalize?: () => void,\n    private shouldUnsubscribe?: () => boolean\n  ) {\n    // It's important - for performance reasons - that all of this class's\n    // members are initialized and that they are always initialized in the same\n    // order. This will ensure that all OperatorSubscriber instances have the\n    // same hidden class in V8. This, in turn, will help keep the number of\n    // hidden classes involved in property accesses within the base class as\n    // low as possible. If the number of hidden classes involved exceeds four,\n    // the property accesses will become megamorphic and performance penalties\n    // will be incurred - i.e. inline caches won't be used.\n    //\n    // The reasons for ensuring all instances have the same hidden class are\n    // further discussed in this blog post from Benedikt Meurer:\n    // https://benediktmeurer.de/2018/03/23/impact-of-polymorphism-on-component-based-frameworks-like-react/\n    super(destination);\n    this._next = onNext\n      ? function (this: OperatorSubscriber<T>, value: T) {\n          try {\n            onNext(value);\n          } catch (err) {\n            destination.error(err);\n          }\n        }\n      : super._next;\n    this._error = onError\n      ? function (this: OperatorSubscriber<T>, err: any) {\n          try {\n            onError(err);\n          } catch (err) {\n            // Send any errors that occur down stream.\n            destination.error(err);\n          } finally {\n            // Ensure finalization.\n            this.unsubscribe();\n          }\n        }\n      : super._error;\n    this._complete = onComplete\n      ? function (this: OperatorSubscriber<T>) {\n          try {\n            onComplete();\n          } catch (err) {\n            // Send any errors that occur down stream.\n            destination.error(err);\n          } finally {\n            // Ensure finalization.\n            this.unsubscribe();\n          }\n        }\n      : super._complete;\n  }\n\n  unsubscribe() {\n    if (!this.shouldUnsubscribe || this.shouldUnsubscribe()) {\n      const { closed } = this;\n      super.unsubscribe();\n      // Execute additional teardown if we have any and we didn't already do so.\n      !closed && this.onFinalize?.();\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAc1C,OAAM,SAAUC,wBAAwBA,CACtCC,WAA4B,EAC5BC,MAA2B,EAC3BC,UAAuB,EACvBC,OAA4B,EAC5BC,UAAuB;EAEvB,OAAO,IAAIC,kBAAkB,CAACL,WAAW,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,UAAU,CAAC;AACrF;AAMA,IAAAC,kBAAA,aAAAC,MAAA;EAA2CC,SAAA,CAAAF,kBAAA,EAAAC,MAAA;EAiBzC,SAAAD,mBACEL,WAA4B,EAC5BC,MAA2B,EAC3BC,UAAuB,EACvBC,OAA4B,EACpBC,UAAuB,EACvBI,iBAAiC;IAN3C,IAAAC,KAAA,GAoBEH,MAAA,CAAAI,IAAA,OAAMV,WAAW,CAAC;IAfVS,KAAA,CAAAL,UAAU,GAAVA,UAAU;IACVK,KAAA,CAAAD,iBAAiB,GAAjBA,iBAAiB;IAezBC,KAAI,CAACE,KAAK,GAAGV,MAAM,GACf,UAAuCW,KAAQ;MAC7C,IAAI;QACFX,MAAM,CAACW,KAAK,CAAC;OACd,CAAC,OAAOC,GAAG,EAAE;QACZb,WAAW,CAACc,KAAK,CAACD,GAAG,CAAC;;IAE1B,CAAC,GACDP,MAAA,CAAAS,SAAA,CAAMJ,KAAK;IACfF,KAAI,CAACO,MAAM,GAAGb,OAAO,GACjB,UAAuCU,GAAQ;MAC7C,IAAI;QACFV,OAAO,CAACU,GAAG,CAAC;OACb,CAAC,OAAOA,GAAG,EAAE;QAEZb,WAAW,CAACc,KAAK,CAACD,GAAG,CAAC;OACvB,SAAS;QAER,IAAI,CAACI,WAAW,EAAE;;IAEtB,CAAC,GACDX,MAAA,CAAAS,SAAA,CAAMC,MAAM;IAChBP,KAAI,CAACS,SAAS,GAAGhB,UAAU,GACvB;MACE,IAAI;QACFA,UAAU,EAAE;OACb,CAAC,OAAOW,GAAG,EAAE;QAEZb,WAAW,CAACc,KAAK,CAACD,GAAG,CAAC;OACvB,SAAS;QAER,IAAI,CAACI,WAAW,EAAE;;IAEtB,CAAC,GACDX,MAAA,CAAAS,SAAA,CAAMG,SAAS;;EACrB;EAEAb,kBAAA,CAAAU,SAAA,CAAAE,WAAW,GAAX;;IACE,IAAI,CAAC,IAAI,CAACT,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,EAAE,EAAE;MAC/C,IAAAW,QAAM,GAAK,IAAI,CAAAC,MAAT;MACdd,MAAA,CAAAS,SAAA,CAAME,WAAW,CAAAP,IAAA,MAAE;MAEnB,CAACS,QAAM,KAAI,CAAAE,EAAA,OAAI,CAACjB,UAAU,cAAAiB,EAAA,uBAAAA,EAAA,CAAAX,IAAA,CAAf,IAAI,CAAe;;EAElC,CAAC;EACH,OAAAL,kBAAC;AAAD,CAAC,CAnF0CP,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}