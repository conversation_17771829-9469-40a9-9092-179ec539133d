{"ast": null, "code": "import { async } from '../scheduler/async';\nimport { isValidDate } from '../util/isDate';\nimport { timeout } from './timeout';\nexport function timeoutWith(due, withObservable, scheduler) {\n  var first;\n  var each;\n  var _with;\n  scheduler = scheduler !== null && scheduler !== void 0 ? scheduler : async;\n  if (isValidDate(due)) {\n    first = due;\n  } else if (typeof due === 'number') {\n    each = due;\n  }\n  if (withObservable) {\n    _with = function () {\n      return withObservable;\n    };\n  } else {\n    throw new TypeError('No observable provided to switch to');\n  }\n  if (first == null && each == null) {\n    throw new TypeError('No timeout provided.');\n  }\n  return timeout({\n    first: first,\n    each: each,\n    scheduler: scheduler,\n    with: _with\n  });\n}", "map": {"version": 3, "names": ["async", "isValidDate", "timeout", "timeoutWith", "due", "withObservable", "scheduler", "first", "each", "_with", "TypeError", "with"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\timeoutWith.ts"], "sourcesContent": ["import { async } from '../scheduler/async';\nimport { isValidDate } from '../util/isDate';\nimport { ObservableInput, OperatorFunction, SchedulerLike } from '../types';\nimport { timeout } from './timeout';\n\n/** @deprecated Replaced with {@link timeout}. Instead of `timeoutWith(someDate, a$, scheduler)`, use the configuration object\n * `timeout({ first: someDate, with: () => a$, scheduler })`. Will be removed in v8. */\nexport function timeoutWith<T, R>(dueBy: Date, switchTo: ObservableInput<R>, scheduler?: SchedulerLike): OperatorFunction<T, T | R>;\n/** @deprecated Replaced with {@link timeout}. Instead of `timeoutWith(100, a$, scheduler)`, use the configuration object\n *  `timeout({ each: 100, with: () => a$, scheduler })`. Will be removed in v8. */\nexport function timeoutWith<T, R>(waitFor: number, switchTo: ObservableInput<R>, scheduler?: SchedulerLike): OperatorFunction<T, T | R>;\n\n/**\n * When the passed timespan elapses before the source emits any given value, it will unsubscribe from the source,\n * and switch the subscription to another observable.\n *\n * <span class=\"informal\">Used to switch to a different observable if your source is being slow.</span>\n *\n * Useful in cases where:\n *\n * - You want to switch to a different source that may be faster.\n * - You want to notify a user that the data stream is slow.\n * - You want to emit a custom error rather than the {@link TimeoutError} emitted\n *   by the default usage of {@link timeout}.\n *\n * If the first parameter is passed as Date and the time of the Date arrives before the first value arrives from the source,\n * it will unsubscribe from the source and switch the subscription to another observable.\n *\n * <span class=\"informal\">Use Date object to switch to a different observable if the first value doesn't arrive by a specific time.</span>\n *\n * Can be used to set a timeout only for the first value, however it's recommended to use the {@link timeout} operator with\n * the `first` configuration to get the same effect.\n *\n * ## Examples\n *\n * Fallback to a faster observable\n *\n * ```ts\n * import { interval, timeoutWith } from 'rxjs';\n *\n * const slow$ = interval(1000);\n * const faster$ = interval(500);\n *\n * slow$\n *   .pipe(timeoutWith(900, faster$))\n *   .subscribe(console.log);\n * ```\n *\n * Emit your own custom timeout error\n *\n * ```ts\n * import { interval, timeoutWith, throwError } from 'rxjs';\n *\n * class CustomTimeoutError extends Error {\n *   constructor() {\n *     super('It was too slow');\n *     this.name = 'CustomTimeoutError';\n *   }\n * }\n *\n * const slow$ = interval(1000);\n *\n * slow$\n *   .pipe(timeoutWith(900, throwError(() => new CustomTimeoutError())))\n *   .subscribe({\n *     error: err => console.error(err.message)\n *   });\n * ```\n *\n * @see {@link timeout}\n *\n * @param due When passed a number, used as the time (in milliseconds) allowed between each value from the source before timeout\n * is triggered. When passed a Date, used as the exact time at which the timeout will be triggered if the first value does not arrive.\n * @param withObservable The observable to switch to when timeout occurs.\n * @param scheduler The scheduler to use with time-related operations within this operator. Defaults to {@link asyncScheduler}\n * @return A function that returns an Observable that mirrors behaviour of the\n * source Observable, unless timeout happens when it starts emitting values\n * from the `ObservableInput` passed as a second parameter.\n * @deprecated Replaced with {@link timeout}. Instead of `timeoutWith(100, a$, scheduler)`, use {@link timeout} with the configuration\n * object: `timeout({ each: 100, with: () => a$, scheduler })`. Instead of `timeoutWith(someDate, a$, scheduler)`, use {@link timeout}\n * with the configuration object: `timeout({ first: someDate, with: () => a$, scheduler })`. Will be removed in v8.\n */\nexport function timeoutWith<T, R>(\n  due: number | Date,\n  withObservable: ObservableInput<R>,\n  scheduler?: SchedulerLike\n): OperatorFunction<T, T | R> {\n  let first: number | Date | undefined;\n  let each: number | undefined;\n  let _with: () => ObservableInput<R>;\n  scheduler = scheduler ?? async;\n\n  if (isValidDate(due)) {\n    first = due;\n  } else if (typeof due === 'number') {\n    each = due;\n  }\n\n  if (withObservable) {\n    _with = () => withObservable;\n  } else {\n    throw new TypeError('No observable provided to switch to');\n  }\n\n  if (first == null && each == null) {\n    // Ensure timeout was provided at runtime.\n    throw new TypeError('No timeout provided.');\n  }\n\n  return timeout<T, ObservableInput<R>>({\n    first,\n    each,\n    scheduler,\n    with: _with,\n  });\n}\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,oBAAoB;AAC1C,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,SAASC,OAAO,QAAQ,WAAW;AA+EnC,OAAM,SAAUC,WAAWA,CACzBC,GAAkB,EAClBC,cAAkC,EAClCC,SAAyB;EAEzB,IAAIC,KAAgC;EACpC,IAAIC,IAAwB;EAC5B,IAAIC,KAA+B;EACnCH,SAAS,GAAGA,SAAS,aAATA,SAAS,cAATA,SAAS,GAAIN,KAAK;EAE9B,IAAIC,WAAW,CAACG,GAAG,CAAC,EAAE;IACpBG,KAAK,GAAGH,GAAG;GACZ,MAAM,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAClCI,IAAI,GAAGJ,GAAG;;EAGZ,IAAIC,cAAc,EAAE;IAClBI,KAAK,GAAG,SAAAA,CAAA;MAAM,OAAAJ,cAAc;IAAd,CAAc;GAC7B,MAAM;IACL,MAAM,IAAIK,SAAS,CAAC,qCAAqC,CAAC;;EAG5D,IAAIH,KAAK,IAAI,IAAI,IAAIC,IAAI,IAAI,IAAI,EAAE;IAEjC,MAAM,IAAIE,SAAS,CAAC,sBAAsB,CAAC;;EAG7C,OAAOR,OAAO,CAAwB;IACpCK,KAAK,EAAAA,KAAA;IACLC,IAAI,EAAAA,IAAA;IACJF,SAAS,EAAAA,SAAA;IACTK,IAAI,EAAEF;GACP,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}