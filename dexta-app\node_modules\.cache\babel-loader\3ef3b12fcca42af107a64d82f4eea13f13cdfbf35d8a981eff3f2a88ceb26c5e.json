{"ast": null, "code": "/**\n * Export.\n */\n\nmodule.exports = toNoCase;\n\n/**\n * Test whether a string is camel-case.\n */\n\nvar hasSpace = /\\s/;\nvar hasSeparator = /(_|-|\\.|:)/;\nvar hasCamel = /([a-z][A-Z]|[A-Z][a-z])/;\n\n/**\n * Remove any starting case from a `string`, like camel or snake, but keep\n * spaces and punctuation that may be important otherwise.\n *\n * @param {String} string\n * @return {String}\n */\n\nfunction toNoCase(string) {\n  if (hasSpace.test(string)) return string.toLowerCase();\n  if (hasSeparator.test(string)) return (unseparate(string) || string).toLowerCase();\n  if (hasCamel.test(string)) return uncamelize(string).toLowerCase();\n  return string.toLowerCase();\n}\n\n/**\n * Separator splitter.\n */\n\nvar separatorSplitter = /[\\W_]+(.|$)/g;\n\n/**\n * Un-separate a `string`.\n *\n * @param {String} string\n * @return {String}\n */\n\nfunction unseparate(string) {\n  return string.replace(separatorSplitter, function (m, next) {\n    return next ? ' ' + next : '';\n  });\n}\n\n/**\n * Camelcase splitter.\n */\n\nvar camelSplitter = /(.)([A-Z]+)/g;\n\n/**\n * Un-camelcase a `string`.\n *\n * @param {String} string\n * @return {String}\n */\n\nfunction uncamelize(string) {\n  return string.replace(camelSplitter, function (m, previous, uppers) {\n    return previous + ' ' + uppers.toLowerCase().split('').join(' ');\n  });\n}", "map": {"version": 3, "names": ["module", "exports", "toNoCase", "hasSpace", "hasSeparator", "hasCamel", "string", "test", "toLowerCase", "unseparate", "uncamelize", "separatorSplitter", "replace", "m", "next", "camel<PERSON><PERSON>litter", "previous", "uppers", "split", "join"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/to-no-case/index.js"], "sourcesContent": ["\n/**\n * Export.\n */\n\nmodule.exports = toNoCase\n\n/**\n * Test whether a string is camel-case.\n */\n\nvar hasSpace = /\\s/\nvar hasSeparator = /(_|-|\\.|:)/\nvar hasCamel = /([a-z][A-Z]|[A-Z][a-z])/\n\n/**\n * Remove any starting case from a `string`, like camel or snake, but keep\n * spaces and punctuation that may be important otherwise.\n *\n * @param {String} string\n * @return {String}\n */\n\nfunction toNoCase(string) {\n  if (hasSpace.test(string)) return string.toLowerCase()\n  if (hasSeparator.test(string)) return (unseparate(string) || string).toLowerCase()\n  if (hasCamel.test(string)) return uncamelize(string).toLowerCase()\n  return string.toLowerCase()\n}\n\n/**\n * Separator splitter.\n */\n\nvar separatorSplitter = /[\\W_]+(.|$)/g\n\n/**\n * Un-separate a `string`.\n *\n * @param {String} string\n * @return {String}\n */\n\nfunction unseparate(string) {\n  return string.replace(separatorSplitter, function (m, next) {\n    return next ? ' ' + next : ''\n  })\n}\n\n/**\n * Camelcase splitter.\n */\n\nvar camelSplitter = /(.)([A-Z]+)/g\n\n/**\n * Un-camelcase a `string`.\n *\n * @param {String} string\n * @return {String}\n */\n\nfunction uncamelize(string) {\n  return string.replace(camelSplitter, function (m, previous, uppers) {\n    return previous + ' ' + uppers.toLowerCase().split('').join(' ')\n  })\n}\n"], "mappings": "AACA;AACA;AACA;;AAEAA,MAAM,CAACC,OAAO,GAAGC,QAAQ;;AAEzB;AACA;AACA;;AAEA,IAAIC,QAAQ,GAAG,IAAI;AACnB,IAAIC,YAAY,GAAG,YAAY;AAC/B,IAAIC,QAAQ,GAAG,yBAAyB;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASH,QAAQA,CAACI,MAAM,EAAE;EACxB,IAAIH,QAAQ,CAACI,IAAI,CAACD,MAAM,CAAC,EAAE,OAAOA,MAAM,CAACE,WAAW,CAAC,CAAC;EACtD,IAAIJ,YAAY,CAACG,IAAI,CAACD,MAAM,CAAC,EAAE,OAAO,CAACG,UAAU,CAACH,MAAM,CAAC,IAAIA,MAAM,EAAEE,WAAW,CAAC,CAAC;EAClF,IAAIH,QAAQ,CAACE,IAAI,CAACD,MAAM,CAAC,EAAE,OAAOI,UAAU,CAACJ,MAAM,CAAC,CAACE,WAAW,CAAC,CAAC;EAClE,OAAOF,MAAM,CAACE,WAAW,CAAC,CAAC;AAC7B;;AAEA;AACA;AACA;;AAEA,IAAIG,iBAAiB,GAAG,cAAc;;AAEtC;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASF,UAAUA,CAACH,MAAM,EAAE;EAC1B,OAAOA,MAAM,CAACM,OAAO,CAACD,iBAAiB,EAAE,UAAUE,CAAC,EAAEC,IAAI,EAAE;IAC1D,OAAOA,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG,EAAE;EAC/B,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;;AAEA,IAAIC,aAAa,GAAG,cAAc;;AAElC;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASL,UAAUA,CAACJ,MAAM,EAAE;EAC1B,OAAOA,MAAM,CAACM,OAAO,CAACG,aAAa,EAAE,UAAUF,CAAC,EAAEG,QAAQ,EAAEC,MAAM,EAAE;IAClE,OAAOD,QAAQ,GAAG,GAAG,GAAGC,MAAM,CAACT,WAAW,CAAC,CAAC,CAACU,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EAClE,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}