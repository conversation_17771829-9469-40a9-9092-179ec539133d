{"ast": null, "code": "import { isFunction } from './isFunction';\nexport function isAsyncIterable(obj) {\n  return Symbol.asyncIterator && isFunction(obj === null || obj === void 0 ? void 0 : obj[Symbol.asyncIterator]);\n}", "map": {"version": 3, "names": ["isFunction", "isAsyncIterable", "obj", "Symbol", "asyncIterator"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\util\\isAsyncIterable.ts"], "sourcesContent": ["import { isFunction } from './isFunction';\n\nexport function isAsyncIterable<T>(obj: any): obj is AsyncIterable<T> {\n  return Symbol.asyncIterator && isFunction(obj?.[Symbol.asyncIterator]);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AAEzC,OAAM,SAAUC,eAAeA,CAAIC,GAAQ;EACzC,OAAOC,MAAM,CAACC,aAAa,IAAIJ,UAAU,CAACE,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAGC,MAAM,CAACC,aAAa,CAAC,CAAC;AACxE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}