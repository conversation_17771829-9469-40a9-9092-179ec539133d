{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.decodeXMLStrict = exports.decodeHTML5Strict = exports.decodeHTML4Strict = exports.decodeHTML5 = exports.decodeHTML4 = exports.decodeHTMLStrict = exports.decodeHTML = exports.decodeXML = exports.encodeHTML5 = exports.encodeHTML4 = exports.escapeUTF8 = exports.escape = exports.encodeNonAsciiHTML = exports.encodeHTML = exports.encodeXML = exports.encode = exports.decodeStrict = exports.decode = void 0;\nvar decode_1 = require(\"./decode\");\nvar encode_1 = require(\"./encode\");\n/**\n * Decodes a string with entities.\n *\n * @param data String to decode.\n * @param level Optional level to decode at. 0 = XML, 1 = HTML. Default is 0.\n * @deprecated Use `decodeXML` or `decodeHTML` directly.\n */\nfunction decode(data, level) {\n  return (!level || level <= 0 ? decode_1.decodeXML : decode_1.decodeHTML)(data);\n}\nexports.decode = decode;\n/**\n * Decodes a string with entities. Does not allow missing trailing semicolons for entities.\n *\n * @param data String to decode.\n * @param level Optional level to decode at. 0 = XML, 1 = HTML. Default is 0.\n * @deprecated Use `decodeHTMLStrict` or `decodeXML` directly.\n */\nfunction decodeStrict(data, level) {\n  return (!level || level <= 0 ? decode_1.decodeXML : decode_1.decodeHTMLStrict)(data);\n}\nexports.decodeStrict = decodeStrict;\n/**\n * Encodes a string with entities.\n *\n * @param data String to encode.\n * @param level Optional level to encode at. 0 = XML, 1 = HTML. Default is 0.\n * @deprecated Use `encodeHTML`, `encodeXML` or `encodeNonAsciiHTML` directly.\n */\nfunction encode(data, level) {\n  return (!level || level <= 0 ? encode_1.encodeXML : encode_1.encodeHTML)(data);\n}\nexports.encode = encode;\nvar encode_2 = require(\"./encode\");\nObject.defineProperty(exports, \"encodeXML\", {\n  enumerable: true,\n  get: function () {\n    return encode_2.encodeXML;\n  }\n});\nObject.defineProperty(exports, \"encodeHTML\", {\n  enumerable: true,\n  get: function () {\n    return encode_2.encodeHTML;\n  }\n});\nObject.defineProperty(exports, \"encodeNonAsciiHTML\", {\n  enumerable: true,\n  get: function () {\n    return encode_2.encodeNonAsciiHTML;\n  }\n});\nObject.defineProperty(exports, \"escape\", {\n  enumerable: true,\n  get: function () {\n    return encode_2.escape;\n  }\n});\nObject.defineProperty(exports, \"escapeUTF8\", {\n  enumerable: true,\n  get: function () {\n    return encode_2.escapeUTF8;\n  }\n});\n// Legacy aliases (deprecated)\nObject.defineProperty(exports, \"encodeHTML4\", {\n  enumerable: true,\n  get: function () {\n    return encode_2.encodeHTML;\n  }\n});\nObject.defineProperty(exports, \"encodeHTML5\", {\n  enumerable: true,\n  get: function () {\n    return encode_2.encodeHTML;\n  }\n});\nvar decode_2 = require(\"./decode\");\nObject.defineProperty(exports, \"decodeXML\", {\n  enumerable: true,\n  get: function () {\n    return decode_2.decodeXML;\n  }\n});\nObject.defineProperty(exports, \"decodeHTML\", {\n  enumerable: true,\n  get: function () {\n    return decode_2.decodeHTML;\n  }\n});\nObject.defineProperty(exports, \"decodeHTMLStrict\", {\n  enumerable: true,\n  get: function () {\n    return decode_2.decodeHTMLStrict;\n  }\n});\n// Legacy aliases (deprecated)\nObject.defineProperty(exports, \"decodeHTML4\", {\n  enumerable: true,\n  get: function () {\n    return decode_2.decodeHTML;\n  }\n});\nObject.defineProperty(exports, \"decodeHTML5\", {\n  enumerable: true,\n  get: function () {\n    return decode_2.decodeHTML;\n  }\n});\nObject.defineProperty(exports, \"decodeHTML4Strict\", {\n  enumerable: true,\n  get: function () {\n    return decode_2.decodeHTMLStrict;\n  }\n});\nObject.defineProperty(exports, \"decodeHTML5Strict\", {\n  enumerable: true,\n  get: function () {\n    return decode_2.decodeHTMLStrict;\n  }\n});\nObject.defineProperty(exports, \"decodeXMLStrict\", {\n  enumerable: true,\n  get: function () {\n    return decode_2.decodeXML;\n  }\n});", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "decodeXMLStrict", "decodeHTML5Strict", "decodeHTML4Strict", "decodeHTML5", "decodeHTML4", "decodeHTMLStrict", "decodeHTML", "decodeXML", "encodeHTML5", "encodeHTML4", "escapeUTF8", "escape", "encodeNonAsciiHTML", "encodeHTML", "encodeXML", "encode", "decodeStrict", "decode", "decode_1", "require", "encode_1", "data", "level", "encode_2", "enumerable", "get", "decode_2"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/entities/lib/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.decodeXMLStrict = exports.decodeHTML5Strict = exports.decodeHTML4Strict = exports.decodeHTML5 = exports.decodeHTML4 = exports.decodeHTMLStrict = exports.decodeHTML = exports.decodeXML = exports.encodeHTML5 = exports.encodeHTML4 = exports.escapeUTF8 = exports.escape = exports.encodeNonAsciiHTML = exports.encodeHTML = exports.encodeXML = exports.encode = exports.decodeStrict = exports.decode = void 0;\nvar decode_1 = require(\"./decode\");\nvar encode_1 = require(\"./encode\");\n/**\n * Decodes a string with entities.\n *\n * @param data String to decode.\n * @param level Optional level to decode at. 0 = XML, 1 = HTML. Default is 0.\n * @deprecated Use `decodeXML` or `decodeHTML` directly.\n */\nfunction decode(data, level) {\n    return (!level || level <= 0 ? decode_1.decodeXML : decode_1.decodeHTML)(data);\n}\nexports.decode = decode;\n/**\n * Decodes a string with entities. Does not allow missing trailing semicolons for entities.\n *\n * @param data String to decode.\n * @param level Optional level to decode at. 0 = XML, 1 = HTML. Default is 0.\n * @deprecated Use `decodeHTMLStrict` or `decodeXML` directly.\n */\nfunction decodeStrict(data, level) {\n    return (!level || level <= 0 ? decode_1.decodeXML : decode_1.decodeHTMLStrict)(data);\n}\nexports.decodeStrict = decodeStrict;\n/**\n * Encodes a string with entities.\n *\n * @param data String to encode.\n * @param level Optional level to encode at. 0 = XML, 1 = HTML. Default is 0.\n * @deprecated Use `encodeHTML`, `encodeXML` or `encodeNonAsciiHTML` directly.\n */\nfunction encode(data, level) {\n    return (!level || level <= 0 ? encode_1.encodeXML : encode_1.encodeHTML)(data);\n}\nexports.encode = encode;\nvar encode_2 = require(\"./encode\");\nObject.defineProperty(exports, \"encodeXML\", { enumerable: true, get: function () { return encode_2.encodeXML; } });\nObject.defineProperty(exports, \"encodeHTML\", { enumerable: true, get: function () { return encode_2.encodeHTML; } });\nObject.defineProperty(exports, \"encodeNonAsciiHTML\", { enumerable: true, get: function () { return encode_2.encodeNonAsciiHTML; } });\nObject.defineProperty(exports, \"escape\", { enumerable: true, get: function () { return encode_2.escape; } });\nObject.defineProperty(exports, \"escapeUTF8\", { enumerable: true, get: function () { return encode_2.escapeUTF8; } });\n// Legacy aliases (deprecated)\nObject.defineProperty(exports, \"encodeHTML4\", { enumerable: true, get: function () { return encode_2.encodeHTML; } });\nObject.defineProperty(exports, \"encodeHTML5\", { enumerable: true, get: function () { return encode_2.encodeHTML; } });\nvar decode_2 = require(\"./decode\");\nObject.defineProperty(exports, \"decodeXML\", { enumerable: true, get: function () { return decode_2.decodeXML; } });\nObject.defineProperty(exports, \"decodeHTML\", { enumerable: true, get: function () { return decode_2.decodeHTML; } });\nObject.defineProperty(exports, \"decodeHTMLStrict\", { enumerable: true, get: function () { return decode_2.decodeHTMLStrict; } });\n// Legacy aliases (deprecated)\nObject.defineProperty(exports, \"decodeHTML4\", { enumerable: true, get: function () { return decode_2.decodeHTML; } });\nObject.defineProperty(exports, \"decodeHTML5\", { enumerable: true, get: function () { return decode_2.decodeHTML; } });\nObject.defineProperty(exports, \"decodeHTML4Strict\", { enumerable: true, get: function () { return decode_2.decodeHTMLStrict; } });\nObject.defineProperty(exports, \"decodeHTML5Strict\", { enumerable: true, get: function () { return decode_2.decodeHTMLStrict; } });\nObject.defineProperty(exports, \"decodeXMLStrict\", { enumerable: true, get: function () { return decode_2.decodeXML; } });\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,eAAe,GAAGF,OAAO,CAACG,iBAAiB,GAAGH,OAAO,CAACI,iBAAiB,GAAGJ,OAAO,CAACK,WAAW,GAAGL,OAAO,CAACM,WAAW,GAAGN,OAAO,CAACO,gBAAgB,GAAGP,OAAO,CAACQ,UAAU,GAAGR,OAAO,CAACS,SAAS,GAAGT,OAAO,CAACU,WAAW,GAAGV,OAAO,CAACW,WAAW,GAAGX,OAAO,CAACY,UAAU,GAAGZ,OAAO,CAACa,MAAM,GAAGb,OAAO,CAACc,kBAAkB,GAAGd,OAAO,CAACe,UAAU,GAAGf,OAAO,CAACgB,SAAS,GAAGhB,OAAO,CAACiB,MAAM,GAAGjB,OAAO,CAACkB,YAAY,GAAGlB,OAAO,CAACmB,MAAM,GAAG,KAAK,CAAC;AACzZ,IAAIC,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;AAClC,IAAIC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,MAAMA,CAACI,IAAI,EAAEC,KAAK,EAAE;EACzB,OAAO,CAAC,CAACA,KAAK,IAAIA,KAAK,IAAI,CAAC,GAAGJ,QAAQ,CAACX,SAAS,GAAGW,QAAQ,CAACZ,UAAU,EAAEe,IAAI,CAAC;AAClF;AACAvB,OAAO,CAACmB,MAAM,GAAGA,MAAM;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,YAAYA,CAACK,IAAI,EAAEC,KAAK,EAAE;EAC/B,OAAO,CAAC,CAACA,KAAK,IAAIA,KAAK,IAAI,CAAC,GAAGJ,QAAQ,CAACX,SAAS,GAAGW,QAAQ,CAACb,gBAAgB,EAAEgB,IAAI,CAAC;AACxF;AACAvB,OAAO,CAACkB,YAAY,GAAGA,YAAY;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,MAAMA,CAACM,IAAI,EAAEC,KAAK,EAAE;EACzB,OAAO,CAAC,CAACA,KAAK,IAAIA,KAAK,IAAI,CAAC,GAAGF,QAAQ,CAACN,SAAS,GAAGM,QAAQ,CAACP,UAAU,EAAEQ,IAAI,CAAC;AAClF;AACAvB,OAAO,CAACiB,MAAM,GAAGA,MAAM;AACvB,IAAIQ,QAAQ,GAAGJ,OAAO,CAAC,UAAU,CAAC;AAClCvB,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,WAAW,EAAE;EAAE0B,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOF,QAAQ,CAACT,SAAS;EAAE;AAAE,CAAC,CAAC;AAClHlB,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAE0B,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOF,QAAQ,CAACV,UAAU;EAAE;AAAE,CAAC,CAAC;AACpHjB,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,oBAAoB,EAAE;EAAE0B,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOF,QAAQ,CAACX,kBAAkB;EAAE;AAAE,CAAC,CAAC;AACpIhB,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,QAAQ,EAAE;EAAE0B,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOF,QAAQ,CAACZ,MAAM;EAAE;AAAE,CAAC,CAAC;AAC5Gf,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAE0B,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOF,QAAQ,CAACb,UAAU;EAAE;AAAE,CAAC,CAAC;AACpH;AACAd,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,aAAa,EAAE;EAAE0B,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOF,QAAQ,CAACV,UAAU;EAAE;AAAE,CAAC,CAAC;AACrHjB,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,aAAa,EAAE;EAAE0B,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOF,QAAQ,CAACV,UAAU;EAAE;AAAE,CAAC,CAAC;AACrH,IAAIa,QAAQ,GAAGP,OAAO,CAAC,UAAU,CAAC;AAClCvB,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,WAAW,EAAE;EAAE0B,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOC,QAAQ,CAACnB,SAAS;EAAE;AAAE,CAAC,CAAC;AAClHX,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAE0B,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOC,QAAQ,CAACpB,UAAU;EAAE;AAAE,CAAC,CAAC;AACpHV,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,kBAAkB,EAAE;EAAE0B,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOC,QAAQ,CAACrB,gBAAgB;EAAE;AAAE,CAAC,CAAC;AAChI;AACAT,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,aAAa,EAAE;EAAE0B,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOC,QAAQ,CAACpB,UAAU;EAAE;AAAE,CAAC,CAAC;AACrHV,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,aAAa,EAAE;EAAE0B,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOC,QAAQ,CAACpB,UAAU;EAAE;AAAE,CAAC,CAAC;AACrHV,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,mBAAmB,EAAE;EAAE0B,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOC,QAAQ,CAACrB,gBAAgB;EAAE;AAAE,CAAC,CAAC;AACjIT,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,mBAAmB,EAAE;EAAE0B,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOC,QAAQ,CAACrB,gBAAgB;EAAE;AAAE,CAAC,CAAC;AACjIT,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,iBAAiB,EAAE;EAAE0B,UAAU,EAAE,IAAI;EAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;IAAE,OAAOC,QAAQ,CAACnB,SAAS;EAAE;AAAE,CAAC,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}