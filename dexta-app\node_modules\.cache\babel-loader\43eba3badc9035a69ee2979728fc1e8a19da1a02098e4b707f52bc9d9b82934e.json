{"ast": null, "code": "import Metadata from '../metadata.js';\nimport { VALID_DIGITS } from '../constants.js';\nvar CAPTURING_DIGIT_PATTERN = new RegExp('([' + VALID_DIGITS + '])');\nexport default function stripIddPrefix(number, country, callingCode, metadata) {\n  if (!country) {\n    return;\n  } // Check if the number is IDD-prefixed.\n\n  var countryMetadata = new Metadata(metadata);\n  countryMetadata.selectNumberingPlan(country, callingCode);\n  var IDDPrefixPattern = new RegExp(countryMetadata.IDDPrefix());\n  if (number.search(IDDPrefixPattern) !== 0) {\n    return;\n  } // Strip IDD prefix.\n\n  number = number.slice(number.match(IDDPrefixPattern)[0].length); // If there're any digits after an IDD prefix,\n  // then those digits are a country calling code.\n  // Since no country code starts with a `0`,\n  // the code below validates that the next digit (if present) is not `0`.\n\n  var matchedGroups = number.match(CAPTURING_DIGIT_PATTERN);\n  if (matchedGroups && matchedGroups[1] != null && matchedGroups[1].length > 0) {\n    if (matchedGroups[1] === '0') {\n      return;\n    }\n  }\n  return number;\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "VALID_DIGITS", "CAPTURING_DIGIT_PATTERN", "RegExp", "stripIddPrefix", "number", "country", "callingCode", "metadata", "countryMetadata", "selectNumberingPlan", "IDDPrefixPattern", "IDDPrefix", "search", "slice", "match", "length", "matchedGroups"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\helpers\\stripIddPrefix.js"], "sourcesContent": ["import Metadata from '../metadata.js'\r\nimport { VALID_DIGITS } from '../constants.js'\r\n\r\nconst CAPTURING_DIGIT_PATTERN = new RegExp('([' + VALID_DIGITS + '])')\r\n\r\nexport default function stripIddPrefix(number, country, callingCode, metadata) {\r\n\tif (!country) {\r\n\t\treturn\r\n\t}\r\n\t// Check if the number is IDD-prefixed.\r\n\tconst countryMetadata = new Metadata(metadata)\r\n\tcountryMetadata.selectNumberingPlan(country, callingCode)\r\n\tconst IDDPrefixPattern = new RegExp(countryMetadata.IDDPrefix())\r\n\tif (number.search(IDDPrefixPattern) !== 0) {\r\n\t\treturn\r\n\t}\r\n\t// Strip IDD prefix.\r\n\tnumber = number.slice(number.match(IDDPrefixPattern)[0].length)\r\n\t// If there're any digits after an IDD prefix,\r\n\t// then those digits are a country calling code.\r\n\t// Since no country code starts with a `0`,\r\n\t// the code below validates that the next digit (if present) is not `0`.\r\n\tconst matchedGroups = number.match(CAPTURING_DIGIT_PATTERN)\r\n\tif (matchedGroups && matchedGroups[1] != null && matchedGroups[1].length > 0) {\r\n\t\tif (matchedGroups[1] === '0') {\r\n\t\t\treturn\r\n\t\t}\r\n\t}\r\n\treturn number\r\n}"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,gBAArB;AACA,SAASC,YAAT,QAA6B,iBAA7B;AAEA,IAAMC,uBAAuB,GAAG,IAAIC,MAAJ,CAAW,OAAOF,YAAP,GAAsB,IAAjC,CAAhC;AAEA,eAAe,SAASG,cAATA,CAAwBC,MAAxB,EAAgCC,OAAhC,EAAyCC,WAAzC,EAAsDC,QAAtD,EAAgE;EAC9E,IAAI,CAACF,OAAL,EAAc;IACb;EACA,CAH6E,CAI9E;;EACA,IAAMG,eAAe,GAAG,IAAIT,QAAJ,CAAaQ,QAAb,CAAxB;EACAC,eAAe,CAACC,mBAAhB,CAAoCJ,OAApC,EAA6CC,WAA7C;EACA,IAAMI,gBAAgB,GAAG,IAAIR,MAAJ,CAAWM,eAAe,CAACG,SAAhB,EAAX,CAAzB;EACA,IAAIP,MAAM,CAACQ,MAAP,CAAcF,gBAAd,MAAoC,CAAxC,EAA2C;IAC1C;EACA,CAV6E,CAW9E;;EACAN,MAAM,GAAGA,MAAM,CAACS,KAAP,CAAaT,MAAM,CAACU,KAAP,CAAaJ,gBAAb,EAA+B,CAA/B,EAAkCK,MAA/C,CAAT,CAZ8E,CAa9E;EACA;EACA;EACA;;EACA,IAAMC,aAAa,GAAGZ,MAAM,CAACU,KAAP,CAAab,uBAAb,CAAtB;EACA,IAAIe,aAAa,IAAIA,aAAa,CAAC,CAAD,CAAb,IAAoB,IAArC,IAA6CA,aAAa,CAAC,CAAD,CAAb,CAAiBD,MAAjB,GAA0B,CAA3E,EAA8E;IAC7E,IAAIC,aAAa,CAAC,CAAD,CAAb,KAAqB,GAAzB,EAA8B;MAC7B;IACA;EACD;EACD,OAAOZ,MAAP;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}