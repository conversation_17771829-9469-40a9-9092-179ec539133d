{"ast": null, "code": "import { disposables as m } from '../../utils/disposables.js';\nimport { isIOS as f } from '../../utils/platform.js';\nfunction T() {\n  if (!f()) return {};\n  let l;\n  return {\n    before() {\n      l = window.pageYOffset;\n    },\n    after({\n      doc: o,\n      d: t,\n      meta: s\n    }) {\n      function i(n) {\n        return s.containers.flatMap(e => e()).some(e => e.contains(n));\n      }\n      t.microTask(() => {\n        if (window.getComputedStyle(o.documentElement).scrollBehavior !== \"auto\") {\n          let e = m();\n          e.style(o.documentElement, \"scroll-behavior\", \"auto\"), t.add(() => t.microTask(() => e.dispose()));\n        }\n        t.style(o.body, \"marginTop\", `-${l}px`), window.scrollTo(0, 0);\n        let n = null;\n        t.addEventListener(o, \"click\", e => {\n          if (e.target instanceof HTMLElement) try {\n            let r = e.target.closest(\"a\");\n            if (!r) return;\n            let {\n                hash: c\n              } = new URL(r.href),\n              a = o.querySelector(c);\n            a && !i(a) && (n = a);\n          } catch {}\n        }, !0), t.addEventListener(o, \"touchmove\", e => {\n          e.target instanceof HTMLElement && !i(e.target) && e.preventDefault();\n        }, {\n          passive: !1\n        }), t.add(() => {\n          window.scrollTo(0, window.pageYOffset + l), n && n.isConnected && (n.scrollIntoView({\n            block: \"nearest\"\n          }), n = null);\n        });\n      });\n    }\n  };\n}\nexport { T as handleIOSLocking };", "map": {"version": 3, "names": ["disposables", "m", "isIOS", "f", "T", "l", "before", "window", "pageYOffset", "after", "doc", "o", "d", "t", "meta", "s", "i", "n", "containers", "flatMap", "e", "some", "contains", "microTask", "getComputedStyle", "documentElement", "scroll<PERSON>eh<PERSON>or", "style", "add", "dispose", "body", "scrollTo", "addEventListener", "target", "HTMLElement", "r", "closest", "hash", "c", "URL", "href", "a", "querySelector", "preventDefault", "passive", "isConnected", "scrollIntoView", "block", "handleIOSLocking"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@headlessui/react/dist/hooks/document-overflow/handle-ios-locking.js"], "sourcesContent": ["import{disposables as m}from'../../utils/disposables.js';import{isIOS as f}from'../../utils/platform.js';function T(){if(!f())return{};let l;return{before(){l=window.pageYOffset},after({doc:o,d:t,meta:s}){function i(n){return s.containers.flatMap(e=>e()).some(e=>e.contains(n))}t.microTask(()=>{if(window.getComputedStyle(o.documentElement).scrollBehavior!==\"auto\"){let e=m();e.style(o.documentElement,\"scroll-behavior\",\"auto\"),t.add(()=>t.microTask(()=>e.dispose()))}t.style(o.body,\"marginTop\",`-${l}px`),window.scrollTo(0,0);let n=null;t.addEventListener(o,\"click\",e=>{if(e.target instanceof HTMLElement)try{let r=e.target.closest(\"a\");if(!r)return;let{hash:c}=new URL(r.href),a=o.querySelector(c);a&&!i(a)&&(n=a)}catch{}},!0),t.addEventListener(o,\"touchmove\",e=>{e.target instanceof HTMLElement&&!i(e.target)&&e.preventDefault()},{passive:!1}),t.add(()=>{window.scrollTo(0,window.pageYOffset+l),n&&n.isConnected&&(n.scrollIntoView({block:\"nearest\"}),n=null)})})}}}export{T as handleIOSLocking};\n"], "mappings": "AAAA,SAAOA,WAAW,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,IAAG,CAACD,CAAC,CAAC,CAAC,EAAC,OAAM,CAAC,CAAC;EAAC,IAAIE,CAAC;EAAC,OAAM;IAACC,MAAMA,CAAA,EAAE;MAACD,CAAC,GAACE,MAAM,CAACC,WAAW;IAAA,CAAC;IAACC,KAAKA,CAAC;MAACC,GAAG,EAACC,CAAC;MAACC,CAAC,EAACC,CAAC;MAACC,IAAI,EAACC;IAAC,CAAC,EAAC;MAAC,SAASC,CAACA,CAACC,CAAC,EAAC;QAAC,OAAOF,CAAC,CAACG,UAAU,CAACC,OAAO,CAACC,CAAC,IAAEA,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAACD,CAAC,IAAEA,CAAC,CAACE,QAAQ,CAACL,CAAC,CAAC,CAAC;MAAA;MAACJ,CAAC,CAACU,SAAS,CAAC,MAAI;QAAC,IAAGhB,MAAM,CAACiB,gBAAgB,CAACb,CAAC,CAACc,eAAe,CAAC,CAACC,cAAc,KAAG,MAAM,EAAC;UAAC,IAAIN,CAAC,GAACnB,CAAC,CAAC,CAAC;UAACmB,CAAC,CAACO,KAAK,CAAChB,CAAC,CAACc,eAAe,EAAC,iBAAiB,EAAC,MAAM,CAAC,EAACZ,CAAC,CAACe,GAAG,CAAC,MAAIf,CAAC,CAACU,SAAS,CAAC,MAAIH,CAAC,CAACS,OAAO,CAAC,CAAC,CAAC,CAAC;QAAA;QAAChB,CAAC,CAACc,KAAK,CAAChB,CAAC,CAACmB,IAAI,EAAC,WAAW,EAAE,IAAGzB,CAAE,IAAG,CAAC,EAACE,MAAM,CAACwB,QAAQ,CAAC,CAAC,EAAC,CAAC,CAAC;QAAC,IAAId,CAAC,GAAC,IAAI;QAACJ,CAAC,CAACmB,gBAAgB,CAACrB,CAAC,EAAC,OAAO,EAACS,CAAC,IAAE;UAAC,IAAGA,CAAC,CAACa,MAAM,YAAYC,WAAW,EAAC,IAAG;YAAC,IAAIC,CAAC,GAACf,CAAC,CAACa,MAAM,CAACG,OAAO,CAAC,GAAG,CAAC;YAAC,IAAG,CAACD,CAAC,EAAC;YAAO,IAAG;gBAACE,IAAI,EAACC;cAAC,CAAC,GAAC,IAAIC,GAAG,CAACJ,CAAC,CAACK,IAAI,CAAC;cAACC,CAAC,GAAC9B,CAAC,CAAC+B,aAAa,CAACJ,CAAC,CAAC;YAACG,CAAC,IAAE,CAACzB,CAAC,CAACyB,CAAC,CAAC,KAAGxB,CAAC,GAACwB,CAAC,CAAC;UAAA,CAAC,OAAK,CAAC;QAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAAC5B,CAAC,CAACmB,gBAAgB,CAACrB,CAAC,EAAC,WAAW,EAACS,CAAC,IAAE;UAACA,CAAC,CAACa,MAAM,YAAYC,WAAW,IAAE,CAAClB,CAAC,CAACI,CAAC,CAACa,MAAM,CAAC,IAAEb,CAAC,CAACuB,cAAc,CAAC,CAAC;QAAA,CAAC,EAAC;UAACC,OAAO,EAAC,CAAC;QAAC,CAAC,CAAC,EAAC/B,CAAC,CAACe,GAAG,CAAC,MAAI;UAACrB,MAAM,CAACwB,QAAQ,CAAC,CAAC,EAACxB,MAAM,CAACC,WAAW,GAACH,CAAC,CAAC,EAACY,CAAC,IAAEA,CAAC,CAAC4B,WAAW,KAAG5B,CAAC,CAAC6B,cAAc,CAAC;YAACC,KAAK,EAAC;UAAS,CAAC,CAAC,EAAC9B,CAAC,GAAC,IAAI,CAAC;QAAA,CAAC,CAAC;MAAA,CAAC,CAAC;IAAA;EAAC,CAAC;AAAA;AAAC,SAAOb,CAAC,IAAI4C,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}