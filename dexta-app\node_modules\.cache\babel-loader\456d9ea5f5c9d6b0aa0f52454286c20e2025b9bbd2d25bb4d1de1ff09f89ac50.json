{"ast": null, "code": "var isTag = require(\"domelementtype\").isTag;\nmodule.exports = {\n  filter: filter,\n  find: find,\n  findOneChild: findOneChild,\n  findOne: findOne,\n  existsOne: existsOne,\n  findAll: findAll\n};\nfunction filter(test, element, recurse, limit) {\n  if (!Array.isArray(element)) element = [element];\n  if (typeof limit !== \"number\" || !isFinite(limit)) {\n    limit = Infinity;\n  }\n  return find(test, element, recurse !== false, limit);\n}\nfunction find(test, elems, recurse, limit) {\n  var result = [],\n    childs;\n  for (var i = 0, j = elems.length; i < j; i++) {\n    if (test(elems[i])) {\n      result.push(elems[i]);\n      if (--limit <= 0) break;\n    }\n    childs = elems[i].children;\n    if (recurse && childs && childs.length > 0) {\n      childs = find(test, childs, recurse, limit);\n      result = result.concat(childs);\n      limit -= childs.length;\n      if (limit <= 0) break;\n    }\n  }\n  return result;\n}\nfunction findOneChild(test, elems) {\n  for (var i = 0, l = elems.length; i < l; i++) {\n    if (test(elems[i])) return elems[i];\n  }\n  return null;\n}\nfunction findOne(test, elems) {\n  var elem = null;\n  for (var i = 0, l = elems.length; i < l && !elem; i++) {\n    if (!isTag(elems[i])) {\n      continue;\n    } else if (test(elems[i])) {\n      elem = elems[i];\n    } else if (elems[i].children.length > 0) {\n      elem = findOne(test, elems[i].children);\n    }\n  }\n  return elem;\n}\nfunction existsOne(test, elems) {\n  for (var i = 0, l = elems.length; i < l; i++) {\n    if (isTag(elems[i]) && (test(elems[i]) || elems[i].children.length > 0 && existsOne(test, elems[i].children))) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction findAll(test, rootElems) {\n  var result = [];\n  var stack = rootElems.slice();\n  while (stack.length) {\n    var elem = stack.shift();\n    if (!isTag(elem)) continue;\n    if (elem.children && elem.children.length > 0) {\n      stack.unshift.apply(stack, elem.children);\n    }\n    if (test(elem)) result.push(elem);\n  }\n  return result;\n}", "map": {"version": 3, "names": ["isTag", "require", "module", "exports", "filter", "find", "find<PERSON>neChild", "findOne", "existsOne", "findAll", "test", "element", "recurse", "limit", "Array", "isArray", "isFinite", "Infinity", "elems", "result", "childs", "i", "j", "length", "push", "children", "concat", "l", "elem", "rootElems", "stack", "slice", "shift", "unshift", "apply"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/domutils/lib/querying.js"], "sourcesContent": ["var isTag = require(\"domelementtype\").isTag;\n\nmodule.exports = {\n\tfilter: filter,\n\tfind: find,\n\tfindOneChild: findOneChild,\n\tfindOne: findOne,\n\texistsOne: existsOne,\n\tfindAll: findAll\n};\n\nfunction filter(test, element, recurse, limit){\n\tif(!Array.isArray(element)) element = [element];\n\n\tif(typeof limit !== \"number\" || !isFinite(limit)){\n\t\tlimit = Infinity;\n\t}\n\treturn find(test, element, recurse !== false, limit);\n}\n\nfunction find(test, elems, recurse, limit){\n\tvar result = [], childs;\n\n\tfor(var i = 0, j = elems.length; i < j; i++){\n\t\tif(test(elems[i])){\n\t\t\tresult.push(elems[i]);\n\t\t\tif(--limit <= 0) break;\n\t\t}\n\n\t\tchilds = elems[i].children;\n\t\tif(recurse && childs && childs.length > 0){\n\t\t\tchilds = find(test, childs, recurse, limit);\n\t\t\tresult = result.concat(childs);\n\t\t\tlimit -= childs.length;\n\t\t\tif(limit <= 0) break;\n\t\t}\n\t}\n\n\treturn result;\n}\n\nfunction findOneChild(test, elems){\n\tfor(var i = 0, l = elems.length; i < l; i++){\n\t\tif(test(elems[i])) return elems[i];\n\t}\n\n\treturn null;\n}\n\nfunction findOne(test, elems){\n\tvar elem = null;\n\n\tfor(var i = 0, l = elems.length; i < l && !elem; i++){\n\t\tif(!isTag(elems[i])){\n\t\t\tcontinue;\n\t\t} else if(test(elems[i])){\n\t\t\telem = elems[i];\n\t\t} else if(elems[i].children.length > 0){\n\t\t\telem = findOne(test, elems[i].children);\n\t\t}\n\t}\n\n\treturn elem;\n}\n\nfunction existsOne(test, elems){\n\tfor(var i = 0, l = elems.length; i < l; i++){\n\t\tif(\n\t\t\tisTag(elems[i]) && (\n\t\t\t\ttest(elems[i]) || (\n\t\t\t\t\telems[i].children.length > 0 &&\n\t\t\t\t\texistsOne(test, elems[i].children)\n\t\t\t\t)\n\t\t\t)\n\t\t){\n\t\t\treturn true;\n\t\t}\n\t}\n\n\treturn false;\n}\n\nfunction findAll(test, rootElems){\n\tvar result = [];\n\tvar stack = rootElems.slice();\n\twhile(stack.length){\n\t\tvar elem = stack.shift();\n\t\tif(!isTag(elem)) continue;\n\t\tif (elem.children && elem.children.length > 0) {\n\t\t\tstack.unshift.apply(stack, elem.children);\n\t\t}\n\t\tif(test(elem)) result.push(elem);\n\t}\n\treturn result;\n}\n"], "mappings": "AAAA,IAAIA,KAAK,GAAGC,OAAO,CAAC,gBAAgB,CAAC,CAACD,KAAK;AAE3CE,MAAM,CAACC,OAAO,GAAG;EAChBC,MAAM,EAAEA,MAAM;EACdC,IAAI,EAAEA,IAAI;EACVC,YAAY,EAAEA,YAAY;EAC1BC,OAAO,EAAEA,OAAO;EAChBC,SAAS,EAAEA,SAAS;EACpBC,OAAO,EAAEA;AACV,CAAC;AAED,SAASL,MAAMA,CAACM,IAAI,EAAEC,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAC;EAC7C,IAAG,CAACC,KAAK,CAACC,OAAO,CAACJ,OAAO,CAAC,EAAEA,OAAO,GAAG,CAACA,OAAO,CAAC;EAE/C,IAAG,OAAOE,KAAK,KAAK,QAAQ,IAAI,CAACG,QAAQ,CAACH,KAAK,CAAC,EAAC;IAChDA,KAAK,GAAGI,QAAQ;EACjB;EACA,OAAOZ,IAAI,CAACK,IAAI,EAAEC,OAAO,EAAEC,OAAO,KAAK,KAAK,EAAEC,KAAK,CAAC;AACrD;AAEA,SAASR,IAAIA,CAACK,IAAI,EAAEQ,KAAK,EAAEN,OAAO,EAAEC,KAAK,EAAC;EACzC,IAAIM,MAAM,GAAG,EAAE;IAAEC,MAAM;EAEvB,KAAI,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGJ,KAAK,CAACK,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAC;IAC3C,IAAGX,IAAI,CAACQ,KAAK,CAACG,CAAC,CAAC,CAAC,EAAC;MACjBF,MAAM,CAACK,IAAI,CAACN,KAAK,CAACG,CAAC,CAAC,CAAC;MACrB,IAAG,EAAER,KAAK,IAAI,CAAC,EAAE;IAClB;IAEAO,MAAM,GAAGF,KAAK,CAACG,CAAC,CAAC,CAACI,QAAQ;IAC1B,IAAGb,OAAO,IAAIQ,MAAM,IAAIA,MAAM,CAACG,MAAM,GAAG,CAAC,EAAC;MACzCH,MAAM,GAAGf,IAAI,CAACK,IAAI,EAAEU,MAAM,EAAER,OAAO,EAAEC,KAAK,CAAC;MAC3CM,MAAM,GAAGA,MAAM,CAACO,MAAM,CAACN,MAAM,CAAC;MAC9BP,KAAK,IAAIO,MAAM,CAACG,MAAM;MACtB,IAAGV,KAAK,IAAI,CAAC,EAAE;IAChB;EACD;EAEA,OAAOM,MAAM;AACd;AAEA,SAASb,YAAYA,CAACI,IAAI,EAAEQ,KAAK,EAAC;EACjC,KAAI,IAAIG,CAAC,GAAG,CAAC,EAAEM,CAAC,GAAGT,KAAK,CAACK,MAAM,EAAEF,CAAC,GAAGM,CAAC,EAAEN,CAAC,EAAE,EAAC;IAC3C,IAAGX,IAAI,CAACQ,KAAK,CAACG,CAAC,CAAC,CAAC,EAAE,OAAOH,KAAK,CAACG,CAAC,CAAC;EACnC;EAEA,OAAO,IAAI;AACZ;AAEA,SAASd,OAAOA,CAACG,IAAI,EAAEQ,KAAK,EAAC;EAC5B,IAAIU,IAAI,GAAG,IAAI;EAEf,KAAI,IAAIP,CAAC,GAAG,CAAC,EAAEM,CAAC,GAAGT,KAAK,CAACK,MAAM,EAAEF,CAAC,GAAGM,CAAC,IAAI,CAACC,IAAI,EAAEP,CAAC,EAAE,EAAC;IACpD,IAAG,CAACrB,KAAK,CAACkB,KAAK,CAACG,CAAC,CAAC,CAAC,EAAC;MACnB;IACD,CAAC,MAAM,IAAGX,IAAI,CAACQ,KAAK,CAACG,CAAC,CAAC,CAAC,EAAC;MACxBO,IAAI,GAAGV,KAAK,CAACG,CAAC,CAAC;IAChB,CAAC,MAAM,IAAGH,KAAK,CAACG,CAAC,CAAC,CAACI,QAAQ,CAACF,MAAM,GAAG,CAAC,EAAC;MACtCK,IAAI,GAAGrB,OAAO,CAACG,IAAI,EAAEQ,KAAK,CAACG,CAAC,CAAC,CAACI,QAAQ,CAAC;IACxC;EACD;EAEA,OAAOG,IAAI;AACZ;AAEA,SAASpB,SAASA,CAACE,IAAI,EAAEQ,KAAK,EAAC;EAC9B,KAAI,IAAIG,CAAC,GAAG,CAAC,EAAEM,CAAC,GAAGT,KAAK,CAACK,MAAM,EAAEF,CAAC,GAAGM,CAAC,EAAEN,CAAC,EAAE,EAAC;IAC3C,IACCrB,KAAK,CAACkB,KAAK,CAACG,CAAC,CAAC,CAAC,KACdX,IAAI,CAACQ,KAAK,CAACG,CAAC,CAAC,CAAC,IACbH,KAAK,CAACG,CAAC,CAAC,CAACI,QAAQ,CAACF,MAAM,GAAG,CAAC,IAC5Bf,SAAS,CAACE,IAAI,EAAEQ,KAAK,CAACG,CAAC,CAAC,CAACI,QAAQ,CACjC,CACD,EACD;MACA,OAAO,IAAI;IACZ;EACD;EAEA,OAAO,KAAK;AACb;AAEA,SAAShB,OAAOA,CAACC,IAAI,EAAEmB,SAAS,EAAC;EAChC,IAAIV,MAAM,GAAG,EAAE;EACf,IAAIW,KAAK,GAAGD,SAAS,CAACE,KAAK,CAAC,CAAC;EAC7B,OAAMD,KAAK,CAACP,MAAM,EAAC;IAClB,IAAIK,IAAI,GAAGE,KAAK,CAACE,KAAK,CAAC,CAAC;IACxB,IAAG,CAAChC,KAAK,CAAC4B,IAAI,CAAC,EAAE;IACjB,IAAIA,IAAI,CAACH,QAAQ,IAAIG,IAAI,CAACH,QAAQ,CAACF,MAAM,GAAG,CAAC,EAAE;MAC9CO,KAAK,CAACG,OAAO,CAACC,KAAK,CAACJ,KAAK,EAAEF,IAAI,CAACH,QAAQ,CAAC;IAC1C;IACA,IAAGf,IAAI,CAACkB,IAAI,CAAC,EAAET,MAAM,CAACK,IAAI,CAACI,IAAI,CAAC;EACjC;EACA,OAAOT,MAAM;AACd"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}