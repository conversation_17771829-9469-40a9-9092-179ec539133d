{"ast": null, "code": "function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    var ownKeys = Object.keys(source);\n    if (typeof Object.getOwnPropertySymbols === 'function') {\n      ownKeys = ownKeys.concat(Object.getOwnPropertySymbols(source).filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(source, sym).enumerable;\n      }));\n    }\n    ownKeys.forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    });\n  }\n  return target;\n}\nimport { BEGIN_DRAG, DROP, END_DRAG, HOVER, PUBLISH_DRAG_SOURCE } from '../actions/dragDrop/index.js';\nimport { REMOVE_TARGET } from '../actions/registry.js';\nimport { without } from '../utils/js_utils.js';\nconst initialState = {\n  itemType: null,\n  item: null,\n  sourceId: null,\n  targetIds: [],\n  dropResult: null,\n  didDrop: false,\n  isSourcePublic: null\n};\nexport function reduce(state = initialState, action) {\n  const {\n    payload\n  } = action;\n  switch (action.type) {\n    case BEGIN_DRAG:\n      return _objectSpread({}, state, {\n        itemType: payload.itemType,\n        item: payload.item,\n        sourceId: payload.sourceId,\n        isSourcePublic: payload.isSourcePublic,\n        dropResult: null,\n        didDrop: false\n      });\n    case PUBLISH_DRAG_SOURCE:\n      return _objectSpread({}, state, {\n        isSourcePublic: true\n      });\n    case HOVER:\n      return _objectSpread({}, state, {\n        targetIds: payload.targetIds\n      });\n    case REMOVE_TARGET:\n      if (state.targetIds.indexOf(payload.targetId) === -1) {\n        return state;\n      }\n      return _objectSpread({}, state, {\n        targetIds: without(state.targetIds, payload.targetId)\n      });\n    case DROP:\n      return _objectSpread({}, state, {\n        dropResult: payload.dropResult,\n        didDrop: true,\n        targetIds: []\n      });\n    case END_DRAG:\n      return _objectSpread({}, state, {\n        itemType: null,\n        item: null,\n        sourceId: null,\n        dropResult: null,\n        didDrop: false,\n        isSourcePublic: null,\n        targetIds: []\n      });\n    default:\n      return state;\n  }\n}", "map": {"version": 3, "names": ["_defineProperty", "obj", "key", "value", "BEGIN_DRAG", "DROP", "END_DRAG", "HOVER", "PUBLISH_DRAG_SOURCE", "REMOVE_TARGET", "without", "initialState", "itemType", "item", "sourceId", "targetIds", "dropResult", "didDrop", "isSourcePublic", "reduce", "state", "action", "payload", "type", "_objectSpread", "indexOf", "targetId"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\dnd-core\\src\\reducers\\dragOperation.ts"], "sourcesContent": ["import {\n\tBEGIN_DRAG,\n\tDROP,\n\t<PERSON>ND_DRAG,\n\tHOVER,\n\tPUBLISH_DRAG_SOURCE,\n} from '../actions/dragDrop/index.js'\nimport { REMOVE_TARGET } from '../actions/registry.js'\nimport type { Action, Identifier } from '../interfaces.js'\nimport { without } from '../utils/js_utils.js'\n\nexport interface State {\n\titemType: Identifier | Identifier[] | null\n\titem: any\n\tsourceId: string | null\n\ttargetIds: string[]\n\tdropResult: any\n\tdidDrop: boolean\n\tisSourcePublic: boolean | null\n}\n\nconst initialState: State = {\n\titemType: null,\n\titem: null,\n\tsourceId: null,\n\ttargetIds: [],\n\tdropResult: null,\n\tdidDrop: false,\n\tisSourcePublic: null,\n}\n\nexport function reduce(\n\tstate: State = initialState,\n\taction: Action<{\n\t\titemType: Identifier | Identifier[]\n\t\titem: any\n\t\tsourceId: string\n\t\ttargetId: string\n\t\ttargetIds: string[]\n\t\tisSourcePublic: boolean\n\t\tdropResult: any\n\t}>,\n): State {\n\tconst { payload } = action\n\tswitch (action.type) {\n\t\tcase BEGIN_DRAG:\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\titemType: payload.itemType,\n\t\t\t\titem: payload.item,\n\t\t\t\tsourceId: payload.sourceId,\n\t\t\t\tisSourcePublic: payload.isSourcePublic,\n\t\t\t\tdropResult: null,\n\t\t\t\tdidDrop: false,\n\t\t\t}\n\t\tcase PUBLISH_DRAG_SOURCE:\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tisSourcePublic: true,\n\t\t\t}\n\t\tcase HOVER:\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\ttargetIds: payload.targetIds,\n\t\t\t}\n\t\tcase REMOVE_TARGET:\n\t\t\tif (state.targetIds.indexOf(payload.targetId) === -1) {\n\t\t\t\treturn state\n\t\t\t}\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\ttargetIds: without(state.targetIds, payload.targetId),\n\t\t\t}\n\t\tcase DROP:\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\tdropResult: payload.dropResult,\n\t\t\t\tdidDrop: true,\n\t\t\t\ttargetIds: [],\n\t\t\t}\n\t\tcase END_DRAG:\n\t\t\treturn {\n\t\t\t\t...state,\n\t\t\t\titemType: null,\n\t\t\t\titem: null,\n\t\t\t\tsourceId: null,\n\t\t\t\tdropResult: null,\n\t\t\t\tdidDrop: false,\n\t\t\t\tisSourcePublic: null,\n\t\t\t\ttargetIds: [],\n\t\t\t}\n\t\tdefault:\n\t\t\treturn state\n\t}\n}\n"], "mappings": "AAAA,SAAAA,gBAAAC,GAAA,EAAAC,GAAA,EAAAC,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SACCC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,mBAAmB,QACb,8BAA8B;AACrC,SAASC,aAAa,QAAQ,wBAAwB;AAEtD,SAASC,OAAO,QAAQ,sBAAsB;AAY9C,MAAMC,YAAY,GAAU;EAC3BC,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,EAAE;EACbC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,KAAK;EACdC,cAAc,EAAE;CAChB;AAED,OAAO,SAASC,MAAMA,CACrBC,KAAY,GAAGT,YAAY,EAC3BU,MAQE,EACM;EACR,MAAM;IAAEC;EAAO,CAAE,GAAGD,MAAM;EAC1B,QAAQA,MAAM,CAACE,IAAI;IAClB,KAAKnB,UAAU;MACd,OAAOoB,aAAA,KACHJ,KAAK;QACRR,QAAQ,EAAEU,OAAO,CAACV,QAAQ;QAC1BC,IAAI,EAAES,OAAO,CAACT,IAAI;QAClBC,QAAQ,EAAEQ,OAAO,CAACR,QAAQ;QAC1BI,cAAc,EAAEI,OAAO,CAACJ,cAAc;QACtCF,UAAU,EAAE,IAAI;QAChBC,OAAO,EAAE;QACT;IACF,KAAKT,mBAAmB;MACvB,OAAOgB,aAAA,KACHJ,KAAK;QACRF,cAAc,EAAE;QAChB;IACF,KAAKX,KAAK;MACT,OAAOiB,aAAA,KACHJ,KAAK;QACRL,SAAS,EAAEO,OAAO,CAACP;QACnB;IACF,KAAKN,aAAa;MACjB,IAAIW,KAAK,CAACL,SAAS,CAACU,OAAO,CAACH,OAAO,CAACI,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;QACrD,OAAON,KAAK;;MAEb,OAAOI,aAAA,KACHJ,KAAK;QACRL,SAAS,EAAEL,OAAO,CAACU,KAAK,CAACL,SAAS,EAAEO,OAAO,CAACI,QAAQ;QACpD;IACF,KAAKrB,IAAI;MACR,OAAOmB,aAAA,KACHJ,KAAK;QACRJ,UAAU,EAAEM,OAAO,CAACN,UAAU;QAC9BC,OAAO,EAAE,IAAI;QACbF,SAAS,EAAE;QACX;IACF,KAAKT,QAAQ;MACZ,OAAOkB,aAAA,KACHJ,KAAK;QACRR,QAAQ,EAAE,IAAI;QACdC,IAAI,EAAE,IAAI;QACVC,QAAQ,EAAE,IAAI;QACdE,UAAU,EAAE,IAAI;QAChBC,OAAO,EAAE,KAAK;QACdC,cAAc,EAAE,IAAI;QACpBH,SAAS,EAAE;QACX;IACF;MACC,OAAOK,KAAK"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}