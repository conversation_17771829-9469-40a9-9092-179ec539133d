{"ast": null, "code": "// This object will be used as the prototype for Nodes when creating a\n// DOM-Level-1-compliant structure.\nvar NodePrototype = module.exports = {\n  get firstChild() {\n    var children = this.children;\n    return children && children[0] || null;\n  },\n  get lastChild() {\n    var children = this.children;\n    return children && children[children.length - 1] || null;\n  },\n  get nodeType() {\n    return nodeTypes[this.type] || nodeTypes.element;\n  }\n};\nvar domLvl1 = {\n  tagName: \"name\",\n  childNodes: \"children\",\n  parentNode: \"parent\",\n  previousSibling: \"prev\",\n  nextSibling: \"next\",\n  nodeValue: \"data\"\n};\nvar nodeTypes = {\n  element: 1,\n  text: 3,\n  cdata: 4,\n  comment: 8\n};\nObject.keys(domLvl1).forEach(function (key) {\n  var shorthand = domLvl1[key];\n  Object.defineProperty(NodePrototype, key, {\n    get: function () {\n      return this[shorthand] || null;\n    },\n    set: function (val) {\n      this[shorthand] = val;\n      return val;\n    }\n  });\n});", "map": {"version": 3, "names": ["NodePrototype", "module", "exports", "<PERSON><PERSON><PERSON><PERSON>", "children", "<PERSON><PERSON><PERSON><PERSON>", "length", "nodeType", "nodeTypes", "type", "element", "domLvl1", "tagName", "childNodes", "parentNode", "previousSibling", "nextS<PERSON>ling", "nodeValue", "text", "cdata", "comment", "Object", "keys", "for<PERSON>ach", "key", "shorthand", "defineProperty", "get", "set", "val"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/htmlparser2/node_modules/domhandler/lib/node.js"], "sourcesContent": ["// This object will be used as the prototype for Nodes when creating a\n// DOM-Level-1-compliant structure.\nvar NodePrototype = module.exports = {\n\tget firstChild() {\n\t\tvar children = this.children;\n\t\treturn children && children[0] || null;\n\t},\n\tget lastChild() {\n\t\tvar children = this.children;\n\t\treturn children && children[children.length - 1] || null;\n\t},\n\tget nodeType() {\n\t\treturn nodeTypes[this.type] || nodeTypes.element;\n\t}\n};\n\nvar domLvl1 = {\n\ttagName: \"name\",\n\tchildNodes: \"children\",\n\tparentNode: \"parent\",\n\tpreviousSibling: \"prev\",\n\tnextSibling: \"next\",\n\tnodeValue: \"data\"\n};\n\nvar nodeTypes = {\n\telement: 1,\n\ttext: 3,\n\tcdata: 4,\n\tcomment: 8\n};\n\nObject.keys(domLvl1).forEach(function(key) {\n\tvar shorthand = domLvl1[key];\n\tObject.defineProperty(NodePrototype, key, {\n\t\tget: function() {\n\t\t\treturn this[shorthand] || null;\n\t\t},\n\t\tset: function(val) {\n\t\t\tthis[shorthand] = val;\n\t\t\treturn val;\n\t\t}\n\t});\n});\n"], "mappings": "AAAA;AACA;AACA,IAAIA,aAAa,GAAGC,MAAM,CAACC,OAAO,GAAG;EACpC,IAAIC,UAAUA,CAAA,EAAG;IAChB,IAAIC,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5B,OAAOA,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI;EACvC,CAAC;EACD,IAAIC,SAASA,CAAA,EAAG;IACf,IAAID,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5B,OAAOA,QAAQ,IAAIA,QAAQ,CAACA,QAAQ,CAACE,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI;EACzD,CAAC;EACD,IAAIC,QAAQA,CAAA,EAAG;IACd,OAAOC,SAAS,CAAC,IAAI,CAACC,IAAI,CAAC,IAAID,SAAS,CAACE,OAAO;EACjD;AACD,CAAC;AAED,IAAIC,OAAO,GAAG;EACbC,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,UAAU;EACtBC,UAAU,EAAE,QAAQ;EACpBC,eAAe,EAAE,MAAM;EACvBC,WAAW,EAAE,MAAM;EACnBC,SAAS,EAAE;AACZ,CAAC;AAED,IAAIT,SAAS,GAAG;EACfE,OAAO,EAAE,CAAC;EACVQ,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE;AACV,CAAC;AAEDC,MAAM,CAACC,IAAI,CAACX,OAAO,CAAC,CAACY,OAAO,CAAC,UAASC,GAAG,EAAE;EAC1C,IAAIC,SAAS,GAAGd,OAAO,CAACa,GAAG,CAAC;EAC5BH,MAAM,CAACK,cAAc,CAAC1B,aAAa,EAAEwB,GAAG,EAAE;IACzCG,GAAG,EAAE,SAAAA,CAAA,EAAW;MACf,OAAO,IAAI,CAACF,SAAS,CAAC,IAAI,IAAI;IAC/B,CAAC;IACDG,GAAG,EAAE,SAAAA,CAASC,GAAG,EAAE;MAClB,IAAI,CAACJ,SAAS,CAAC,GAAGI,GAAG;MACrB,OAAOA,GAAG;IACX;EACD,CAAC,CAAC;AACH,CAAC,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}