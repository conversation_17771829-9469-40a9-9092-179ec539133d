{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"actions\", \"anchorEl\", \"children\", \"component\", \"components\", \"componentsProps\", \"keepMounted\", \"listboxId\", \"onClose\", \"open\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { HTMLElementType, refType } from '@mui/utils';\nimport MenuUnstyledContext from './MenuUnstyledContext';\nimport { getMenuUnstyledUtilityClass } from './menuUnstyledClasses';\nimport useMenu from './useMenu';\nimport composeClasses from '../composeClasses';\nimport PopperUnstyled from '../PopperUnstyled';\nimport useSlotProps from '../utils/useSlotProps';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction getUtilityClasses(ownerState) {\n  const {\n    open\n  } = ownerState;\n  const slots = {\n    root: ['root', open && 'expanded'],\n    listbox: ['listbox', open && 'expanded']\n  };\n  return composeClasses(slots, getMenuUnstyledUtilityClass, {});\n}\n/**\n *\n * Demos:\n *\n * - [Unstyled menu](https://mui.com/base/react-menu/)\n *\n * API:\n *\n * - [MenuUnstyled API](https://mui.com/base/api/menu-unstyled/)\n */\n\nconst MenuUnstyled = /*#__PURE__*/React.forwardRef(function MenuUnstyled(props, forwardedRef) {\n  var _ref, _components$Listbox;\n  const {\n      actions,\n      anchorEl,\n      children,\n      component,\n      components = {},\n      componentsProps = {},\n      keepMounted = false,\n      listboxId,\n      onClose,\n      open = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    registerItem,\n    unregisterItem,\n    getListboxProps,\n    getItemProps,\n    getItemState,\n    highlightFirstItem,\n    highlightLastItem\n  } = useMenu({\n    open,\n    onClose,\n    listboxId\n  });\n  React.useImperativeHandle(actions, () => ({\n    highlightFirstItem,\n    highlightLastItem\n  }), [highlightFirstItem, highlightLastItem]);\n  const ownerState = _extends({}, props, {\n    open\n  });\n  const classes = getUtilityClasses(ownerState);\n  const Root = (_ref = component != null ? component : components.Root) != null ? _ref : PopperUnstyled;\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalForwardedProps: other,\n    externalSlotProps: componentsProps.root,\n    additionalProps: {\n      anchorEl,\n      open,\n      keepMounted,\n      role: undefined,\n      ref: forwardedRef\n    },\n    className: classes.root,\n    ownerState\n  });\n  const Listbox = (_components$Listbox = components.Listbox) != null ? _components$Listbox : 'ul';\n  const listboxProps = useSlotProps({\n    elementType: Listbox,\n    getSlotProps: getListboxProps,\n    externalSlotProps: componentsProps.listbox,\n    ownerState,\n    className: classes.listbox\n  });\n  const contextValue = {\n    registerItem,\n    unregisterItem,\n    getItemState,\n    getItemProps,\n    open\n  };\n  return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n    children: /*#__PURE__*/_jsx(Listbox, _extends({}, listboxProps, {\n      children: /*#__PURE__*/_jsx(MenuUnstyledContext.Provider, {\n        value: contextValue,\n        children: children\n      })\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? MenuUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * A ref with imperative actions.\n   * It allows to select the first or last menu item.\n   */\n  actions: refType,\n  /**\n   * An HTML element, [virtualElement](https://popper.js.org/docs/v2/virtual-elements/),\n   * or a function that returns either.\n   * It's used to set the position of the popper.\n   */\n  anchorEl: PropTypes\n  /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.object, PropTypes.func]),\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the Menu.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Listbox: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The props used for each slot inside the Menu.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    listbox: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * Always keep the menu in the DOM.\n   * This prop can be useful in SEO situation or when you want to maximize the responsiveness of the Menu.\n   *\n   * @default false\n   */\n  keepMounted: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  listboxId: PropTypes.string,\n  /**\n   * Triggered when focus leaves the menu and the menu should close.\n   */\n  onClose: PropTypes.func,\n  /**\n   * Controls whether the menu is displayed.\n   * @default false\n   */\n  open: PropTypes.bool\n} : void 0;\nexport default MenuUnstyled;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "HTMLElementType", "refType", "MenuUnstyledContext", "getMenuUnstyledUtilityClass", "useMenu", "composeClasses", "PopperUnstyled", "useSlotProps", "jsx", "_jsx", "getUtilityClasses", "ownerState", "open", "slots", "root", "listbox", "MenuUnstyled", "forwardRef", "props", "forwardedRef", "_ref", "_components$Listbox", "actions", "anchorEl", "children", "component", "components", "componentsProps", "keepMounted", "listboxId", "onClose", "other", "registerItem", "unregisterItem", "getListboxProps", "getItemProps", "getItemState", "highlightFirstItem", "highlightLastItem", "useImperativeHandle", "classes", "Root", "rootProps", "elementType", "externalForwardedProps", "externalSlotProps", "additionalProps", "role", "undefined", "ref", "className", "Listbox", "listboxProps", "getSlotProps", "contextValue", "Provider", "value", "process", "env", "NODE_ENV", "propTypes", "oneOfType", "object", "func", "node", "shape", "bool", "string"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/MenuUnstyled/MenuUnstyled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"actions\", \"anchorEl\", \"children\", \"component\", \"components\", \"componentsProps\", \"keepMounted\", \"listboxId\", \"onClose\", \"open\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { HTMLElementType, refType } from '@mui/utils';\nimport MenuUnstyledContext from './MenuUnstyledContext';\nimport { getMenuUnstyledUtilityClass } from './menuUnstyledClasses';\nimport useMenu from './useMenu';\nimport composeClasses from '../composeClasses';\nimport PopperUnstyled from '../PopperUnstyled';\nimport useSlotProps from '../utils/useSlotProps';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n\nfunction getUtilityClasses(ownerState) {\n  const {\n    open\n  } = ownerState;\n  const slots = {\n    root: ['root', open && 'expanded'],\n    listbox: ['listbox', open && 'expanded']\n  };\n  return composeClasses(slots, getMenuUnstyledUtilityClass, {});\n}\n/**\n *\n * Demos:\n *\n * - [Unstyled menu](https://mui.com/base/react-menu/)\n *\n * API:\n *\n * - [MenuUnstyled API](https://mui.com/base/api/menu-unstyled/)\n */\n\n\nconst MenuUnstyled = /*#__PURE__*/React.forwardRef(function MenuUnstyled(props, forwardedRef) {\n  var _ref, _components$Listbox;\n\n  const {\n    actions,\n    anchorEl,\n    children,\n    component,\n    components = {},\n    componentsProps = {},\n    keepMounted = false,\n    listboxId,\n    onClose,\n    open = false\n  } = props,\n        other = _objectWithoutPropertiesLoose(props, _excluded);\n\n  const {\n    registerItem,\n    unregisterItem,\n    getListboxProps,\n    getItemProps,\n    getItemState,\n    highlightFirstItem,\n    highlightLastItem\n  } = useMenu({\n    open,\n    onClose,\n    listboxId\n  });\n  React.useImperativeHandle(actions, () => ({\n    highlightFirstItem,\n    highlightLastItem\n  }), [highlightFirstItem, highlightLastItem]);\n\n  const ownerState = _extends({}, props, {\n    open\n  });\n\n  const classes = getUtilityClasses(ownerState);\n  const Root = (_ref = component != null ? component : components.Root) != null ? _ref : PopperUnstyled;\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalForwardedProps: other,\n    externalSlotProps: componentsProps.root,\n    additionalProps: {\n      anchorEl,\n      open,\n      keepMounted,\n      role: undefined,\n      ref: forwardedRef\n    },\n    className: classes.root,\n    ownerState\n  });\n  const Listbox = (_components$Listbox = components.Listbox) != null ? _components$Listbox : 'ul';\n  const listboxProps = useSlotProps({\n    elementType: Listbox,\n    getSlotProps: getListboxProps,\n    externalSlotProps: componentsProps.listbox,\n    ownerState,\n    className: classes.listbox\n  });\n  const contextValue = {\n    registerItem,\n    unregisterItem,\n    getItemState,\n    getItemProps,\n    open\n  };\n  return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n    children: /*#__PURE__*/_jsx(Listbox, _extends({}, listboxProps, {\n      children: /*#__PURE__*/_jsx(MenuUnstyledContext.Provider, {\n        value: contextValue,\n        children: children\n      })\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? MenuUnstyled.propTypes\n/* remove-proptypes */\n= {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * A ref with imperative actions.\n   * It allows to select the first or last menu item.\n   */\n  actions: refType,\n\n  /**\n   * An HTML element, [virtualElement](https://popper.js.org/docs/v2/virtual-elements/),\n   * or a function that returns either.\n   * It's used to set the position of the popper.\n   */\n  anchorEl: PropTypes\n  /* @typescript-to-proptypes-ignore */\n  .oneOfType([HTMLElementType, PropTypes.object, PropTypes.func]),\n\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n\n  /**\n   * The components used for each slot inside the Menu.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Listbox: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n\n  /**\n   * The props used for each slot inside the Menu.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    listbox: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n\n  /**\n   * Always keep the menu in the DOM.\n   * This prop can be useful in SEO situation or when you want to maximize the responsiveness of the Menu.\n   *\n   * @default false\n   */\n  keepMounted: PropTypes.bool,\n\n  /**\n   * @ignore\n   */\n  listboxId: PropTypes.string,\n\n  /**\n   * Triggered when focus leaves the menu and the menu should close.\n   */\n  onClose: PropTypes.func,\n\n  /**\n   * Controls whether the menu is displayed.\n   * @default false\n   */\n  open: PropTypes.bool\n} : void 0;\nexport default MenuUnstyled;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,aAAa,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,CAAC;AAClJ,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,eAAe,EAAEC,OAAO,QAAQ,YAAY;AACrD,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,2BAA2B,QAAQ,uBAAuB;AACnE,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,YAAY,MAAM,uBAAuB;AAChD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAE/C,SAASC,iBAAiBA,CAACC,UAAU,EAAE;EACrC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,IAAI,IAAI,UAAU,CAAC;IAClCG,OAAO,EAAE,CAAC,SAAS,EAAEH,IAAI,IAAI,UAAU;EACzC,CAAC;EACD,OAAOP,cAAc,CAACQ,KAAK,EAAEV,2BAA2B,EAAE,CAAC,CAAC,CAAC;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,MAAMa,YAAY,GAAG,aAAalB,KAAK,CAACmB,UAAU,CAAC,SAASD,YAAYA,CAACE,KAAK,EAAEC,YAAY,EAAE;EAC5F,IAAIC,IAAI,EAAEC,mBAAmB;EAE7B,MAAM;MACJC,OAAO;MACPC,QAAQ;MACRC,QAAQ;MACRC,SAAS;MACTC,UAAU,GAAG,CAAC,CAAC;MACfC,eAAe,GAAG,CAAC,CAAC;MACpBC,WAAW,GAAG,KAAK;MACnBC,SAAS;MACTC,OAAO;MACPlB,IAAI,GAAG;IACT,CAAC,GAAGM,KAAK;IACHa,KAAK,GAAGnC,6BAA6B,CAACsB,KAAK,EAAErB,SAAS,CAAC;EAE7D,MAAM;IACJmC,YAAY;IACZC,cAAc;IACdC,eAAe;IACfC,YAAY;IACZC,YAAY;IACZC,kBAAkB;IAClBC;EACF,CAAC,GAAGlC,OAAO,CAAC;IACVQ,IAAI;IACJkB,OAAO;IACPD;EACF,CAAC,CAAC;EACF/B,KAAK,CAACyC,mBAAmB,CAACjB,OAAO,EAAE,OAAO;IACxCe,kBAAkB;IAClBC;EACF,CAAC,CAAC,EAAE,CAACD,kBAAkB,EAAEC,iBAAiB,CAAC,CAAC;EAE5C,MAAM3B,UAAU,GAAGhB,QAAQ,CAAC,CAAC,CAAC,EAAEuB,KAAK,EAAE;IACrCN;EACF,CAAC,CAAC;EAEF,MAAM4B,OAAO,GAAG9B,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM8B,IAAI,GAAG,CAACrB,IAAI,GAAGK,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAGC,UAAU,CAACe,IAAI,KAAK,IAAI,GAAGrB,IAAI,GAAGd,cAAc;EACrG,MAAMoC,SAAS,GAAGnC,YAAY,CAAC;IAC7BoC,WAAW,EAAEF,IAAI;IACjBG,sBAAsB,EAAEb,KAAK;IAC7Bc,iBAAiB,EAAElB,eAAe,CAACb,IAAI;IACvCgC,eAAe,EAAE;MACfvB,QAAQ;MACRX,IAAI;MACJgB,WAAW;MACXmB,IAAI,EAAEC,SAAS;MACfC,GAAG,EAAE9B;IACP,CAAC;IACD+B,SAAS,EAAEV,OAAO,CAAC1B,IAAI;IACvBH;EACF,CAAC,CAAC;EACF,MAAMwC,OAAO,GAAG,CAAC9B,mBAAmB,GAAGK,UAAU,CAACyB,OAAO,KAAK,IAAI,GAAG9B,mBAAmB,GAAG,IAAI;EAC/F,MAAM+B,YAAY,GAAG7C,YAAY,CAAC;IAChCoC,WAAW,EAAEQ,OAAO;IACpBE,YAAY,EAAEnB,eAAe;IAC7BW,iBAAiB,EAAElB,eAAe,CAACZ,OAAO;IAC1CJ,UAAU;IACVuC,SAAS,EAAEV,OAAO,CAACzB;EACrB,CAAC,CAAC;EACF,MAAMuC,YAAY,GAAG;IACnBtB,YAAY;IACZC,cAAc;IACdG,YAAY;IACZD,YAAY;IACZvB;EACF,CAAC;EACD,OAAO,aAAaH,IAAI,CAACgC,IAAI,EAAE9C,QAAQ,CAAC,CAAC,CAAC,EAAE+C,SAAS,EAAE;IACrDlB,QAAQ,EAAE,aAAaf,IAAI,CAAC0C,OAAO,EAAExD,QAAQ,CAAC,CAAC,CAAC,EAAEyD,YAAY,EAAE;MAC9D5B,QAAQ,EAAE,aAAaf,IAAI,CAACP,mBAAmB,CAACqD,QAAQ,EAAE;QACxDC,KAAK,EAAEF,YAAY;QACnB9B,QAAQ,EAAEA;MACZ,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFiC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG3C,YAAY,CAAC4C;AACrD,yBACE;EACA;EACA;EACA;EACA;;EAEA;AACF;AACA;AACA;EACEtC,OAAO,EAAErB,OAAO;EAEhB;AACF;AACA;AACA;AACA;EACEsB,QAAQ,EAAExB;EACV,sCACC8D,SAAS,CAAC,CAAC7D,eAAe,EAAED,SAAS,CAAC+D,MAAM,EAAE/D,SAAS,CAACgE,IAAI,CAAC,CAAC;EAE/D;AACF;AACA;EACEvC,QAAQ,EAAEzB,SAAS,CAACiE,IAAI;EAExB;AACF;AACA;AACA;EACEvC,SAAS,EAAE1B,SAAS,CAAC4C,WAAW;EAEhC;AACF;AACA;AACA;AACA;EACEjB,UAAU,EAAE3B,SAAS,CAACkE,KAAK,CAAC;IAC1Bd,OAAO,EAAEpD,SAAS,CAAC4C,WAAW;IAC9BF,IAAI,EAAE1C,SAAS,CAAC4C;EAClB,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACEhB,eAAe,EAAE5B,SAAS,CAACkE,KAAK,CAAC;IAC/BlD,OAAO,EAAEhB,SAAS,CAAC8D,SAAS,CAAC,CAAC9D,SAAS,CAACgE,IAAI,EAAEhE,SAAS,CAAC+D,MAAM,CAAC,CAAC;IAChEhD,IAAI,EAAEf,SAAS,CAAC8D,SAAS,CAAC,CAAC9D,SAAS,CAACgE,IAAI,EAAEhE,SAAS,CAAC+D,MAAM,CAAC;EAC9D,CAAC,CAAC;EAEF;AACF;AACA;AACA;AACA;AACA;EACElC,WAAW,EAAE7B,SAAS,CAACmE,IAAI;EAE3B;AACF;AACA;EACErC,SAAS,EAAE9B,SAAS,CAACoE,MAAM;EAE3B;AACF;AACA;EACErC,OAAO,EAAE/B,SAAS,CAACgE,IAAI;EAEvB;AACF;AACA;AACA;EACEnD,IAAI,EAAEb,SAAS,CAACmE;AAClB,CAAC,GAAG,KAAK,CAAC;AACV,eAAelD,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}