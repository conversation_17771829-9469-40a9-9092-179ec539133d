{"ast": null, "code": "export function reduce(state = 0) {\n  return state + 1;\n}", "map": {"version": 3, "names": ["reduce", "state"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\dnd-core\\src\\reducers\\stateId.ts"], "sourcesContent": ["export type State = number\n\nexport function reduce(state: State = 0): State {\n\treturn state + 1\n}\n"], "mappings": "AAEA,OAAO,SAASA,MAAMA,CAACC,KAAY,GAAG,CAAC,EAAS;EAC/C,OAAOA,KAAK,GAAG,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}