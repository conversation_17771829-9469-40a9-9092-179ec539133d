{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Doctype = exports.CDATA = exports.Tag = exports.Style = exports.Script = exports.Comment = exports.Directive = exports.Text = exports.Root = exports.isTag = exports.ElementType = void 0;\n/** Types of elements found in htmlparser2's DOM */\nvar ElementType;\n(function (ElementType) {\n  /** Type for the root element of a document */\n  ElementType[\"Root\"] = \"root\";\n  /** Type for Text */\n  ElementType[\"Text\"] = \"text\";\n  /** Type for <? ... ?> */\n  ElementType[\"Directive\"] = \"directive\";\n  /** Type for <!-- ... --> */\n  ElementType[\"Comment\"] = \"comment\";\n  /** Type for <script> tags */\n  ElementType[\"Script\"] = \"script\";\n  /** Type for <style> tags */\n  ElementType[\"Style\"] = \"style\";\n  /** Type for Any tag */\n  ElementType[\"Tag\"] = \"tag\";\n  /** Type for <![CDATA[ ... ]]> */\n  ElementType[\"CDATA\"] = \"cdata\";\n  /** Type for <!doctype ...> */\n  ElementType[\"Doctype\"] = \"doctype\";\n})(ElementType = exports.ElementType || (exports.ElementType = {}));\n/**\n * Tests whether an element is a tag or not.\n *\n * @param elem Element to test\n */\nfunction isTag(elem) {\n  return elem.type === ElementType.Tag || elem.type === ElementType.Script || elem.type === ElementType.Style;\n}\nexports.isTag = isTag;\n// Exports for backwards compatibility\n/** Type for the root element of a document */\nexports.Root = ElementType.Root;\n/** Type for Text */\nexports.Text = ElementType.Text;\n/** Type for <? ... ?> */\nexports.Directive = ElementType.Directive;\n/** Type for <!-- ... --> */\nexports.Comment = ElementType.Comment;\n/** Type for <script> tags */\nexports.Script = ElementType.Script;\n/** Type for <style> tags */\nexports.Style = ElementType.Style;\n/** Type for Any tag */\nexports.Tag = ElementType.Tag;\n/** Type for <![CDATA[ ... ]]> */\nexports.CDATA = ElementType.CDATA;\n/** Type for <!doctype ...> */\nexports.Doctype = ElementType.Doctype;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "Doctype", "CDATA", "Tag", "Style", "<PERSON><PERSON><PERSON>", "Comment", "Directive", "Text", "Root", "isTag", "ElementType", "elem", "type"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/domelementtype/lib/index.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Doctype = exports.CDATA = exports.Tag = exports.Style = exports.Script = exports.Comment = exports.Directive = exports.Text = exports.Root = exports.isTag = exports.ElementType = void 0;\n/** Types of elements found in htmlparser2's DOM */\nvar ElementType;\n(function (ElementType) {\n    /** Type for the root element of a document */\n    ElementType[\"Root\"] = \"root\";\n    /** Type for Text */\n    ElementType[\"Text\"] = \"text\";\n    /** Type for <? ... ?> */\n    ElementType[\"Directive\"] = \"directive\";\n    /** Type for <!-- ... --> */\n    ElementType[\"Comment\"] = \"comment\";\n    /** Type for <script> tags */\n    ElementType[\"Script\"] = \"script\";\n    /** Type for <style> tags */\n    ElementType[\"Style\"] = \"style\";\n    /** Type for Any tag */\n    ElementType[\"Tag\"] = \"tag\";\n    /** Type for <![CDATA[ ... ]]> */\n    ElementType[\"CDATA\"] = \"cdata\";\n    /** Type for <!doctype ...> */\n    ElementType[\"Doctype\"] = \"doctype\";\n})(ElementType = exports.ElementType || (exports.ElementType = {}));\n/**\n * Tests whether an element is a tag or not.\n *\n * @param elem Element to test\n */\nfunction isTag(elem) {\n    return (elem.type === ElementType.Tag ||\n        elem.type === ElementType.Script ||\n        elem.type === ElementType.Style);\n}\nexports.isTag = isTag;\n// Exports for backwards compatibility\n/** Type for the root element of a document */\nexports.Root = ElementType.Root;\n/** Type for Text */\nexports.Text = ElementType.Text;\n/** Type for <? ... ?> */\nexports.Directive = ElementType.Directive;\n/** Type for <!-- ... --> */\nexports.Comment = ElementType.Comment;\n/** Type for <script> tags */\nexports.Script = ElementType.Script;\n/** Type for <style> tags */\nexports.Style = ElementType.Style;\n/** Type for Any tag */\nexports.Tag = ElementType.Tag;\n/** Type for <![CDATA[ ... ]]> */\nexports.CDATA = ElementType.CDATA;\n/** Type for <!doctype ...> */\nexports.Doctype = ElementType.Doctype;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,OAAO,GAAGF,OAAO,CAACG,KAAK,GAAGH,OAAO,CAACI,GAAG,GAAGJ,OAAO,CAACK,KAAK,GAAGL,OAAO,CAACM,MAAM,GAAGN,OAAO,CAACO,OAAO,GAAGP,OAAO,CAACQ,SAAS,GAAGR,OAAO,CAACS,IAAI,GAAGT,OAAO,CAACU,IAAI,GAAGV,OAAO,CAACW,KAAK,GAAGX,OAAO,CAACY,WAAW,GAAG,KAAK,CAAC;AACjM;AACA,IAAIA,WAAW;AACf,CAAC,UAAUA,WAAW,EAAE;EACpB;EACAA,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM;EAC5B;EACAA,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM;EAC5B;EACAA,WAAW,CAAC,WAAW,CAAC,GAAG,WAAW;EACtC;EACAA,WAAW,CAAC,SAAS,CAAC,GAAG,SAAS;EAClC;EACAA,WAAW,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAChC;EACAA,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;EAC9B;EACAA,WAAW,CAAC,KAAK,CAAC,GAAG,KAAK;EAC1B;EACAA,WAAW,CAAC,OAAO,CAAC,GAAG,OAAO;EAC9B;EACAA,WAAW,CAAC,SAAS,CAAC,GAAG,SAAS;AACtC,CAAC,EAAEA,WAAW,GAAGZ,OAAO,CAACY,WAAW,KAAKZ,OAAO,CAACY,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA,SAASD,KAAKA,CAACE,IAAI,EAAE;EACjB,OAAQA,IAAI,CAACC,IAAI,KAAKF,WAAW,CAACR,GAAG,IACjCS,IAAI,CAACC,IAAI,KAAKF,WAAW,CAACN,MAAM,IAChCO,IAAI,CAACC,IAAI,KAAKF,WAAW,CAACP,KAAK;AACvC;AACAL,OAAO,CAACW,KAAK,GAAGA,KAAK;AACrB;AACA;AACAX,OAAO,CAACU,IAAI,GAAGE,WAAW,CAACF,IAAI;AAC/B;AACAV,OAAO,CAACS,IAAI,GAAGG,WAAW,CAACH,IAAI;AAC/B;AACAT,OAAO,CAACQ,SAAS,GAAGI,WAAW,CAACJ,SAAS;AACzC;AACAR,OAAO,CAACO,OAAO,GAAGK,WAAW,CAACL,OAAO;AACrC;AACAP,OAAO,CAACM,MAAM,GAAGM,WAAW,CAACN,MAAM;AACnC;AACAN,OAAO,CAACK,KAAK,GAAGO,WAAW,CAACP,KAAK;AACjC;AACAL,OAAO,CAACI,GAAG,GAAGQ,WAAW,CAACR,GAAG;AAC7B;AACAJ,OAAO,CAACG,KAAK,GAAGS,WAAW,CAACT,KAAK;AACjC;AACAH,OAAO,CAACE,OAAO,GAAGU,WAAW,CAACV,OAAO"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}