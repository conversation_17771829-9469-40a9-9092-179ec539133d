{"ast": null, "code": "function t(r, e, n) {\n  return e();\n}\nexport { t as useSyncExternalStore };", "map": {"version": 3, "names": ["t", "r", "e", "n", "useSyncExternalStore"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@headlessui/react/dist/use-sync-external-store-shim/useSyncExternalStoreShimServer.js"], "sourcesContent": ["function t(r,e,n){return e()}export{t as useSyncExternalStore};\n"], "mappings": "AAAA,SAASA,CAACA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;EAAC,OAAOD,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOF,CAAC,IAAII,oBAAoB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}