{"ast": null, "code": "import ClassNameGenerator from '../ClassNameGenerator';\n\n// If GlobalStateSlot is changed, GLOBAL_STATE_CLASSES in\n// \\packages\\api-docs-builder\\utils\\parseSlotsAndClasses.ts must be updated accordingly.\nconst globalStateClassesMapping = {\n  active: 'active',\n  checked: 'checked',\n  completed: 'completed',\n  disabled: 'disabled',\n  error: 'error',\n  expanded: 'expanded',\n  focused: 'focused',\n  focusVisible: 'focusVisible',\n  open: 'open',\n  readOnly: 'readOnly',\n  required: 'required',\n  selected: 'selected'\n};\nexport default function generateUtilityClass(componentName, slot, globalStatePrefix = 'Mui') {\n  const globalStateClass = globalStateClassesMapping[slot];\n  return globalStateClass ? `${globalStatePrefix}-${globalStateClass}` : `${ClassNameGenerator.generate(componentName)}-${slot}`;\n}", "map": {"version": 3, "names": ["ClassNameGenerator", "globalStateClassesMapping", "active", "checked", "completed", "disabled", "error", "expanded", "focused", "focusVisible", "open", "readOnly", "required", "selected", "generateUtilityClass", "componentName", "slot", "globalStatePrefix", "globalStateClass", "generate"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/utils/esm/generateUtilityClass/generateUtilityClass.js"], "sourcesContent": ["import ClassNameGenerator from '../ClassNameGenerator';\n\n// If GlobalStateSlot is changed, GLOBAL_STATE_CLASSES in\n// \\packages\\api-docs-builder\\utils\\parseSlotsAndClasses.ts must be updated accordingly.\nconst globalStateClassesMapping = {\n  active: 'active',\n  checked: 'checked',\n  completed: 'completed',\n  disabled: 'disabled',\n  error: 'error',\n  expanded: 'expanded',\n  focused: 'focused',\n  focusVisible: 'focusVisible',\n  open: 'open',\n  readOnly: 'readOnly',\n  required: 'required',\n  selected: 'selected'\n};\nexport default function generateUtilityClass(componentName, slot, globalStatePrefix = 'Mui') {\n  const globalStateClass = globalStateClassesMapping[slot];\n  return globalStateClass ? `${globalStatePrefix}-${globalStateClass}` : `${ClassNameGenerator.generate(componentName)}-${slot}`;\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,uBAAuB;;AAEtD;AACA;AACA,MAAMC,yBAAyB,GAAG;EAChCC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,WAAW;EACtBC,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE,SAAS;EAClBC,YAAY,EAAE,cAAc;EAC5BC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE;AACZ,CAAC;AACD,eAAe,SAASC,oBAAoBA,CAACC,aAAa,EAAEC,IAAI,EAAEC,iBAAiB,GAAG,KAAK,EAAE;EAC3F,MAAMC,gBAAgB,GAAGjB,yBAAyB,CAACe,IAAI,CAAC;EACxD,OAAOE,gBAAgB,GAAI,GAAED,iBAAkB,IAAGC,gBAAiB,EAAC,GAAI,GAAElB,kBAAkB,CAACmB,QAAQ,CAACJ,aAAa,CAAE,IAAGC,IAAK,EAAC;AAChI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}