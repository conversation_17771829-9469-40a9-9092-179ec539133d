{"ast": null, "code": "import { ReplaySubject } from '../ReplaySubject';\nimport { share } from './share';\nexport function shareReplay(configOrBufferSize, windowTime, scheduler) {\n  var _a, _b, _c;\n  var bufferSize;\n  var refCount = false;\n  if (configOrBufferSize && typeof configOrBufferSize === 'object') {\n    _a = configOrBufferSize.bufferSize, bufferSize = _a === void 0 ? Infinity : _a, _b = configOrBufferSize.windowTime, windowTime = _b === void 0 ? Infinity : _b, _c = configOrBufferSize.refCount, refCount = _c === void 0 ? false : _c, scheduler = configOrBufferSize.scheduler;\n  } else {\n    bufferSize = configOrBufferSize !== null && configOrBufferSize !== void 0 ? configOrBufferSize : Infinity;\n  }\n  return share({\n    connector: function () {\n      return new ReplaySubject(bufferSize, windowTime, scheduler);\n    },\n    resetOnError: true,\n    resetOnComplete: false,\n    resetOnRefCountZero: refCount\n  });\n}", "map": {"version": 3, "names": ["ReplaySubject", "share", "shareReplay", "configOrBufferSize", "windowTime", "scheduler", "bufferSize", "refCount", "_a", "Infinity", "_b", "_c", "connector", "resetOnError", "resetOnComplete", "resetOnRefCountZero"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\shareReplay.ts"], "sourcesContent": ["import { ReplaySubject } from '../ReplaySubject';\nimport { MonoTypeOperatorFunction, SchedulerLike } from '../types';\nimport { share } from './share';\n\nexport interface ShareReplayConfig {\n  bufferSize?: number;\n  windowTime?: number;\n  refCount: boolean;\n  scheduler?: SchedulerLike;\n}\n\nexport function shareReplay<T>(config: ShareReplayConfig): MonoTypeOperatorFunction<T>;\nexport function shareReplay<T>(bufferSize?: number, windowTime?: number, scheduler?: SchedulerLike): MonoTypeOperatorFunction<T>;\n\n/**\n * Share source and replay specified number of emissions on subscription.\n *\n * This operator is a specialization of `replay` that connects to a source observable\n * and multicasts through a `ReplaySubject` constructed with the specified arguments.\n * A successfully completed source will stay cached in the `shareReplay`ed observable forever,\n * but an errored source can be retried.\n *\n * ## Why use `shareReplay`?\n *\n * You generally want to use `shareReplay` when you have side-effects or taxing computations\n * that you do not wish to be executed amongst multiple subscribers.\n * It may also be valuable in situations where you know you will have late subscribers to\n * a stream that need access to previously emitted values.\n * This ability to replay values on subscription is what differentiates {@link share} and `shareReplay`.\n *\n * ## Reference counting\n *\n * By default `shareReplay` will use `refCount` of false, meaning that it will _not_ unsubscribe the\n * source when the reference counter drops to zero, i.e. the inner `ReplaySubject` will _not_ be unsubscribed\n * (and potentially run for ever).\n * This is the default as it is expected that `shareReplay` is often used to keep around expensive to setup\n * observables which we want to keep running instead of having to do the expensive setup again.\n *\n * As of RXJS version 6.4.0 a new overload signature was added to allow for manual control over what\n * happens when the operators internal reference counter drops to zero.\n * If `refCount` is true, the source will be unsubscribed from once the reference count drops to zero, i.e.\n * the inner `ReplaySubject` will be unsubscribed. All new subscribers will receive value emissions from a\n * new `ReplaySubject` which in turn will cause a new subscription to the source observable.\n *\n * ## Examples\n *\n * Example with a third subscriber coming late to the party\n *\n * ```ts\n * import { interval, take, shareReplay } from 'rxjs';\n *\n * const shared$ = interval(2000).pipe(\n *   take(6),\n *   shareReplay(3)\n * );\n *\n * shared$.subscribe(x => console.log('sub A: ', x));\n * shared$.subscribe(y => console.log('sub B: ', y));\n *\n * setTimeout(() => {\n *   shared$.subscribe(y => console.log('sub C: ', y));\n * }, 11000);\n *\n * // Logs:\n * // (after ~2000 ms)\n * // sub A: 0\n * // sub B: 0\n * // (after ~4000 ms)\n * // sub A: 1\n * // sub B: 1\n * // (after ~6000 ms)\n * // sub A: 2\n * // sub B: 2\n * // (after ~8000 ms)\n * // sub A: 3\n * // sub B: 3\n * // (after ~10000 ms)\n * // sub A: 4\n * // sub B: 4\n * // (after ~11000 ms, sub C gets the last 3 values)\n * // sub C: 2\n * // sub C: 3\n * // sub C: 4\n * // (after ~12000 ms)\n * // sub A: 5\n * // sub B: 5\n * // sub C: 5\n * ```\n *\n * Example for `refCount` usage\n *\n * ```ts\n * import { Observable, tap, interval, shareReplay, take } from 'rxjs';\n *\n * const log = <T>(name: string, source: Observable<T>) => source.pipe(\n *   tap({\n *     subscribe: () => console.log(`${ name }: subscribed`),\n *     next: value => console.log(`${ name }: ${ value }`),\n *     complete: () => console.log(`${ name }: completed`),\n *     finalize: () => console.log(`${ name }: unsubscribed`)\n *   })\n * );\n *\n * const obs$ = log('source', interval(1000));\n *\n * const shared$ = log('shared', obs$.pipe(\n *   shareReplay({ bufferSize: 1, refCount: true }),\n *   take(2)\n * ));\n *\n * shared$.subscribe(x => console.log('sub A: ', x));\n * shared$.subscribe(y => console.log('sub B: ', y));\n *\n * // PRINTS:\n * // shared: subscribed <-- reference count = 1\n * // source: subscribed\n * // shared: subscribed <-- reference count = 2\n * // source: 0\n * // shared: 0\n * // sub A: 0\n * // shared: 0\n * // sub B: 0\n * // source: 1\n * // shared: 1\n * // sub A: 1\n * // shared: completed <-- take(2) completes the subscription for sub A\n * // shared: unsubscribed <-- reference count = 1\n * // shared: 1\n * // sub B: 1\n * // shared: completed <-- take(2) completes the subscription for sub B\n * // shared: unsubscribed <-- reference count = 0\n * // source: unsubscribed <-- replaySubject unsubscribes from source observable because the reference count dropped to 0 and refCount is true\n *\n * // In case of refCount being false, the unsubscribe is never called on the source and the source would keep on emitting, even if no subscribers\n * // are listening.\n * // source: 2\n * // source: 3\n * // source: 4\n * // ...\n * ```\n *\n * @see {@link publish}\n * @see {@link share}\n * @see {@link publishReplay}\n *\n * @param configOrBufferSize Maximum element count of the replay buffer or {@link ShareReplayConfig configuration}\n * object.\n * @param windowTime Maximum time length of the replay buffer in milliseconds.\n * @param scheduler Scheduler where connected observers within the selector function\n * will be invoked on.\n * @return A function that returns an Observable sequence that contains the\n * elements of a sequence produced by multicasting the source sequence within a\n * selector function.\n */\nexport function shareReplay<T>(\n  configOrBufferSize?: ShareReplayConfig | number,\n  windowTime?: number,\n  scheduler?: SchedulerLike\n): MonoTypeOperatorFunction<T> {\n  let bufferSize: number;\n  let refCount = false;\n  if (configOrBufferSize && typeof configOrBufferSize === 'object') {\n    ({ bufferSize = Infinity, windowTime = Infinity, refCount = false, scheduler } = configOrBufferSize);\n  } else {\n    bufferSize = (configOrBufferSize ?? Infinity) as number;\n  }\n  return share<T>({\n    connector: () => new ReplaySubject(bufferSize, windowTime, scheduler),\n    resetOnError: true,\n    resetOnComplete: false,\n    resetOnRefCountZero: refCount,\n  });\n}\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,kBAAkB;AAEhD,SAASC,KAAK,QAAQ,SAAS;AAwJ/B,OAAM,SAAUC,WAAWA,CACzBC,kBAA+C,EAC/CC,UAAmB,EACnBC,SAAyB;;EAEzB,IAAIC,UAAkB;EACtB,IAAIC,QAAQ,GAAG,KAAK;EACpB,IAAIJ,kBAAkB,IAAI,OAAOA,kBAAkB,KAAK,QAAQ,EAAE;IAC7DK,EAAA,GAA8EL,kBAAkB,CAAAG,UAA3E,EAArBA,UAAU,GAAAE,EAAA,cAAGC,QAAQ,GAAAD,EAAA,EAAEE,EAAA,GAAuDP,kBAAkB,CAAAC,UAApD,EAArBA,UAAU,GAAAM,EAAA,cAAGD,QAAQ,GAAAC,EAAA,EAAEC,EAAA,GAAgCR,kBAAkB,CAAAI,QAAlC,EAAhBA,QAAQ,GAAAI,EAAA,cAAG,KAAK,GAAAA,EAAA,EAAEN,SAAS,GAAKF,kBAAkB,CAAAE,SAAvB;GAC7E,MAAM;IACLC,UAAU,GAAIH,kBAAkB,aAAlBA,kBAAkB,cAAlBA,kBAAkB,GAAIM,QAAmB;;EAEzD,OAAOR,KAAK,CAAI;IACdW,SAAS,EAAE,SAAAA,CAAA;MAAM,WAAIZ,aAAa,CAACM,UAAU,EAAEF,UAAU,EAAEC,SAAS,CAAC;IAApD,CAAoD;IACrEQ,YAAY,EAAE,IAAI;IAClBC,eAAe,EAAE,KAAK;IACtBC,mBAAmB,EAAER;GACtB,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}