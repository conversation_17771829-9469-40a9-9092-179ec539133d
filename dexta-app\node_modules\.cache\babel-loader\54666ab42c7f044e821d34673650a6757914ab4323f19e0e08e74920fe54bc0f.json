{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { combineLatestInit } from '../observable/combineLatest';\nimport { operate } from '../util/lift';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { pipe } from '../util/pipe';\nimport { popResultSelector } from '../util/args';\nexport function combineLatest() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var resultSelector = popResultSelector(args);\n  return resultSelector ? pipe(combineLatest.apply(void 0, __spreadArray([], __read(args))), mapOneOrManyArgs(resultSelector)) : operate(function (source, subscriber) {\n    combineLatestInit(__spreadArray([source], __read(argsOrArgArray(args))))(subscriber);\n  });\n}", "map": {"version": 3, "names": ["combineLatestInit", "operate", "argsOrArgArray", "mapOneOrManyArgs", "pipe", "popResultSelector", "combineLatest", "args", "_i", "arguments", "length", "resultSelector", "apply", "__spread<PERSON><PERSON>y", "__read", "source", "subscriber"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\combineLatest.ts"], "sourcesContent": ["import { combineLatestInit } from '../observable/combineLatest';\nimport { ObservableInput, ObservableInputTuple, OperatorFunction } from '../types';\nimport { operate } from '../util/lift';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { pipe } from '../util/pipe';\nimport { popResultSelector } from '../util/args';\n\n/** @deprecated Replaced with {@link combineLatestWith}. Will be removed in v8. */\nexport function combineLatest<T, A extends readonly unknown[], R>(\n  sources: [...ObservableInputTuple<A>],\n  project: (...values: [T, ...A]) => R\n): OperatorFunction<T, R>;\n/** @deprecated Replaced with {@link combineLatestWith}. Will be removed in v8. */\nexport function combineLatest<T, A extends readonly unknown[], R>(sources: [...ObservableInputTuple<A>]): OperatorFunction<T, [T, ...A]>;\n\n/** @deprecated Replaced with {@link combineLatestWith}. Will be removed in v8. */\nexport function combineLatest<T, A extends readonly unknown[], R>(\n  ...sourcesAndProject: [...ObservableInputTuple<A>, (...values: [T, ...A]) => R]\n): OperatorFunction<T, R>;\n/** @deprecated Replaced with {@link combineLatestWith}. Will be removed in v8. */\nexport function combineLatest<T, A extends readonly unknown[], R>(...sources: [...ObservableInputTuple<A>]): OperatorFunction<T, [T, ...A]>;\n\n/**\n * @deprecated Replaced with {@link combineLatestWith}. Will be removed in v8.\n */\nexport function combineLatest<T, R>(...args: (ObservableInput<any> | ((...values: any[]) => R))[]): OperatorFunction<T, unknown> {\n  const resultSelector = popResultSelector(args);\n  return resultSelector\n    ? pipe(combineLatest(...(args as Array<ObservableInput<any>>)), mapOneOrManyArgs(resultSelector))\n    : operate((source, subscriber) => {\n        combineLatestInit([source, ...argsOrArgArray(args)])(subscriber);\n      });\n}\n"], "mappings": ";AAAA,SAASA,iBAAiB,QAAQ,6BAA6B;AAE/D,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,cAAc,QAAQ,wBAAwB;AACvD,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,iBAAiB,QAAQ,cAAc;AAoBhD,OAAM,SAAUC,aAAaA,CAAA;EAAO,IAAAC,IAAA;OAAA,IAAAC,EAAA,IAA6D,EAA7DA,EAAA,GAAAC,SAAA,CAAAC,MAA6D,EAA7DF,EAAA,EAA6D;IAA7DD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAClC,IAAMG,cAAc,GAAGN,iBAAiB,CAACE,IAAI,CAAC;EAC9C,OAAOI,cAAc,GACjBP,IAAI,CAACE,aAAa,CAAAM,KAAA,SAAAC,aAAA,KAAAC,MAAA,CAAKP,IAAoC,KAAGJ,gBAAgB,CAACQ,cAAc,CAAC,CAAC,GAC/FV,OAAO,CAAC,UAACc,MAAM,EAAEC,UAAU;IACzBhB,iBAAiB,CAAAa,aAAA,EAAEE,MAAM,GAAAD,MAAA,CAAKZ,cAAc,CAACK,IAAI,CAAC,GAAE,CAACS,UAAU,CAAC;EAClE,CAAC,CAAC;AACR"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}