{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _slicedToArray = function () {\n  function sliceIterator(arr, i) {\n    var _arr = [];\n    var _n = true;\n    var _d = false;\n    var _e = undefined;\n    try {\n      for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n        _arr.push(_s.value);\n        if (i && _arr.length === i) break;\n      }\n    } catch (err) {\n      _d = true;\n      _e = err;\n    } finally {\n      try {\n        if (!_n && _i[\"return\"]) _i[\"return\"]();\n      } finally {\n        if (_d) throw _e;\n      }\n    }\n    return _arr;\n  }\n  return function (arr, i) {\n    if (Array.isArray(arr)) {\n      return arr;\n    } else if (Symbol.iterator in Object(arr)) {\n      return sliceIterator(arr, i);\n    } else {\n      throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");\n    }\n  };\n}();\nexports.default = InlineStyleToObject;\n/**\n * Converts an inline style string into an object of React style properties\n *\n * @param {String} inlineStyle='' The inline style to convert\n * @returns {Object} The converted style\n */\nfunction InlineStyleToObject() {\n  var inlineStyle = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n\n  // just return empty object if the inlineStyle is empty\n  if (inlineStyle === '') {\n    return {};\n  }\n  return inlineStyle.split(';').reduce(function (styleObject, stylePropertyValue) {\n    // extract the style property name and value\n    var _stylePropertyValue$s = stylePropertyValue.split(/^([^:]+):/).filter(function (val, i) {\n        return i > 0;\n      }).map(function (item) {\n        return item.trim().toLowerCase();\n      }),\n      _stylePropertyValue$s2 = _slicedToArray(_stylePropertyValue$s, 2),\n      property = _stylePropertyValue$s2[0],\n      value = _stylePropertyValue$s2[1];\n\n    // if there is no value (i.e. no : in the style) then ignore it\n\n    if (value === undefined) {\n      return styleObject;\n    }\n\n    // convert the property name into the correct React format\n    // remove all hyphens and convert the letter immediately after each hyphen to upper case\n    // additionally don't uppercase any -ms- prefix\n    // e.g. -ms-style-property = msStyleProperty\n    //      -webkit-style-property = WebkitStyleProperty\n    property = property.replace(/^-ms-/, 'ms-').replace(/-(.)/g, function (_, character) {\n      return character.toUpperCase();\n    });\n\n    // add the new style property and value to the style object\n    styleObject[property] = value;\n    return styleObject;\n  }, {});\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_slicedToArray", "sliceIterator", "arr", "i", "_arr", "_n", "_d", "_e", "undefined", "_i", "Symbol", "iterator", "_s", "next", "done", "push", "length", "err", "Array", "isArray", "TypeError", "default", "InlineStyleToObject", "inlineStyle", "arguments", "split", "reduce", "styleObject", "stylePropertyValue", "_stylePropertyValue$s", "filter", "val", "map", "item", "trim", "toLowerCase", "_stylePropertyValue$s2", "property", "replace", "_", "character", "toUpperCase"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/react-html-parser/lib/utils/inlineStyleToObject.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _slicedToArray = function () { function sliceIterator(arr, i) { var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"]) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; } return function (arr, i) { if (Array.isArray(arr)) { return arr; } else if (Symbol.iterator in Object(arr)) { return sliceIterator(arr, i); } else { throw new TypeError(\"Invalid attempt to destructure non-iterable instance\"); } }; }();\n\nexports.default = InlineStyleToObject;\n/**\n * Converts an inline style string into an object of React style properties\n *\n * @param {String} inlineStyle='' The inline style to convert\n * @returns {Object} The converted style\n */\nfunction InlineStyleToObject() {\n  var inlineStyle = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n\n\n  // just return empty object if the inlineStyle is empty\n  if (inlineStyle === '') {\n    return {};\n  }\n\n  return inlineStyle.split(';').reduce(function (styleObject, stylePropertyValue) {\n\n    // extract the style property name and value\n    var _stylePropertyValue$s = stylePropertyValue.split(/^([^:]+):/).filter(function (val, i) {\n      return i > 0;\n    }).map(function (item) {\n      return item.trim().toLowerCase();\n    }),\n        _stylePropertyValue$s2 = _slicedToArray(_stylePropertyValue$s, 2),\n        property = _stylePropertyValue$s2[0],\n        value = _stylePropertyValue$s2[1];\n\n    // if there is no value (i.e. no : in the style) then ignore it\n\n\n    if (value === undefined) {\n      return styleObject;\n    }\n\n    // convert the property name into the correct React format\n    // remove all hyphens and convert the letter immediately after each hyphen to upper case\n    // additionally don't uppercase any -ms- prefix\n    // e.g. -ms-style-property = msStyleProperty\n    //      -webkit-style-property = WebkitStyleProperty\n    property = property.replace(/^-ms-/, 'ms-').replace(/-(.)/g, function (_, character) {\n      return character.toUpperCase();\n    });\n\n    // add the new style property and value to the style object\n    styleObject[property] = value;\n\n    return styleObject;\n  }, {});\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AAEF,IAAIC,cAAc,GAAG,YAAY;EAAE,SAASC,aAAaA,CAACC,GAAG,EAAEC,CAAC,EAAE;IAAE,IAAIC,IAAI,GAAG,EAAE;IAAE,IAAIC,EAAE,GAAG,IAAI;IAAE,IAAIC,EAAE,GAAG,KAAK;IAAE,IAAIC,EAAE,GAAGC,SAAS;IAAE,IAAI;MAAE,KAAK,IAAIC,EAAE,GAAGP,GAAG,CAACQ,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC,EAAEC,EAAE,EAAE,EAAEP,EAAE,GAAG,CAACO,EAAE,GAAGH,EAAE,CAACI,IAAI,CAAC,CAAC,EAAEC,IAAI,CAAC,EAAET,EAAE,GAAG,IAAI,EAAE;QAAED,IAAI,CAACW,IAAI,CAACH,EAAE,CAACb,KAAK,CAAC;QAAE,IAAII,CAAC,IAAIC,IAAI,CAACY,MAAM,KAAKb,CAAC,EAAE;MAAO;IAAE,CAAC,CAAC,OAAOc,GAAG,EAAE;MAAEX,EAAE,GAAG,IAAI;MAAEC,EAAE,GAAGU,GAAG;IAAE,CAAC,SAAS;MAAE,IAAI;QAAE,IAAI,CAACZ,EAAE,IAAII,EAAE,CAAC,QAAQ,CAAC,EAAEA,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;MAAE,CAAC,SAAS;QAAE,IAAIH,EAAE,EAAE,MAAMC,EAAE;MAAE;IAAE;IAAE,OAAOH,IAAI;EAAE;EAAE,OAAO,UAAUF,GAAG,EAAEC,CAAC,EAAE;IAAE,IAAIe,KAAK,CAACC,OAAO,CAACjB,GAAG,CAAC,EAAE;MAAE,OAAOA,GAAG;IAAE,CAAC,MAAM,IAAIQ,MAAM,CAACC,QAAQ,IAAIf,MAAM,CAACM,GAAG,CAAC,EAAE;MAAE,OAAOD,aAAa,CAACC,GAAG,EAAEC,CAAC,CAAC;IAAE,CAAC,MAAM;MAAE,MAAM,IAAIiB,SAAS,CAAC,sDAAsD,CAAC;IAAE;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAEvpBtB,OAAO,CAACuB,OAAO,GAAGC,mBAAmB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,mBAAmBA,CAAA,EAAG;EAC7B,IAAIC,WAAW,GAAGC,SAAS,CAACR,MAAM,GAAG,CAAC,IAAIQ,SAAS,CAAC,CAAC,CAAC,KAAKhB,SAAS,GAAGgB,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;;EAGxF;EACA,IAAID,WAAW,KAAK,EAAE,EAAE;IACtB,OAAO,CAAC,CAAC;EACX;EAEA,OAAOA,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,UAAUC,WAAW,EAAEC,kBAAkB,EAAE;IAE9E;IACA,IAAIC,qBAAqB,GAAGD,kBAAkB,CAACH,KAAK,CAAC,WAAW,CAAC,CAACK,MAAM,CAAC,UAAUC,GAAG,EAAE5B,CAAC,EAAE;QACzF,OAAOA,CAAC,GAAG,CAAC;MACd,CAAC,CAAC,CAAC6B,GAAG,CAAC,UAAUC,IAAI,EAAE;QACrB,OAAOA,IAAI,CAACC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MAClC,CAAC,CAAC;MACEC,sBAAsB,GAAGpC,cAAc,CAAC6B,qBAAqB,EAAE,CAAC,CAAC;MACjEQ,QAAQ,GAAGD,sBAAsB,CAAC,CAAC,CAAC;MACpCrC,KAAK,GAAGqC,sBAAsB,CAAC,CAAC,CAAC;;IAErC;;IAGA,IAAIrC,KAAK,KAAKS,SAAS,EAAE;MACvB,OAAOmB,WAAW;IACpB;;IAEA;IACA;IACA;IACA;IACA;IACAU,QAAQ,GAAGA,QAAQ,CAACC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,CAACA,OAAO,CAAC,OAAO,EAAE,UAAUC,CAAC,EAAEC,SAAS,EAAE;MACnF,OAAOA,SAAS,CAACC,WAAW,CAAC,CAAC;IAChC,CAAC,CAAC;;IAEF;IACAd,WAAW,CAACU,QAAQ,CAAC,GAAGtC,KAAK;IAE7B,OAAO4B,WAAW;EACpB,CAAC,EAAE,CAAC,CAAC,CAAC;AACR"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}