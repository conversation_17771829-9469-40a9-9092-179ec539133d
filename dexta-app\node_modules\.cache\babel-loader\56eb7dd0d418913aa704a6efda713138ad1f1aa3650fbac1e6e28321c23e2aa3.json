{"ast": null, "code": "var div = null;\nvar prefixes = ['Webkit', '<PERSON>z', 'O', 'ms'];\nmodule.exports = function prefixStyle(prop) {\n  // re-use a dummy div\n  if (!div) {\n    div = document.createElement('div');\n  }\n  var style = div.style;\n\n  // prop exists without prefix\n  if (prop in style) {\n    return prop;\n  }\n\n  // borderRadius -> BorderRadius\n  var titleCase = prop.charAt(0).toUpperCase() + prop.slice(1);\n\n  // find the vendor-prefixed prop\n  for (var i = prefixes.length; i >= 0; i--) {\n    var name = prefixes[i] + titleCase;\n    // e.g. WebkitBorderRadius or webkitBorderRadius\n    if (name in style) {\n      return name;\n    }\n  }\n  return false;\n};", "map": {"version": 3, "names": ["div", "prefixes", "module", "exports", "prefixStyle", "prop", "document", "createElement", "style", "titleCase", "char<PERSON>t", "toUpperCase", "slice", "i", "length", "name"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/prefix-style/index.js"], "sourcesContent": ["var div = null\nvar prefixes = [ 'Webkit', 'Moz', 'O', 'ms' ]\n\nmodule.exports = function prefixStyle (prop) {\n  // re-use a dummy div\n  if (!div) {\n    div = document.createElement('div')\n  }\n\n  var style = div.style\n\n  // prop exists without prefix\n  if (prop in style) {\n    return prop\n  }\n\n  // borderRadius -> BorderRadius\n  var titleCase = prop.charAt(0).toUpperCase() + prop.slice(1)\n\n  // find the vendor-prefixed prop\n  for (var i = prefixes.length; i >= 0; i--) {\n    var name = prefixes[i] + titleCase\n    // e.g. WebkitBorderRadius or webkitBorderRadius\n    if (name in style) {\n      return name\n    }\n  }\n\n  return false\n}\n"], "mappings": "AAAA,IAAIA,GAAG,GAAG,IAAI;AACd,IAAIC,QAAQ,GAAG,CAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,CAAE;AAE7CC,MAAM,CAACC,OAAO,GAAG,SAASC,WAAWA,CAAEC,IAAI,EAAE;EAC3C;EACA,IAAI,CAACL,GAAG,EAAE;IACRA,GAAG,GAAGM,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EACrC;EAEA,IAAIC,KAAK,GAAGR,GAAG,CAACQ,KAAK;;EAErB;EACA,IAAIH,IAAI,IAAIG,KAAK,EAAE;IACjB,OAAOH,IAAI;EACb;;EAEA;EACA,IAAII,SAAS,GAAGJ,IAAI,CAACK,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGN,IAAI,CAACO,KAAK,CAAC,CAAC,CAAC;;EAE5D;EACA,KAAK,IAAIC,CAAC,GAAGZ,QAAQ,CAACa,MAAM,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACzC,IAAIE,IAAI,GAAGd,QAAQ,CAACY,CAAC,CAAC,GAAGJ,SAAS;IAClC;IACA,IAAIM,IAAI,IAAIP,KAAK,EAAE;MACjB,OAAOO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}