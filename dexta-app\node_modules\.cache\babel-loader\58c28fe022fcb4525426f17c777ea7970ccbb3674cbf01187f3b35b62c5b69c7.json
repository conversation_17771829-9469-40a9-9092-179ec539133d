{"ast": null, "code": "'use strict';\n\nvar path = require('../internals/path');\nvar global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\nvar aFunction = function (variable) {\n  return isCallable(variable) ? variable : undefined;\n};\nmodule.exports = function (namespace, method) {\n  return arguments.length < 2 ? aFunction(path[namespace]) || aFunction(global[namespace]) : path[namespace] && path[namespace][method] || global[namespace] && global[namespace][method];\n};", "map": {"version": 3, "names": ["path", "require", "global", "isCallable", "aFunction", "variable", "undefined", "module", "exports", "namespace", "method", "arguments", "length"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/core-js-pure/internals/get-built-in.js"], "sourcesContent": ["'use strict';\nvar path = require('../internals/path');\nvar global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\n\nvar aFunction = function (variable) {\n  return isCallable(variable) ? variable : undefined;\n};\n\nmodule.exports = function (namespace, method) {\n  return arguments.length < 2 ? aFunction(path[namespace]) || aFunction(global[namespace])\n    : path[namespace] && path[namespace][method] || global[namespace] && global[namespace][method];\n};\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,IAAI,GAAGC,OAAO,CAAC,mBAAmB,CAAC;AACvC,IAAIC,MAAM,GAAGD,OAAO,CAAC,qBAAqB,CAAC;AAC3C,IAAIE,UAAU,GAAGF,OAAO,CAAC,0BAA0B,CAAC;AAEpD,IAAIG,SAAS,GAAG,SAAAA,CAAUC,QAAQ,EAAE;EAClC,OAAOF,UAAU,CAACE,QAAQ,CAAC,GAAGA,QAAQ,GAAGC,SAAS;AACpD,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG,UAAUC,SAAS,EAAEC,MAAM,EAAE;EAC5C,OAAOC,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGR,SAAS,CAACJ,IAAI,CAACS,SAAS,CAAC,CAAC,IAAIL,SAAS,CAACF,MAAM,CAACO,SAAS,CAAC,CAAC,GACpFT,IAAI,CAACS,SAAS,CAAC,IAAIT,IAAI,CAACS,SAAS,CAAC,CAACC,MAAM,CAAC,IAAIR,MAAM,CAACO,SAAS,CAAC,IAAIP,MAAM,CAACO,SAAS,CAAC,CAACC,MAAM,CAAC;AAClG,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}