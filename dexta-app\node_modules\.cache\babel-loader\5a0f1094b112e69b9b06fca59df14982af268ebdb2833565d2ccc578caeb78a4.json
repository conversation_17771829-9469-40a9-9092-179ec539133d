{"ast": null, "code": "import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getFormControlUnstyledUtilityClass(slot) {\n  return generateUtilityClass('BaseFormControl', slot);\n}\nconst formControlUnstyledClasses = generateUtilityClasses('BaseFormControl', ['root', 'disabled', 'error', 'filled', 'focused', 'required']);\nexport default formControlUnstyledClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getFormControlUnstyledUtilityClass", "slot", "formControlUnstyledClasses"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/FormControlUnstyled/formControlUnstyledClasses.js"], "sourcesContent": ["import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getFormControlUnstyledUtilityClass(slot) {\n  return generateUtilityClass('BaseFormControl', slot);\n}\nconst formControlUnstyledClasses = generateUtilityClasses('BaseFormControl', ['root', 'disabled', 'error', 'filled', 'focused', 'required']);\nexport default formControlUnstyledClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,yBAAyB;AAC1D,OAAOC,sBAAsB,MAAM,2BAA2B;AAC9D,OAAO,SAASC,kCAAkCA,CAACC,IAAI,EAAE;EACvD,OAAOH,oBAAoB,CAAC,iBAAiB,EAAEG,IAAI,CAAC;AACtD;AACA,MAAMC,0BAA0B,GAAGH,sBAAsB,CAAC,iBAAiB,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;AAC5I,eAAeG,0BAA0B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}