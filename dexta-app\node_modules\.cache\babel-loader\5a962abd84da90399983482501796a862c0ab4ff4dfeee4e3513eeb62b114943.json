{"ast": null, "code": "import { invariant } from '@react-dnd/invariant';\nimport { cloneElement, isValidElement } from 'react';\nfunction throwIfCompositeComponentElement(element) {\n  // Custom components can no longer be wrapped directly in React DnD 2.0\n  // so that we don't need to depend on findDOMNode() from react-dom.\n  if (typeof element.type === 'string') {\n    return;\n  }\n  const displayName = element.type.displayName || element.type.name || 'the component';\n  throw new Error('Only native element nodes can now be passed to React DnD connectors.' + `You can either wrap ${displayName} into a <div>, or turn it into a ` + 'drag source or a drop target itself.');\n}\nfunction wrapHookToRecognizeElement(hook) {\n  return (elementOrNode = null, options = null) => {\n    // When passed a node, call the hook straight away.\n    if (!isValidElement(elementOrNode)) {\n      const node = elementOrNode;\n      hook(node, options);\n      // return the node so it can be chained (e.g. when within callback refs\n      // <div ref={node => connectDragSource(connectDropTarget(node))}/>\n      return node;\n    }\n    // If passed a ReactElement, clone it and attach this function as a ref.\n    // This helps us achieve a neat API where user doesn't even know that refs\n    // are being used under the hood.\n    const element = elementOrNode;\n    throwIfCompositeComponentElement(element);\n    // When no options are passed, use the hook directly\n    const ref = options ? node => hook(node, options) : hook;\n    return cloneWithRef(element, ref);\n  };\n}\nexport function wrapConnectorHooks(hooks) {\n  const wrappedHooks = {};\n  Object.keys(hooks).forEach(key => {\n    const hook = hooks[key];\n    // ref objects should be passed straight through without wrapping\n    if (key.endsWith('Ref')) {\n      wrappedHooks[key] = hooks[key];\n    } else {\n      const wrappedHook = wrapHookToRecognizeElement(hook);\n      wrappedHooks[key] = () => wrappedHook;\n    }\n  });\n  return wrappedHooks;\n}\nfunction setRef(ref, node) {\n  if (typeof ref === 'function') {\n    ref(node);\n  } else {\n    ref.current = node;\n  }\n}\nfunction cloneWithRef(element, newRef) {\n  const previousRef = element.ref;\n  invariant(typeof previousRef !== 'string', 'Cannot connect React DnD to an element with an existing string ref. ' + 'Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. ' + 'Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs');\n  if (!previousRef) {\n    // When there is no ref on the element, use the new ref directly\n    return cloneElement(element, {\n      ref: newRef\n    });\n  } else {\n    return cloneElement(element, {\n      ref: node => {\n        setRef(previousRef, node);\n        setRef(newRef, node);\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["invariant", "cloneElement", "isValidElement", "throwIfCompositeComponentElement", "element", "type", "displayName", "name", "Error", "wrapHookToRecognizeElement", "hook", "elementOrNode", "options", "node", "ref", "cloneWithRef", "wrapConnectorHooks", "hooks", "<PERSON><PERSON><PERSON>s", "Object", "keys", "for<PERSON>ach", "key", "endsWith", "wrappedHook", "setRef", "current", "newRef", "previousRef"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\react-dnd\\src\\internals\\wrapConnectorHooks.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\nimport type { ReactElement } from 'react'\nimport { cloneElement, isValidElement } from 'react'\n\nfunction throwIfCompositeComponentElement(element: ReactElement<any>) {\n\t// Custom components can no longer be wrapped directly in React DnD 2.0\n\t// so that we don't need to depend on findDOMNode() from react-dom.\n\tif (typeof element.type === 'string') {\n\t\treturn\n\t}\n\n\tconst displayName =\n\t\t(element.type as any).displayName || element.type.name || 'the component'\n\n\tthrow new Error(\n\t\t'Only native element nodes can now be passed to React DnD connectors.' +\n\t\t\t`You can either wrap ${displayName} into a <div>, or turn it into a ` +\n\t\t\t'drag source or a drop target itself.',\n\t)\n}\n\nfunction wrapHookToRecognizeElement(hook: (node: any, options: any) => void) {\n\treturn (elementOrNode = null, options = null) => {\n\t\t// When passed a node, call the hook straight away.\n\t\tif (!isValidElement(elementOrNode)) {\n\t\t\tconst node = elementOrNode\n\t\t\thook(node, options)\n\t\t\t// return the node so it can be chained (e.g. when within callback refs\n\t\t\t// <div ref={node => connectDragSource(connectDropTarget(node))}/>\n\t\t\treturn node\n\t\t}\n\n\t\t// If passed a ReactElement, clone it and attach this function as a ref.\n\t\t// This helps us achieve a neat API where user doesn't even know that refs\n\t\t// are being used under the hood.\n\t\tconst element: ReactElement | null = elementOrNode\n\t\tthrowIfCompositeComponentElement(element as any)\n\n\t\t// When no options are passed, use the hook directly\n\t\tconst ref = options ? (node: Element) => hook(node, options) : hook\n\t\treturn cloneWithRef(element, ref)\n\t}\n}\n\nexport function wrapConnectorHooks(hooks: any) {\n\tconst wrappedHooks: any = {}\n\n\tObject.keys(hooks).forEach((key) => {\n\t\tconst hook = hooks[key]\n\n\t\t// ref objects should be passed straight through without wrapping\n\t\tif (key.endsWith('Ref')) {\n\t\t\twrappedHooks[key] = hooks[key]\n\t\t} else {\n\t\t\tconst wrappedHook = wrapHookToRecognizeElement(hook)\n\t\t\twrappedHooks[key] = () => wrappedHook\n\t\t}\n\t})\n\n\treturn wrappedHooks\n}\n\nfunction setRef(ref: any, node: any) {\n\tif (typeof ref === 'function') {\n\t\tref(node)\n\t} else {\n\t\tref.current = node\n\t}\n}\n\nfunction cloneWithRef(element: any, newRef: any): ReactElement<any> {\n\tconst previousRef = element.ref\n\tinvariant(\n\t\ttypeof previousRef !== 'string',\n\t\t'Cannot connect React DnD to an element with an existing string ref. ' +\n\t\t\t'Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. ' +\n\t\t\t'Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs',\n\t)\n\n\tif (!previousRef) {\n\t\t// When there is no ref on the element, use the new ref directly\n\t\treturn cloneElement(element, {\n\t\t\tref: newRef,\n\t\t})\n\t} else {\n\t\treturn cloneElement(element, {\n\t\t\tref: (node: any) => {\n\t\t\t\tsetRef(previousRef, node)\n\t\t\t\tsetRef(newRef, node)\n\t\t\t},\n\t\t})\n\t}\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB;AAEhD,SAASC,YAAY,EAAEC,cAAc,QAAQ,OAAO;AAEpD,SAASC,gCAAgCA,CAACC,OAA0B,EAAE;EACrE;EACA;EACA,IAAI,OAAOA,OAAO,CAACC,IAAI,KAAK,QAAQ,EAAE;IACrC;;EAGD,MAAMC,WAAW,GAChBF,OAAQ,CAACC,IAAI,CAASC,WAAW,IAAIF,OAAO,CAACC,IAAI,CAACE,IAAI,IAAI,eAAe;EAE1E,MAAM,IAAIC,KAAK,CACd,sEAAsE,GACpE,uBAAsBF,WAAY,mCAAkC,GACrE,sCAAsC,CACvC;;AAGF,SAASG,0BAA0BA,CAACC,IAAuC,EAAE;EAC5E,OAAO,CAACC,aAAa,GAAG,IAAI,EAAEC,OAAO,GAAG,IAAI,KAAK;IAChD;IACA,IAAI,CAACV,cAAc,CAACS,aAAa,CAAC,EAAE;MACnC,MAAME,IAAI,GAAGF,aAAa;MAC1BD,IAAI,CAACG,IAAI,EAAED,OAAO,CAAC;MACnB;MACA;MACA,OAAOC,IAAI;;IAGZ;IACA;IACA;IACA,MAAMT,OAAO,GAAwBO,aAAa;IAClDR,gCAAgC,CAACC,OAAO,CAAQ;IAEhD;IACA,MAAMU,GAAG,GAAGF,OAAO,GAAIC,IAAa,IAAKH,IAAI,CAACG,IAAI,EAAED,OAAO,CAAC,GAAGF,IAAI;IACnE,OAAOK,YAAY,CAACX,OAAO,EAAEU,GAAG,CAAC;GACjC;;AAGF,OAAO,SAASE,kBAAkBA,CAACC,KAAU,EAAE;EAC9C,MAAMC,YAAY,GAAQ,EAAE;EAE5BC,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC,CAACI,OAAO,CAAEC,GAAG,IAAK;IACnC,MAAMZ,IAAI,GAAGO,KAAK,CAACK,GAAG,CAAC;IAEvB;IACA,IAAIA,GAAG,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;MACxBL,YAAY,CAACI,GAAG,CAAC,GAAGL,KAAK,CAACK,GAAG,CAAC;KAC9B,MAAM;MACN,MAAME,WAAW,GAAGf,0BAA0B,CAACC,IAAI,CAAC;MACpDQ,YAAY,CAACI,GAAG,CAAC,GAAG,MAAME,WAAW;;GAEtC,CAAC;EAEF,OAAON,YAAY;;AAGpB,SAASO,MAAMA,CAACX,GAAQ,EAAED,IAAS,EAAE;EACpC,IAAI,OAAOC,GAAG,KAAK,UAAU,EAAE;IAC9BA,GAAG,CAACD,IAAI,CAAC;GACT,MAAM;IACNC,GAAG,CAACY,OAAO,GAAGb,IAAI;;;AAIpB,SAASE,YAAYA,CAACX,OAAY,EAAEuB,MAAW,EAAqB;EACnE,MAAMC,WAAW,GAAGxB,OAAO,CAACU,GAAG;EAC/Bd,SAAS,CACR,OAAO4B,WAAW,KAAK,QAAQ,EAC/B,sEAAsE,GACrE,sFAAsF,GACtF,yEAAyE,CAC1E;EAED,IAAI,CAACA,WAAW,EAAE;IACjB;IACA,OAAO3B,YAAY,CAACG,OAAO,EAAE;MAC5BU,GAAG,EAAEa;KACL,CAAC;GACF,MAAM;IACN,OAAO1B,YAAY,CAACG,OAAO,EAAE;MAC5BU,GAAG,EAAGD,IAAS,IAAK;QACnBY,MAAM,CAACG,WAAW,EAAEf,IAAI,CAAC;QACzBY,MAAM,CAACE,MAAM,EAAEd,IAAI,CAAC;;KAErB,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}