{"ast": null, "code": "import { Subject } from '../Subject';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { fromSubscribable } from '../observable/fromSubscribable';\nvar DEFAULT_CONFIG = {\n  connector: function () {\n    return new Subject();\n  }\n};\nexport function connect(selector, config) {\n  if (config === void 0) {\n    config = DEFAULT_CONFIG;\n  }\n  var connector = config.connector;\n  return operate(function (source, subscriber) {\n    var subject = connector();\n    innerFrom(selector(fromSubscribable(subject))).subscribe(subscriber);\n    subscriber.add(source.subscribe(subject));\n  });\n}", "map": {"version": 3, "names": ["Subject", "innerFrom", "operate", "fromSubscribable", "DEFAULT_CONFIG", "connector", "connect", "selector", "config", "source", "subscriber", "subject", "subscribe", "add"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\connect.ts"], "sourcesContent": ["import { OperatorFunction, ObservableInput, ObservedValueOf, SubjectLike } from '../types';\nimport { Observable } from '../Observable';\nimport { Subject } from '../Subject';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { fromSubscribable } from '../observable/fromSubscribable';\n\n/**\n * An object used to configure {@link connect} operator.\n */\nexport interface ConnectConfig<T> {\n  /**\n   * A factory function used to create the Subject through which the source\n   * is multicast. By default, this creates a {@link Subject}.\n   */\n  connector: () => SubjectLike<T>;\n}\n\n/**\n * The default configuration for `connect`.\n */\nconst DEFAULT_CONFIG: ConnectConfig<unknown> = {\n  connector: () => new Subject<unknown>(),\n};\n\n/**\n * Creates an observable by multicasting the source within a function that\n * allows the developer to define the usage of the multicast prior to connection.\n *\n * This is particularly useful if the observable source you wish to multicast could\n * be synchronous or asynchronous. This sets it apart from {@link share}, which, in the\n * case of totally synchronous sources will fail to share a single subscription with\n * multiple consumers, as by the time the subscription to the result of {@link share}\n * has returned, if the source is synchronous its internal reference count will jump from\n * 0 to 1 back to 0 and reset.\n *\n * To use `connect`, you provide a `selector` function that will give you\n * a multicast observable that is not yet connected. You then use that multicast observable\n * to create a resulting observable that, when subscribed, will set up your multicast. This is\n * generally, but not always, accomplished with {@link merge}.\n *\n * Note that using a {@link takeUntil} inside of `connect`'s `selector` _might_ mean you were looking\n * to use the {@link takeWhile} operator instead.\n *\n * When you subscribe to the result of `connect`, the `selector` function will be called. After\n * the `selector` function returns, the observable it returns will be subscribed to, _then_ the\n * multicast will be connected to the source.\n *\n * ## Example\n *\n * Sharing a totally synchronous observable\n *\n * ```ts\n * import { of, tap, connect, merge, map, filter } from 'rxjs';\n *\n * const source$ = of(1, 2, 3, 4, 5).pipe(\n *   tap({\n *     subscribe: () => console.log('subscription started'),\n *     next: n => console.log(`source emitted ${ n }`)\n *   })\n * );\n *\n * source$.pipe(\n *   // Notice in here we're merging 3 subscriptions to `shared$`.\n *   connect(shared$ => merge(\n *     shared$.pipe(map(n => `all ${ n }`)),\n *     shared$.pipe(filter(n => n % 2 === 0), map(n => `even ${ n }`)),\n *     shared$.pipe(filter(n => n % 2 === 1), map(n => `odd ${ n }`))\n *   ))\n * )\n * .subscribe(console.log);\n *\n * // Expected output: (notice only one subscription)\n * 'subscription started'\n * 'source emitted 1'\n * 'all 1'\n * 'odd 1'\n * 'source emitted 2'\n * 'all 2'\n * 'even 2'\n * 'source emitted 3'\n * 'all 3'\n * 'odd 3'\n * 'source emitted 4'\n * 'all 4'\n * 'even 4'\n * 'source emitted 5'\n * 'all 5'\n * 'odd 5'\n * ```\n *\n * @param selector A function used to set up the multicast. Gives you a multicast observable\n * that is not yet connected. With that, you're expected to create and return\n * and Observable, that when subscribed to, will utilize the multicast observable.\n * After this function is executed -- and its return value subscribed to -- the\n * operator will subscribe to the source, and the connection will be made.\n * @param config The configuration object for `connect`.\n */\nexport function connect<T, O extends ObservableInput<unknown>>(\n  selector: (shared: Observable<T>) => O,\n  config: ConnectConfig<T> = DEFAULT_CONFIG\n): OperatorFunction<T, ObservedValueOf<O>> {\n  const { connector } = config;\n  return operate((source, subscriber) => {\n    const subject = connector();\n    innerFrom(selector(fromSubscribable(subject))).subscribe(subscriber);\n    subscriber.add(source.subscribe(subject));\n  });\n}\n"], "mappings": "AAEA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,gBAAgB,QAAQ,gCAAgC;AAgBjE,IAAMC,cAAc,GAA2B;EAC7CC,SAAS,EAAE,SAAAA,CAAA;IAAM,WAAIL,OAAO,EAAW;EAAtB;CAClB;AA2ED,OAAM,SAAUM,OAAOA,CACrBC,QAAsC,EACtCC,MAAyC;EAAzC,IAAAA,MAAA;IAAAA,MAAA,GAAAJ,cAAyC;EAAA;EAEjC,IAAAC,SAAS,GAAKG,MAAM,CAAAH,SAAX;EACjB,OAAOH,OAAO,CAAC,UAACO,MAAM,EAAEC,UAAU;IAChC,IAAMC,OAAO,GAAGN,SAAS,EAAE;IAC3BJ,SAAS,CAACM,QAAQ,CAACJ,gBAAgB,CAACQ,OAAO,CAAC,CAAC,CAAC,CAACC,SAAS,CAACF,UAAU,CAAC;IACpEA,UAAU,CAACG,GAAG,CAACJ,MAAM,CAACG,SAAS,CAACD,OAAO,CAAC,CAAC;EAC3C,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}