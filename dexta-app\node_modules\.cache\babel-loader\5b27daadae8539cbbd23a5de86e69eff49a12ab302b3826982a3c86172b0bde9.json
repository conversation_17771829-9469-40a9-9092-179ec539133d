{"ast": null, "code": "function ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nimport parse from './parse.js';\nexport default function parsePhoneNumberWithError(text, options, metadata) {\n  return parse(text, _objectSpread(_objectSpread({}, options), {}, {\n    v2: true\n  }), metadata);\n}", "map": {"version": 3, "names": ["parse", "parsePhoneNumberWithError", "text", "options", "metadata", "_objectSpread", "v2"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\parsePhoneNumberWithError_.js"], "sourcesContent": ["import parse from './parse.js'\r\n\r\nexport default function parsePhoneNumberWithError(text, options, metadata) {\r\n\treturn parse(text, { ...options, v2: true }, metadata)\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAP,MAAkB,YAAlB;AAEA,eAAe,SAASC,yBAATA,CAAmCC,IAAnC,EAAyCC,OAAzC,EAAkDC,QAAlD,EAA4D;EAC1E,OAAOJ,KAAK,CAACE,IAAD,EAAAG,aAAA,CAAAA,aAAA,KAAYF,OAAZ;IAAqBG,EAAE,EAAE;EAAzB,IAAiCF,QAAjC,CAAZ;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}