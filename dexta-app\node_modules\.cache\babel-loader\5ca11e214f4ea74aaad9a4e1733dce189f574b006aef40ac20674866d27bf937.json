{"ast": null, "code": "import memoize from 'lodash.memoize';\nfunction resolver(options) {\n  return JSON.stringify(options);\n}\nfunction isString(el) {\n  return typeof el === 'string';\n}\nfunction isUnique(el, index, arr) {\n  return arr.indexOf(el) === index;\n}\nfunction isAllLowerCase(el) {\n  return el.toLowerCase() === el;\n}\nfunction fixCommas(el) {\n  return el.indexOf(',') === -1 ? el : el.split(',');\n}\nfunction normalizeLocale(locale) {\n  if (!locale) {\n    return locale;\n  }\n  if (locale === 'C' || locale === 'posix' || locale === 'POSIX') {\n    return 'en-US';\n  }\n  // If there's a dot (.) in the locale, it's likely in the format of \"en-US.UTF-8\", so we only take the first part\n  if (locale.indexOf('.') !== -1) {\n    var _a = locale.split('.')[0],\n      actualLocale = _a === void 0 ? '' : _a;\n    return normalizeLocale(actualLocale);\n  }\n  // If there's an at sign (@) in the locale, it's likely in the format of \"en-US@posix\", so we only take the first part\n  if (locale.indexOf('@') !== -1) {\n    var _b = locale.split('@')[0],\n      actualLocale = _b === void 0 ? '' : _b;\n    return normalizeLocale(actualLocale);\n  }\n  // If there's a dash (-) in the locale and it's not all lower case, it's already in the format of \"en-US\", so we return it\n  if (locale.indexOf('-') === -1 || !isAllLowerCase(locale)) {\n    return locale;\n  }\n  var _c = locale.split('-'),\n    splitEl1 = _c[0],\n    _d = _c[1],\n    splitEl2 = _d === void 0 ? '' : _d;\n  return \"\".concat(splitEl1, \"-\").concat(splitEl2.toUpperCase());\n}\nfunction getUserLocalesInternal(_a) {\n  var _b = _a === void 0 ? {} : _a,\n    _c = _b.useFallbackLocale,\n    useFallbackLocale = _c === void 0 ? true : _c,\n    _d = _b.fallbackLocale,\n    fallbackLocale = _d === void 0 ? 'en-US' : _d;\n  var languageList = [];\n  if (typeof navigator !== 'undefined') {\n    var rawLanguages = navigator.languages || [];\n    var languages = [];\n    for (var _i = 0, rawLanguages_1 = rawLanguages; _i < rawLanguages_1.length; _i++) {\n      var rawLanguagesItem = rawLanguages_1[_i];\n      languages = languages.concat(fixCommas(rawLanguagesItem));\n    }\n    var rawLanguage = navigator.language;\n    var language = rawLanguage ? fixCommas(rawLanguage) : rawLanguage;\n    languageList = languageList.concat(languages, language);\n  }\n  if (useFallbackLocale) {\n    languageList.push(fallbackLocale);\n  }\n  return languageList.filter(isString).map(normalizeLocale).filter(isUnique);\n}\nexport var getUserLocales = memoize(getUserLocalesInternal, resolver);\nfunction getUserLocaleInternal(options) {\n  return getUserLocales(options)[0] || null;\n}\nexport var getUserLocale = memoize(getUserLocaleInternal, resolver);\nexport default getUserLocale;", "map": {"version": 3, "names": ["memoize", "resolver", "options", "JSON", "stringify", "isString", "el", "isUnique", "index", "arr", "indexOf", "isAllLowerCase", "toLowerCase", "fixCommas", "split", "normalizeLocale", "locale", "_a", "actualLocale", "_b", "_c", "splitEl1", "_d", "splitEl2", "concat", "toUpperCase", "getUserLocalesInternal", "useFallbackLocale", "fallback<PERSON><PERSON><PERSON>", "languageList", "navigator", "rawLanguages", "languages", "_i", "rawLanguages_1", "length", "rawLanguagesItem", "rawLanguage", "language", "push", "filter", "map", "getUserLocales", "getUserLocaleInternal", "getUserLocale"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/get-user-locale/dist/esm/index.js"], "sourcesContent": ["import memoize from 'lodash.memoize';\nfunction resolver(options) {\n    return JSON.stringify(options);\n}\nfunction isString(el) {\n    return typeof el === 'string';\n}\nfunction isUnique(el, index, arr) {\n    return arr.indexOf(el) === index;\n}\nfunction isAllLowerCase(el) {\n    return el.toLowerCase() === el;\n}\nfunction fixCommas(el) {\n    return el.indexOf(',') === -1 ? el : el.split(',');\n}\nfunction normalizeLocale(locale) {\n    if (!locale) {\n        return locale;\n    }\n    if (locale === 'C' || locale === 'posix' || locale === 'POSIX') {\n        return 'en-US';\n    }\n    // If there's a dot (.) in the locale, it's likely in the format of \"en-US.UTF-8\", so we only take the first part\n    if (locale.indexOf('.') !== -1) {\n        var _a = locale.split('.')[0], actualLocale = _a === void 0 ? '' : _a;\n        return normalizeLocale(actualLocale);\n    }\n    // If there's an at sign (@) in the locale, it's likely in the format of \"en-US@posix\", so we only take the first part\n    if (locale.indexOf('@') !== -1) {\n        var _b = locale.split('@')[0], actualLocale = _b === void 0 ? '' : _b;\n        return normalizeLocale(actualLocale);\n    }\n    // If there's a dash (-) in the locale and it's not all lower case, it's already in the format of \"en-US\", so we return it\n    if (locale.indexOf('-') === -1 || !isAllLowerCase(locale)) {\n        return locale;\n    }\n    var _c = locale.split('-'), splitEl1 = _c[0], _d = _c[1], splitEl2 = _d === void 0 ? '' : _d;\n    return \"\".concat(splitEl1, \"-\").concat(splitEl2.toUpperCase());\n}\nfunction getUserLocalesInternal(_a) {\n    var _b = _a === void 0 ? {} : _a, _c = _b.useFallbackLocale, useFallbackLocale = _c === void 0 ? true : _c, _d = _b.fallbackLocale, fallbackLocale = _d === void 0 ? 'en-US' : _d;\n    var languageList = [];\n    if (typeof navigator !== 'undefined') {\n        var rawLanguages = navigator.languages || [];\n        var languages = [];\n        for (var _i = 0, rawLanguages_1 = rawLanguages; _i < rawLanguages_1.length; _i++) {\n            var rawLanguagesItem = rawLanguages_1[_i];\n            languages = languages.concat(fixCommas(rawLanguagesItem));\n        }\n        var rawLanguage = navigator.language;\n        var language = rawLanguage ? fixCommas(rawLanguage) : rawLanguage;\n        languageList = languageList.concat(languages, language);\n    }\n    if (useFallbackLocale) {\n        languageList.push(fallbackLocale);\n    }\n    return languageList.filter(isString).map(normalizeLocale).filter(isUnique);\n}\nexport var getUserLocales = memoize(getUserLocalesInternal, resolver);\nfunction getUserLocaleInternal(options) {\n    return getUserLocales(options)[0] || null;\n}\nexport var getUserLocale = memoize(getUserLocaleInternal, resolver);\nexport default getUserLocale;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,gBAAgB;AACpC,SAASC,QAAQA,CAACC,OAAO,EAAE;EACvB,OAAOC,IAAI,CAACC,SAAS,CAACF,OAAO,CAAC;AAClC;AACA,SAASG,QAAQA,CAACC,EAAE,EAAE;EAClB,OAAO,OAAOA,EAAE,KAAK,QAAQ;AACjC;AACA,SAASC,QAAQA,CAACD,EAAE,EAAEE,KAAK,EAAEC,GAAG,EAAE;EAC9B,OAAOA,GAAG,CAACC,OAAO,CAACJ,EAAE,CAAC,KAAKE,KAAK;AACpC;AACA,SAASG,cAAcA,CAACL,EAAE,EAAE;EACxB,OAAOA,EAAE,CAACM,WAAW,CAAC,CAAC,KAAKN,EAAE;AAClC;AACA,SAASO,SAASA,CAACP,EAAE,EAAE;EACnB,OAAOA,EAAE,CAACI,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAGJ,EAAE,GAAGA,EAAE,CAACQ,KAAK,CAAC,GAAG,CAAC;AACtD;AACA,SAASC,eAAeA,CAACC,MAAM,EAAE;EAC7B,IAAI,CAACA,MAAM,EAAE;IACT,OAAOA,MAAM;EACjB;EACA,IAAIA,MAAM,KAAK,GAAG,IAAIA,MAAM,KAAK,OAAO,IAAIA,MAAM,KAAK,OAAO,EAAE;IAC5D,OAAO,OAAO;EAClB;EACA;EACA,IAAIA,MAAM,CAACN,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IAC5B,IAAIO,EAAE,GAAGD,MAAM,CAACF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAAEI,YAAY,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IACrE,OAAOF,eAAe,CAACG,YAAY,CAAC;EACxC;EACA;EACA,IAAIF,MAAM,CAACN,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IAC5B,IAAIS,EAAE,GAAGH,MAAM,CAACF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MAAEI,YAAY,GAAGC,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IACrE,OAAOJ,eAAe,CAACG,YAAY,CAAC;EACxC;EACA;EACA,IAAIF,MAAM,CAACN,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAACC,cAAc,CAACK,MAAM,CAAC,EAAE;IACvD,OAAOA,MAAM;EACjB;EACA,IAAII,EAAE,GAAGJ,MAAM,CAACF,KAAK,CAAC,GAAG,CAAC;IAAEO,QAAQ,GAAGD,EAAE,CAAC,CAAC,CAAC;IAAEE,EAAE,GAAGF,EAAE,CAAC,CAAC,CAAC;IAAEG,QAAQ,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;EAC5F,OAAO,EAAE,CAACE,MAAM,CAACH,QAAQ,EAAE,GAAG,CAAC,CAACG,MAAM,CAACD,QAAQ,CAACE,WAAW,CAAC,CAAC,CAAC;AAClE;AACA,SAASC,sBAAsBA,CAACT,EAAE,EAAE;EAChC,IAAIE,EAAE,GAAGF,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,EAAE;IAAEG,EAAE,GAAGD,EAAE,CAACQ,iBAAiB;IAAEA,iBAAiB,GAAGP,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;IAAEE,EAAE,GAAGH,EAAE,CAACS,cAAc;IAAEA,cAAc,GAAGN,EAAE,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,EAAE;EACjL,IAAIO,YAAY,GAAG,EAAE;EACrB,IAAI,OAAOC,SAAS,KAAK,WAAW,EAAE;IAClC,IAAIC,YAAY,GAAGD,SAAS,CAACE,SAAS,IAAI,EAAE;IAC5C,IAAIA,SAAS,GAAG,EAAE;IAClB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEC,cAAc,GAAGH,YAAY,EAAEE,EAAE,GAAGC,cAAc,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC9E,IAAIG,gBAAgB,GAAGF,cAAc,CAACD,EAAE,CAAC;MACzCD,SAAS,GAAGA,SAAS,CAACR,MAAM,CAACX,SAAS,CAACuB,gBAAgB,CAAC,CAAC;IAC7D;IACA,IAAIC,WAAW,GAAGP,SAAS,CAACQ,QAAQ;IACpC,IAAIA,QAAQ,GAAGD,WAAW,GAAGxB,SAAS,CAACwB,WAAW,CAAC,GAAGA,WAAW;IACjER,YAAY,GAAGA,YAAY,CAACL,MAAM,CAACQ,SAAS,EAAEM,QAAQ,CAAC;EAC3D;EACA,IAAIX,iBAAiB,EAAE;IACnBE,YAAY,CAACU,IAAI,CAACX,cAAc,CAAC;EACrC;EACA,OAAOC,YAAY,CAACW,MAAM,CAACnC,QAAQ,CAAC,CAACoC,GAAG,CAAC1B,eAAe,CAAC,CAACyB,MAAM,CAACjC,QAAQ,CAAC;AAC9E;AACA,OAAO,IAAImC,cAAc,GAAG1C,OAAO,CAAC0B,sBAAsB,EAAEzB,QAAQ,CAAC;AACrE,SAAS0C,qBAAqBA,CAACzC,OAAO,EAAE;EACpC,OAAOwC,cAAc,CAACxC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;AAC7C;AACA,OAAO,IAAI0C,aAAa,GAAG5C,OAAO,CAAC2C,qBAAqB,EAAE1C,QAAQ,CAAC;AACnE,eAAe2C,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}