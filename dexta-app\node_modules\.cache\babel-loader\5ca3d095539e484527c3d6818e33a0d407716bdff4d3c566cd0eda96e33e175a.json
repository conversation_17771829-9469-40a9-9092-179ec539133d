{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\n\n// Should be the same as `DIGIT_PLACEHOLDER` in `libphonenumber-metadata-generator`.\nexport var DIGIT_PLACEHOLDER = 'x'; // '\\u2008' (punctuation space)\n\nvar DIGIT_PLACEHOLDER_MATCHER = new RegExp(DIGIT_PLACEHOLDER); // Counts all occurences of a symbol in a string.\n// Unicode-unsafe (because using `.split()`).\n\nexport function countOccurences(symbol, string) {\n  var count = 0; // Using `.split('')` to iterate through a string here\n  // to avoid requiring `Symbol.iterator` polyfill.\n  // `.split('')` is generally not safe for Unicode,\n  // but in this particular case for counting brackets it is safe.\n  // for (const character of string)\n\n  for (var _iterator = _createForOfIteratorHelperLoose(string.split('')), _step; !(_step = _iterator()).done;) {\n    var character = _step.value;\n    if (character === symbol) {\n      count++;\n    }\n  }\n  return count;\n} // Repeats a string (or a symbol) N times.\n// http://stackoverflow.com/questions/202605/repeat-string-javascript\n\nexport function repeat(string, times) {\n  if (times < 1) {\n    return '';\n  }\n  var result = '';\n  while (times > 1) {\n    if (times & 1) {\n      result += string;\n    }\n    times >>= 1;\n    string += string;\n  }\n  return result + string;\n}\nexport function cutAndStripNonPairedParens(string, cutBeforeIndex) {\n  if (string[cutBeforeIndex] === ')') {\n    cutBeforeIndex++;\n  }\n  return stripNonPairedParens(string.slice(0, cutBeforeIndex));\n}\nexport function closeNonPairedParens(template, cut_before) {\n  var retained_template = template.slice(0, cut_before);\n  var opening_braces = countOccurences('(', retained_template);\n  var closing_braces = countOccurences(')', retained_template);\n  var dangling_braces = opening_braces - closing_braces;\n  while (dangling_braces > 0 && cut_before < template.length) {\n    if (template[cut_before] === ')') {\n      dangling_braces--;\n    }\n    cut_before++;\n  }\n  return template.slice(0, cut_before);\n}\nexport function stripNonPairedParens(string) {\n  var dangling_braces = [];\n  var i = 0;\n  while (i < string.length) {\n    if (string[i] === '(') {\n      dangling_braces.push(i);\n    } else if (string[i] === ')') {\n      dangling_braces.pop();\n    }\n    i++;\n  }\n  var start = 0;\n  var cleared_string = '';\n  dangling_braces.push(string.length);\n  for (var _i = 0, _dangling_braces = dangling_braces; _i < _dangling_braces.length; _i++) {\n    var index = _dangling_braces[_i];\n    cleared_string += string.slice(start, index);\n    start = index + 1;\n  }\n  return cleared_string;\n}\nexport function populateTemplateWithDigits(template, position, digits) {\n  // Using `.split('')` to iterate through a string here\n  // to avoid requiring `Symbol.iterator` polyfill.\n  // `.split('')` is generally not safe for Unicode,\n  // but in this particular case for `digits` it is safe.\n  // for (const digit of digits)\n  for (var _iterator2 = _createForOfIteratorHelperLoose(digits.split('')), _step2; !(_step2 = _iterator2()).done;) {\n    var digit = _step2.value;\n\n    // If there is room for more digits in current `template`,\n    // then set the next digit in the `template`,\n    // and return the formatted digits so far.\n    // If more digits are entered than the current format could handle.\n    if (template.slice(position + 1).search(DIGIT_PLACEHOLDER_MATCHER) < 0) {\n      return;\n    }\n    position = template.search(DIGIT_PLACEHOLDER_MATCHER);\n    template = template.replace(DIGIT_PLACEHOLDER_MATCHER, digit);\n  }\n  return [template, position];\n}", "map": {"version": 3, "names": ["DIGIT_PLACEHOLDER", "DIGIT_PLACEHOLDER_MATCHER", "RegExp", "countOccurences", "symbol", "string", "count", "_iterator", "_createForOfIteratorHelperLoose", "split", "_step", "done", "character", "value", "repeat", "times", "result", "cutAndStripNonPairedParens", "cutBeforeIndex", "stripNonPairedParens", "slice", "closeNonPairedParens", "template", "cut_before", "retained_template", "opening_braces", "closing_braces", "dangling_braces", "length", "i", "push", "pop", "start", "cleared_string", "_i", "_dangling_braces", "index", "populateTemplateWithDigits", "position", "digits", "_iterator2", "_step2", "digit", "search", "replace"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\AsYouTypeFormatter.util.js"], "sourcesContent": ["// Should be the same as `DIGIT_PLACEHOLDER` in `libphonenumber-metadata-generator`.\r\nexport const DIGIT_PLACEHOLDER = 'x' // '\\u2008' (punctuation space)\r\nconst DIGIT_PLACEHOLDER_MATCHER = new RegExp(DIGIT_PLACEHOLDER)\r\n\r\n// Counts all occurences of a symbol in a string.\r\n// Unicode-unsafe (because using `.split()`).\r\nexport function countOccurences(symbol, string) {\r\n\tlet count = 0\r\n\t// Using `.split('')` to iterate through a string here\r\n\t// to avoid requiring `Symbol.iterator` polyfill.\r\n\t// `.split('')` is generally not safe for Unicode,\r\n\t// but in this particular case for counting brackets it is safe.\r\n\t// for (const character of string)\r\n\tfor (const character of string.split('')) {\r\n\t\tif (character === symbol) {\r\n\t\t\tcount++\r\n\t\t}\r\n\t}\r\n\treturn count\r\n}\r\n\r\n// Repeats a string (or a symbol) N times.\r\n// http://stackoverflow.com/questions/202605/repeat-string-javascript\r\nexport function repeat(string, times) {\r\n\tif (times < 1) {\r\n\t\treturn ''\r\n\t}\r\n\tlet result = ''\r\n\twhile (times > 1) {\r\n\t\tif (times & 1) {\r\n\t\t\tresult += string\r\n\t\t}\r\n\t\ttimes >>= 1\r\n\t\tstring += string\r\n\t}\r\n\treturn result + string\r\n}\r\n\r\nexport function cutAndStripNonPairedParens(string, cutBeforeIndex) {\r\n\tif (string[cutBeforeIndex] === ')') {\r\n\t\tcutBeforeIndex++\r\n\t}\r\n\treturn stripNonPairedParens(string.slice(0, cutBeforeIndex))\r\n}\r\n\r\nexport function closeNonPairedParens(template, cut_before) {\r\n\tconst retained_template = template.slice(0, cut_before)\r\n\tconst opening_braces = countOccurences('(', retained_template)\r\n\tconst closing_braces = countOccurences(')', retained_template)\r\n\tlet dangling_braces = opening_braces - closing_braces\r\n\twhile (dangling_braces > 0 && cut_before < template.length) {\r\n\t\tif (template[cut_before] === ')') {\r\n\t\t\tdangling_braces--\r\n\t\t}\r\n\t\tcut_before++\r\n\t}\r\n\treturn template.slice(0, cut_before)\r\n}\r\n\r\nexport function stripNonPairedParens(string) {\r\n\tconst dangling_braces =[]\r\n\tlet i = 0\r\n\twhile (i < string.length) {\r\n\t\tif (string[i] === '(') {\r\n\t\t\tdangling_braces.push(i)\r\n\t\t}\r\n\t\telse if (string[i] === ')') {\r\n\t\t\tdangling_braces.pop()\r\n\t\t}\r\n\t\ti++\r\n\t}\r\n\tlet start = 0\r\n\tlet cleared_string = ''\r\n\tdangling_braces.push(string.length)\r\n\tfor (const index of dangling_braces) {\r\n\t\tcleared_string += string.slice(start, index)\r\n\t\tstart = index + 1\r\n\t}\r\n\treturn cleared_string\r\n}\r\n\r\nexport function populateTemplateWithDigits(template, position, digits) {\r\n\t// Using `.split('')` to iterate through a string here\r\n\t// to avoid requiring `Symbol.iterator` polyfill.\r\n\t// `.split('')` is generally not safe for Unicode,\r\n\t// but in this particular case for `digits` it is safe.\r\n\t// for (const digit of digits)\r\n\tfor (const digit of digits.split('')) {\r\n\t\t// If there is room for more digits in current `template`,\r\n\t\t// then set the next digit in the `template`,\r\n\t\t// and return the formatted digits so far.\r\n\t\t// If more digits are entered than the current format could handle.\r\n\t\tif (template.slice(position + 1).search(DIGIT_PLACEHOLDER_MATCHER) < 0) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\tposition = template.search(DIGIT_PLACEHOLDER_MATCHER)\r\n\t\ttemplate = template.replace(DIGIT_PLACEHOLDER_MATCHER, digit)\r\n\t}\r\n\treturn [template, position]\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA,OAAO,IAAMA,iBAAiB,GAAG,GAA1B,C,CAA8B;;AACrC,IAAMC,yBAAyB,GAAG,IAAIC,MAAJ,CAAWF,iBAAX,CAAlC,C,CAEA;AACA;;AACA,OAAO,SAASG,eAATA,CAAyBC,MAAzB,EAAiCC,MAAjC,EAAyC;EAC/C,IAAIC,KAAK,GAAG,CAAZ,CAD+C,CAE/C;EACA;EACA;EACA;EACA;;EACA,SAAAC,SAAA,GAAAC,+BAAA,CAAwBH,MAAM,CAACI,KAAP,CAAa,EAAb,CAAxB,GAAAC,KAAA,IAAAA,KAAA,GAAAH,SAAA,IAAAI,IAAA,GAA0C;IAAA,IAA/BC,SAA+B,GAAAF,KAAA,CAAAG,KAAA;IACzC,IAAID,SAAS,KAAKR,MAAlB,EAA0B;MACzBE,KAAK;IACL;EACD;EACD,OAAOA,KAAP;AACA,C,CAED;AACA;;AACA,OAAO,SAASQ,MAATA,CAAgBT,MAAhB,EAAwBU,KAAxB,EAA+B;EACrC,IAAIA,KAAK,GAAG,CAAZ,EAAe;IACd,OAAO,EAAP;EACA;EACD,IAAIC,MAAM,GAAG,EAAb;EACA,OAAOD,KAAK,GAAG,CAAf,EAAkB;IACjB,IAAIA,KAAK,GAAG,CAAZ,EAAe;MACdC,MAAM,IAAIX,MAAV;IACA;IACDU,KAAK,KAAK,CAAV;IACAV,MAAM,IAAIA,MAAV;EACA;EACD,OAAOW,MAAM,GAAGX,MAAhB;AACA;AAED,OAAO,SAASY,0BAATA,CAAoCZ,MAApC,EAA4Ca,cAA5C,EAA4D;EAClE,IAAIb,MAAM,CAACa,cAAD,CAAN,KAA2B,GAA/B,EAAoC;IACnCA,cAAc;EACd;EACD,OAAOC,oBAAoB,CAACd,MAAM,CAACe,KAAP,CAAa,CAAb,EAAgBF,cAAhB,CAAD,CAA3B;AACA;AAED,OAAO,SAASG,oBAATA,CAA8BC,QAA9B,EAAwCC,UAAxC,EAAoD;EAC1D,IAAMC,iBAAiB,GAAGF,QAAQ,CAACF,KAAT,CAAe,CAAf,EAAkBG,UAAlB,CAA1B;EACA,IAAME,cAAc,GAAGtB,eAAe,CAAC,GAAD,EAAMqB,iBAAN,CAAtC;EACA,IAAME,cAAc,GAAGvB,eAAe,CAAC,GAAD,EAAMqB,iBAAN,CAAtC;EACA,IAAIG,eAAe,GAAGF,cAAc,GAAGC,cAAvC;EACA,OAAOC,eAAe,GAAG,CAAlB,IAAuBJ,UAAU,GAAGD,QAAQ,CAACM,MAApD,EAA4D;IAC3D,IAAIN,QAAQ,CAACC,UAAD,CAAR,KAAyB,GAA7B,EAAkC;MACjCI,eAAe;IACf;IACDJ,UAAU;EACV;EACD,OAAOD,QAAQ,CAACF,KAAT,CAAe,CAAf,EAAkBG,UAAlB,CAAP;AACA;AAED,OAAO,SAASJ,oBAATA,CAA8Bd,MAA9B,EAAsC;EAC5C,IAAMsB,eAAe,GAAE,EAAvB;EACA,IAAIE,CAAC,GAAG,CAAR;EACA,OAAOA,CAAC,GAAGxB,MAAM,CAACuB,MAAlB,EAA0B;IACzB,IAAIvB,MAAM,CAACwB,CAAD,CAAN,KAAc,GAAlB,EAAuB;MACtBF,eAAe,CAACG,IAAhB,CAAqBD,CAArB;IACA,CAFD,MAGK,IAAIxB,MAAM,CAACwB,CAAD,CAAN,KAAc,GAAlB,EAAuB;MAC3BF,eAAe,CAACI,GAAhB;IACA;IACDF,CAAC;EACD;EACD,IAAIG,KAAK,GAAG,CAAZ;EACA,IAAIC,cAAc,GAAG,EAArB;EACAN,eAAe,CAACG,IAAhB,CAAqBzB,MAAM,CAACuB,MAA5B;EACA,SAAAM,EAAA,MAAAC,gBAAA,GAAoBR,eAApB,EAAAO,EAAA,GAAAC,gBAAA,CAAAP,MAAA,EAAAM,EAAA,IAAqC;IAAhC,IAAME,KAAK,GAAAD,gBAAA,CAAAD,EAAA,CAAX;IACJD,cAAc,IAAI5B,MAAM,CAACe,KAAP,CAAaY,KAAb,EAAoBI,KAApB,CAAlB;IACAJ,KAAK,GAAGI,KAAK,GAAG,CAAhB;EACA;EACD,OAAOH,cAAP;AACA;AAED,OAAO,SAASI,0BAATA,CAAoCf,QAApC,EAA8CgB,QAA9C,EAAwDC,MAAxD,EAAgE;EACtE;EACA;EACA;EACA;EACA;EACA,SAAAC,UAAA,GAAAhC,+BAAA,CAAoB+B,MAAM,CAAC9B,KAAP,CAAa,EAAb,CAApB,GAAAgC,MAAA,IAAAA,MAAA,GAAAD,UAAA,IAAA7B,IAAA,GAAsC;IAAA,IAA3B+B,KAA2B,GAAAD,MAAA,CAAA5B,KAAA;;IACrC;IACA;IACA;IACA;IACA,IAAIS,QAAQ,CAACF,KAAT,CAAekB,QAAQ,GAAG,CAA1B,EAA6BK,MAA7B,CAAoC1C,yBAApC,IAAiE,CAArE,EAAwE;MACvE;IACA;IACDqC,QAAQ,GAAGhB,QAAQ,CAACqB,MAAT,CAAgB1C,yBAAhB,CAAX;IACAqB,QAAQ,GAAGA,QAAQ,CAACsB,OAAT,CAAiB3C,yBAAjB,EAA4CyC,KAA5C,CAAX;EACA;EACD,OAAO,CAACpB,QAAD,EAAWgB,QAAX,CAAP;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}