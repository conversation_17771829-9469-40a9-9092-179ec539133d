{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { zip } from './zip';\nexport function zipWith() {\n  var otherInputs = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    otherInputs[_i] = arguments[_i];\n  }\n  return zip.apply(void 0, __spreadArray([], __read(otherInputs)));\n}", "map": {"version": 3, "names": ["zip", "zipWith", "otherInputs", "_i", "arguments", "length", "apply", "__spread<PERSON><PERSON>y", "__read"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\zipWith.ts"], "sourcesContent": ["import { ObservableInputTuple, OperatorFunction, Cons } from '../types';\nimport { zip } from './zip';\n\n/**\n * Subscribes to the source, and the observable inputs provided as arguments, and combines their values, by index, into arrays.\n *\n * What is meant by \"combine by index\": The first value from each will be made into a single array, then emitted,\n * then the second value from each will be combined into a single array and emitted, then the third value\n * from each will be combined into a single array and emitted, and so on.\n *\n * This will continue until it is no longer able to combine values of the same index into an array.\n *\n * After the last value from any one completed source is emitted in an array, the resulting observable will complete,\n * as there is no way to continue \"zipping\" values together by index.\n *\n * Use-cases for this operator are limited. There are memory concerns if one of the streams is emitting\n * values at a much faster rate than the others. Usage should likely be limited to streams that emit\n * at a similar pace, or finite streams of known length.\n *\n * In many cases, authors want `combineLatestWith` and not `zipWith`.\n *\n * @param otherInputs other observable inputs to collate values from.\n * @return A function that returns an Observable that emits items by index\n * combined from the source Observable and provided Observables, in form of an\n * array.\n */\nexport function zipWith<T, A extends readonly unknown[]>(...otherInputs: [...ObservableInputTuple<A>]): OperatorFunction<T, Cons<T, A>> {\n  return zip(...otherInputs);\n}\n"], "mappings": ";AACA,SAASA,GAAG,QAAQ,OAAO;AAyB3B,OAAM,SAAUC,OAAOA,CAAA;EAAkC,IAAAC,WAAA;OAAA,IAAAC,EAAA,IAA4C,EAA5CA,EAAA,GAAAC,SAAA,CAAAC,MAA4C,EAA5CF,EAAA,EAA4C;IAA5CD,WAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EACvD,OAAOH,GAAG,CAAAM,KAAA,SAAAC,aAAA,KAAAC,MAAA,CAAIN,WAAW;AAC3B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}