{"ast": null, "code": "import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport sliderUnstyledClasses from './sliderUnstyledClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useValueLabelClasses = props => {\n  const {\n    open\n  } = props;\n  const utilityClasses = {\n    offset: clsx(open && sliderUnstyledClasses.valueLabelOpen),\n    circle: sliderUnstyledClasses.valueLabelCircle,\n    label: sliderUnstyledClasses.valueLabelLabel\n  };\n  return utilityClasses;\n};\n/**\n * @ignore - internal component.\n */\n\nexport default function SliderValueLabelUnstyled(props) {\n  const {\n    children,\n    className,\n    value,\n    theme\n  } = props;\n  const classes = useValueLabelClasses(props);\n  return /*#__PURE__*/React.cloneElement(children, {\n    className: clsx(children.props.className)\n  }, /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [children.props.children, /*#__PURE__*/_jsx(\"span\", {\n      className: clsx(classes.offset, className),\n      theme: theme,\n      \"aria-hidden\": true,\n      children: /*#__PURE__*/_jsx(\"span\", {\n        className: classes.circle,\n        children: /*#__PURE__*/_jsx(\"span\", {\n          className: classes.label,\n          children: value\n        })\n      })\n    })]\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? SliderValueLabelUnstyled.propTypes = {\n  children: PropTypes.element.isRequired,\n  className: PropTypes.string,\n  theme: PropTypes.any,\n  value: PropTypes.node\n} : void 0;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "sliderUnstyledClasses", "jsx", "_jsx", "jsxs", "_jsxs", "useValueLabelClasses", "props", "open", "utilityClasses", "offset", "valueLabelOpen", "circle", "valueLabelCircle", "label", "valueLabelLabel", "SliderValueLabelUnstyled", "children", "className", "value", "theme", "classes", "cloneElement", "Fragment", "process", "env", "NODE_ENV", "propTypes", "element", "isRequired", "string", "any", "node"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/SliderUnstyled/SliderValueLabelUnstyled.js"], "sourcesContent": ["import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport sliderUnstyledClasses from './sliderUnstyledClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\n\nconst useValueLabelClasses = props => {\n  const {\n    open\n  } = props;\n  const utilityClasses = {\n    offset: clsx(open && sliderUnstyledClasses.valueLabelOpen),\n    circle: sliderUnstyledClasses.valueLabelCircle,\n    label: sliderUnstyledClasses.valueLabelLabel\n  };\n  return utilityClasses;\n};\n/**\n * @ignore - internal component.\n */\n\n\nexport default function SliderValueLabelUnstyled(props) {\n  const {\n    children,\n    className,\n    value,\n    theme\n  } = props;\n  const classes = useValueLabelClasses(props);\n  return /*#__PURE__*/React.cloneElement(children, {\n    className: clsx(children.props.className)\n  }, /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [children.props.children, /*#__PURE__*/_jsx(\"span\", {\n      className: clsx(classes.offset, className),\n      theme: theme,\n      \"aria-hidden\": true,\n      children: /*#__PURE__*/_jsx(\"span\", {\n        className: classes.circle,\n        children: /*#__PURE__*/_jsx(\"span\", {\n          className: classes.label,\n          children: value\n        })\n      })\n    })]\n  }));\n}\nprocess.env.NODE_ENV !== \"production\" ? SliderValueLabelUnstyled.propTypes = {\n  children: PropTypes.element.isRequired,\n  className: PropTypes.string,\n  theme: PropTypes.any,\n  value: PropTypes.node\n} : void 0;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAEjD,MAAMC,oBAAoB,GAAGC,KAAK,IAAI;EACpC,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,MAAME,cAAc,GAAG;IACrBC,MAAM,EAAEV,IAAI,CAACQ,IAAI,IAAIP,qBAAqB,CAACU,cAAc,CAAC;IAC1DC,MAAM,EAAEX,qBAAqB,CAACY,gBAAgB;IAC9CC,KAAK,EAAEb,qBAAqB,CAACc;EAC/B,CAAC;EACD,OAAON,cAAc;AACvB,CAAC;AACD;AACA;AACA;;AAGA,eAAe,SAASO,wBAAwBA,CAACT,KAAK,EAAE;EACtD,MAAM;IACJU,QAAQ;IACRC,SAAS;IACTC,KAAK;IACLC;EACF,CAAC,GAAGb,KAAK;EACT,MAAMc,OAAO,GAAGf,oBAAoB,CAACC,KAAK,CAAC;EAC3C,OAAO,aAAaT,KAAK,CAACwB,YAAY,CAACL,QAAQ,EAAE;IAC/CC,SAAS,EAAElB,IAAI,CAACiB,QAAQ,CAACV,KAAK,CAACW,SAAS;EAC1C,CAAC,EAAE,aAAab,KAAK,CAACP,KAAK,CAACyB,QAAQ,EAAE;IACpCN,QAAQ,EAAE,CAACA,QAAQ,CAACV,KAAK,CAACU,QAAQ,EAAE,aAAad,IAAI,CAAC,MAAM,EAAE;MAC5De,SAAS,EAAElB,IAAI,CAACqB,OAAO,CAACX,MAAM,EAAEQ,SAAS,CAAC;MAC1CE,KAAK,EAAEA,KAAK;MACZ,aAAa,EAAE,IAAI;MACnBH,QAAQ,EAAE,aAAad,IAAI,CAAC,MAAM,EAAE;QAClCe,SAAS,EAAEG,OAAO,CAACT,MAAM;QACzBK,QAAQ,EAAE,aAAad,IAAI,CAAC,MAAM,EAAE;UAClCe,SAAS,EAAEG,OAAO,CAACP,KAAK;UACxBG,QAAQ,EAAEE;QACZ,CAAC;MACH,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL;AACAK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGV,wBAAwB,CAACW,SAAS,GAAG;EAC3EV,QAAQ,EAAElB,SAAS,CAAC6B,OAAO,CAACC,UAAU;EACtCX,SAAS,EAAEnB,SAAS,CAAC+B,MAAM;EAC3BV,KAAK,EAAErB,SAAS,CAACgC,GAAG;EACpBZ,KAAK,EAAEpB,SAAS,CAACiC;AACnB,CAAC,GAAG,KAAK,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}