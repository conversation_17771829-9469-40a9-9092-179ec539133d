{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"component\", \"components\", \"componentsProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '../composeClasses';\nimport { useSlotProps } from '../utils';\nimport { getTabsListUnstyledUtilityClass } from './tabsListUnstyledClasses';\nimport useTabsList from './useTabsList';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    orientation\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation]\n  };\n  return composeClasses(slots, getTabsListUnstyledUtilityClass, {});\n};\n/**\n *\n * Demos:\n *\n * - [Unstyled tabs](https://mui.com/base/react-tabs/)\n *\n * API:\n *\n * - [TabsListUnstyled API](https://mui.com/base/api/tabs-list-unstyled/)\n */\n\nconst TabsListUnstyled = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _ref;\n  const {\n      component,\n      components = {},\n      componentsProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    isRtl,\n    orientation,\n    getRootProps,\n    processChildren\n  } = useTabsList(_extends({}, props, {\n    ref\n  }));\n  const ownerState = _extends({}, props, {\n    isRtl,\n    orientation\n  });\n  const classes = useUtilityClasses(ownerState);\n  const TabsListRoot = (_ref = component != null ? component : components.Root) != null ? _ref : 'div';\n  const tabsListRootProps = useSlotProps({\n    elementType: TabsListRoot,\n    getSlotProps: getRootProps,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    ownerState,\n    className: classes.root\n  });\n  const processedChildren = processChildren();\n  return /*#__PURE__*/_jsx(TabsListRoot, _extends({}, tabsListRootProps, {\n    children: processedChildren\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TabsListUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the TabsList.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The props used for each slot inside the TabsList.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  })\n} : void 0;\nexport default TabsListUnstyled;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "composeClasses", "useSlotProps", "getTabsListUnstyledUtilityClass", "useTabsList", "jsx", "_jsx", "useUtilityClasses", "ownerState", "orientation", "slots", "root", "TabsListUnstyled", "forwardRef", "props", "ref", "_ref", "component", "components", "componentsProps", "other", "isRtl", "getRootProps", "processChildren", "classes", "TabsListRoot", "Root", "tabsListRootProps", "elementType", "getSlotProps", "externalSlotProps", "externalForwardedProps", "className", "processedChildren", "children", "process", "env", "NODE_ENV", "propTypes", "node", "shape", "oneOfType", "func", "object"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/TabsListUnstyled/TabsListUnstyled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"component\", \"components\", \"componentsProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '../composeClasses';\nimport { useSlotProps } from '../utils';\nimport { getTabsListUnstyledUtilityClass } from './tabsListUnstyledClasses';\nimport useTabsList from './useTabsList';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n\nconst useUtilityClasses = ownerState => {\n  const {\n    orientation\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation]\n  };\n  return composeClasses(slots, getTabsListUnstyledUtilityClass, {});\n};\n/**\n *\n * Demos:\n *\n * - [Unstyled tabs](https://mui.com/base/react-tabs/)\n *\n * API:\n *\n * - [TabsListUnstyled API](https://mui.com/base/api/tabs-list-unstyled/)\n */\n\n\nconst TabsListUnstyled = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _ref;\n\n  const {\n    component,\n    components = {},\n    componentsProps = {}\n  } = props,\n        other = _objectWithoutPropertiesLoose(props, _excluded);\n\n  const {\n    isRtl,\n    orientation,\n    getRootProps,\n    processChildren\n  } = useTabsList(_extends({}, props, {\n    ref\n  }));\n\n  const ownerState = _extends({}, props, {\n    isRtl,\n    orientation\n  });\n\n  const classes = useUtilityClasses(ownerState);\n  const TabsListRoot = (_ref = component != null ? component : components.Root) != null ? _ref : 'div';\n  const tabsListRootProps = useSlotProps({\n    elementType: TabsListRoot,\n    getSlotProps: getRootProps,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    ownerState,\n    className: classes.root\n  });\n  const processedChildren = processChildren();\n  return /*#__PURE__*/_jsx(TabsListRoot, _extends({}, tabsListRootProps, {\n    children: processedChildren\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TabsListUnstyled.propTypes\n/* remove-proptypes */\n= {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n\n  /**\n   * The components used for each slot inside the TabsList.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n\n  /**\n   * The props used for each slot inside the TabsList.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  })\n} : void 0;\nexport default TabsListUnstyled;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,CAAC;AAC5E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,+BAA+B,QAAQ,2BAA2B;AAC3E,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAE/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,WAAW;EAC5B,CAAC;EACD,OAAOR,cAAc,CAACS,KAAK,EAAEP,+BAA+B,EAAE,CAAC,CAAC,CAAC;AACnE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,MAAMS,gBAAgB,GAAG,aAAab,KAAK,CAACc,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACrE,IAAIC,IAAI;EAER,MAAM;MACJC,SAAS;MACTC,UAAU,GAAG,CAAC,CAAC;MACfC,eAAe,GAAG,CAAC;IACrB,CAAC,GAAGL,KAAK;IACHM,KAAK,GAAGvB,6BAA6B,CAACiB,KAAK,EAAEhB,SAAS,CAAC;EAE7D,MAAM;IACJuB,KAAK;IACLZ,WAAW;IACXa,YAAY;IACZC;EACF,CAAC,GAAGnB,WAAW,CAACR,QAAQ,CAAC,CAAC,CAAC,EAAEkB,KAAK,EAAE;IAClCC;EACF,CAAC,CAAC,CAAC;EAEH,MAAMP,UAAU,GAAGZ,QAAQ,CAAC,CAAC,CAAC,EAAEkB,KAAK,EAAE;IACrCO,KAAK;IACLZ;EACF,CAAC,CAAC;EAEF,MAAMe,OAAO,GAAGjB,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMiB,YAAY,GAAG,CAACT,IAAI,GAAGC,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAGC,UAAU,CAACQ,IAAI,KAAK,IAAI,GAAGV,IAAI,GAAG,KAAK;EACpG,MAAMW,iBAAiB,GAAGzB,YAAY,CAAC;IACrC0B,WAAW,EAAEH,YAAY;IACzBI,YAAY,EAAEP,YAAY;IAC1BQ,iBAAiB,EAAEX,eAAe,CAACR,IAAI;IACvCoB,sBAAsB,EAAEX,KAAK;IAC7BZ,UAAU;IACVwB,SAAS,EAAER,OAAO,CAACb;EACrB,CAAC,CAAC;EACF,MAAMsB,iBAAiB,GAAGV,eAAe,CAAC,CAAC;EAC3C,OAAO,aAAajB,IAAI,CAACmB,YAAY,EAAE7B,QAAQ,CAAC,CAAC,CAAC,EAAE+B,iBAAiB,EAAE;IACrEO,QAAQ,EAAED;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzB,gBAAgB,CAAC0B;AACzD,yBACE;EACA;EACA;EACA;EACA;;EAEA;AACF;AACA;EACEJ,QAAQ,EAAElC,SAAS,CAACuC,IAAI;EAExB;AACF;AACA;AACA;EACEtB,SAAS,EAAEjB,SAAS,CAAC4B,WAAW;EAEhC;AACF;AACA;AACA;AACA;EACEV,UAAU,EAAElB,SAAS,CAACwC,KAAK,CAAC;IAC1Bd,IAAI,EAAE1B,SAAS,CAAC4B;EAClB,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACET,eAAe,EAAEnB,SAAS,CAACwC,KAAK,CAAC;IAC/B7B,IAAI,EAAEX,SAAS,CAACyC,SAAS,CAAC,CAACzC,SAAS,CAAC0C,IAAI,EAAE1C,SAAS,CAAC2C,MAAM,CAAC;EAC9D,CAAC;AACH,CAAC,GAAG,KAAK,CAAC;AACV,eAAe/B,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}