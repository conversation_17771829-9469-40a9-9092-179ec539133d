{"ast": null, "code": "function ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nimport parsePhoneNumberWithError from './parsePhoneNumberWithError_.js';\nimport ParseError from './ParseError.js';\nimport { isSupportedCountry } from './metadata.js';\nexport default function parsePhoneNumber(text, options, metadata) {\n  // Validate `defaultCountry`.\n  if (options && options.defaultCountry && !isSupportedCountry(options.defaultCountry, metadata)) {\n    options = _objectSpread(_objectSpread({}, options), {}, {\n      defaultCountry: undefined\n    });\n  } // Parse phone number.\n\n  try {\n    return parsePhoneNumberWithError(text, options, metadata);\n  } catch (error) {\n    /* istanbul ignore else */\n    if (error instanceof ParseError) {//\n    } else {\n      throw error;\n    }\n  }\n}", "map": {"version": 3, "names": ["parsePhoneNumberWithError", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isSupportedCountry", "parsePhoneNumber", "text", "options", "metadata", "defaultCountry", "_objectSpread", "undefined", "error"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\parsePhoneNumber_.js"], "sourcesContent": ["import parsePhoneNumberWithError from './parsePhoneNumberWithError_.js'\r\nimport ParseError from './ParseError.js'\r\nimport { isSupportedCountry } from './metadata.js'\r\n\r\nexport default function parsePhoneNumber(text, options, metadata) {\r\n\t// Validate `defaultCountry`.\r\n\tif (options && options.defaultCountry && !isSupportedCountry(options.defaultCountry, metadata)) {\r\n\t\toptions = {\r\n\t\t\t...options,\r\n\t\t\tdefaultCountry: undefined\r\n\t\t}\r\n\t}\r\n\t// Parse phone number.\r\n\ttry {\r\n\t\treturn parsePhoneNumberWithError(text, options, metadata)\r\n\t} catch (error) {\r\n\t\t/* istanbul ignore else */\r\n\t\tif (error instanceof ParseError) {\r\n\t\t\t//\r\n\t\t} else {\r\n\t\t\tthrow error\r\n\t\t}\r\n\t}\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,yBAAP,MAAsC,iCAAtC;AACA,OAAOC,UAAP,MAAuB,iBAAvB;AACA,SAASC,kBAAT,QAAmC,eAAnC;AAEA,eAAe,SAASC,gBAATA,CAA0BC,IAA1B,EAAgCC,OAAhC,EAAyCC,QAAzC,EAAmD;EACjE;EACA,IAAID,OAAO,IAAIA,OAAO,CAACE,cAAnB,IAAqC,CAACL,kBAAkB,CAACG,OAAO,CAACE,cAAT,EAAyBD,QAAzB,CAA5D,EAAgG;IAC/FD,OAAO,GAAAG,aAAA,CAAAA,aAAA,KACHH,OADG;MAENE,cAAc,EAAEE;IAFV,EAAP;EAIA,CAPgE,CAQjE;;EACA,IAAI;IACH,OAAOT,yBAAyB,CAACI,IAAD,EAAOC,OAAP,EAAgBC,QAAhB,CAAhC;EACA,CAFD,CAEE,OAAOI,KAAP,EAAc;IACf;IACA,IAAIA,KAAK,YAAYT,UAArB,EAAiC,CAChC;IAAA,CADD,MAEO;MACN,MAAMS,KAAN;IACA;EACD;AACD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}