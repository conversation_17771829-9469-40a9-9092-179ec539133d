{"ast": null, "code": "import * as React from 'react';\nimport { unstable_useControlled as useControlled, unstable_useId as useId } from '@mui/utils';\nconst useTabs = parameters => {\n  const {\n    value: valueProp,\n    defaultValue,\n    onChange,\n    orientation,\n    direction,\n    selectionFollowsFocus\n  } = parameters;\n  const [value, setValue] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: 'Tabs',\n    state: 'value'\n  });\n  const idPrefix = useId();\n  const onSelected = React.useCallback((e, newValue) => {\n    setValue(newValue);\n    if (onChange) {\n      onChange(e, newValue);\n    }\n  }, [onChange, setValue]);\n  const tabsContextValue = React.useMemo(() => {\n    return {\n      idPrefix,\n      value,\n      onSelected,\n      orientation,\n      direction,\n      selectionFollowsFocus\n    };\n  }, [idPrefix, value, onSelected, orientation, direction, selectionFollowsFocus]);\n  return {\n    tabsContextValue\n  };\n};\nexport default useTabs;", "map": {"version": 3, "names": ["React", "unstable_useControlled", "useControlled", "unstable_useId", "useId", "useTabs", "parameters", "value", "valueProp", "defaultValue", "onChange", "orientation", "direction", "selectionFollowsFocus", "setValue", "controlled", "default", "name", "state", "idPrefix", "onSelected", "useCallback", "e", "newValue", "tabsContextValue", "useMemo"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/TabsUnstyled/useTabs.js"], "sourcesContent": ["import * as React from 'react';\nimport { unstable_useControlled as useControlled, unstable_useId as useId } from '@mui/utils';\n\nconst useTabs = parameters => {\n  const {\n    value: valueProp,\n    defaultValue,\n    onChange,\n    orientation,\n    direction,\n    selectionFollowsFocus\n  } = parameters;\n  const [value, setValue] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: 'Tabs',\n    state: 'value'\n  });\n  const idPrefix = useId();\n  const onSelected = React.useCallback((e, newValue) => {\n    setValue(newValue);\n\n    if (onChange) {\n      onChange(e, newValue);\n    }\n  }, [onChange, setValue]);\n  const tabsContextValue = React.useMemo(() => {\n    return {\n      idPrefix,\n      value,\n      onSelected,\n      orientation,\n      direction,\n      selectionFollowsFocus\n    };\n  }, [idPrefix, value, onSelected, orientation, direction, selectionFollowsFocus]);\n  return {\n    tabsContextValue\n  };\n};\n\nexport default useTabs;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,sBAAsB,IAAIC,aAAa,EAAEC,cAAc,IAAIC,KAAK,QAAQ,YAAY;AAE7F,MAAMC,OAAO,GAAGC,UAAU,IAAI;EAC5B,MAAM;IACJC,KAAK,EAAEC,SAAS;IAChBC,YAAY;IACZC,QAAQ;IACRC,WAAW;IACXC,SAAS;IACTC;EACF,CAAC,GAAGP,UAAU;EACd,MAAM,CAACC,KAAK,EAAEO,QAAQ,CAAC,GAAGZ,aAAa,CAAC;IACtCa,UAAU,EAAEP,SAAS;IACrBQ,OAAO,EAAEP,YAAY;IACrBQ,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAMC,QAAQ,GAAGf,KAAK,CAAC,CAAC;EACxB,MAAMgB,UAAU,GAAGpB,KAAK,CAACqB,WAAW,CAAC,CAACC,CAAC,EAAEC,QAAQ,KAAK;IACpDT,QAAQ,CAACS,QAAQ,CAAC;IAElB,IAAIb,QAAQ,EAAE;MACZA,QAAQ,CAACY,CAAC,EAAEC,QAAQ,CAAC;IACvB;EACF,CAAC,EAAE,CAACb,QAAQ,EAAEI,QAAQ,CAAC,CAAC;EACxB,MAAMU,gBAAgB,GAAGxB,KAAK,CAACyB,OAAO,CAAC,MAAM;IAC3C,OAAO;MACLN,QAAQ;MACRZ,KAAK;MACLa,UAAU;MACVT,WAAW;MACXC,SAAS;MACTC;IACF,CAAC;EACH,CAAC,EAAE,CAACM,QAAQ,EAAEZ,KAAK,EAAEa,UAAU,EAAET,WAAW,EAAEC,SAAS,EAAEC,qBAAqB,CAAC,CAAC;EAChF,OAAO;IACLW;EACF,CAAC;AACH,CAAC;AAED,eAAenB,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}