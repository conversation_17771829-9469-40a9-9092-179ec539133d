{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function retryWhen(notifier) {\n  return operate(function (source, subscriber) {\n    var innerSub;\n    var syncResub = false;\n    var errors$;\n    var subscribeForRetryWhen = function () {\n      innerSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, undefined, function (err) {\n        if (!errors$) {\n          errors$ = new Subject();\n          innerFrom(notifier(errors$)).subscribe(createOperatorSubscriber(subscriber, function () {\n            return innerSub ? subscribeForRetryWhen() : syncResub = true;\n          }));\n        }\n        if (errors$) {\n          errors$.next(err);\n        }\n      }));\n      if (syncResub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        syncResub = false;\n        subscribeForRetryWhen();\n      }\n    };\n    subscribeForRetryWhen();\n  });\n}", "map": {"version": 3, "names": ["innerFrom", "Subject", "operate", "createOperatorSubscriber", "retry<PERSON><PERSON>", "notifier", "source", "subscriber", "innerSub", "syncResub", "errors$", "subscribeForRetryWhen", "subscribe", "undefined", "err", "next", "unsubscribe"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\retryWhen.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { Subscription } from '../Subscription';\n\nimport { MonoTypeOperatorFunction, ObservableInput } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/**\n * Returns an Observable that mirrors the source Observable with the exception of an `error`. If the source Observable\n * calls `error`, this method will emit the Throwable that caused the error to the `ObservableInput` returned from `notifier`.\n * If that Observable calls `complete` or `error` then this method will call `complete` or `error` on the child\n * subscription. Otherwise this method will resubscribe to the source Observable.\n *\n * ![](retryWhen.png)\n *\n * Retry an observable sequence on error based on custom criteria.\n *\n * ## Example\n *\n * ```ts\n * import { interval, map, retryWhen, tap, delayWhen, timer } from 'rxjs';\n *\n * const source = interval(1000);\n * const result = source.pipe(\n *   map(value => {\n *     if (value > 5) {\n *       // error will be picked up by retryWhen\n *       throw value;\n *     }\n *     return value;\n *   }),\n *   retryWhen(errors =>\n *     errors.pipe(\n *       // log error message\n *       tap(value => console.log(`Value ${ value } was too high!`)),\n *       // restart in 5 seconds\n *       delayWhen(value => timer(value * 1000))\n *     )\n *   )\n * );\n *\n * result.subscribe(value => console.log(value));\n *\n * // results:\n * // 0\n * // 1\n * // 2\n * // 3\n * // 4\n * // 5\n * // 'Value 6 was too high!'\n * // - Wait 5 seconds then repeat\n * ```\n *\n * @see {@link retry}\n *\n * @param notifier Function that receives an Observable of notifications with which a\n * user can `complete` or `error`, aborting the retry.\n * @return A function that returns an Observable that mirrors the source\n * Observable with the exception of an `error`.\n * @deprecated Will be removed in v9 or v10, use {@link retry}'s `delay` option instead.\n * Will be removed in v9 or v10. Use {@link retry}'s {@link RetryConfig#delay delay} option instead.\n * Instead of `retryWhen(() => notify$)`, use: `retry({ delay: () => notify$ })`.\n */\nexport function retryWhen<T>(notifier: (errors: Observable<any>) => ObservableInput<any>): MonoTypeOperatorFunction<T> {\n  return operate((source, subscriber) => {\n    let innerSub: Subscription | null;\n    let syncResub = false;\n    let errors$: Subject<any>;\n\n    const subscribeForRetryWhen = () => {\n      innerSub = source.subscribe(\n        createOperatorSubscriber(subscriber, undefined, undefined, (err) => {\n          if (!errors$) {\n            errors$ = new Subject();\n            innerFrom(notifier(errors$)).subscribe(\n              createOperatorSubscriber(subscriber, () =>\n                // If we have an innerSub, this was an asynchronous call, kick off the retry.\n                // Otherwise, if we don't have an innerSub yet, that's because the inner subscription\n                // call hasn't even returned yet. We've arrived here synchronously.\n                // So we flag that we want to resub, such that we can ensure finalization\n                // happens before we resubscribe.\n                innerSub ? subscribeForRetryWhen() : (syncResub = true)\n              )\n            );\n          }\n          if (errors$) {\n            // We have set up the notifier without error.\n            errors$.next(err);\n          }\n        })\n      );\n\n      if (syncResub) {\n        // Ensure that the inner subscription is torn down before\n        // moving on to the next subscription in the synchronous case.\n        // If we don't do this here, all inner subscriptions will not be\n        // torn down until the entire observable is done.\n        innerSub.unsubscribe();\n        innerSub = null;\n        // We may need to do this multiple times, so reset the flag.\n        syncResub = false;\n        // Resubscribe\n        subscribeForRetryWhen();\n      }\n    };\n\n    // Start the subscription\n    subscribeForRetryWhen();\n  });\n}\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,yBAAyB;AACnD,SAASC,OAAO,QAAQ,YAAY;AAIpC,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AA2D/D,OAAM,SAAUC,SAASA,CAAIC,QAA2D;EACtF,OAAOH,OAAO,CAAC,UAACI,MAAM,EAAEC,UAAU;IAChC,IAAIC,QAA6B;IACjC,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,OAAqB;IAEzB,IAAMC,qBAAqB,GAAG,SAAAA,CAAA;MAC5BH,QAAQ,GAAGF,MAAM,CAACM,SAAS,CACzBT,wBAAwB,CAACI,UAAU,EAAEM,SAAS,EAAEA,SAAS,EAAE,UAACC,GAAG;QAC7D,IAAI,CAACJ,OAAO,EAAE;UACZA,OAAO,GAAG,IAAIT,OAAO,EAAE;UACvBD,SAAS,CAACK,QAAQ,CAACK,OAAO,CAAC,CAAC,CAACE,SAAS,CACpCT,wBAAwB,CAACI,UAAU,EAAE;YAMnC,OAAAC,QAAQ,GAAGG,qBAAqB,EAAE,GAAIF,SAAS,GAAG,IAAK;UAAvD,CAAuD,CACxD,CACF;;QAEH,IAAIC,OAAO,EAAE;UAEXA,OAAO,CAACK,IAAI,CAACD,GAAG,CAAC;;MAErB,CAAC,CAAC,CACH;MAED,IAAIL,SAAS,EAAE;QAKbD,QAAQ,CAACQ,WAAW,EAAE;QACtBR,QAAQ,GAAG,IAAI;QAEfC,SAAS,GAAG,KAAK;QAEjBE,qBAAqB,EAAE;;IAE3B,CAAC;IAGDA,qBAAqB,EAAE;EACzB,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}