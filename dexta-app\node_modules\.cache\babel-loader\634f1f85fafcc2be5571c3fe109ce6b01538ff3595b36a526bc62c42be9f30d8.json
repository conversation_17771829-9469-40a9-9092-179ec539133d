{"ast": null, "code": "import { identity } from '../util/identity';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function skipLast(skipCount) {\n  return skipCount <= 0 ? identity : operate(function (source, subscriber) {\n    var ring = new Array(skipCount);\n    var seen = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      var valueIndex = seen++;\n      if (valueIndex < skipCount) {\n        ring[valueIndex] = value;\n      } else {\n        var index = valueIndex % skipCount;\n        var oldValue = ring[index];\n        ring[index] = value;\n        subscriber.next(oldValue);\n      }\n    }));\n    return function () {\n      ring = null;\n    };\n  });\n}", "map": {"version": 3, "names": ["identity", "operate", "createOperatorSubscriber", "skipLast", "skip<PERSON><PERSON>nt", "source", "subscriber", "ring", "Array", "seen", "subscribe", "value", "valueIndex", "index", "oldValue", "next"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\skipLast.ts"], "sourcesContent": ["import { MonoTypeOperatorFunction } from '../types';\nimport { identity } from '../util/identity';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/**\n * Skip a specified number of values before the completion of an observable.\n *\n * ![](skipLast.png)\n *\n * Returns an observable that will emit values as soon as it can, given a number of\n * skipped values. For example, if you `skipLast(3)` on a source, when the source\n * emits its fourth value, the first value the source emitted will finally be emitted\n * from the returned observable, as it is no longer part of what needs to be skipped.\n *\n * All values emitted by the result of `skipLast(N)` will be delayed by `N` emissions,\n * as each value is held in a buffer until enough values have been emitted that that\n * the buffered value may finally be sent to the consumer.\n *\n * After subscribing, unsubscribing will not result in the emission of the buffered\n * skipped values.\n *\n * ## Example\n *\n * Skip the last 2 values of an observable with many values\n *\n * ```ts\n * import { of, skipLast } from 'rxjs';\n *\n * const numbers = of(1, 2, 3, 4, 5);\n * const skipLastTwo = numbers.pipe(skipLast(2));\n * skipLastTwo.subscribe(x => console.log(x));\n *\n * // Results in:\n * // 1 2 3\n * // (4 and 5 are skipped)\n * ```\n *\n * @see {@link skip}\n * @see {@link skipUntil}\n * @see {@link skipWhile}\n * @see {@link take}\n *\n * @param skipCount Number of elements to skip from the end of the source Observable.\n * @return A function that returns an Observable that skips the last `count`\n * values emitted by the source Observable.\n */\nexport function skipLast<T>(skipCount: number): MonoTypeOperatorFunction<T> {\n  return skipCount <= 0\n    ? // For skipCounts less than or equal to zero, we are just mirroring the source.\n      identity\n    : operate((source, subscriber) => {\n        // A ring buffer to hold the values while we wait to see\n        // if we can emit it or it's part of the \"skipped\" last values.\n        // Note that it is the _same size_ as the skip count.\n        let ring: T[] = new Array(skipCount);\n        // The number of values seen so far. This is used to get\n        // the index of the current value when it arrives.\n        let seen = 0;\n        source.subscribe(\n          createOperatorSubscriber(subscriber, (value) => {\n            // Get the index of the value we have right now\n            // relative to all other values we've seen, then\n            // increment `seen`. This ensures we've moved to\n            // the next slot in our ring buffer.\n            const valueIndex = seen++;\n            if (valueIndex < skipCount) {\n              // If we haven't seen enough values to fill our buffer yet,\n              // Then we aren't to a number of seen values where we can\n              // emit anything, so let's just start by filling the ring buffer.\n              ring[valueIndex] = value;\n            } else {\n              // We are traversing over the ring array in such\n              // a way that when we get to the end, we loop back\n              // and go to the start.\n              const index = valueIndex % skipCount;\n              // Pull the oldest value out so we can emit it,\n              // and stuff the new value in it's place.\n              const oldValue = ring[index];\n              ring[index] = value;\n              // Emit the old value. It is important that this happens\n              // after we swap the value in the buffer, if it happens\n              // before we swap the value in the buffer, then a synchronous\n              // source can get the buffer out of whack.\n              subscriber.next(oldValue);\n            }\n          })\n        );\n\n        return () => {\n          // Release our values in memory\n          ring = null!;\n        };\n      });\n}\n"], "mappings": "AACA,SAASA,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AA4C/D,OAAM,SAAUC,QAAQA,CAAIC,SAAiB;EAC3C,OAAOA,SAAS,IAAI,CAAC,GAEjBJ,QAAQ,GACRC,OAAO,CAAC,UAACI,MAAM,EAAEC,UAAU;IAIzB,IAAIC,IAAI,GAAQ,IAAIC,KAAK,CAACJ,SAAS,CAAC;IAGpC,IAAIK,IAAI,GAAG,CAAC;IACZJ,MAAM,CAACK,SAAS,CACdR,wBAAwB,CAACI,UAAU,EAAE,UAACK,KAAK;MAKzC,IAAMC,UAAU,GAAGH,IAAI,EAAE;MACzB,IAAIG,UAAU,GAAGR,SAAS,EAAE;QAI1BG,IAAI,CAACK,UAAU,CAAC,GAAGD,KAAK;OACzB,MAAM;QAIL,IAAME,KAAK,GAAGD,UAAU,GAAGR,SAAS;QAGpC,IAAMU,QAAQ,GAAGP,IAAI,CAACM,KAAK,CAAC;QAC5BN,IAAI,CAACM,KAAK,CAAC,GAAGF,KAAK;QAKnBL,UAAU,CAACS,IAAI,CAACD,QAAQ,CAAC;;IAE7B,CAAC,CAAC,CACH;IAED,OAAO;MAELP,IAAI,GAAG,IAAK;IACd,CAAC;EACH,CAAC,CAAC;AACR"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}