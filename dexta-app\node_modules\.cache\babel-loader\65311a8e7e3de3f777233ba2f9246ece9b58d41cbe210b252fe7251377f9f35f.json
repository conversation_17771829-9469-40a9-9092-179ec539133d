{"ast": null, "code": "/* eslint-disable consistent-return, jsx-a11y/no-noninteractive-tabindex */\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { exactProp, elementAcceptingRef, unstable_useForkRef as useForkRef, unstable_ownerDocument as ownerDocument } from '@mui/utils'; // Inspired by https://github.com/focus-trap/tabbable\n\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst candidatesSelector = ['input', 'select', 'textarea', 'a[href]', 'button', '[tabindex]', 'audio[controls]', 'video[controls]', '[contenteditable]:not([contenteditable=\"false\"])'].join(',');\nfunction getTabIndex(node) {\n  const tabindexAttr = parseInt(node.getAttribute('tabindex'), 10);\n  if (!Number.isNaN(tabindexAttr)) {\n    return tabindexAttr;\n  } // Browsers do not return `tabIndex` correctly for contentEditable nodes;\n  // https://bugs.chromium.org/p/chromium/issues/detail?id=661108&q=contenteditable%20tabindex&can=2\n  // so if they don't have a tabindex attribute specifically set, assume it's 0.\n  // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n  //  `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n  //  yet they are still part of the regular tab order; in FF, they get a default\n  //  `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n  //  order, consider their tab index to be 0.\n\n  if (node.contentEditable === 'true' || (node.nodeName === 'AUDIO' || node.nodeName === 'VIDEO' || node.nodeName === 'DETAILS') && node.getAttribute('tabindex') === null) {\n    return 0;\n  }\n  return node.tabIndex;\n}\nfunction isNonTabbableRadio(node) {\n  if (node.tagName !== 'INPUT' || node.type !== 'radio') {\n    return false;\n  }\n  if (!node.name) {\n    return false;\n  }\n  const getRadio = selector => node.ownerDocument.querySelector(`input[type=\"radio\"]${selector}`);\n  let roving = getRadio(`[name=\"${node.name}\"]:checked`);\n  if (!roving) {\n    roving = getRadio(`[name=\"${node.name}\"]`);\n  }\n  return roving !== node;\n}\nfunction isNodeMatchingSelectorFocusable(node) {\n  if (node.disabled || node.tagName === 'INPUT' && node.type === 'hidden' || isNonTabbableRadio(node)) {\n    return false;\n  }\n  return true;\n}\nfunction defaultGetTabbable(root) {\n  const regularTabNodes = [];\n  const orderedTabNodes = [];\n  Array.from(root.querySelectorAll(candidatesSelector)).forEach((node, i) => {\n    const nodeTabIndex = getTabIndex(node);\n    if (nodeTabIndex === -1 || !isNodeMatchingSelectorFocusable(node)) {\n      return;\n    }\n    if (nodeTabIndex === 0) {\n      regularTabNodes.push(node);\n    } else {\n      orderedTabNodes.push({\n        documentOrder: i,\n        tabIndex: nodeTabIndex,\n        node\n      });\n    }\n  });\n  return orderedTabNodes.sort((a, b) => a.tabIndex === b.tabIndex ? a.documentOrder - b.documentOrder : a.tabIndex - b.tabIndex).map(a => a.node).concat(regularTabNodes);\n}\nfunction defaultIsEnabled() {\n  return true;\n}\n/**\n * Utility component that locks focus inside the component.\n */\n\nfunction TrapFocus(props) {\n  const {\n    children,\n    disableAutoFocus = false,\n    disableEnforceFocus = false,\n    disableRestoreFocus = false,\n    getTabbable = defaultGetTabbable,\n    isEnabled = defaultIsEnabled,\n    open\n  } = props;\n  const ignoreNextEnforceFocus = React.useRef();\n  const sentinelStart = React.useRef(null);\n  const sentinelEnd = React.useRef(null);\n  const nodeToRestore = React.useRef(null);\n  const reactFocusEventTarget = React.useRef(null); // This variable is useful when disableAutoFocus is true.\n  // It waits for the active element to move into the component to activate.\n\n  const activated = React.useRef(false);\n  const rootRef = React.useRef(null);\n  const handleRef = useForkRef(children.ref, rootRef);\n  const lastKeydown = React.useRef(null);\n  React.useEffect(() => {\n    // We might render an empty child.\n    if (!open || !rootRef.current) {\n      return;\n    }\n    activated.current = !disableAutoFocus;\n  }, [disableAutoFocus, open]);\n  React.useEffect(() => {\n    // We might render an empty child.\n    if (!open || !rootRef.current) {\n      return;\n    }\n    const doc = ownerDocument(rootRef.current);\n    if (!rootRef.current.contains(doc.activeElement)) {\n      if (!rootRef.current.hasAttribute('tabIndex')) {\n        if (process.env.NODE_ENV !== 'production') {\n          console.error(['MUI: The modal content node does not accept focus.', 'For the benefit of assistive technologies, ' + 'the tabIndex of the node is being set to \"-1\".'].join('\\n'));\n        }\n        rootRef.current.setAttribute('tabIndex', -1);\n      }\n      if (activated.current) {\n        rootRef.current.focus();\n      }\n    }\n    return () => {\n      // restoreLastFocus()\n      if (!disableRestoreFocus) {\n        // In IE11 it is possible for document.activeElement to be null resulting\n        // in nodeToRestore.current being null.\n        // Not all elements in IE11 have a focus method.\n        // Once IE11 support is dropped the focus() call can be unconditional.\n        if (nodeToRestore.current && nodeToRestore.current.focus) {\n          ignoreNextEnforceFocus.current = true;\n          nodeToRestore.current.focus();\n        }\n        nodeToRestore.current = null;\n      }\n    }; // Missing `disableRestoreFocus` which is fine.\n    // We don't support changing that prop on an open TrapFocus\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [open]);\n  React.useEffect(() => {\n    // We might render an empty child.\n    if (!open || !rootRef.current) {\n      return;\n    }\n    const doc = ownerDocument(rootRef.current);\n    const contain = nativeEvent => {\n      const {\n        current: rootElement\n      } = rootRef; // Cleanup functions are executed lazily in React 17.\n      // Contain can be called between the component being unmounted and its cleanup function being run.\n\n      if (rootElement === null) {\n        return;\n      }\n      if (!doc.hasFocus() || disableEnforceFocus || !isEnabled() || ignoreNextEnforceFocus.current) {\n        ignoreNextEnforceFocus.current = false;\n        return;\n      }\n      if (!rootElement.contains(doc.activeElement)) {\n        // if the focus event is not coming from inside the children's react tree, reset the refs\n        if (nativeEvent && reactFocusEventTarget.current !== nativeEvent.target || doc.activeElement !== reactFocusEventTarget.current) {\n          reactFocusEventTarget.current = null;\n        } else if (reactFocusEventTarget.current !== null) {\n          return;\n        }\n        if (!activated.current) {\n          return;\n        }\n        let tabbable = [];\n        if (doc.activeElement === sentinelStart.current || doc.activeElement === sentinelEnd.current) {\n          tabbable = getTabbable(rootRef.current);\n        }\n        if (tabbable.length > 0) {\n          var _lastKeydown$current, _lastKeydown$current2;\n          const isShiftTab = Boolean(((_lastKeydown$current = lastKeydown.current) == null ? void 0 : _lastKeydown$current.shiftKey) && ((_lastKeydown$current2 = lastKeydown.current) == null ? void 0 : _lastKeydown$current2.key) === 'Tab');\n          const focusNext = tabbable[0];\n          const focusPrevious = tabbable[tabbable.length - 1];\n          if (isShiftTab) {\n            focusPrevious.focus();\n          } else {\n            focusNext.focus();\n          }\n        } else {\n          rootElement.focus();\n        }\n      }\n    };\n    const loopFocus = nativeEvent => {\n      lastKeydown.current = nativeEvent;\n      if (disableEnforceFocus || !isEnabled() || nativeEvent.key !== 'Tab') {\n        return;\n      } // Make sure the next tab starts from the right place.\n      // doc.activeElement referes to the origin.\n\n      if (doc.activeElement === rootRef.current && nativeEvent.shiftKey) {\n        // We need to ignore the next contain as\n        // it will try to move the focus back to the rootRef element.\n        ignoreNextEnforceFocus.current = true;\n        sentinelEnd.current.focus();\n      }\n    };\n    doc.addEventListener('focusin', contain);\n    doc.addEventListener('keydown', loopFocus, true); // With Edge, Safari and Firefox, no focus related events are fired when the focused area stops being a focused area.\n    // e.g. https://bugzilla.mozilla.org/show_bug.cgi?id=559561.\n    // Instead, we can look if the active element was restored on the BODY element.\n    //\n    // The whatwg spec defines how the browser should behave but does not explicitly mention any events:\n    // https://html.spec.whatwg.org/multipage/interaction.html#focus-fixup-rule.\n\n    const interval = setInterval(() => {\n      if (doc.activeElement.tagName === 'BODY') {\n        contain();\n      }\n    }, 50);\n    return () => {\n      clearInterval(interval);\n      doc.removeEventListener('focusin', contain);\n      doc.removeEventListener('keydown', loopFocus, true);\n    };\n  }, [disableAutoFocus, disableEnforceFocus, disableRestoreFocus, isEnabled, open, getTabbable]);\n  const onFocus = event => {\n    if (nodeToRestore.current === null) {\n      nodeToRestore.current = event.relatedTarget;\n    }\n    activated.current = true;\n    reactFocusEventTarget.current = event.target;\n    const childrenPropsHandler = children.props.onFocus;\n    if (childrenPropsHandler) {\n      childrenPropsHandler(event);\n    }\n  };\n  const handleFocusSentinel = event => {\n    if (nodeToRestore.current === null) {\n      nodeToRestore.current = event.relatedTarget;\n    }\n    activated.current = true;\n  };\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(\"div\", {\n      tabIndex: open ? 0 : -1,\n      onFocus: handleFocusSentinel,\n      ref: sentinelStart,\n      \"data-testid\": \"sentinelStart\"\n    }), /*#__PURE__*/React.cloneElement(children, {\n      ref: handleRef,\n      onFocus\n    }), /*#__PURE__*/_jsx(\"div\", {\n      tabIndex: open ? 0 : -1,\n      onFocus: handleFocusSentinel,\n      ref: sentinelEnd,\n      \"data-testid\": \"sentinelEnd\"\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? TrapFocus.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef,\n  /**\n   * If `true`, the trap focus will not automatically shift focus to itself when it opens, and\n   * replace it to the last focused element when it closes.\n   * This also works correctly with any trap focus children that have the `disableAutoFocus` prop.\n   *\n   * Generally this should never be set to `true` as it makes the trap focus less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableAutoFocus: PropTypes.bool,\n  /**\n   * If `true`, the trap focus will not prevent focus from leaving the trap focus while open.\n   *\n   * Generally this should never be set to `true` as it makes the trap focus less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableEnforceFocus: PropTypes.bool,\n  /**\n   * If `true`, the trap focus will not restore focus to previously focused element once\n   * trap focus is hidden or unmounted.\n   * @default false\n   */\n  disableRestoreFocus: PropTypes.bool,\n  /**\n   * Returns an array of ordered tabbable nodes (i.e. in tab order) within the root.\n   * For instance, you can provide the \"tabbable\" npm dependency.\n   * @param {HTMLElement} root\n   */\n  getTabbable: PropTypes.func,\n  /**\n   * This prop extends the `open` prop.\n   * It allows to toggle the open state without having to wait for a rerender when changing the `open` prop.\n   * This prop should be memoized.\n   * It can be used to support multiple trap focus mounted at the same time.\n   * @default function defaultIsEnabled() {\n   *   return true;\n   * }\n   */\n  isEnabled: PropTypes.func,\n  /**\n   * If `true`, focus is locked.\n   */\n  open: PropTypes.bool.isRequired\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  TrapFocus['propTypes' + ''] = exactProp(TrapFocus.propTypes);\n}\nexport default TrapFocus;", "map": {"version": 3, "names": ["React", "PropTypes", "exactProp", "elementAcceptingRef", "unstable_useForkRef", "useForkRef", "unstable_ownerDocument", "ownerDocument", "jsx", "_jsx", "jsxs", "_jsxs", "candidatesSelector", "join", "getTabIndex", "node", "tabindexAttr", "parseInt", "getAttribute", "Number", "isNaN", "contentEditable", "nodeName", "tabIndex", "isNonTabbableRadio", "tagName", "type", "name", "getRadio", "selector", "querySelector", "roving", "isNodeMatchingSelectorFocusable", "disabled", "defaultGetTabbable", "root", "regularTabNodes", "orderedTabNodes", "Array", "from", "querySelectorAll", "for<PERSON>ach", "i", "nodeTabIndex", "push", "documentOrder", "sort", "a", "b", "map", "concat", "defaultIsEnabled", "TrapFocus", "props", "children", "disableAutoFocus", "disableEnforceFocus", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getTabbable", "isEnabled", "open", "ignoreNextEnforceFocus", "useRef", "sentinelStart", "sentinelEnd", "nodeToRestore", "reactFocusEventTarget", "activated", "rootRef", "handleRef", "ref", "lastKeydown", "useEffect", "current", "doc", "contains", "activeElement", "hasAttribute", "process", "env", "NODE_ENV", "console", "error", "setAttribute", "focus", "contain", "nativeEvent", "rootElement", "hasFocus", "target", "tabbable", "length", "_lastKeydown$current", "_lastKeydown$current2", "isShiftTab", "Boolean", "shift<PERSON>ey", "key", "focusNext", "focusPrevious", "loopFocus", "addEventListener", "interval", "setInterval", "clearInterval", "removeEventListener", "onFocus", "event", "relatedTarget", "childrenPropsHandler", "handleFocusSentinel", "Fragment", "cloneElement", "propTypes", "bool", "func", "isRequired"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/TrapFocus/TrapFocus.js"], "sourcesContent": ["/* eslint-disable consistent-return, jsx-a11y/no-noninteractive-tabindex */\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { exactProp, elementAcceptingRef, unstable_useForkRef as useForkRef, unstable_ownerDocument as ownerDocument } from '@mui/utils'; // Inspired by https://github.com/focus-trap/tabbable\n\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst candidatesSelector = ['input', 'select', 'textarea', 'a[href]', 'button', '[tabindex]', 'audio[controls]', 'video[controls]', '[contenteditable]:not([contenteditable=\"false\"])'].join(',');\n\nfunction getTabIndex(node) {\n  const tabindexAttr = parseInt(node.getAttribute('tabindex'), 10);\n\n  if (!Number.isNaN(tabindexAttr)) {\n    return tabindexAttr;\n  } // Browsers do not return `tabIndex` correctly for contentEditable nodes;\n  // https://bugs.chromium.org/p/chromium/issues/detail?id=661108&q=contenteditable%20tabindex&can=2\n  // so if they don't have a tabindex attribute specifically set, assume it's 0.\n  // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n  //  `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n  //  yet they are still part of the regular tab order; in FF, they get a default\n  //  `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n  //  order, consider their tab index to be 0.\n\n\n  if (node.contentEditable === 'true' || (node.nodeName === 'AUDIO' || node.nodeName === 'VIDEO' || node.nodeName === 'DETAILS') && node.getAttribute('tabindex') === null) {\n    return 0;\n  }\n\n  return node.tabIndex;\n}\n\nfunction isNonTabbableRadio(node) {\n  if (node.tagName !== 'INPUT' || node.type !== 'radio') {\n    return false;\n  }\n\n  if (!node.name) {\n    return false;\n  }\n\n  const getRadio = selector => node.ownerDocument.querySelector(`input[type=\"radio\"]${selector}`);\n\n  let roving = getRadio(`[name=\"${node.name}\"]:checked`);\n\n  if (!roving) {\n    roving = getRadio(`[name=\"${node.name}\"]`);\n  }\n\n  return roving !== node;\n}\n\nfunction isNodeMatchingSelectorFocusable(node) {\n  if (node.disabled || node.tagName === 'INPUT' && node.type === 'hidden' || isNonTabbableRadio(node)) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction defaultGetTabbable(root) {\n  const regularTabNodes = [];\n  const orderedTabNodes = [];\n  Array.from(root.querySelectorAll(candidatesSelector)).forEach((node, i) => {\n    const nodeTabIndex = getTabIndex(node);\n\n    if (nodeTabIndex === -1 || !isNodeMatchingSelectorFocusable(node)) {\n      return;\n    }\n\n    if (nodeTabIndex === 0) {\n      regularTabNodes.push(node);\n    } else {\n      orderedTabNodes.push({\n        documentOrder: i,\n        tabIndex: nodeTabIndex,\n        node\n      });\n    }\n  });\n  return orderedTabNodes.sort((a, b) => a.tabIndex === b.tabIndex ? a.documentOrder - b.documentOrder : a.tabIndex - b.tabIndex).map(a => a.node).concat(regularTabNodes);\n}\n\nfunction defaultIsEnabled() {\n  return true;\n}\n/**\n * Utility component that locks focus inside the component.\n */\n\n\nfunction TrapFocus(props) {\n  const {\n    children,\n    disableAutoFocus = false,\n    disableEnforceFocus = false,\n    disableRestoreFocus = false,\n    getTabbable = defaultGetTabbable,\n    isEnabled = defaultIsEnabled,\n    open\n  } = props;\n  const ignoreNextEnforceFocus = React.useRef();\n  const sentinelStart = React.useRef(null);\n  const sentinelEnd = React.useRef(null);\n  const nodeToRestore = React.useRef(null);\n  const reactFocusEventTarget = React.useRef(null); // This variable is useful when disableAutoFocus is true.\n  // It waits for the active element to move into the component to activate.\n\n  const activated = React.useRef(false);\n  const rootRef = React.useRef(null);\n  const handleRef = useForkRef(children.ref, rootRef);\n  const lastKeydown = React.useRef(null);\n  React.useEffect(() => {\n    // We might render an empty child.\n    if (!open || !rootRef.current) {\n      return;\n    }\n\n    activated.current = !disableAutoFocus;\n  }, [disableAutoFocus, open]);\n  React.useEffect(() => {\n    // We might render an empty child.\n    if (!open || !rootRef.current) {\n      return;\n    }\n\n    const doc = ownerDocument(rootRef.current);\n\n    if (!rootRef.current.contains(doc.activeElement)) {\n      if (!rootRef.current.hasAttribute('tabIndex')) {\n        if (process.env.NODE_ENV !== 'production') {\n          console.error(['MUI: The modal content node does not accept focus.', 'For the benefit of assistive technologies, ' + 'the tabIndex of the node is being set to \"-1\".'].join('\\n'));\n        }\n\n        rootRef.current.setAttribute('tabIndex', -1);\n      }\n\n      if (activated.current) {\n        rootRef.current.focus();\n      }\n    }\n\n    return () => {\n      // restoreLastFocus()\n      if (!disableRestoreFocus) {\n        // In IE11 it is possible for document.activeElement to be null resulting\n        // in nodeToRestore.current being null.\n        // Not all elements in IE11 have a focus method.\n        // Once IE11 support is dropped the focus() call can be unconditional.\n        if (nodeToRestore.current && nodeToRestore.current.focus) {\n          ignoreNextEnforceFocus.current = true;\n          nodeToRestore.current.focus();\n        }\n\n        nodeToRestore.current = null;\n      }\n    }; // Missing `disableRestoreFocus` which is fine.\n    // We don't support changing that prop on an open TrapFocus\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [open]);\n  React.useEffect(() => {\n    // We might render an empty child.\n    if (!open || !rootRef.current) {\n      return;\n    }\n\n    const doc = ownerDocument(rootRef.current);\n\n    const contain = nativeEvent => {\n      const {\n        current: rootElement\n      } = rootRef; // Cleanup functions are executed lazily in React 17.\n      // Contain can be called between the component being unmounted and its cleanup function being run.\n\n      if (rootElement === null) {\n        return;\n      }\n\n      if (!doc.hasFocus() || disableEnforceFocus || !isEnabled() || ignoreNextEnforceFocus.current) {\n        ignoreNextEnforceFocus.current = false;\n        return;\n      }\n\n      if (!rootElement.contains(doc.activeElement)) {\n        // if the focus event is not coming from inside the children's react tree, reset the refs\n        if (nativeEvent && reactFocusEventTarget.current !== nativeEvent.target || doc.activeElement !== reactFocusEventTarget.current) {\n          reactFocusEventTarget.current = null;\n        } else if (reactFocusEventTarget.current !== null) {\n          return;\n        }\n\n        if (!activated.current) {\n          return;\n        }\n\n        let tabbable = [];\n\n        if (doc.activeElement === sentinelStart.current || doc.activeElement === sentinelEnd.current) {\n          tabbable = getTabbable(rootRef.current);\n        }\n\n        if (tabbable.length > 0) {\n          var _lastKeydown$current, _lastKeydown$current2;\n\n          const isShiftTab = Boolean(((_lastKeydown$current = lastKeydown.current) == null ? void 0 : _lastKeydown$current.shiftKey) && ((_lastKeydown$current2 = lastKeydown.current) == null ? void 0 : _lastKeydown$current2.key) === 'Tab');\n          const focusNext = tabbable[0];\n          const focusPrevious = tabbable[tabbable.length - 1];\n\n          if (isShiftTab) {\n            focusPrevious.focus();\n          } else {\n            focusNext.focus();\n          }\n        } else {\n          rootElement.focus();\n        }\n      }\n    };\n\n    const loopFocus = nativeEvent => {\n      lastKeydown.current = nativeEvent;\n\n      if (disableEnforceFocus || !isEnabled() || nativeEvent.key !== 'Tab') {\n        return;\n      } // Make sure the next tab starts from the right place.\n      // doc.activeElement referes to the origin.\n\n\n      if (doc.activeElement === rootRef.current && nativeEvent.shiftKey) {\n        // We need to ignore the next contain as\n        // it will try to move the focus back to the rootRef element.\n        ignoreNextEnforceFocus.current = true;\n        sentinelEnd.current.focus();\n      }\n    };\n\n    doc.addEventListener('focusin', contain);\n    doc.addEventListener('keydown', loopFocus, true); // With Edge, Safari and Firefox, no focus related events are fired when the focused area stops being a focused area.\n    // e.g. https://bugzilla.mozilla.org/show_bug.cgi?id=559561.\n    // Instead, we can look if the active element was restored on the BODY element.\n    //\n    // The whatwg spec defines how the browser should behave but does not explicitly mention any events:\n    // https://html.spec.whatwg.org/multipage/interaction.html#focus-fixup-rule.\n\n    const interval = setInterval(() => {\n      if (doc.activeElement.tagName === 'BODY') {\n        contain();\n      }\n    }, 50);\n    return () => {\n      clearInterval(interval);\n      doc.removeEventListener('focusin', contain);\n      doc.removeEventListener('keydown', loopFocus, true);\n    };\n  }, [disableAutoFocus, disableEnforceFocus, disableRestoreFocus, isEnabled, open, getTabbable]);\n\n  const onFocus = event => {\n    if (nodeToRestore.current === null) {\n      nodeToRestore.current = event.relatedTarget;\n    }\n\n    activated.current = true;\n    reactFocusEventTarget.current = event.target;\n    const childrenPropsHandler = children.props.onFocus;\n\n    if (childrenPropsHandler) {\n      childrenPropsHandler(event);\n    }\n  };\n\n  const handleFocusSentinel = event => {\n    if (nodeToRestore.current === null) {\n      nodeToRestore.current = event.relatedTarget;\n    }\n\n    activated.current = true;\n  };\n\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(\"div\", {\n      tabIndex: open ? 0 : -1,\n      onFocus: handleFocusSentinel,\n      ref: sentinelStart,\n      \"data-testid\": \"sentinelStart\"\n    }), /*#__PURE__*/React.cloneElement(children, {\n      ref: handleRef,\n      onFocus\n    }), /*#__PURE__*/_jsx(\"div\", {\n      tabIndex: open ? 0 : -1,\n      onFocus: handleFocusSentinel,\n      ref: sentinelEnd,\n      \"data-testid\": \"sentinelEnd\"\n    })]\n  });\n}\n\nprocess.env.NODE_ENV !== \"production\" ? TrapFocus.propTypes\n/* remove-proptypes */\n= {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef,\n\n  /**\n   * If `true`, the trap focus will not automatically shift focus to itself when it opens, and\n   * replace it to the last focused element when it closes.\n   * This also works correctly with any trap focus children that have the `disableAutoFocus` prop.\n   *\n   * Generally this should never be set to `true` as it makes the trap focus less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableAutoFocus: PropTypes.bool,\n\n  /**\n   * If `true`, the trap focus will not prevent focus from leaving the trap focus while open.\n   *\n   * Generally this should never be set to `true` as it makes the trap focus less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableEnforceFocus: PropTypes.bool,\n\n  /**\n   * If `true`, the trap focus will not restore focus to previously focused element once\n   * trap focus is hidden or unmounted.\n   * @default false\n   */\n  disableRestoreFocus: PropTypes.bool,\n\n  /**\n   * Returns an array of ordered tabbable nodes (i.e. in tab order) within the root.\n   * For instance, you can provide the \"tabbable\" npm dependency.\n   * @param {HTMLElement} root\n   */\n  getTabbable: PropTypes.func,\n\n  /**\n   * This prop extends the `open` prop.\n   * It allows to toggle the open state without having to wait for a rerender when changing the `open` prop.\n   * This prop should be memoized.\n   * It can be used to support multiple trap focus mounted at the same time.\n   * @default function defaultIsEnabled() {\n   *   return true;\n   * }\n   */\n  isEnabled: PropTypes.func,\n\n  /**\n   * If `true`, focus is locked.\n   */\n  open: PropTypes.bool.isRequired\n} : void 0;\n\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  TrapFocus['propTypes' + ''] = exactProp(TrapFocus.propTypes);\n}\n\nexport default TrapFocus;"], "mappings": "AAAA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,SAAS,EAAEC,mBAAmB,EAAEC,mBAAmB,IAAIC,UAAU,EAAEC,sBAAsB,IAAIC,aAAa,QAAQ,YAAY,CAAC,CAAC;;AAEzI,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,kBAAkB,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,kDAAkD,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AAEjM,SAASC,WAAWA,CAACC,IAAI,EAAE;EACzB,MAAMC,YAAY,GAAGC,QAAQ,CAACF,IAAI,CAACG,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC;EAEhE,IAAI,CAACC,MAAM,CAACC,KAAK,CAACJ,YAAY,CAAC,EAAE;IAC/B,OAAOA,YAAY;EACrB,CAAC,CAAC;EACF;EACA;EACA;EACA;EACA;EACA;EACA;;EAGA,IAAID,IAAI,CAACM,eAAe,KAAK,MAAM,IAAI,CAACN,IAAI,CAACO,QAAQ,KAAK,OAAO,IAAIP,IAAI,CAACO,QAAQ,KAAK,OAAO,IAAIP,IAAI,CAACO,QAAQ,KAAK,SAAS,KAAKP,IAAI,CAACG,YAAY,CAAC,UAAU,CAAC,KAAK,IAAI,EAAE;IACxK,OAAO,CAAC;EACV;EAEA,OAAOH,IAAI,CAACQ,QAAQ;AACtB;AAEA,SAASC,kBAAkBA,CAACT,IAAI,EAAE;EAChC,IAAIA,IAAI,CAACU,OAAO,KAAK,OAAO,IAAIV,IAAI,CAACW,IAAI,KAAK,OAAO,EAAE;IACrD,OAAO,KAAK;EACd;EAEA,IAAI,CAACX,IAAI,CAACY,IAAI,EAAE;IACd,OAAO,KAAK;EACd;EAEA,MAAMC,QAAQ,GAAGC,QAAQ,IAAId,IAAI,CAACR,aAAa,CAACuB,aAAa,CAAE,sBAAqBD,QAAS,EAAC,CAAC;EAE/F,IAAIE,MAAM,GAAGH,QAAQ,CAAE,UAASb,IAAI,CAACY,IAAK,YAAW,CAAC;EAEtD,IAAI,CAACI,MAAM,EAAE;IACXA,MAAM,GAAGH,QAAQ,CAAE,UAASb,IAAI,CAACY,IAAK,IAAG,CAAC;EAC5C;EAEA,OAAOI,MAAM,KAAKhB,IAAI;AACxB;AAEA,SAASiB,+BAA+BA,CAACjB,IAAI,EAAE;EAC7C,IAAIA,IAAI,CAACkB,QAAQ,IAAIlB,IAAI,CAACU,OAAO,KAAK,OAAO,IAAIV,IAAI,CAACW,IAAI,KAAK,QAAQ,IAAIF,kBAAkB,CAACT,IAAI,CAAC,EAAE;IACnG,OAAO,KAAK;EACd;EAEA,OAAO,IAAI;AACb;AAEA,SAASmB,kBAAkBA,CAACC,IAAI,EAAE;EAChC,MAAMC,eAAe,GAAG,EAAE;EAC1B,MAAMC,eAAe,GAAG,EAAE;EAC1BC,KAAK,CAACC,IAAI,CAACJ,IAAI,CAACK,gBAAgB,CAAC5B,kBAAkB,CAAC,CAAC,CAAC6B,OAAO,CAAC,CAAC1B,IAAI,EAAE2B,CAAC,KAAK;IACzE,MAAMC,YAAY,GAAG7B,WAAW,CAACC,IAAI,CAAC;IAEtC,IAAI4B,YAAY,KAAK,CAAC,CAAC,IAAI,CAACX,+BAA+B,CAACjB,IAAI,CAAC,EAAE;MACjE;IACF;IAEA,IAAI4B,YAAY,KAAK,CAAC,EAAE;MACtBP,eAAe,CAACQ,IAAI,CAAC7B,IAAI,CAAC;IAC5B,CAAC,MAAM;MACLsB,eAAe,CAACO,IAAI,CAAC;QACnBC,aAAa,EAAEH,CAAC;QAChBnB,QAAQ,EAAEoB,YAAY;QACtB5B;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAOsB,eAAe,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACxB,QAAQ,KAAKyB,CAAC,CAACzB,QAAQ,GAAGwB,CAAC,CAACF,aAAa,GAAGG,CAAC,CAACH,aAAa,GAAGE,CAAC,CAACxB,QAAQ,GAAGyB,CAAC,CAACzB,QAAQ,CAAC,CAAC0B,GAAG,CAACF,CAAC,IAAIA,CAAC,CAAChC,IAAI,CAAC,CAACmC,MAAM,CAACd,eAAe,CAAC;AACzK;AAEA,SAASe,gBAAgBA,CAAA,EAAG;EAC1B,OAAO,IAAI;AACb;AACA;AACA;AACA;;AAGA,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,MAAM;IACJC,QAAQ;IACRC,gBAAgB,GAAG,KAAK;IACxBC,mBAAmB,GAAG,KAAK;IAC3BC,mBAAmB,GAAG,KAAK;IAC3BC,WAAW,GAAGxB,kBAAkB;IAChCyB,SAAS,GAAGR,gBAAgB;IAC5BS;EACF,CAAC,GAAGP,KAAK;EACT,MAAMQ,sBAAsB,GAAG7D,KAAK,CAAC8D,MAAM,CAAC,CAAC;EAC7C,MAAMC,aAAa,GAAG/D,KAAK,CAAC8D,MAAM,CAAC,IAAI,CAAC;EACxC,MAAME,WAAW,GAAGhE,KAAK,CAAC8D,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMG,aAAa,GAAGjE,KAAK,CAAC8D,MAAM,CAAC,IAAI,CAAC;EACxC,MAAMI,qBAAqB,GAAGlE,KAAK,CAAC8D,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;EAClD;;EAEA,MAAMK,SAAS,GAAGnE,KAAK,CAAC8D,MAAM,CAAC,KAAK,CAAC;EACrC,MAAMM,OAAO,GAAGpE,KAAK,CAAC8D,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMO,SAAS,GAAGhE,UAAU,CAACiD,QAAQ,CAACgB,GAAG,EAAEF,OAAO,CAAC;EACnD,MAAMG,WAAW,GAAGvE,KAAK,CAAC8D,MAAM,CAAC,IAAI,CAAC;EACtC9D,KAAK,CAACwE,SAAS,CAAC,MAAM;IACpB;IACA,IAAI,CAACZ,IAAI,IAAI,CAACQ,OAAO,CAACK,OAAO,EAAE;MAC7B;IACF;IAEAN,SAAS,CAACM,OAAO,GAAG,CAAClB,gBAAgB;EACvC,CAAC,EAAE,CAACA,gBAAgB,EAAEK,IAAI,CAAC,CAAC;EAC5B5D,KAAK,CAACwE,SAAS,CAAC,MAAM;IACpB;IACA,IAAI,CAACZ,IAAI,IAAI,CAACQ,OAAO,CAACK,OAAO,EAAE;MAC7B;IACF;IAEA,MAAMC,GAAG,GAAGnE,aAAa,CAAC6D,OAAO,CAACK,OAAO,CAAC;IAE1C,IAAI,CAACL,OAAO,CAACK,OAAO,CAACE,QAAQ,CAACD,GAAG,CAACE,aAAa,CAAC,EAAE;MAChD,IAAI,CAACR,OAAO,CAACK,OAAO,CAACI,YAAY,CAAC,UAAU,CAAC,EAAE;QAC7C,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCC,OAAO,CAACC,KAAK,CAAC,CAAC,oDAAoD,EAAE,6CAA6C,GAAG,gDAAgD,CAAC,CAACrE,IAAI,CAAC,IAAI,CAAC,CAAC;QACpL;QAEAuD,OAAO,CAACK,OAAO,CAACU,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;MAC9C;MAEA,IAAIhB,SAAS,CAACM,OAAO,EAAE;QACrBL,OAAO,CAACK,OAAO,CAACW,KAAK,CAAC,CAAC;MACzB;IACF;IAEA,OAAO,MAAM;MACX;MACA,IAAI,CAAC3B,mBAAmB,EAAE;QACxB;QACA;QACA;QACA;QACA,IAAIQ,aAAa,CAACQ,OAAO,IAAIR,aAAa,CAACQ,OAAO,CAACW,KAAK,EAAE;UACxDvB,sBAAsB,CAACY,OAAO,GAAG,IAAI;UACrCR,aAAa,CAACQ,OAAO,CAACW,KAAK,CAAC,CAAC;QAC/B;QAEAnB,aAAa,CAACQ,OAAO,GAAG,IAAI;MAC9B;IACF,CAAC,CAAC,CAAC;IACH;IACA;EACF,CAAC,EAAE,CAACb,IAAI,CAAC,CAAC;EACV5D,KAAK,CAACwE,SAAS,CAAC,MAAM;IACpB;IACA,IAAI,CAACZ,IAAI,IAAI,CAACQ,OAAO,CAACK,OAAO,EAAE;MAC7B;IACF;IAEA,MAAMC,GAAG,GAAGnE,aAAa,CAAC6D,OAAO,CAACK,OAAO,CAAC;IAE1C,MAAMY,OAAO,GAAGC,WAAW,IAAI;MAC7B,MAAM;QACJb,OAAO,EAAEc;MACX,CAAC,GAAGnB,OAAO,CAAC,CAAC;MACb;;MAEA,IAAImB,WAAW,KAAK,IAAI,EAAE;QACxB;MACF;MAEA,IAAI,CAACb,GAAG,CAACc,QAAQ,CAAC,CAAC,IAAIhC,mBAAmB,IAAI,CAACG,SAAS,CAAC,CAAC,IAAIE,sBAAsB,CAACY,OAAO,EAAE;QAC5FZ,sBAAsB,CAACY,OAAO,GAAG,KAAK;QACtC;MACF;MAEA,IAAI,CAACc,WAAW,CAACZ,QAAQ,CAACD,GAAG,CAACE,aAAa,CAAC,EAAE;QAC5C;QACA,IAAIU,WAAW,IAAIpB,qBAAqB,CAACO,OAAO,KAAKa,WAAW,CAACG,MAAM,IAAIf,GAAG,CAACE,aAAa,KAAKV,qBAAqB,CAACO,OAAO,EAAE;UAC9HP,qBAAqB,CAACO,OAAO,GAAG,IAAI;QACtC,CAAC,MAAM,IAAIP,qBAAqB,CAACO,OAAO,KAAK,IAAI,EAAE;UACjD;QACF;QAEA,IAAI,CAACN,SAAS,CAACM,OAAO,EAAE;UACtB;QACF;QAEA,IAAIiB,QAAQ,GAAG,EAAE;QAEjB,IAAIhB,GAAG,CAACE,aAAa,KAAKb,aAAa,CAACU,OAAO,IAAIC,GAAG,CAACE,aAAa,KAAKZ,WAAW,CAACS,OAAO,EAAE;UAC5FiB,QAAQ,GAAGhC,WAAW,CAACU,OAAO,CAACK,OAAO,CAAC;QACzC;QAEA,IAAIiB,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;UACvB,IAAIC,oBAAoB,EAAEC,qBAAqB;UAE/C,MAAMC,UAAU,GAAGC,OAAO,CAAC,CAAC,CAACH,oBAAoB,GAAGrB,WAAW,CAACE,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmB,oBAAoB,CAACI,QAAQ,KAAK,CAAC,CAACH,qBAAqB,GAAGtB,WAAW,CAACE,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoB,qBAAqB,CAACI,GAAG,MAAM,KAAK,CAAC;UACrO,MAAMC,SAAS,GAAGR,QAAQ,CAAC,CAAC,CAAC;UAC7B,MAAMS,aAAa,GAAGT,QAAQ,CAACA,QAAQ,CAACC,MAAM,GAAG,CAAC,CAAC;UAEnD,IAAIG,UAAU,EAAE;YACdK,aAAa,CAACf,KAAK,CAAC,CAAC;UACvB,CAAC,MAAM;YACLc,SAAS,CAACd,KAAK,CAAC,CAAC;UACnB;QACF,CAAC,MAAM;UACLG,WAAW,CAACH,KAAK,CAAC,CAAC;QACrB;MACF;IACF,CAAC;IAED,MAAMgB,SAAS,GAAGd,WAAW,IAAI;MAC/Bf,WAAW,CAACE,OAAO,GAAGa,WAAW;MAEjC,IAAI9B,mBAAmB,IAAI,CAACG,SAAS,CAAC,CAAC,IAAI2B,WAAW,CAACW,GAAG,KAAK,KAAK,EAAE;QACpE;MACF,CAAC,CAAC;MACF;;MAGA,IAAIvB,GAAG,CAACE,aAAa,KAAKR,OAAO,CAACK,OAAO,IAAIa,WAAW,CAACU,QAAQ,EAAE;QACjE;QACA;QACAnC,sBAAsB,CAACY,OAAO,GAAG,IAAI;QACrCT,WAAW,CAACS,OAAO,CAACW,KAAK,CAAC,CAAC;MAC7B;IACF,CAAC;IAEDV,GAAG,CAAC2B,gBAAgB,CAAC,SAAS,EAAEhB,OAAO,CAAC;IACxCX,GAAG,CAAC2B,gBAAgB,CAAC,SAAS,EAAED,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;IAClD;IACA;IACA;IACA;IACA;;IAEA,MAAME,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC,IAAI7B,GAAG,CAACE,aAAa,CAACnD,OAAO,KAAK,MAAM,EAAE;QACxC4D,OAAO,CAAC,CAAC;MACX;IACF,CAAC,EAAE,EAAE,CAAC;IACN,OAAO,MAAM;MACXmB,aAAa,CAACF,QAAQ,CAAC;MACvB5B,GAAG,CAAC+B,mBAAmB,CAAC,SAAS,EAAEpB,OAAO,CAAC;MAC3CX,GAAG,CAAC+B,mBAAmB,CAAC,SAAS,EAAEL,SAAS,EAAE,IAAI,CAAC;IACrD,CAAC;EACH,CAAC,EAAE,CAAC7C,gBAAgB,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEE,SAAS,EAAEC,IAAI,EAAEF,WAAW,CAAC,CAAC;EAE9F,MAAMgD,OAAO,GAAGC,KAAK,IAAI;IACvB,IAAI1C,aAAa,CAACQ,OAAO,KAAK,IAAI,EAAE;MAClCR,aAAa,CAACQ,OAAO,GAAGkC,KAAK,CAACC,aAAa;IAC7C;IAEAzC,SAAS,CAACM,OAAO,GAAG,IAAI;IACxBP,qBAAqB,CAACO,OAAO,GAAGkC,KAAK,CAAClB,MAAM;IAC5C,MAAMoB,oBAAoB,GAAGvD,QAAQ,CAACD,KAAK,CAACqD,OAAO;IAEnD,IAAIG,oBAAoB,EAAE;MACxBA,oBAAoB,CAACF,KAAK,CAAC;IAC7B;EACF,CAAC;EAED,MAAMG,mBAAmB,GAAGH,KAAK,IAAI;IACnC,IAAI1C,aAAa,CAACQ,OAAO,KAAK,IAAI,EAAE;MAClCR,aAAa,CAACQ,OAAO,GAAGkC,KAAK,CAACC,aAAa;IAC7C;IAEAzC,SAAS,CAACM,OAAO,GAAG,IAAI;EAC1B,CAAC;EAED,OAAO,aAAa9D,KAAK,CAACX,KAAK,CAAC+G,QAAQ,EAAE;IACxCzD,QAAQ,EAAE,CAAC,aAAa7C,IAAI,CAAC,KAAK,EAAE;MAClCc,QAAQ,EAAEqC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;MACvB8C,OAAO,EAAEI,mBAAmB;MAC5BxC,GAAG,EAAEP,aAAa;MAClB,aAAa,EAAE;IACjB,CAAC,CAAC,EAAE,aAAa/D,KAAK,CAACgH,YAAY,CAAC1D,QAAQ,EAAE;MAC5CgB,GAAG,EAAED,SAAS;MACdqC;IACF,CAAC,CAAC,EAAE,aAAajG,IAAI,CAAC,KAAK,EAAE;MAC3Bc,QAAQ,EAAEqC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;MACvB8C,OAAO,EAAEI,mBAAmB;MAC5BxC,GAAG,EAAEN,WAAW;MAChB,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEAc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG5B,SAAS,CAAC6D;AAClD,yBACE;EACA;EACA;EACA;EACA;;EAEA;AACF;AACA;EACE3D,QAAQ,EAAEnD,mBAAmB;EAE7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEoD,gBAAgB,EAAEtD,SAAS,CAACiH,IAAI;EAEhC;AACF;AACA;AACA;AACA;AACA;AACA;EACE1D,mBAAmB,EAAEvD,SAAS,CAACiH,IAAI;EAEnC;AACF;AACA;AACA;AACA;EACEzD,mBAAmB,EAAExD,SAAS,CAACiH,IAAI;EAEnC;AACF;AACA;AACA;AACA;EACExD,WAAW,EAAEzD,SAAS,CAACkH,IAAI;EAE3B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACExD,SAAS,EAAE1D,SAAS,CAACkH,IAAI;EAEzB;AACF;AACA;EACEvD,IAAI,EAAE3D,SAAS,CAACiH,IAAI,CAACE;AACvB,CAAC,GAAG,KAAK,CAAC;AAEV,IAAItC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC;EACA5B,SAAS,CAAC,WAAW,GAAG,EAAE,CAAC,GAAGlD,SAAS,CAACkD,SAAS,CAAC6D,SAAS,CAAC;AAC9D;AAEA,eAAe7D,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}