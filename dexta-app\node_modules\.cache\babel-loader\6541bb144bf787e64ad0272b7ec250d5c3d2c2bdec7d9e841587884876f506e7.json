{"ast": null, "code": "import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getTablePaginationUnstyledUtilityClass(slot) {\n  return generateUtilityClass('MuiTablePaginationUnstyled', slot);\n}\nconst tablePaginationClasses = generateUtilityClasses('MuiTablePaginationUnstyled', ['root', 'toolbar', 'spacer', 'selectLabel', 'selectRoot', 'select', 'selectIcon', 'input', 'menuItem', 'displayedRows', 'actions']);\nexport default tablePaginationClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTablePaginationUnstyledUtilityClass", "slot", "tablePaginationClasses"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/TablePaginationUnstyled/tablePaginationUnstyledClasses.js"], "sourcesContent": ["import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getTablePaginationUnstyledUtilityClass(slot) {\n  return generateUtilityClass('MuiTablePaginationUnstyled', slot);\n}\nconst tablePaginationClasses = generateUtilityClasses('MuiTablePaginationUnstyled', ['root', 'toolbar', 'spacer', 'selectLabel', 'selectRoot', 'select', 'selectIcon', 'input', 'menuItem', 'displayedRows', 'actions']);\nexport default tablePaginationClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,yBAAyB;AAC1D,OAAOC,sBAAsB,MAAM,2BAA2B;AAC9D,OAAO,SAASC,sCAAsCA,CAACC,IAAI,EAAE;EAC3D,OAAOH,oBAAoB,CAAC,4BAA4B,EAAEG,IAAI,CAAC;AACjE;AACA,MAAMC,sBAAsB,GAAGH,sBAAsB,CAAC,4BAA4B,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,aAAa,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,SAAS,CAAC,CAAC;AACxN,eAAeG,sBAAsB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}