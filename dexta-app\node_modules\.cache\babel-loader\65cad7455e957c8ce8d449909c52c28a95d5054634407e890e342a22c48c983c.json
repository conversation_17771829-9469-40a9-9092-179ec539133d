{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { ActionTypes } from './useListbox.types';\nconst pageSize = 5;\nfunction findValidOptionToHighlight(index, lookupDirection, options, focusDisabled, isOptionDisabled, wrapAround) {\n  if (options.length === 0 || options.every((o, i) => isOptionDisabled(o, i))) {\n    return -1;\n  }\n  let nextFocus = index;\n  for (;;) {\n    // No valid options found\n    if (!wrapAround && lookupDirection === 'next' && nextFocus === options.length || !wrapAround && lookupDirection === 'previous' && nextFocus === -1) {\n      return -1;\n    }\n    const nextFocusDisabled = focusDisabled ? false : isOptionDisabled(options[nextFocus], nextFocus);\n    if (nextFocusDisabled) {\n      nextFocus += lookupDirection === 'next' ? 1 : -1;\n      if (wrapAround) {\n        nextFocus = (nextFocus + options.length) % options.length;\n      }\n    } else {\n      return nextFocus;\n    }\n  }\n}\nfunction getNewHighlightedOption(options, previouslyHighlightedOption, diff, lookupDirection, highlightDisabled, isOptionDisabled, wrapAround, optionComparer) {\n  var _options$nextIndex;\n  const maxIndex = options.length - 1;\n  const defaultHighlightedIndex = -1;\n  let nextIndexCandidate;\n  const previouslyHighlightedIndex = previouslyHighlightedOption == null ? -1 : options.findIndex(option => optionComparer(option, previouslyHighlightedOption));\n  if (diff === 'reset') {\n    var _options$defaultHighl;\n    return defaultHighlightedIndex === -1 ? null : (_options$defaultHighl = options[defaultHighlightedIndex]) != null ? _options$defaultHighl : null;\n  }\n  if (diff === 'start') {\n    nextIndexCandidate = 0;\n  } else if (diff === 'end') {\n    nextIndexCandidate = maxIndex;\n  } else {\n    const newIndex = previouslyHighlightedIndex + diff;\n    if (newIndex < 0) {\n      if (!wrapAround && previouslyHighlightedIndex !== -1 || Math.abs(diff) > 1) {\n        nextIndexCandidate = 0;\n      } else {\n        nextIndexCandidate = maxIndex;\n      }\n    } else if (newIndex > maxIndex) {\n      if (!wrapAround || Math.abs(diff) > 1) {\n        nextIndexCandidate = maxIndex;\n      } else {\n        nextIndexCandidate = 0;\n      }\n    } else {\n      nextIndexCandidate = newIndex;\n    }\n  }\n  const nextIndex = findValidOptionToHighlight(nextIndexCandidate, lookupDirection, options, highlightDisabled, isOptionDisabled, wrapAround);\n  return (_options$nextIndex = options[nextIndex]) != null ? _options$nextIndex : null;\n}\nfunction handleOptionSelection(option, state, props) {\n  const {\n    multiple,\n    optionComparer = (o, v) => o === v,\n    isOptionDisabled = () => false\n  } = props;\n  const {\n    selectedValue\n  } = state;\n  const optionIndex = props.options.findIndex(o => props.optionComparer(option, o));\n  if (isOptionDisabled(option, optionIndex)) {\n    return state;\n  }\n  if (multiple) {\n    var _ref, _ref2;\n    const selectedValues = (_ref = selectedValue) != null ? _ref : []; // if the option is already selected, remove it from the selection, otherwise add it\n\n    const newSelectedValues = selectedValues.some(sv => optionComparer(sv, option)) ? selectedValue.filter(v => !optionComparer(v, option)) : [...((_ref2 = selectedValue) != null ? _ref2 : []), option];\n    return {\n      selectedValue: newSelectedValues,\n      highlightedValue: option\n    };\n  }\n  if (selectedValue != null && optionComparer(option, selectedValue)) {\n    return state;\n  }\n  return {\n    selectedValue: option,\n    highlightedValue: option\n  };\n}\nfunction handleKeyDown(event, state, props) {\n  const {\n    options,\n    isOptionDisabled,\n    disableListWrap,\n    disabledItemsFocusable,\n    optionComparer\n  } = props;\n  const moveHighlight = (diff, direction, wrapAround) => {\n    return getNewHighlightedOption(options, state.highlightedValue, diff, direction, disabledItemsFocusable != null ? disabledItemsFocusable : false, isOptionDisabled != null ? isOptionDisabled : () => false, wrapAround, optionComparer);\n  };\n  switch (event.key) {\n    case 'Home':\n      return _extends({}, state, {\n        highlightedValue: moveHighlight('start', 'next', false)\n      });\n    case 'End':\n      return _extends({}, state, {\n        highlightedValue: moveHighlight('end', 'previous', false)\n      });\n    case 'PageUp':\n      return _extends({}, state, {\n        highlightedValue: moveHighlight(-pageSize, 'previous', false)\n      });\n    case 'PageDown':\n      return _extends({}, state, {\n        highlightedValue: moveHighlight(pageSize, 'next', false)\n      });\n    case 'ArrowUp':\n      // TODO: extend current selection with Shift modifier\n      return _extends({}, state, {\n        highlightedValue: moveHighlight(-1, 'previous', !(disableListWrap != null ? disableListWrap : false))\n      });\n    case 'ArrowDown':\n      // TODO: extend current selection with Shift modifier\n      return _extends({}, state, {\n        highlightedValue: moveHighlight(1, 'next', !(disableListWrap != null ? disableListWrap : false))\n      });\n    case 'Enter':\n    case ' ':\n      if (state.highlightedValue === null) {\n        return state;\n      }\n      return handleOptionSelection(state.highlightedValue, state, props);\n    default:\n      break;\n  }\n  return state;\n}\nfunction handleBlur(state) {\n  return _extends({}, state, {\n    highlightedValue: null\n  });\n}\nconst textCriteriaMatches = (nextFocus, searchString, stringifyOption) => {\n  var _stringifyOption;\n  const text = (_stringifyOption = stringifyOption(nextFocus)) == null ? void 0 : _stringifyOption.trim().toLowerCase();\n  if (!text || text.length === 0) {\n    // Make option not navigable if stringification fails or results in empty string.\n    return false;\n  }\n  return text.indexOf(searchString) === 0;\n};\nfunction handleTextNavigation(state, searchString, props) {\n  const {\n    options,\n    isOptionDisabled,\n    disableListWrap,\n    disabledItemsFocusable,\n    optionComparer,\n    optionStringifier\n  } = props;\n  const moveHighlight = previouslyHighlightedOption => {\n    return getNewHighlightedOption(options, previouslyHighlightedOption, 1, 'next', disabledItemsFocusable != null ? disabledItemsFocusable : false, isOptionDisabled != null ? isOptionDisabled : () => false, !(disableListWrap != null ? disableListWrap : false), optionComparer);\n  };\n  const startWithCurrentOption = searchString.length > 1;\n  let nextOption = startWithCurrentOption ? state.highlightedValue : moveHighlight(state.highlightedValue); // use `for` instead of `while` prevent infinite loop\n\n  for (let index = 0; index < options.length; index += 1) {\n    // Return un-mutated state if looped back to the currently highlighted value\n    if (!nextOption || !startWithCurrentOption && state.highlightedValue === nextOption) {\n      return state;\n    }\n    if (textCriteriaMatches(nextOption, searchString, optionStringifier) && (!isOptionDisabled(nextOption, options.indexOf(nextOption)) || disabledItemsFocusable)) {\n      // The nextOption is the element to be highlighted\n      return _extends({}, state, {\n        highlightedValue: nextOption\n      });\n    } // Move to the next element.\n\n    nextOption = moveHighlight(nextOption);\n  } // No option match text search criteria\n\n  return state;\n}\nfunction handleOptionsChange(options, previousOptions, state, props) {\n  var _options$find, _options$find2;\n  const {\n    multiple,\n    optionComparer\n  } = props;\n  const newHighlightedOption = state.highlightedValue == null ? null : (_options$find = options.find(option => optionComparer(option, state.highlightedValue))) != null ? _options$find : null;\n  if (multiple) {\n    var _ref3;\n\n    // exclude selected values that are no longer in the options\n    const selectedValues = (_ref3 = state.selectedValue) != null ? _ref3 : [];\n    const newSelectedValues = selectedValues.filter(selectedValue => options.some(option => optionComparer(option, selectedValue)));\n    return {\n      highlightedValue: newHighlightedOption,\n      selectedValue: newSelectedValues\n    };\n  }\n  const newSelectedValue = (_options$find2 = options.find(option => optionComparer(option, state.selectedValue))) != null ? _options$find2 : null;\n  return {\n    highlightedValue: newHighlightedOption,\n    selectedValue: newSelectedValue\n  };\n}\nexport default function defaultListboxReducer(state, action) {\n  const {\n    type\n  } = action;\n  switch (type) {\n    case ActionTypes.keyDown:\n      return handleKeyDown(action.event, state, action.props);\n    case ActionTypes.optionClick:\n      return handleOptionSelection(action.option, state, action.props);\n    case ActionTypes.blur:\n      return handleBlur(state);\n    case ActionTypes.setValue:\n      return _extends({}, state, {\n        selectedValue: action.value\n      });\n    case ActionTypes.setHighlight:\n      return _extends({}, state, {\n        highlightedValue: action.highlight\n      });\n    case ActionTypes.textNavigation:\n      return handleTextNavigation(state, action.searchString, action.props);\n    case ActionTypes.optionsChange:\n      return handleOptionsChange(action.options, action.previousOptions, state, action.props);\n    default:\n      return state;\n  }\n}", "map": {"version": 3, "names": ["_extends", "ActionTypes", "pageSize", "findValidOptionToHighlight", "index", "lookupDirection", "options", "focusDisabled", "isOptionDisabled", "wrapAround", "length", "every", "o", "i", "nextFocus", "nextFocusDisabled", "getNewHighlightedOption", "previouslyHighlightedOption", "diff", "highlightDisabled", "optionComparer", "_options$nextIndex", "maxIndex", "defaultHighlightedIndex", "nextIndexCandidate", "previouslyHighlightedIndex", "findIndex", "option", "_options$defaultHighl", "newIndex", "Math", "abs", "nextIndex", "handleOptionSelection", "state", "props", "multiple", "v", "selected<PERSON><PERSON><PERSON>", "optionIndex", "_ref", "_ref2", "<PERSON><PERSON><PERSON><PERSON>", "newSelectedValues", "some", "sv", "filter", "highlightedValue", "handleKeyDown", "event", "disableListWrap", "disabledItemsFocusable", "moveHighlight", "direction", "key", "handleBlur", "textCriteriaMatches", "searchString", "stringifyOption", "_stringifyOption", "text", "trim", "toLowerCase", "indexOf", "handleTextNavigation", "optionStringifier", "startWithCurrentOption", "nextOption", "handleOptionsChange", "previousOptions", "_options$find", "_options$find2", "newHighlightedOption", "find", "_ref3", "newSelectedValue", "defaultListboxReducer", "action", "type", "keyDown", "optionClick", "blur", "setValue", "value", "<PERSON><PERSON><PERSON><PERSON>", "highlight", "textNavigation", "optionsChange"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/ListboxUnstyled/defaultListboxReducer.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { ActionTypes } from './useListbox.types';\nconst pageSize = 5;\n\nfunction findValidOptionToHighlight(index, lookupDirection, options, focusDisabled, isOptionDisabled, wrapAround) {\n  if (options.length === 0 || options.every((o, i) => isOptionDisabled(o, i))) {\n    return -1;\n  }\n\n  let nextFocus = index;\n\n  for (;;) {\n    // No valid options found\n    if (!wrapAround && lookupDirection === 'next' && nextFocus === options.length || !wrapAround && lookupDirection === 'previous' && nextFocus === -1) {\n      return -1;\n    }\n\n    const nextFocusDisabled = focusDisabled ? false : isOptionDisabled(options[nextFocus], nextFocus);\n\n    if (nextFocusDisabled) {\n      nextFocus += lookupDirection === 'next' ? 1 : -1;\n\n      if (wrapAround) {\n        nextFocus = (nextFocus + options.length) % options.length;\n      }\n    } else {\n      return nextFocus;\n    }\n  }\n}\n\nfunction getNewHighlightedOption(options, previouslyHighlightedOption, diff, lookupDirection, highlightDisabled, isOptionDisabled, wrapAround, optionComparer) {\n  var _options$nextIndex;\n\n  const maxIndex = options.length - 1;\n  const defaultHighlightedIndex = -1;\n  let nextIndexCandidate;\n  const previouslyHighlightedIndex = previouslyHighlightedOption == null ? -1 : options.findIndex(option => optionComparer(option, previouslyHighlightedOption));\n\n  if (diff === 'reset') {\n    var _options$defaultHighl;\n\n    return defaultHighlightedIndex === -1 ? null : (_options$defaultHighl = options[defaultHighlightedIndex]) != null ? _options$defaultHighl : null;\n  }\n\n  if (diff === 'start') {\n    nextIndexCandidate = 0;\n  } else if (diff === 'end') {\n    nextIndexCandidate = maxIndex;\n  } else {\n    const newIndex = previouslyHighlightedIndex + diff;\n\n    if (newIndex < 0) {\n      if (!wrapAround && previouslyHighlightedIndex !== -1 || Math.abs(diff) > 1) {\n        nextIndexCandidate = 0;\n      } else {\n        nextIndexCandidate = maxIndex;\n      }\n    } else if (newIndex > maxIndex) {\n      if (!wrapAround || Math.abs(diff) > 1) {\n        nextIndexCandidate = maxIndex;\n      } else {\n        nextIndexCandidate = 0;\n      }\n    } else {\n      nextIndexCandidate = newIndex;\n    }\n  }\n\n  const nextIndex = findValidOptionToHighlight(nextIndexCandidate, lookupDirection, options, highlightDisabled, isOptionDisabled, wrapAround);\n  return (_options$nextIndex = options[nextIndex]) != null ? _options$nextIndex : null;\n}\n\nfunction handleOptionSelection(option, state, props) {\n  const {\n    multiple,\n    optionComparer = (o, v) => o === v,\n    isOptionDisabled = () => false\n  } = props;\n  const {\n    selectedValue\n  } = state;\n  const optionIndex = props.options.findIndex(o => props.optionComparer(option, o));\n\n  if (isOptionDisabled(option, optionIndex)) {\n    return state;\n  }\n\n  if (multiple) {\n    var _ref, _ref2;\n\n    const selectedValues = (_ref = selectedValue) != null ? _ref : []; // if the option is already selected, remove it from the selection, otherwise add it\n\n    const newSelectedValues = selectedValues.some(sv => optionComparer(sv, option)) ? selectedValue.filter(v => !optionComparer(v, option)) : [...((_ref2 = selectedValue) != null ? _ref2 : []), option];\n    return {\n      selectedValue: newSelectedValues,\n      highlightedValue: option\n    };\n  }\n\n  if (selectedValue != null && optionComparer(option, selectedValue)) {\n    return state;\n  }\n\n  return {\n    selectedValue: option,\n    highlightedValue: option\n  };\n}\n\nfunction handleKeyDown(event, state, props) {\n  const {\n    options,\n    isOptionDisabled,\n    disableListWrap,\n    disabledItemsFocusable,\n    optionComparer\n  } = props;\n\n  const moveHighlight = (diff, direction, wrapAround) => {\n    return getNewHighlightedOption(options, state.highlightedValue, diff, direction, disabledItemsFocusable != null ? disabledItemsFocusable : false, isOptionDisabled != null ? isOptionDisabled : () => false, wrapAround, optionComparer);\n  };\n\n  switch (event.key) {\n    case 'Home':\n      return _extends({}, state, {\n        highlightedValue: moveHighlight('start', 'next', false)\n      });\n\n    case 'End':\n      return _extends({}, state, {\n        highlightedValue: moveHighlight('end', 'previous', false)\n      });\n\n    case 'PageUp':\n      return _extends({}, state, {\n        highlightedValue: moveHighlight(-pageSize, 'previous', false)\n      });\n\n    case 'PageDown':\n      return _extends({}, state, {\n        highlightedValue: moveHighlight(pageSize, 'next', false)\n      });\n\n    case 'ArrowUp':\n      // TODO: extend current selection with Shift modifier\n      return _extends({}, state, {\n        highlightedValue: moveHighlight(-1, 'previous', !(disableListWrap != null ? disableListWrap : false))\n      });\n\n    case 'ArrowDown':\n      // TODO: extend current selection with Shift modifier\n      return _extends({}, state, {\n        highlightedValue: moveHighlight(1, 'next', !(disableListWrap != null ? disableListWrap : false))\n      });\n\n    case 'Enter':\n    case ' ':\n      if (state.highlightedValue === null) {\n        return state;\n      }\n\n      return handleOptionSelection(state.highlightedValue, state, props);\n\n    default:\n      break;\n  }\n\n  return state;\n}\n\nfunction handleBlur(state) {\n  return _extends({}, state, {\n    highlightedValue: null\n  });\n}\n\nconst textCriteriaMatches = (nextFocus, searchString, stringifyOption) => {\n  var _stringifyOption;\n\n  const text = (_stringifyOption = stringifyOption(nextFocus)) == null ? void 0 : _stringifyOption.trim().toLowerCase();\n\n  if (!text || text.length === 0) {\n    // Make option not navigable if stringification fails or results in empty string.\n    return false;\n  }\n\n  return text.indexOf(searchString) === 0;\n};\n\nfunction handleTextNavigation(state, searchString, props) {\n  const {\n    options,\n    isOptionDisabled,\n    disableListWrap,\n    disabledItemsFocusable,\n    optionComparer,\n    optionStringifier\n  } = props;\n\n  const moveHighlight = previouslyHighlightedOption => {\n    return getNewHighlightedOption(options, previouslyHighlightedOption, 1, 'next', disabledItemsFocusable != null ? disabledItemsFocusable : false, isOptionDisabled != null ? isOptionDisabled : () => false, !(disableListWrap != null ? disableListWrap : false), optionComparer);\n  };\n\n  const startWithCurrentOption = searchString.length > 1;\n  let nextOption = startWithCurrentOption ? state.highlightedValue : moveHighlight(state.highlightedValue); // use `for` instead of `while` prevent infinite loop\n\n  for (let index = 0; index < options.length; index += 1) {\n    // Return un-mutated state if looped back to the currently highlighted value\n    if (!nextOption || !startWithCurrentOption && state.highlightedValue === nextOption) {\n      return state;\n    }\n\n    if (textCriteriaMatches(nextOption, searchString, optionStringifier) && (!isOptionDisabled(nextOption, options.indexOf(nextOption)) || disabledItemsFocusable)) {\n      // The nextOption is the element to be highlighted\n      return _extends({}, state, {\n        highlightedValue: nextOption\n      });\n    } // Move to the next element.\n\n\n    nextOption = moveHighlight(nextOption);\n  } // No option match text search criteria\n\n\n  return state;\n}\n\nfunction handleOptionsChange(options, previousOptions, state, props) {\n  var _options$find, _options$find2;\n\n  const {\n    multiple,\n    optionComparer\n  } = props;\n  const newHighlightedOption = state.highlightedValue == null ? null : (_options$find = options.find(option => optionComparer(option, state.highlightedValue))) != null ? _options$find : null;\n\n  if (multiple) {\n    var _ref3;\n\n    // exclude selected values that are no longer in the options\n    const selectedValues = (_ref3 = state.selectedValue) != null ? _ref3 : [];\n    const newSelectedValues = selectedValues.filter(selectedValue => options.some(option => optionComparer(option, selectedValue)));\n    return {\n      highlightedValue: newHighlightedOption,\n      selectedValue: newSelectedValues\n    };\n  }\n\n  const newSelectedValue = (_options$find2 = options.find(option => optionComparer(option, state.selectedValue))) != null ? _options$find2 : null;\n  return {\n    highlightedValue: newHighlightedOption,\n    selectedValue: newSelectedValue\n  };\n}\n\nexport default function defaultListboxReducer(state, action) {\n  const {\n    type\n  } = action;\n\n  switch (type) {\n    case ActionTypes.keyDown:\n      return handleKeyDown(action.event, state, action.props);\n\n    case ActionTypes.optionClick:\n      return handleOptionSelection(action.option, state, action.props);\n\n    case ActionTypes.blur:\n      return handleBlur(state);\n\n    case ActionTypes.setValue:\n      return _extends({}, state, {\n        selectedValue: action.value\n      });\n\n    case ActionTypes.setHighlight:\n      return _extends({}, state, {\n        highlightedValue: action.highlight\n      });\n\n    case ActionTypes.textNavigation:\n      return handleTextNavigation(state, action.searchString, action.props);\n\n    case ActionTypes.optionsChange:\n      return handleOptionsChange(action.options, action.previousOptions, state, action.props);\n\n    default:\n      return state;\n  }\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,WAAW,QAAQ,oBAAoB;AAChD,MAAMC,QAAQ,GAAG,CAAC;AAElB,SAASC,0BAA0BA,CAACC,KAAK,EAAEC,eAAe,EAAEC,OAAO,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,UAAU,EAAE;EAChH,IAAIH,OAAO,CAACI,MAAM,KAAK,CAAC,IAAIJ,OAAO,CAACK,KAAK,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKL,gBAAgB,CAACI,CAAC,EAAEC,CAAC,CAAC,CAAC,EAAE;IAC3E,OAAO,CAAC,CAAC;EACX;EAEA,IAAIC,SAAS,GAAGV,KAAK;EAErB,SAAS;IACP;IACA,IAAI,CAACK,UAAU,IAAIJ,eAAe,KAAK,MAAM,IAAIS,SAAS,KAAKR,OAAO,CAACI,MAAM,IAAI,CAACD,UAAU,IAAIJ,eAAe,KAAK,UAAU,IAAIS,SAAS,KAAK,CAAC,CAAC,EAAE;MAClJ,OAAO,CAAC,CAAC;IACX;IAEA,MAAMC,iBAAiB,GAAGR,aAAa,GAAG,KAAK,GAAGC,gBAAgB,CAACF,OAAO,CAACQ,SAAS,CAAC,EAAEA,SAAS,CAAC;IAEjG,IAAIC,iBAAiB,EAAE;MACrBD,SAAS,IAAIT,eAAe,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MAEhD,IAAII,UAAU,EAAE;QACdK,SAAS,GAAG,CAACA,SAAS,GAAGR,OAAO,CAACI,MAAM,IAAIJ,OAAO,CAACI,MAAM;MAC3D;IACF,CAAC,MAAM;MACL,OAAOI,SAAS;IAClB;EACF;AACF;AAEA,SAASE,uBAAuBA,CAACV,OAAO,EAAEW,2BAA2B,EAAEC,IAAI,EAAEb,eAAe,EAAEc,iBAAiB,EAAEX,gBAAgB,EAAEC,UAAU,EAAEW,cAAc,EAAE;EAC7J,IAAIC,kBAAkB;EAEtB,MAAMC,QAAQ,GAAGhB,OAAO,CAACI,MAAM,GAAG,CAAC;EACnC,MAAMa,uBAAuB,GAAG,CAAC,CAAC;EAClC,IAAIC,kBAAkB;EACtB,MAAMC,0BAA0B,GAAGR,2BAA2B,IAAI,IAAI,GAAG,CAAC,CAAC,GAAGX,OAAO,CAACoB,SAAS,CAACC,MAAM,IAAIP,cAAc,CAACO,MAAM,EAAEV,2BAA2B,CAAC,CAAC;EAE9J,IAAIC,IAAI,KAAK,OAAO,EAAE;IACpB,IAAIU,qBAAqB;IAEzB,OAAOL,uBAAuB,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,CAACK,qBAAqB,GAAGtB,OAAO,CAACiB,uBAAuB,CAAC,KAAK,IAAI,GAAGK,qBAAqB,GAAG,IAAI;EAClJ;EAEA,IAAIV,IAAI,KAAK,OAAO,EAAE;IACpBM,kBAAkB,GAAG,CAAC;EACxB,CAAC,MAAM,IAAIN,IAAI,KAAK,KAAK,EAAE;IACzBM,kBAAkB,GAAGF,QAAQ;EAC/B,CAAC,MAAM;IACL,MAAMO,QAAQ,GAAGJ,0BAA0B,GAAGP,IAAI;IAElD,IAAIW,QAAQ,GAAG,CAAC,EAAE;MAChB,IAAI,CAACpB,UAAU,IAAIgB,0BAA0B,KAAK,CAAC,CAAC,IAAIK,IAAI,CAACC,GAAG,CAACb,IAAI,CAAC,GAAG,CAAC,EAAE;QAC1EM,kBAAkB,GAAG,CAAC;MACxB,CAAC,MAAM;QACLA,kBAAkB,GAAGF,QAAQ;MAC/B;IACF,CAAC,MAAM,IAAIO,QAAQ,GAAGP,QAAQ,EAAE;MAC9B,IAAI,CAACb,UAAU,IAAIqB,IAAI,CAACC,GAAG,CAACb,IAAI,CAAC,GAAG,CAAC,EAAE;QACrCM,kBAAkB,GAAGF,QAAQ;MAC/B,CAAC,MAAM;QACLE,kBAAkB,GAAG,CAAC;MACxB;IACF,CAAC,MAAM;MACLA,kBAAkB,GAAGK,QAAQ;IAC/B;EACF;EAEA,MAAMG,SAAS,GAAG7B,0BAA0B,CAACqB,kBAAkB,EAAEnB,eAAe,EAAEC,OAAO,EAAEa,iBAAiB,EAAEX,gBAAgB,EAAEC,UAAU,CAAC;EAC3I,OAAO,CAACY,kBAAkB,GAAGf,OAAO,CAAC0B,SAAS,CAAC,KAAK,IAAI,GAAGX,kBAAkB,GAAG,IAAI;AACtF;AAEA,SAASY,qBAAqBA,CAACN,MAAM,EAAEO,KAAK,EAAEC,KAAK,EAAE;EACnD,MAAM;IACJC,QAAQ;IACRhB,cAAc,GAAGA,CAACR,CAAC,EAAEyB,CAAC,KAAKzB,CAAC,KAAKyB,CAAC;IAClC7B,gBAAgB,GAAGA,CAAA,KAAM;EAC3B,CAAC,GAAG2B,KAAK;EACT,MAAM;IACJG;EACF,CAAC,GAAGJ,KAAK;EACT,MAAMK,WAAW,GAAGJ,KAAK,CAAC7B,OAAO,CAACoB,SAAS,CAACd,CAAC,IAAIuB,KAAK,CAACf,cAAc,CAACO,MAAM,EAAEf,CAAC,CAAC,CAAC;EAEjF,IAAIJ,gBAAgB,CAACmB,MAAM,EAAEY,WAAW,CAAC,EAAE;IACzC,OAAOL,KAAK;EACd;EAEA,IAAIE,QAAQ,EAAE;IACZ,IAAII,IAAI,EAAEC,KAAK;IAEf,MAAMC,cAAc,GAAG,CAACF,IAAI,GAAGF,aAAa,KAAK,IAAI,GAAGE,IAAI,GAAG,EAAE,CAAC,CAAC;;IAEnE,MAAMG,iBAAiB,GAAGD,cAAc,CAACE,IAAI,CAACC,EAAE,IAAIzB,cAAc,CAACyB,EAAE,EAAElB,MAAM,CAAC,CAAC,GAAGW,aAAa,CAACQ,MAAM,CAACT,CAAC,IAAI,CAACjB,cAAc,CAACiB,CAAC,EAAEV,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAACc,KAAK,GAAGH,aAAa,KAAK,IAAI,GAAGG,KAAK,GAAG,EAAE,CAAC,EAAEd,MAAM,CAAC;IACrM,OAAO;MACLW,aAAa,EAAEK,iBAAiB;MAChCI,gBAAgB,EAAEpB;IACpB,CAAC;EACH;EAEA,IAAIW,aAAa,IAAI,IAAI,IAAIlB,cAAc,CAACO,MAAM,EAAEW,aAAa,CAAC,EAAE;IAClE,OAAOJ,KAAK;EACd;EAEA,OAAO;IACLI,aAAa,EAAEX,MAAM;IACrBoB,gBAAgB,EAAEpB;EACpB,CAAC;AACH;AAEA,SAASqB,aAAaA,CAACC,KAAK,EAAEf,KAAK,EAAEC,KAAK,EAAE;EAC1C,MAAM;IACJ7B,OAAO;IACPE,gBAAgB;IAChB0C,eAAe;IACfC,sBAAsB;IACtB/B;EACF,CAAC,GAAGe,KAAK;EAET,MAAMiB,aAAa,GAAGA,CAAClC,IAAI,EAAEmC,SAAS,EAAE5C,UAAU,KAAK;IACrD,OAAOO,uBAAuB,CAACV,OAAO,EAAE4B,KAAK,CAACa,gBAAgB,EAAE7B,IAAI,EAAEmC,SAAS,EAAEF,sBAAsB,IAAI,IAAI,GAAGA,sBAAsB,GAAG,KAAK,EAAE3C,gBAAgB,IAAI,IAAI,GAAGA,gBAAgB,GAAG,MAAM,KAAK,EAAEC,UAAU,EAAEW,cAAc,CAAC;EAC1O,CAAC;EAED,QAAQ6B,KAAK,CAACK,GAAG;IACf,KAAK,MAAM;MACT,OAAOtD,QAAQ,CAAC,CAAC,CAAC,EAAEkC,KAAK,EAAE;QACzBa,gBAAgB,EAAEK,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK;MACxD,CAAC,CAAC;IAEJ,KAAK,KAAK;MACR,OAAOpD,QAAQ,CAAC,CAAC,CAAC,EAAEkC,KAAK,EAAE;QACzBa,gBAAgB,EAAEK,aAAa,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK;MAC1D,CAAC,CAAC;IAEJ,KAAK,QAAQ;MACX,OAAOpD,QAAQ,CAAC,CAAC,CAAC,EAAEkC,KAAK,EAAE;QACzBa,gBAAgB,EAAEK,aAAa,CAAC,CAAClD,QAAQ,EAAE,UAAU,EAAE,KAAK;MAC9D,CAAC,CAAC;IAEJ,KAAK,UAAU;MACb,OAAOF,QAAQ,CAAC,CAAC,CAAC,EAAEkC,KAAK,EAAE;QACzBa,gBAAgB,EAAEK,aAAa,CAAClD,QAAQ,EAAE,MAAM,EAAE,KAAK;MACzD,CAAC,CAAC;IAEJ,KAAK,SAAS;MACZ;MACA,OAAOF,QAAQ,CAAC,CAAC,CAAC,EAAEkC,KAAK,EAAE;QACzBa,gBAAgB,EAAEK,aAAa,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,EAAEF,eAAe,IAAI,IAAI,GAAGA,eAAe,GAAG,KAAK,CAAC;MACtG,CAAC,CAAC;IAEJ,KAAK,WAAW;MACd;MACA,OAAOlD,QAAQ,CAAC,CAAC,CAAC,EAAEkC,KAAK,EAAE;QACzBa,gBAAgB,EAAEK,aAAa,CAAC,CAAC,EAAE,MAAM,EAAE,EAAEF,eAAe,IAAI,IAAI,GAAGA,eAAe,GAAG,KAAK,CAAC;MACjG,CAAC,CAAC;IAEJ,KAAK,OAAO;IACZ,KAAK,GAAG;MACN,IAAIhB,KAAK,CAACa,gBAAgB,KAAK,IAAI,EAAE;QACnC,OAAOb,KAAK;MACd;MAEA,OAAOD,qBAAqB,CAACC,KAAK,CAACa,gBAAgB,EAAEb,KAAK,EAAEC,KAAK,CAAC;IAEpE;MACE;EACJ;EAEA,OAAOD,KAAK;AACd;AAEA,SAASqB,UAAUA,CAACrB,KAAK,EAAE;EACzB,OAAOlC,QAAQ,CAAC,CAAC,CAAC,EAAEkC,KAAK,EAAE;IACzBa,gBAAgB,EAAE;EACpB,CAAC,CAAC;AACJ;AAEA,MAAMS,mBAAmB,GAAGA,CAAC1C,SAAS,EAAE2C,YAAY,EAAEC,eAAe,KAAK;EACxE,IAAIC,gBAAgB;EAEpB,MAAMC,IAAI,GAAG,CAACD,gBAAgB,GAAGD,eAAe,CAAC5C,SAAS,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG6C,gBAAgB,CAACE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAErH,IAAI,CAACF,IAAI,IAAIA,IAAI,CAAClD,MAAM,KAAK,CAAC,EAAE;IAC9B;IACA,OAAO,KAAK;EACd;EAEA,OAAOkD,IAAI,CAACG,OAAO,CAACN,YAAY,CAAC,KAAK,CAAC;AACzC,CAAC;AAED,SAASO,oBAAoBA,CAAC9B,KAAK,EAAEuB,YAAY,EAAEtB,KAAK,EAAE;EACxD,MAAM;IACJ7B,OAAO;IACPE,gBAAgB;IAChB0C,eAAe;IACfC,sBAAsB;IACtB/B,cAAc;IACd6C;EACF,CAAC,GAAG9B,KAAK;EAET,MAAMiB,aAAa,GAAGnC,2BAA2B,IAAI;IACnD,OAAOD,uBAAuB,CAACV,OAAO,EAAEW,2BAA2B,EAAE,CAAC,EAAE,MAAM,EAAEkC,sBAAsB,IAAI,IAAI,GAAGA,sBAAsB,GAAG,KAAK,EAAE3C,gBAAgB,IAAI,IAAI,GAAGA,gBAAgB,GAAG,MAAM,KAAK,EAAE,EAAE0C,eAAe,IAAI,IAAI,GAAGA,eAAe,GAAG,KAAK,CAAC,EAAE9B,cAAc,CAAC;EACnR,CAAC;EAED,MAAM8C,sBAAsB,GAAGT,YAAY,CAAC/C,MAAM,GAAG,CAAC;EACtD,IAAIyD,UAAU,GAAGD,sBAAsB,GAAGhC,KAAK,CAACa,gBAAgB,GAAGK,aAAa,CAAClB,KAAK,CAACa,gBAAgB,CAAC,CAAC,CAAC;;EAE1G,KAAK,IAAI3C,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGE,OAAO,CAACI,MAAM,EAAEN,KAAK,IAAI,CAAC,EAAE;IACtD;IACA,IAAI,CAAC+D,UAAU,IAAI,CAACD,sBAAsB,IAAIhC,KAAK,CAACa,gBAAgB,KAAKoB,UAAU,EAAE;MACnF,OAAOjC,KAAK;IACd;IAEA,IAAIsB,mBAAmB,CAACW,UAAU,EAAEV,YAAY,EAAEQ,iBAAiB,CAAC,KAAK,CAACzD,gBAAgB,CAAC2D,UAAU,EAAE7D,OAAO,CAACyD,OAAO,CAACI,UAAU,CAAC,CAAC,IAAIhB,sBAAsB,CAAC,EAAE;MAC9J;MACA,OAAOnD,QAAQ,CAAC,CAAC,CAAC,EAAEkC,KAAK,EAAE;QACzBa,gBAAgB,EAAEoB;MACpB,CAAC,CAAC;IACJ,CAAC,CAAC;;IAGFA,UAAU,GAAGf,aAAa,CAACe,UAAU,CAAC;EACxC,CAAC,CAAC;;EAGF,OAAOjC,KAAK;AACd;AAEA,SAASkC,mBAAmBA,CAAC9D,OAAO,EAAE+D,eAAe,EAAEnC,KAAK,EAAEC,KAAK,EAAE;EACnE,IAAImC,aAAa,EAAEC,cAAc;EAEjC,MAAM;IACJnC,QAAQ;IACRhB;EACF,CAAC,GAAGe,KAAK;EACT,MAAMqC,oBAAoB,GAAGtC,KAAK,CAACa,gBAAgB,IAAI,IAAI,GAAG,IAAI,GAAG,CAACuB,aAAa,GAAGhE,OAAO,CAACmE,IAAI,CAAC9C,MAAM,IAAIP,cAAc,CAACO,MAAM,EAAEO,KAAK,CAACa,gBAAgB,CAAC,CAAC,KAAK,IAAI,GAAGuB,aAAa,GAAG,IAAI;EAE5L,IAAIlC,QAAQ,EAAE;IACZ,IAAIsC,KAAK;;IAET;IACA,MAAMhC,cAAc,GAAG,CAACgC,KAAK,GAAGxC,KAAK,CAACI,aAAa,KAAK,IAAI,GAAGoC,KAAK,GAAG,EAAE;IACzE,MAAM/B,iBAAiB,GAAGD,cAAc,CAACI,MAAM,CAACR,aAAa,IAAIhC,OAAO,CAACsC,IAAI,CAACjB,MAAM,IAAIP,cAAc,CAACO,MAAM,EAAEW,aAAa,CAAC,CAAC,CAAC;IAC/H,OAAO;MACLS,gBAAgB,EAAEyB,oBAAoB;MACtClC,aAAa,EAAEK;IACjB,CAAC;EACH;EAEA,MAAMgC,gBAAgB,GAAG,CAACJ,cAAc,GAAGjE,OAAO,CAACmE,IAAI,CAAC9C,MAAM,IAAIP,cAAc,CAACO,MAAM,EAAEO,KAAK,CAACI,aAAa,CAAC,CAAC,KAAK,IAAI,GAAGiC,cAAc,GAAG,IAAI;EAC/I,OAAO;IACLxB,gBAAgB,EAAEyB,oBAAoB;IACtClC,aAAa,EAAEqC;EACjB,CAAC;AACH;AAEA,eAAe,SAASC,qBAAqBA,CAAC1C,KAAK,EAAE2C,MAAM,EAAE;EAC3D,MAAM;IACJC;EACF,CAAC,GAAGD,MAAM;EAEV,QAAQC,IAAI;IACV,KAAK7E,WAAW,CAAC8E,OAAO;MACtB,OAAO/B,aAAa,CAAC6B,MAAM,CAAC5B,KAAK,EAAEf,KAAK,EAAE2C,MAAM,CAAC1C,KAAK,CAAC;IAEzD,KAAKlC,WAAW,CAAC+E,WAAW;MAC1B,OAAO/C,qBAAqB,CAAC4C,MAAM,CAAClD,MAAM,EAAEO,KAAK,EAAE2C,MAAM,CAAC1C,KAAK,CAAC;IAElE,KAAKlC,WAAW,CAACgF,IAAI;MACnB,OAAO1B,UAAU,CAACrB,KAAK,CAAC;IAE1B,KAAKjC,WAAW,CAACiF,QAAQ;MACvB,OAAOlF,QAAQ,CAAC,CAAC,CAAC,EAAEkC,KAAK,EAAE;QACzBI,aAAa,EAAEuC,MAAM,CAACM;MACxB,CAAC,CAAC;IAEJ,KAAKlF,WAAW,CAACmF,YAAY;MAC3B,OAAOpF,QAAQ,CAAC,CAAC,CAAC,EAAEkC,KAAK,EAAE;QACzBa,gBAAgB,EAAE8B,MAAM,CAACQ;MAC3B,CAAC,CAAC;IAEJ,KAAKpF,WAAW,CAACqF,cAAc;MAC7B,OAAOtB,oBAAoB,CAAC9B,KAAK,EAAE2C,MAAM,CAACpB,YAAY,EAAEoB,MAAM,CAAC1C,KAAK,CAAC;IAEvE,KAAKlC,WAAW,CAACsF,aAAa;MAC5B,OAAOnB,mBAAmB,CAACS,MAAM,CAACvE,OAAO,EAAEuE,MAAM,CAACR,eAAe,EAAEnC,KAAK,EAAE2C,MAAM,CAAC1C,KAAK,CAAC;IAEzF;MACE,OAAOD,KAAK;EAChB;AACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}