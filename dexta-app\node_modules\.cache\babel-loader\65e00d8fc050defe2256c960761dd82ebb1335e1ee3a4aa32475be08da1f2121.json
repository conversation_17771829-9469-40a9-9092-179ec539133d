{"ast": null, "code": "import { useRef, useEffect, useCallback } from 'react';\n\n// Saves incoming handler to the ref in order to avoid \"useCallback hell\"\nexport function useEventCallback(handler) {\n  var callbackRef = useRef(handler);\n  useEffect(() => {\n    callbackRef.current = handler;\n  });\n  return useCallback((value, event) => callbackRef.current && callbackRef.current(value, event), []);\n}\n\n// Check if an event was triggered by touch\nexport var isTouch = event => 'touches' in event;\n\n// Browsers introduced an intervention, making touch events passive by default.\n// This workaround removes `preventDefault` call from the touch handlers.\n// https://github.com/facebook/react/issues/19651\nexport var preventDefaultMove = event => {\n  !isTouch(event) && event.preventDefault && event.preventDefault();\n};\n// Clamps a value between an upper and lower bound.\n// We use ternary operators because it makes the minified code\n// 2 times shorter then `Math.min(Math.max(a,b),c)`\nexport var clamp = function clamp(number, min, max) {\n  if (min === void 0) {\n    min = 0;\n  }\n  if (max === void 0) {\n    max = 1;\n  }\n  return number > max ? max : number < min ? min : number;\n};\n// Returns a relative position of the pointer inside the node's bounding box\nexport var getRelativePosition = (node, event) => {\n  var rect = node.getBoundingClientRect();\n\n  // Get user's pointer position from `touches` array if it's a `TouchEvent`\n  var pointer = isTouch(event) ? event.touches[0] : event;\n  return {\n    left: clamp((pointer.pageX - (rect.left + window.pageXOffset)) / rect.width),\n    top: clamp((pointer.pageY - (rect.top + window.pageYOffset)) / rect.height),\n    width: rect.width,\n    height: rect.height,\n    x: pointer.pageX - (rect.left + window.pageXOffset),\n    y: pointer.pageY - (rect.top + window.pageYOffset)\n  };\n};", "map": {"version": 3, "names": ["useRef", "useEffect", "useCallback", "useEventCallback", "handler", "callback<PERSON><PERSON>", "current", "value", "event", "is<PERSON><PERSON>ch", "preventDefaultMove", "preventDefault", "clamp", "number", "min", "max", "getRelativePosition", "node", "rect", "getBoundingClientRect", "pointer", "touches", "left", "pageX", "window", "pageXOffset", "width", "top", "pageY", "pageYOffset", "height", "x", "y"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@uiw/react-drag-event-interactive/esm/utils.js"], "sourcesContent": ["import { useRef, useEffect, useCallback } from 'react';\n\n// Saves incoming handler to the ref in order to avoid \"useCallback hell\"\nexport function useEventCallback(handler) {\n  var callbackRef = useRef(handler);\n  useEffect(() => {\n    callbackRef.current = handler;\n  });\n  return useCallback((value, event) => callbackRef.current && callbackRef.current(value, event), []);\n}\n\n// Check if an event was triggered by touch\nexport var isTouch = event => 'touches' in event;\n\n// Browsers introduced an intervention, making touch events passive by default.\n// This workaround removes `preventDefault` call from the touch handlers.\n// https://github.com/facebook/react/issues/19651\nexport var preventDefaultMove = event => {\n  !isTouch(event) && event.preventDefault && event.preventDefault();\n};\n// Clamps a value between an upper and lower bound.\n// We use ternary operators because it makes the minified code\n// 2 times shorter then `Math.min(Math.max(a,b),c)`\nexport var clamp = function clamp(number, min, max) {\n  if (min === void 0) {\n    min = 0;\n  }\n  if (max === void 0) {\n    max = 1;\n  }\n  return number > max ? max : number < min ? min : number;\n};\n// Returns a relative position of the pointer inside the node's bounding box\nexport var getRelativePosition = (node, event) => {\n  var rect = node.getBoundingClientRect();\n\n  // Get user's pointer position from `touches` array if it's a `TouchEvent`\n  var pointer = isTouch(event) ? event.touches[0] : event;\n  return {\n    left: clamp((pointer.pageX - (rect.left + window.pageXOffset)) / rect.width),\n    top: clamp((pointer.pageY - (rect.top + window.pageYOffset)) / rect.height),\n    width: rect.width,\n    height: rect.height,\n    x: pointer.pageX - (rect.left + window.pageXOffset),\n    y: pointer.pageY - (rect.top + window.pageYOffset)\n  };\n};"], "mappings": "AAAA,SAASA,MAAM,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;;AAEtD;AACA,OAAO,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EACxC,IAAIC,WAAW,GAAGL,MAAM,CAACI,OAAO,CAAC;EACjCH,SAAS,CAAC,MAAM;IACdI,WAAW,CAACC,OAAO,GAAGF,OAAO;EAC/B,CAAC,CAAC;EACF,OAAOF,WAAW,CAAC,CAACK,KAAK,EAAEC,KAAK,KAAKH,WAAW,CAACC,OAAO,IAAID,WAAW,CAACC,OAAO,CAACC,KAAK,EAAEC,KAAK,CAAC,EAAE,EAAE,CAAC;AACpG;;AAEA;AACA,OAAO,IAAIC,OAAO,GAAGD,KAAK,IAAI,SAAS,IAAIA,KAAK;;AAEhD;AACA;AACA;AACA,OAAO,IAAIE,kBAAkB,GAAGF,KAAK,IAAI;EACvC,CAACC,OAAO,CAACD,KAAK,CAAC,IAAIA,KAAK,CAACG,cAAc,IAAIH,KAAK,CAACG,cAAc,CAAC,CAAC;AACnE,CAAC;AACD;AACA;AACA;AACA,OAAO,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAClD,IAAID,GAAG,KAAK,KAAK,CAAC,EAAE;IAClBA,GAAG,GAAG,CAAC;EACT;EACA,IAAIC,GAAG,KAAK,KAAK,CAAC,EAAE;IAClBA,GAAG,GAAG,CAAC;EACT;EACA,OAAOF,MAAM,GAAGE,GAAG,GAAGA,GAAG,GAAGF,MAAM,GAAGC,GAAG,GAAGA,GAAG,GAAGD,MAAM;AACzD,CAAC;AACD;AACA,OAAO,IAAIG,mBAAmB,GAAGA,CAACC,IAAI,EAAET,KAAK,KAAK;EAChD,IAAIU,IAAI,GAAGD,IAAI,CAACE,qBAAqB,CAAC,CAAC;;EAEvC;EACA,IAAIC,OAAO,GAAGX,OAAO,CAACD,KAAK,CAAC,GAAGA,KAAK,CAACa,OAAO,CAAC,CAAC,CAAC,GAAGb,KAAK;EACvD,OAAO;IACLc,IAAI,EAAEV,KAAK,CAAC,CAACQ,OAAO,CAACG,KAAK,IAAIL,IAAI,CAACI,IAAI,GAAGE,MAAM,CAACC,WAAW,CAAC,IAAIP,IAAI,CAACQ,KAAK,CAAC;IAC5EC,GAAG,EAAEf,KAAK,CAAC,CAACQ,OAAO,CAACQ,KAAK,IAAIV,IAAI,CAACS,GAAG,GAAGH,MAAM,CAACK,WAAW,CAAC,IAAIX,IAAI,CAACY,MAAM,CAAC;IAC3EJ,KAAK,EAAER,IAAI,CAACQ,KAAK;IACjBI,MAAM,EAAEZ,IAAI,CAACY,MAAM;IACnBC,CAAC,EAAEX,OAAO,CAACG,KAAK,IAAIL,IAAI,CAACI,IAAI,GAAGE,MAAM,CAACC,WAAW,CAAC;IACnDO,CAAC,EAAEZ,OAAO,CAACQ,KAAK,IAAIV,IAAI,CAACS,GAAG,GAAGH,MAAM,CAACK,WAAW;EACnD,CAAC;AACH,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}