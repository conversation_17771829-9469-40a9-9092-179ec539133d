{"ast": null, "code": "import { reduce } from './reduce';\nimport { isFunction } from '../util/isFunction';\nexport function min(comparer) {\n  return reduce(isFunction(comparer) ? function (x, y) {\n    return comparer(x, y) < 0 ? x : y;\n  } : function (x, y) {\n    return x < y ? x : y;\n  });\n}", "map": {"version": 3, "names": ["reduce", "isFunction", "min", "comparer", "x", "y"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\min.ts"], "sourcesContent": ["import { reduce } from './reduce';\nimport { MonoTypeOperatorFunction } from '../types';\nimport { isFunction } from '../util/isFunction';\n\n/**\n * The `min` operator operates on an Observable that emits numbers (or items that\n * can be compared with a provided function), and when source Observable completes\n * it emits a single item: the item with the smallest value.\n *\n * ![](min.png)\n *\n * ## Examples\n *\n * Get the minimal value of a series of numbers\n *\n * ```ts\n * import { of, min } from 'rxjs';\n *\n * of(5, 4, 7, 2, 8)\n *   .pipe(min())\n *   .subscribe(x => console.log(x));\n *\n * // Outputs\n * // 2\n * ```\n *\n * Use a comparer function to get the minimal item\n *\n * ```ts\n * import { of, min } from 'rxjs';\n *\n * of(\n *   { age: 7, name: 'Foo' },\n *   { age: 5, name: 'Bar' },\n *   { age: 9, name: 'Beer' }\n * ).pipe(\n *   min((a, b) => a.age < b.age ? -1 : 1)\n * )\n * .subscribe(x => console.log(x.name));\n *\n * // Outputs\n * // 'Bar'\n * ```\n *\n * @see {@link max}\n *\n * @param comparer Optional comparer function that it will use instead of its\n * default to compare the value of two items.\n * @return A function that returns an Observable that emits item with the\n * smallest value.\n */\nexport function min<T>(comparer?: (x: T, y: T) => number): MonoTypeOperatorFunction<T> {\n  return reduce(isFunction(comparer) ? (x, y) => (comparer(x, y) < 0 ? x : y) : (x, y) => (x < y ? x : y));\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,UAAU;AAEjC,SAASC,UAAU,QAAQ,oBAAoB;AAiD/C,OAAM,SAAUC,GAAGA,CAAIC,QAAiC;EACtD,OAAOH,MAAM,CAACC,UAAU,CAACE,QAAQ,CAAC,GAAG,UAACC,CAAC,EAAEC,CAAC;IAAK,OAACF,QAAQ,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAGC,CAAC;EAA3B,CAA4B,GAAG,UAACD,CAAC,EAAEC,CAAC;IAAK,OAACD,CAAC,GAAGC,CAAC,GAAGD,CAAC,GAAGC,CAAC;EAAd,CAAe,CAAC;AAC1G"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}