{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport areArraysEqual from '../utils/areArraysEqual';\n/**\n * Gets the current state. If the selectedValue is controlled,\n * the `value` prop is the source of truth instead of the internal state.\n */\n\nfunction getControlledState(internalState, props) {\n  if (props.value !== undefined) {\n    return _extends({}, internalState, {\n      selectedValue: props.value\n    });\n  }\n  return internalState;\n}\nfunction areOptionsEqual(option1, option2, optionComparer) {\n  if (option1 === option2) {\n    return true;\n  }\n  if (option1 === null || option2 === null) {\n    return false;\n  }\n  return optionComparer(option1, option2);\n}\n/**\n * Triggers change event handlers when reducer returns changed state.\n */\n\nfunction useStateChangeDetection(nextState, internalPreviousState, propsRef, hasDispatchedActionRef) {\n  React.useEffect(() => {\n    if (!propsRef.current || !hasDispatchedActionRef.current) {\n      // Detect changes only if an action has been dispatched.\n      return;\n    }\n    hasDispatchedActionRef.current = false;\n    const previousState = getControlledState(internalPreviousState, propsRef.current);\n    const {\n      multiple,\n      optionComparer\n    } = propsRef.current;\n    if (multiple) {\n      var _previousState$select;\n      const previousSelectedValues = (_previousState$select = previousState == null ? void 0 : previousState.selectedValue) != null ? _previousState$select : [];\n      const nextSelectedValues = nextState.selectedValue;\n      const onChange = propsRef.current.onChange;\n      if (!areArraysEqual(nextSelectedValues, previousSelectedValues, optionComparer)) {\n        onChange == null ? void 0 : onChange(nextSelectedValues);\n      }\n    } else {\n      const previousSelectedValue = previousState == null ? void 0 : previousState.selectedValue;\n      const nextSelectedValue = nextState.selectedValue;\n      const onChange = propsRef.current.onChange;\n      if (!areOptionsEqual(nextSelectedValue, previousSelectedValue, optionComparer)) {\n        onChange == null ? void 0 : onChange(nextSelectedValue);\n      }\n    }\n  }, [nextState.selectedValue, internalPreviousState, propsRef, hasDispatchedActionRef]);\n  React.useEffect(() => {\n    if (!propsRef.current) {\n      return;\n    } // Fires the highlightChange event when reducer returns changed `highlightedValue`.\n\n    if (!areOptionsEqual(internalPreviousState.highlightedValue, nextState.highlightedValue, propsRef.current.optionComparer)) {\n      var _propsRef$current, _propsRef$current$onH;\n      (_propsRef$current = propsRef.current) == null ? void 0 : (_propsRef$current$onH = _propsRef$current.onHighlightChange) == null ? void 0 : _propsRef$current$onH.call(_propsRef$current, nextState.highlightedValue);\n    }\n  }, [nextState.highlightedValue, internalPreviousState.highlightedValue, propsRef]);\n}\nexport default function useControllableReducer(internalReducer, externalReducer, props) {\n  var _ref;\n  const {\n    value,\n    defaultValue\n  } = props;\n  const propsRef = React.useRef(props);\n  propsRef.current = props;\n  const hasDispatchedActionRef = React.useRef(false);\n  const initialSelectedValue = (_ref = value === undefined ? defaultValue : value) != null ? _ref : props.multiple ? [] : null;\n  const initalState = {\n    highlightedValue: null,\n    selectedValue: initialSelectedValue\n  };\n  const combinedReducer = React.useCallback((state, action) => {\n    hasDispatchedActionRef.current = true;\n    if (externalReducer) {\n      return externalReducer(getControlledState(state, propsRef.current), action);\n    }\n    return internalReducer(getControlledState(state, propsRef.current), action);\n  }, [externalReducer, internalReducer, propsRef]);\n  const [nextState, dispatch] = React.useReducer(combinedReducer, initalState);\n  const previousState = React.useRef(initalState);\n  React.useEffect(() => {\n    previousState.current = nextState;\n  }, [previousState, nextState]);\n  useStateChangeDetection(nextState, previousState.current, propsRef, hasDispatchedActionRef);\n  return [getControlledState(nextState, propsRef.current), dispatch];\n}", "map": {"version": 3, "names": ["_extends", "React", "areArraysEqual", "getControlledState", "internalState", "props", "value", "undefined", "selected<PERSON><PERSON><PERSON>", "areOptionsEqual", "option1", "option2", "optionComparer", "useStateChangeDetection", "nextState", "internalPreviousState", "propsRef", "hasDispatchedActionRef", "useEffect", "current", "previousState", "multiple", "_previousState$select", "previousSelectedValues", "nextSelectedValues", "onChange", "previousSelectedValue", "nextSelectedValue", "highlightedValue", "_propsRef$current", "_propsRef$current$onH", "onHighlightChange", "call", "useControllableReducer", "internalReducer", "externalReducer", "_ref", "defaultValue", "useRef", "initialSelectedValue", "initalState", "combinedReducer", "useCallback", "state", "action", "dispatch", "useReducer"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/ListboxUnstyled/useControllableReducer.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport areArraysEqual from '../utils/areArraysEqual';\n/**\n * Gets the current state. If the selectedValue is controlled,\n * the `value` prop is the source of truth instead of the internal state.\n */\n\nfunction getControlledState(internalState, props) {\n  if (props.value !== undefined) {\n    return _extends({}, internalState, {\n      selectedValue: props.value\n    });\n  }\n\n  return internalState;\n}\n\nfunction areOptionsEqual(option1, option2, optionComparer) {\n  if (option1 === option2) {\n    return true;\n  }\n\n  if (option1 === null || option2 === null) {\n    return false;\n  }\n\n  return optionComparer(option1, option2);\n}\n/**\n * Triggers change event handlers when reducer returns changed state.\n */\n\n\nfunction useStateChangeDetection(nextState, internalPreviousState, propsRef, hasDispatchedActionRef) {\n  React.useEffect(() => {\n    if (!propsRef.current || !hasDispatchedActionRef.current) {\n      // Detect changes only if an action has been dispatched.\n      return;\n    }\n\n    hasDispatchedActionRef.current = false;\n    const previousState = getControlledState(internalPreviousState, propsRef.current);\n    const {\n      multiple,\n      optionComparer\n    } = propsRef.current;\n\n    if (multiple) {\n      var _previousState$select;\n\n      const previousSelectedValues = (_previousState$select = previousState == null ? void 0 : previousState.selectedValue) != null ? _previousState$select : [];\n      const nextSelectedValues = nextState.selectedValue;\n      const onChange = propsRef.current.onChange;\n\n      if (!areArraysEqual(nextSelectedValues, previousSelectedValues, optionComparer)) {\n        onChange == null ? void 0 : onChange(nextSelectedValues);\n      }\n    } else {\n      const previousSelectedValue = previousState == null ? void 0 : previousState.selectedValue;\n      const nextSelectedValue = nextState.selectedValue;\n      const onChange = propsRef.current.onChange;\n\n      if (!areOptionsEqual(nextSelectedValue, previousSelectedValue, optionComparer)) {\n        onChange == null ? void 0 : onChange(nextSelectedValue);\n      }\n    }\n  }, [nextState.selectedValue, internalPreviousState, propsRef, hasDispatchedActionRef]);\n  React.useEffect(() => {\n    if (!propsRef.current) {\n      return;\n    } // Fires the highlightChange event when reducer returns changed `highlightedValue`.\n\n\n    if (!areOptionsEqual(internalPreviousState.highlightedValue, nextState.highlightedValue, propsRef.current.optionComparer)) {\n      var _propsRef$current, _propsRef$current$onH;\n\n      (_propsRef$current = propsRef.current) == null ? void 0 : (_propsRef$current$onH = _propsRef$current.onHighlightChange) == null ? void 0 : _propsRef$current$onH.call(_propsRef$current, nextState.highlightedValue);\n    }\n  }, [nextState.highlightedValue, internalPreviousState.highlightedValue, propsRef]);\n}\n\nexport default function useControllableReducer(internalReducer, externalReducer, props) {\n  var _ref;\n\n  const {\n    value,\n    defaultValue\n  } = props;\n  const propsRef = React.useRef(props);\n  propsRef.current = props;\n  const hasDispatchedActionRef = React.useRef(false);\n  const initialSelectedValue = (_ref = value === undefined ? defaultValue : value) != null ? _ref : props.multiple ? [] : null;\n  const initalState = {\n    highlightedValue: null,\n    selectedValue: initialSelectedValue\n  };\n  const combinedReducer = React.useCallback((state, action) => {\n    hasDispatchedActionRef.current = true;\n\n    if (externalReducer) {\n      return externalReducer(getControlledState(state, propsRef.current), action);\n    }\n\n    return internalReducer(getControlledState(state, propsRef.current), action);\n  }, [externalReducer, internalReducer, propsRef]);\n  const [nextState, dispatch] = React.useReducer(combinedReducer, initalState);\n  const previousState = React.useRef(initalState);\n  React.useEffect(() => {\n    previousState.current = nextState;\n  }, [previousState, nextState]);\n  useStateChangeDetection(nextState, previousState.current, propsRef, hasDispatchedActionRef);\n  return [getControlledState(nextState, propsRef.current), dispatch];\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,yBAAyB;AACpD;AACA;AACA;AACA;;AAEA,SAASC,kBAAkBA,CAACC,aAAa,EAAEC,KAAK,EAAE;EAChD,IAAIA,KAAK,CAACC,KAAK,KAAKC,SAAS,EAAE;IAC7B,OAAOP,QAAQ,CAAC,CAAC,CAAC,EAAEI,aAAa,EAAE;MACjCI,aAAa,EAAEH,KAAK,CAACC;IACvB,CAAC,CAAC;EACJ;EAEA,OAAOF,aAAa;AACtB;AAEA,SAASK,eAAeA,CAACC,OAAO,EAAEC,OAAO,EAAEC,cAAc,EAAE;EACzD,IAAIF,OAAO,KAAKC,OAAO,EAAE;IACvB,OAAO,IAAI;EACb;EAEA,IAAID,OAAO,KAAK,IAAI,IAAIC,OAAO,KAAK,IAAI,EAAE;IACxC,OAAO,KAAK;EACd;EAEA,OAAOC,cAAc,CAACF,OAAO,EAAEC,OAAO,CAAC;AACzC;AACA;AACA;AACA;;AAGA,SAASE,uBAAuBA,CAACC,SAAS,EAAEC,qBAAqB,EAAEC,QAAQ,EAAEC,sBAAsB,EAAE;EACnGhB,KAAK,CAACiB,SAAS,CAAC,MAAM;IACpB,IAAI,CAACF,QAAQ,CAACG,OAAO,IAAI,CAACF,sBAAsB,CAACE,OAAO,EAAE;MACxD;MACA;IACF;IAEAF,sBAAsB,CAACE,OAAO,GAAG,KAAK;IACtC,MAAMC,aAAa,GAAGjB,kBAAkB,CAACY,qBAAqB,EAAEC,QAAQ,CAACG,OAAO,CAAC;IACjF,MAAM;MACJE,QAAQ;MACRT;IACF,CAAC,GAAGI,QAAQ,CAACG,OAAO;IAEpB,IAAIE,QAAQ,EAAE;MACZ,IAAIC,qBAAqB;MAEzB,MAAMC,sBAAsB,GAAG,CAACD,qBAAqB,GAAGF,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACZ,aAAa,KAAK,IAAI,GAAGc,qBAAqB,GAAG,EAAE;MAC1J,MAAME,kBAAkB,GAAGV,SAAS,CAACN,aAAa;MAClD,MAAMiB,QAAQ,GAAGT,QAAQ,CAACG,OAAO,CAACM,QAAQ;MAE1C,IAAI,CAACvB,cAAc,CAACsB,kBAAkB,EAAED,sBAAsB,EAAEX,cAAc,CAAC,EAAE;QAC/Ea,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACD,kBAAkB,CAAC;MAC1D;IACF,CAAC,MAAM;MACL,MAAME,qBAAqB,GAAGN,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACZ,aAAa;MAC1F,MAAMmB,iBAAiB,GAAGb,SAAS,CAACN,aAAa;MACjD,MAAMiB,QAAQ,GAAGT,QAAQ,CAACG,OAAO,CAACM,QAAQ;MAE1C,IAAI,CAAChB,eAAe,CAACkB,iBAAiB,EAAED,qBAAqB,EAAEd,cAAc,CAAC,EAAE;QAC9Ea,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACE,iBAAiB,CAAC;MACzD;IACF;EACF,CAAC,EAAE,CAACb,SAAS,CAACN,aAAa,EAAEO,qBAAqB,EAAEC,QAAQ,EAAEC,sBAAsB,CAAC,CAAC;EACtFhB,KAAK,CAACiB,SAAS,CAAC,MAAM;IACpB,IAAI,CAACF,QAAQ,CAACG,OAAO,EAAE;MACrB;IACF,CAAC,CAAC;;IAGF,IAAI,CAACV,eAAe,CAACM,qBAAqB,CAACa,gBAAgB,EAAEd,SAAS,CAACc,gBAAgB,EAAEZ,QAAQ,CAACG,OAAO,CAACP,cAAc,CAAC,EAAE;MACzH,IAAIiB,iBAAiB,EAAEC,qBAAqB;MAE5C,CAACD,iBAAiB,GAAGb,QAAQ,CAACG,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,CAACW,qBAAqB,GAAGD,iBAAiB,CAACE,iBAAiB,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,qBAAqB,CAACE,IAAI,CAACH,iBAAiB,EAAEf,SAAS,CAACc,gBAAgB,CAAC;IACtN;EACF,CAAC,EAAE,CAACd,SAAS,CAACc,gBAAgB,EAAEb,qBAAqB,CAACa,gBAAgB,EAAEZ,QAAQ,CAAC,CAAC;AACpF;AAEA,eAAe,SAASiB,sBAAsBA,CAACC,eAAe,EAAEC,eAAe,EAAE9B,KAAK,EAAE;EACtF,IAAI+B,IAAI;EAER,MAAM;IACJ9B,KAAK;IACL+B;EACF,CAAC,GAAGhC,KAAK;EACT,MAAMW,QAAQ,GAAGf,KAAK,CAACqC,MAAM,CAACjC,KAAK,CAAC;EACpCW,QAAQ,CAACG,OAAO,GAAGd,KAAK;EACxB,MAAMY,sBAAsB,GAAGhB,KAAK,CAACqC,MAAM,CAAC,KAAK,CAAC;EAClD,MAAMC,oBAAoB,GAAG,CAACH,IAAI,GAAG9B,KAAK,KAAKC,SAAS,GAAG8B,YAAY,GAAG/B,KAAK,KAAK,IAAI,GAAG8B,IAAI,GAAG/B,KAAK,CAACgB,QAAQ,GAAG,EAAE,GAAG,IAAI;EAC5H,MAAMmB,WAAW,GAAG;IAClBZ,gBAAgB,EAAE,IAAI;IACtBpB,aAAa,EAAE+B;EACjB,CAAC;EACD,MAAME,eAAe,GAAGxC,KAAK,CAACyC,WAAW,CAAC,CAACC,KAAK,EAAEC,MAAM,KAAK;IAC3D3B,sBAAsB,CAACE,OAAO,GAAG,IAAI;IAErC,IAAIgB,eAAe,EAAE;MACnB,OAAOA,eAAe,CAAChC,kBAAkB,CAACwC,KAAK,EAAE3B,QAAQ,CAACG,OAAO,CAAC,EAAEyB,MAAM,CAAC;IAC7E;IAEA,OAAOV,eAAe,CAAC/B,kBAAkB,CAACwC,KAAK,EAAE3B,QAAQ,CAACG,OAAO,CAAC,EAAEyB,MAAM,CAAC;EAC7E,CAAC,EAAE,CAACT,eAAe,EAAED,eAAe,EAAElB,QAAQ,CAAC,CAAC;EAChD,MAAM,CAACF,SAAS,EAAE+B,QAAQ,CAAC,GAAG5C,KAAK,CAAC6C,UAAU,CAACL,eAAe,EAAED,WAAW,CAAC;EAC5E,MAAMpB,aAAa,GAAGnB,KAAK,CAACqC,MAAM,CAACE,WAAW,CAAC;EAC/CvC,KAAK,CAACiB,SAAS,CAAC,MAAM;IACpBE,aAAa,CAACD,OAAO,GAAGL,SAAS;EACnC,CAAC,EAAE,CAACM,aAAa,EAAEN,SAAS,CAAC,CAAC;EAC9BD,uBAAuB,CAACC,SAAS,EAAEM,aAAa,CAACD,OAAO,EAAEH,QAAQ,EAAEC,sBAAsB,CAAC;EAC3F,OAAO,CAACd,kBAAkB,CAACW,SAAS,EAAEE,QAAQ,CAACG,OAAO,CAAC,EAAE0B,QAAQ,CAAC;AACpE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}