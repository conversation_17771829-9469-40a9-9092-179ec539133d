{"ast": null, "code": "'use client';\n\nimport PropTypes from 'prop-types';\nimport createBox from '../createBox';\nconst Box = createBox();\nprocess.env.NODE_ENV !== \"production\" ? Box.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Box;", "map": {"version": 3, "names": ["PropTypes", "createBox", "Box", "process", "env", "NODE_ENV", "propTypes", "children", "node", "component", "elementType", "sx", "oneOfType", "arrayOf", "func", "object", "bool"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/system/esm/Box/Box.js"], "sourcesContent": ["'use client';\n\nimport PropTypes from 'prop-types';\nimport createBox from '../createBox';\nconst Box = createBox();\nprocess.env.NODE_ENV !== \"production\" ? Box.propTypes /* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit the d.ts file and run \"yarn proptypes\"     |\n  // ----------------------------------------------------------------------\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Box;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,SAAS,MAAM,YAAY;AAClC,OAAOC,SAAS,MAAM,cAAc;AACpC,MAAMC,GAAG,GAAGD,SAAS,CAAC,CAAC;AACvBE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGH,GAAG,CAACI,SAAS,CAAC,yBAAyB;EAC7E;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEP,SAAS,CAACQ,IAAI;EACxB;AACF;AACA;AACA;EACEC,SAAS,EAAET,SAAS,CAACU,WAAW;EAChC;AACF;AACA;EACEC,EAAE,EAAEX,SAAS,CAACY,SAAS,CAAC,CAACZ,SAAS,CAACa,OAAO,CAACb,SAAS,CAACY,SAAS,CAAC,CAACZ,SAAS,CAACc,IAAI,EAAEd,SAAS,CAACe,MAAM,EAAEf,SAAS,CAACgB,IAAI,CAAC,CAAC,CAAC,EAAEhB,SAAS,CAACc,IAAI,EAAEd,SAAS,CAACe,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAeb,GAAG"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}