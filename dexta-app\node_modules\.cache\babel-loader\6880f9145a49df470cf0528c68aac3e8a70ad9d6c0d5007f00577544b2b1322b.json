{"ast": null, "code": "export function isOptionGroup(child) {\n  return !!child.options;\n}", "map": {"version": 3, "names": ["isOptionGroup", "child", "options"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/SelectUnstyled/useSelect.types.js"], "sourcesContent": ["export function isOptionGroup(child) {\n  return !!child.options;\n}"], "mappings": "AAAA,OAAO,SAASA,aAAaA,CAACC,KAAK,EAAE;EACnC,OAAO,CAAC,CAACA,KAAK,CAACC,OAAO;AACxB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}