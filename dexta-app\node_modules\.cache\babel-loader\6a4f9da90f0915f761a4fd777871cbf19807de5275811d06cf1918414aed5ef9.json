{"ast": null, "code": "var getChildren = exports.getChildren = function (elem) {\n  return elem.children;\n};\nvar getParent = exports.getParent = function (elem) {\n  return elem.parent;\n};\nexports.getSiblings = function (elem) {\n  var parent = getParent(elem);\n  return parent ? getChildren(parent) : [elem];\n};\nexports.getAttributeValue = function (elem, name) {\n  return elem.attribs && elem.attribs[name];\n};\nexports.hasAttrib = function (elem, name) {\n  return !!elem.attribs && hasOwnProperty.call(elem.attribs, name);\n};\nexports.getName = function (elem) {\n  return elem.name;\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "exports", "elem", "children", "getParent", "parent", "get<PERSON><PERSON><PERSON>", "getAttributeValue", "name", "attribs", "hasAttrib", "hasOwnProperty", "call", "getName"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/domutils/lib/traversal.js"], "sourcesContent": ["var getChildren = exports.getChildren = function(elem){\n\treturn elem.children;\n};\n\nvar getParent = exports.getParent = function(elem){\n\treturn elem.parent;\n};\n\nexports.getSiblings = function(elem){\n\tvar parent = getParent(elem);\n\treturn parent ? getChildren(parent) : [elem];\n};\n\nexports.getAttributeValue = function(elem, name){\n\treturn elem.attribs && elem.attribs[name];\n};\n\nexports.hasAttrib = function(elem, name){\n\treturn !!elem.attribs && hasOwnProperty.call(elem.attribs, name);\n};\n\nexports.getName = function(elem){\n\treturn elem.name;\n};\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAACD,WAAW,GAAG,UAASE,IAAI,EAAC;EACrD,OAAOA,IAAI,CAACC,QAAQ;AACrB,CAAC;AAED,IAAIC,SAAS,GAAGH,OAAO,CAACG,SAAS,GAAG,UAASF,IAAI,EAAC;EACjD,OAAOA,IAAI,CAACG,MAAM;AACnB,CAAC;AAEDJ,OAAO,CAACK,WAAW,GAAG,UAASJ,IAAI,EAAC;EACnC,IAAIG,MAAM,GAAGD,SAAS,CAACF,IAAI,CAAC;EAC5B,OAAOG,MAAM,GAAGL,WAAW,CAACK,MAAM,CAAC,GAAG,CAACH,IAAI,CAAC;AAC7C,CAAC;AAEDD,OAAO,CAACM,iBAAiB,GAAG,UAASL,IAAI,EAAEM,IAAI,EAAC;EAC/C,OAAON,IAAI,CAACO,OAAO,IAAIP,IAAI,CAACO,OAAO,CAACD,IAAI,CAAC;AAC1C,CAAC;AAEDP,OAAO,CAACS,SAAS,GAAG,UAASR,IAAI,EAAEM,IAAI,EAAC;EACvC,OAAO,CAAC,CAACN,IAAI,CAACO,OAAO,IAAIE,cAAc,CAACC,IAAI,CAACV,IAAI,CAACO,OAAO,EAAED,IAAI,CAAC;AACjE,CAAC;AAEDP,OAAO,CAACY,OAAO,GAAG,UAASX,IAAI,EAAC;EAC/B,OAAOA,IAAI,CAACM,IAAI;AACjB,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}