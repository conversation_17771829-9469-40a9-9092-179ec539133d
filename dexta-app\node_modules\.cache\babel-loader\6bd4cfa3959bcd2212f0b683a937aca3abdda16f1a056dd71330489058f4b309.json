{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useControlled as useControlled, unstable_useForkRef as useForkRef, unstable_useIsFocusVisible as useIsFocusVisible } from '@mui/utils';\n\n/**\n * The basic building block for creating custom switches.\n *\n * Demos:\n *\n * - [Switches](https://mui.com/components/switches/)\n */\nexport default function useSwitch(props) {\n  const {\n    checked: checkedProp,\n    defaultChecked,\n    disabled,\n    onBlur,\n    onChange,\n    onFocus,\n    onFocusVisible,\n    readOnly,\n    required\n  } = props;\n  const [checked, setCheckedState] = useControlled({\n    controlled: checkedProp,\n    default: Boolean(defaultChecked),\n    name: 'Switch',\n    state: 'checked'\n  });\n  const createHandleInputChange = otherProps => event => {\n    var _otherProps$onChange;\n\n    // Workaround for https://github.com/facebook/react/issues/9023\n    if (event.nativeEvent.defaultPrevented) {\n      return;\n    }\n    setCheckedState(event.target.checked);\n    onChange == null ? void 0 : onChange(event);\n    (_otherProps$onChange = otherProps.onChange) == null ? void 0 : _otherProps$onChange.call(otherProps, event);\n  };\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusVisible, setFocusVisible] = React.useState(false);\n  if (disabled && focusVisible) {\n    setFocusVisible(false);\n  }\n  React.useEffect(() => {\n    isFocusVisibleRef.current = focusVisible;\n  }, [focusVisible, isFocusVisibleRef]);\n  const inputRef = React.useRef(null);\n  const createHandleFocus = otherProps => event => {\n    var _otherProps$onFocus;\n\n    // Fix for https://github.com/facebook/react/issues/7769\n    if (!inputRef.current) {\n      inputRef.current = event.currentTarget;\n    }\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusVisible(true);\n      onFocusVisible == null ? void 0 : onFocusVisible(event);\n    }\n    onFocus == null ? void 0 : onFocus(event);\n    (_otherProps$onFocus = otherProps.onFocus) == null ? void 0 : _otherProps$onFocus.call(otherProps, event);\n  };\n  const createHandleBlur = otherProps => event => {\n    var _otherProps$onBlur;\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusVisible(false);\n    }\n    onBlur == null ? void 0 : onBlur(event);\n    (_otherProps$onBlur = otherProps.onBlur) == null ? void 0 : _otherProps$onBlur.call(otherProps, event);\n  };\n  const handleRefChange = useForkRef(focusVisibleRef, inputRef);\n  const getInputProps = (otherProps = {}) => _extends({\n    checked: checkedProp,\n    defaultChecked,\n    disabled,\n    readOnly,\n    ref: handleRefChange,\n    required,\n    type: 'checkbox'\n  }, otherProps, {\n    onChange: createHandleInputChange(otherProps),\n    onFocus: createHandleFocus(otherProps),\n    onBlur: createHandleBlur(otherProps)\n  });\n  return {\n    checked,\n    disabled: Boolean(disabled),\n    focusVisible,\n    getInputProps,\n    readOnly: Boolean(readOnly)\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_useControlled", "useControlled", "unstable_useForkRef", "useForkRef", "unstable_useIsFocusVisible", "useIsFocusVisible", "useSwitch", "props", "checked", "checkedProp", "defaultChecked", "disabled", "onBlur", "onChange", "onFocus", "onFocusVisible", "readOnly", "required", "setCheckedState", "controlled", "default", "Boolean", "name", "state", "createHandleInputChange", "otherProps", "event", "_otherProps$onChange", "nativeEvent", "defaultPrevented", "target", "call", "isFocusVisibleRef", "handleBlurVisible", "handleFocusVisible", "ref", "focusVisibleRef", "focusVisible", "setFocusVisible", "useState", "useEffect", "current", "inputRef", "useRef", "createHandleFocus", "_otherProps$onFocus", "currentTarget", "createHandleBlur", "_otherProps$onBlur", "handleRefChange", "getInputProps", "type"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/SwitchUnstyled/useSwitch.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useControlled as useControlled, unstable_useForkRef as useForkRef, unstable_useIsFocusVisible as useIsFocusVisible } from '@mui/utils';\n\n/**\n * The basic building block for creating custom switches.\n *\n * Demos:\n *\n * - [Switches](https://mui.com/components/switches/)\n */\nexport default function useSwitch(props) {\n  const {\n    checked: checkedProp,\n    defaultChecked,\n    disabled,\n    onBlur,\n    onChange,\n    onFocus,\n    onFocusVisible,\n    readOnly,\n    required\n  } = props;\n  const [checked, setCheckedState] = useControlled({\n    controlled: checkedProp,\n    default: Boolean(defaultChecked),\n    name: 'Switch',\n    state: 'checked'\n  });\n\n  const createHandleInputChange = otherProps => event => {\n    var _otherProps$onChange;\n\n    // Workaround for https://github.com/facebook/react/issues/9023\n    if (event.nativeEvent.defaultPrevented) {\n      return;\n    }\n\n    setCheckedState(event.target.checked);\n    onChange == null ? void 0 : onChange(event);\n    (_otherProps$onChange = otherProps.onChange) == null ? void 0 : _otherProps$onChange.call(otherProps, event);\n  };\n\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusVisible, setFocusVisible] = React.useState(false);\n\n  if (disabled && focusVisible) {\n    setFocusVisible(false);\n  }\n\n  React.useEffect(() => {\n    isFocusVisibleRef.current = focusVisible;\n  }, [focusVisible, isFocusVisibleRef]);\n  const inputRef = React.useRef(null);\n\n  const createHandleFocus = otherProps => event => {\n    var _otherProps$onFocus;\n\n    // Fix for https://github.com/facebook/react/issues/7769\n    if (!inputRef.current) {\n      inputRef.current = event.currentTarget;\n    }\n\n    handleFocusVisible(event);\n\n    if (isFocusVisibleRef.current === true) {\n      setFocusVisible(true);\n      onFocusVisible == null ? void 0 : onFocusVisible(event);\n    }\n\n    onFocus == null ? void 0 : onFocus(event);\n    (_otherProps$onFocus = otherProps.onFocus) == null ? void 0 : _otherProps$onFocus.call(otherProps, event);\n  };\n\n  const createHandleBlur = otherProps => event => {\n    var _otherProps$onBlur;\n\n    handleBlurVisible(event);\n\n    if (isFocusVisibleRef.current === false) {\n      setFocusVisible(false);\n    }\n\n    onBlur == null ? void 0 : onBlur(event);\n    (_otherProps$onBlur = otherProps.onBlur) == null ? void 0 : _otherProps$onBlur.call(otherProps, event);\n  };\n\n  const handleRefChange = useForkRef(focusVisibleRef, inputRef);\n\n  const getInputProps = (otherProps = {}) => _extends({\n    checked: checkedProp,\n    defaultChecked,\n    disabled,\n    readOnly,\n    ref: handleRefChange,\n    required,\n    type: 'checkbox'\n  }, otherProps, {\n    onChange: createHandleInputChange(otherProps),\n    onFocus: createHandleFocus(otherProps),\n    onBlur: createHandleBlur(otherProps)\n  });\n\n  return {\n    checked,\n    disabled: Boolean(disabled),\n    focusVisible,\n    getInputProps,\n    readOnly: Boolean(readOnly)\n  };\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,sBAAsB,IAAIC,aAAa,EAAEC,mBAAmB,IAAIC,UAAU,EAAEC,0BAA0B,IAAIC,iBAAiB,QAAQ,YAAY;;AAExJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,SAASA,CAACC,KAAK,EAAE;EACvC,MAAM;IACJC,OAAO,EAAEC,WAAW;IACpBC,cAAc;IACdC,QAAQ;IACRC,MAAM;IACNC,QAAQ;IACRC,OAAO;IACPC,cAAc;IACdC,QAAQ;IACRC;EACF,CAAC,GAAGV,KAAK;EACT,MAAM,CAACC,OAAO,EAAEU,eAAe,CAAC,GAAGjB,aAAa,CAAC;IAC/CkB,UAAU,EAAEV,WAAW;IACvBW,OAAO,EAAEC,OAAO,CAACX,cAAc,CAAC;IAChCY,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAMC,uBAAuB,GAAGC,UAAU,IAAIC,KAAK,IAAI;IACrD,IAAIC,oBAAoB;;IAExB;IACA,IAAID,KAAK,CAACE,WAAW,CAACC,gBAAgB,EAAE;MACtC;IACF;IAEAX,eAAe,CAACQ,KAAK,CAACI,MAAM,CAACtB,OAAO,CAAC;IACrCK,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACa,KAAK,CAAC;IAC3C,CAACC,oBAAoB,GAAGF,UAAU,CAACZ,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGc,oBAAoB,CAACI,IAAI,CAACN,UAAU,EAAEC,KAAK,CAAC;EAC9G,CAAC;EAED,MAAM;IACJM,iBAAiB;IACjBpB,MAAM,EAAEqB,iBAAiB;IACzBnB,OAAO,EAAEoB,kBAAkB;IAC3BC,GAAG,EAAEC;EACP,CAAC,GAAG/B,iBAAiB,CAAC,CAAC;EACvB,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGvC,KAAK,CAACwC,QAAQ,CAAC,KAAK,CAAC;EAE7D,IAAI5B,QAAQ,IAAI0B,YAAY,EAAE;IAC5BC,eAAe,CAAC,KAAK,CAAC;EACxB;EAEAvC,KAAK,CAACyC,SAAS,CAAC,MAAM;IACpBR,iBAAiB,CAACS,OAAO,GAAGJ,YAAY;EAC1C,CAAC,EAAE,CAACA,YAAY,EAAEL,iBAAiB,CAAC,CAAC;EACrC,MAAMU,QAAQ,GAAG3C,KAAK,CAAC4C,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAMC,iBAAiB,GAAGnB,UAAU,IAAIC,KAAK,IAAI;IAC/C,IAAImB,mBAAmB;;IAEvB;IACA,IAAI,CAACH,QAAQ,CAACD,OAAO,EAAE;MACrBC,QAAQ,CAACD,OAAO,GAAGf,KAAK,CAACoB,aAAa;IACxC;IAEAZ,kBAAkB,CAACR,KAAK,CAAC;IAEzB,IAAIM,iBAAiB,CAACS,OAAO,KAAK,IAAI,EAAE;MACtCH,eAAe,CAAC,IAAI,CAAC;MACrBvB,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACW,KAAK,CAAC;IACzD;IAEAZ,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACY,KAAK,CAAC;IACzC,CAACmB,mBAAmB,GAAGpB,UAAU,CAACX,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG+B,mBAAmB,CAACd,IAAI,CAACN,UAAU,EAAEC,KAAK,CAAC;EAC3G,CAAC;EAED,MAAMqB,gBAAgB,GAAGtB,UAAU,IAAIC,KAAK,IAAI;IAC9C,IAAIsB,kBAAkB;IAEtBf,iBAAiB,CAACP,KAAK,CAAC;IAExB,IAAIM,iBAAiB,CAACS,OAAO,KAAK,KAAK,EAAE;MACvCH,eAAe,CAAC,KAAK,CAAC;IACxB;IAEA1B,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACc,KAAK,CAAC;IACvC,CAACsB,kBAAkB,GAAGvB,UAAU,CAACb,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoC,kBAAkB,CAACjB,IAAI,CAACN,UAAU,EAAEC,KAAK,CAAC;EACxG,CAAC;EAED,MAAMuB,eAAe,GAAG9C,UAAU,CAACiC,eAAe,EAAEM,QAAQ,CAAC;EAE7D,MAAMQ,aAAa,GAAGA,CAACzB,UAAU,GAAG,CAAC,CAAC,KAAK3B,QAAQ,CAAC;IAClDU,OAAO,EAAEC,WAAW;IACpBC,cAAc;IACdC,QAAQ;IACRK,QAAQ;IACRmB,GAAG,EAAEc,eAAe;IACpBhC,QAAQ;IACRkC,IAAI,EAAE;EACR,CAAC,EAAE1B,UAAU,EAAE;IACbZ,QAAQ,EAAEW,uBAAuB,CAACC,UAAU,CAAC;IAC7CX,OAAO,EAAE8B,iBAAiB,CAACnB,UAAU,CAAC;IACtCb,MAAM,EAAEmC,gBAAgB,CAACtB,UAAU;EACrC,CAAC,CAAC;EAEF,OAAO;IACLjB,OAAO;IACPG,QAAQ,EAAEU,OAAO,CAACV,QAAQ,CAAC;IAC3B0B,YAAY;IACZa,aAAa;IACblC,QAAQ,EAAEK,OAAO,CAACL,QAAQ;EAC5B,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}