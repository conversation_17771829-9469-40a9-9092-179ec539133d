{"ast": null, "code": "/**\r\n * Checks whether the entire input sequence can be matched\r\n * against the regular expression.\r\n * @return {boolean}\r\n */\nexport default function matchesEntirely(text, regular_expression) {\n  // If assigning the `''` default value is moved to the arguments above,\n  // code coverage would decrease for some weird reason.\n  text = text || '';\n  return new RegExp('^(?:' + regular_expression + ')$').test(text);\n}", "map": {"version": 3, "names": ["matchesEntirely", "text", "regular_expression", "RegExp", "test"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\helpers\\matchesEntirely.js"], "sourcesContent": ["/**\r\n * Checks whether the entire input sequence can be matched\r\n * against the regular expression.\r\n * @return {boolean}\r\n */\r\nexport default function matchesEntirely(text, regular_expression) {\r\n\t// If assigning the `''` default value is moved to the arguments above,\r\n\t// code coverage would decrease for some weird reason.\r\n\ttext = text || ''\r\n\treturn new RegExp('^(?:' + regular_expression + ')$').test(text)\r\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,eAATA,CAAyBC,IAAzB,EAA+BC,kBAA/B,EAAmD;EACjE;EACA;EACAD,IAAI,GAAGA,IAAI,IAAI,EAAf;EACA,OAAO,IAAIE,MAAJ,CAAW,SAASD,kBAAT,GAA8B,IAAzC,EAA+CE,IAA/C,CAAoDH,IAApD,CAAP;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}