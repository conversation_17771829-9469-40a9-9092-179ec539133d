{"ast": null, "code": "export function shallowEqual(objA, objB, compare, compareContext) {\n  let compareResult = compare ? compare.call(compareContext, objA, objB) : void 0;\n  if (compareResult !== void 0) {\n    return !!compareResult;\n  }\n  if (objA === objB) {\n    return true;\n  }\n  if (typeof objA !== 'object' || !objA || typeof objB !== 'object' || !objB) {\n    return false;\n  }\n  const keysA = Object.keys(objA);\n  const keysB = Object.keys(objB);\n  if (keysA.length !== keysB.length) {\n    return false;\n  }\n  const bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB);\n  // Test for A's keys different from B.\n  for (let idx = 0; idx < keysA.length; idx++) {\n    const key = keysA[idx];\n    if (!bHasOwnProperty(key)) {\n      return false;\n    }\n    const valueA = objA[key];\n    const valueB = objB[key];\n    compareResult = compare ? compare.call(compareContext, valueA, valueB, key) : void 0;\n    if (compareResult === false || compareResult === void 0 && valueA !== valueB) {\n      return false;\n    }\n  }\n  return true;\n}", "map": {"version": 3, "names": ["shallowEqual", "objA", "objB", "compare", "compareContext", "compareResult", "call", "keysA", "Object", "keys", "keysB", "length", "bHasOwnProperty", "prototype", "hasOwnProperty", "bind", "idx", "key", "valueA", "valueB"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\@react-dnd\\shallowequal\\src\\index.ts"], "sourcesContent": ["export function shallowEqual<T>(\n\tobjA: T,\n\tobjB: T,\n\tcompare?: (a: T, b: T, key?: string) => boolean | void,\n\tcompareContext?: any,\n) {\n\tlet compareResult = compare\n\t\t? compare.call(compareContext, objA, objB)\n\t\t: void 0\n\tif (compareResult !== void 0) {\n\t\treturn !!compareResult\n\t}\n\n\tif (objA === objB) {\n\t\treturn true\n\t}\n\n\tif (typeof objA !== 'object' || !objA || typeof objB !== 'object' || !objB) {\n\t\treturn false\n\t}\n\n\tconst keysA = Object.keys(objA)\n\tconst keysB = Object.keys(objB)\n\n\tif (keysA.length !== keysB.length) {\n\t\treturn false\n\t}\n\n\tconst bHasOwnProperty = Object.prototype.hasOwnProperty.bind(objB)\n\n\t// Test for A's keys different from B.\n\tfor (let idx = 0; idx < keysA.length; idx++) {\n\t\tconst key = keysA[idx] as string\n\n\t\tif (!bHasOwnProperty(key)) {\n\t\t\treturn false\n\t\t}\n\n\t\tconst valueA = (objA as any)[key]\n\t\tconst valueB = (objB as any)[key]\n\n\t\tcompareResult = compare\n\t\t\t? compare.call(compareContext, valueA, valueB, key)\n\t\t\t: void 0\n\n\t\tif (\n\t\t\tcompareResult === false ||\n\t\t\t(compareResult === void 0 && valueA !== valueB)\n\t\t) {\n\t\t\treturn false\n\t\t}\n\t}\n\n\treturn true\n}\n"], "mappings": "AAAA,OAAO,SAASA,YAAYA,CAC3BC,IAAO,EACPC,IAAO,EACPC,OAAsD,EACtDC,cAAoB,EACnB;EACD,IAAIC,aAAa,GAAGF,OAAO,GACxBA,OAAO,CAACG,IAAI,CAACF,cAAc,EAAEH,IAAI,EAAEC,IAAI,CAAC,GACxC,KAAK,CAAC;EACT,IAAIG,aAAa,KAAK,KAAK,CAAC,EAAE;IAC7B,OAAO,CAAC,CAACA,aAAa;;EAGvB,IAAIJ,IAAI,KAAKC,IAAI,EAAE;IAClB,OAAO,IAAI;;EAGZ,IAAI,OAAOD,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAI,IAAI,OAAOC,IAAI,KAAK,QAAQ,IAAI,CAACA,IAAI,EAAE;IAC3E,OAAO,KAAK;;EAGb,MAAMK,KAAK,GAAGC,MAAM,CAACC,IAAI,CAACR,IAAI,CAAC;EAC/B,MAAMS,KAAK,GAAGF,MAAM,CAACC,IAAI,CAACP,IAAI,CAAC;EAE/B,IAAIK,KAAK,CAACI,MAAM,KAAKD,KAAK,CAACC,MAAM,EAAE;IAClC,OAAO,KAAK;;EAGb,MAAMC,eAAe,GAAGJ,MAAM,CAACK,SAAS,CAACC,cAAc,CAACC,IAAI,CAACb,IAAI,CAAC;EAElE;EACA,KAAK,IAAIc,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGT,KAAK,CAACI,MAAM,EAAEK,GAAG,EAAE,EAAE;IAC5C,MAAMC,GAAG,GAAGV,KAAK,CAACS,GAAG,CAAC;IAEtB,IAAI,CAACJ,eAAe,CAACK,GAAG,CAAC,EAAE;MAC1B,OAAO,KAAK;;IAGb,MAAMC,MAAM,GAAGjB,IAAK,CAASgB,GAAG,CAAC;IACjC,MAAME,MAAM,GAAGjB,IAAK,CAASe,GAAG,CAAC;IAEjCZ,aAAa,GAAGF,OAAO,GACpBA,OAAO,CAACG,IAAI,CAACF,cAAc,EAAEc,MAAM,EAAEC,MAAM,EAAEF,GAAG,CAAC,GACjD,KAAK,CAAC;IAET,IACCZ,aAAa,KAAK,KAAK,IACtBA,aAAa,KAAK,KAAK,CAAC,IAAIa,MAAM,KAAKC,MAAM,EAC7C;MACD,OAAO,KAAK;;;EAId,OAAO,IAAI"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}