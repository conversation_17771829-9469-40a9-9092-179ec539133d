{"ast": null, "code": "import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getTabsUnstyledUtilityClass(slot) {\n  return generateUtilityClass('TabsUnstyled', slot);\n}\nconst tabsUnstyledClasses = generateUtilityClasses('TabsUnstyled', ['root', 'horizontal', 'vertical']);\nexport default tabsUnstyledClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTabsUnstyledUtilityClass", "slot", "tabsUnstyledClasses"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/TabsUnstyled/tabsUnstyledClasses.js"], "sourcesContent": ["import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getTabsUnstyledUtilityClass(slot) {\n  return generateUtilityClass('TabsUnstyled', slot);\n}\nconst tabsUnstyledClasses = generateUtilityClasses('TabsUnstyled', ['root', 'horizontal', 'vertical']);\nexport default tabsUnstyledClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,yBAAyB;AAC1D,OAAOC,sBAAsB,MAAM,2BAA2B;AAC9D,OAAO,SAASC,2BAA2BA,CAACC,IAAI,EAAE;EAChD,OAAOH,oBAAoB,CAAC,cAAc,EAAEG,IAAI,CAAC;AACnD;AACA,MAAMC,mBAAmB,GAAGH,sBAAsB,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;AACtG,eAAeG,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}