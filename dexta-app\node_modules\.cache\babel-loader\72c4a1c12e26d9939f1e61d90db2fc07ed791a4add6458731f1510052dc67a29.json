{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { raceInit } from '../observable/race';\nimport { operate } from '../util/lift';\nimport { identity } from '../util/identity';\nexport function raceWith() {\n  var otherSources = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    otherSources[_i] = arguments[_i];\n  }\n  return !otherSources.length ? identity : operate(function (source, subscriber) {\n    raceInit(__spreadArray([source], __read(otherSources)))(subscriber);\n  });\n}", "map": {"version": 3, "names": ["raceInit", "operate", "identity", "raceWith", "otherSources", "_i", "arguments", "length", "source", "subscriber", "__spread<PERSON><PERSON>y", "__read"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\raceWith.ts"], "sourcesContent": ["import { OperatorFunction, ObservableInputTuple } from '../types';\nimport { raceInit } from '../observable/race';\nimport { operate } from '../util/lift';\nimport { identity } from '../util/identity';\n\n/**\n * Creates an Observable that mirrors the first source Observable to emit a next,\n * error or complete notification from the combination of the Observable to which\n * the operator is applied and supplied Observables.\n *\n * ## Example\n *\n * ```ts\n * import { interval, map, raceWith } from 'rxjs';\n *\n * const obs1 = interval(7000).pipe(map(() => 'slow one'));\n * const obs2 = interval(3000).pipe(map(() => 'fast one'));\n * const obs3 = interval(5000).pipe(map(() => 'medium one'));\n *\n * obs1\n *   .pipe(raceWith(obs2, obs3))\n *   .subscribe(winner => console.log(winner));\n *\n * // Outputs\n * // a series of 'fast one'\n * ```\n *\n * @param otherSources Sources used to race for which Observable emits first.\n * @return A function that returns an Observable that mirrors the output of the\n * first Observable to emit an item.\n */\nexport function raceWith<T, A extends readonly unknown[]>(\n  ...otherSources: [...ObservableInputTuple<A>]\n): OperatorFunction<T, T | A[number]> {\n  return !otherSources.length\n    ? identity\n    : operate((source, subscriber) => {\n        raceInit<T | A[number]>([source, ...otherSources])(subscriber);\n      });\n}\n"], "mappings": ";AACA,SAASA,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,QAAQ,QAAQ,kBAAkB;AA4B3C,OAAM,SAAUC,QAAQA,CAAA;EACtB,IAAAC,YAAA;OAAA,IAAAC,EAAA,IAA6C,EAA7CA,EAAA,GAAAC,SAAA,CAAAC,MAA6C,EAA7CF,EAAA,EAA6C;IAA7CD,YAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EAEA,OAAO,CAACD,YAAY,CAACG,MAAM,GACvBL,QAAQ,GACRD,OAAO,CAAC,UAACO,MAAM,EAAEC,UAAU;IACzBT,QAAQ,CAAAU,aAAA,EAAiBF,MAAM,GAAAG,MAAA,CAAKP,YAAY,GAAE,CAACK,UAAU,CAAC;EAChE,CAAC,CAAC;AACR"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}