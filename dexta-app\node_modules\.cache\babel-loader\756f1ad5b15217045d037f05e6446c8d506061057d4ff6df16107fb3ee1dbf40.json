{"ast": null, "code": "import { EmptyError } from '../util/EmptyError';\nimport { filter } from './filter';\nimport { take } from './take';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { throwIfEmpty } from './throwIfEmpty';\nimport { identity } from '../util/identity';\nexport function first(predicate, defaultValue) {\n  var hasDefaultValue = arguments.length >= 2;\n  return function (source) {\n    return source.pipe(predicate ? filter(function (v, i) {\n      return predicate(v, i, source);\n    }) : identity, take(1), hasDefaultValue ? defaultIfEmpty(defaultValue) : throwIfEmpty(function () {\n      return new EmptyError();\n    }));\n  };\n}", "map": {"version": 3, "names": ["EmptyError", "filter", "take", "defaultIfEmpty", "throwIfEmpty", "identity", "first", "predicate", "defaultValue", "hasDefaultValue", "arguments", "length", "source", "pipe", "v", "i"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\first.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { EmptyError } from '../util/EmptyError';\nimport { OperatorFunction, TruthyTypesOf } from '../types';\nimport { filter } from './filter';\nimport { take } from './take';\nimport { defaultIfEmpty } from './defaultIfEmpty';\nimport { throwIfEmpty } from './throwIfEmpty';\nimport { identity } from '../util/identity';\n\nexport function first<T, D = T>(predicate?: null, defaultValue?: D): OperatorFunction<T, T | D>;\nexport function first<T>(predicate: BooleanConstructor): OperatorFunction<T, TruthyTypesOf<T>>;\nexport function first<T, D>(predicate: BooleanConstructor, defaultValue: D): OperatorFunction<T, TruthyTypesOf<T> | D>;\nexport function first<T, S extends T>(\n  predicate: (value: T, index: number, source: Observable<T>) => value is S,\n  defaultValue?: S\n): OperatorFunction<T, S>;\nexport function first<T, S extends T, D>(\n  predicate: (value: T, index: number, source: Observable<T>) => value is S,\n  defaultValue: D\n): OperatorFunction<T, S | D>;\nexport function first<T, D = T>(\n  predicate: (value: T, index: number, source: Observable<T>) => boolean,\n  defaultValue?: D\n): OperatorFunction<T, T | D>;\n\n/**\n * Emits only the first value (or the first value that meets some condition)\n * emitted by the source Observable.\n *\n * <span class=\"informal\">Emits only the first value. Or emits only the first\n * value that passes some test.</span>\n *\n * ![](first.png)\n *\n * If called with no arguments, `first` emits the first value of the source\n * Observable, then completes. If called with a `predicate` function, `first`\n * emits the first value of the source that matches the specified condition. Emits an error\n * notification if `defaultValue` was not provided and a matching element is not found.\n *\n * ## Examples\n *\n * Emit only the first click that happens on the DOM\n *\n * ```ts\n * import { fromEvent, first } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(first());\n * result.subscribe(x => console.log(x));\n * ```\n *\n * Emits the first click that happens on a DIV\n *\n * ```ts\n * import { fromEvent, first } from 'rxjs';\n *\n * const div = document.createElement('div');\n * div.style.cssText = 'width: 200px; height: 200px; background: #09c;';\n * document.body.appendChild(div);\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(first(ev => (<HTMLElement>ev.target).tagName === 'DIV'));\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link filter}\n * @see {@link find}\n * @see {@link take}\n * @see {@link last}\n *\n * @throws {EmptyError} Delivers an `EmptyError` to the Observer's `error`\n * callback if the Observable completes before any `next` notification was sent.\n * This is how `first()` is different from `take(1)` which completes instead.\n *\n * @param predicate An optional function called with each item to test for condition\n * matching.\n * @param defaultValue The default value emitted in case no valid value was found on\n * the source.\n * @return A function that returns an Observable that emits the first item that\n * matches the condition.\n */\nexport function first<T, D>(\n  predicate?: ((value: T, index: number, source: Observable<T>) => boolean) | null,\n  defaultValue?: D\n): OperatorFunction<T, T | D> {\n  const hasDefaultValue = arguments.length >= 2;\n  return (source: Observable<T>) =>\n    source.pipe(\n      predicate ? filter((v, i) => predicate(v, i, source)) : identity,\n      take(1),\n      hasDefaultValue ? defaultIfEmpty(defaultValue!) : throwIfEmpty(() => new EmptyError())\n    );\n}\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,oBAAoB;AAE/C,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,IAAI,QAAQ,QAAQ;AAC7B,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,QAAQ,QAAQ,kBAAkB;AA0E3C,OAAM,SAAUC,KAAKA,CACnBC,SAAgF,EAChFC,YAAgB;EAEhB,IAAMC,eAAe,GAAGC,SAAS,CAACC,MAAM,IAAI,CAAC;EAC7C,OAAO,UAACC,MAAqB;IAC3B,OAAAA,MAAM,CAACC,IAAI,CACTN,SAAS,GAAGN,MAAM,CAAC,UAACa,CAAC,EAAEC,CAAC;MAAK,OAAAR,SAAS,CAACO,CAAC,EAAEC,CAAC,EAAEH,MAAM,CAAC;IAAvB,CAAuB,CAAC,GAAGP,QAAQ,EAChEH,IAAI,CAAC,CAAC,CAAC,EACPO,eAAe,GAAGN,cAAc,CAACK,YAAa,CAAC,GAAGJ,YAAY,CAAC;MAAM,WAAIJ,UAAU,EAAE;IAAhB,CAAgB,CAAC,CACvF;EAJD,CAIC;AACL"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}