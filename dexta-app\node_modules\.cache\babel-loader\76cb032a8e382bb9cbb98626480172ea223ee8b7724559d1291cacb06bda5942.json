{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"value\", \"defaultValue\", \"orientation\", \"direction\", \"component\", \"components\", \"componentsProps\", \"onChange\", \"selectionFollowsFocus\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useSlotProps } from '../utils';\nimport composeClasses from '../composeClasses';\nimport { getTabsUnstyledUtilityClass } from './tabsUnstyledClasses';\nimport useTabs from './useTabs';\nimport Context from './TabsContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    orientation\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation]\n  };\n  return composeClasses(slots, getTabsUnstyledUtilityClass, {});\n};\n/**\n *\n * Demos:\n *\n * - [Unstyled tabs](https://mui.com/base/react-tabs/)\n *\n * API:\n *\n * - [TabsUnstyled API](https://mui.com/base/api/tabs-unstyled/)\n */\n\nconst TabsUnstyled = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _ref;\n  const {\n      children,\n      orientation = 'horizontal',\n      direction = 'ltr',\n      component,\n      components = {},\n      componentsProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    tabsContextValue\n  } = useTabs(props);\n  const ownerState = _extends({}, props, {\n    orientation,\n    direction\n  });\n  const classes = useUtilityClasses(ownerState);\n  const TabsRoot = (_ref = component != null ? component : components.Root) != null ? _ref : 'div';\n  const tabsRootProps = useSlotProps({\n    elementType: TabsRoot,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(TabsRoot, _extends({}, tabsRootProps, {\n    children: /*#__PURE__*/_jsx(Context.Provider, {\n      value: tabsContextValue,\n      children: children\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TabsUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the Tabs.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The props used for each slot inside the Tabs.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The default value. Use when the component is not controlled.\n   */\n  defaultValue: PropTypes.oneOfType([PropTypes.oneOf([false]), PropTypes.number, PropTypes.string]),\n  /**\n   * The direction of the text.\n   * @default 'ltr'\n   */\n  direction: PropTypes.oneOf(['ltr', 'rtl']),\n  /**\n   * Callback invoked when new value is being set.\n   */\n  onChange: PropTypes.func,\n  /**\n   * The component orientation (layout flow direction).\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * If `true` the selected tab changes on focus. Otherwise it only\n   * changes on activation.\n   */\n  selectionFollowsFocus: PropTypes.bool,\n  /**\n   * The value of the currently selected `Tab`.\n   * If you don't want any selected `Tab`, you can set this prop to `false`.\n   */\n  value: PropTypes.oneOfType([PropTypes.oneOf([false]), PropTypes.number, PropTypes.string])\n} : void 0;\nexport default TabsUnstyled;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "useSlotProps", "composeClasses", "getTabsUnstyledUtilityClass", "useTabs", "Context", "jsx", "_jsx", "useUtilityClasses", "ownerState", "orientation", "slots", "root", "TabsUnstyled", "forwardRef", "props", "ref", "_ref", "children", "direction", "component", "components", "componentsProps", "other", "tabsContextValue", "classes", "TabsRoot", "Root", "tabsRootProps", "elementType", "externalSlotProps", "externalForwardedProps", "additionalProps", "className", "Provider", "value", "process", "env", "NODE_ENV", "propTypes", "node", "shape", "oneOfType", "func", "object", "defaultValue", "oneOf", "number", "string", "onChange", "selectionFollowsFocus", "bool"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/TabsUnstyled/TabsUnstyled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"value\", \"defaultValue\", \"orientation\", \"direction\", \"component\", \"components\", \"componentsProps\", \"onChange\", \"selectionFollowsFocus\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useSlotProps } from '../utils';\nimport composeClasses from '../composeClasses';\nimport { getTabsUnstyledUtilityClass } from './tabsUnstyledClasses';\nimport useTabs from './useTabs';\nimport Context from './TabsContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n\nconst useUtilityClasses = ownerState => {\n  const {\n    orientation\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation]\n  };\n  return composeClasses(slots, getTabsUnstyledUtilityClass, {});\n};\n/**\n *\n * Demos:\n *\n * - [Unstyled tabs](https://mui.com/base/react-tabs/)\n *\n * API:\n *\n * - [TabsUnstyled API](https://mui.com/base/api/tabs-unstyled/)\n */\n\n\nconst TabsUnstyled = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _ref;\n\n  const {\n    children,\n    orientation = 'horizontal',\n    direction = 'ltr',\n    component,\n    components = {},\n    componentsProps = {}\n  } = props,\n        other = _objectWithoutPropertiesLoose(props, _excluded);\n\n  const {\n    tabsContextValue\n  } = useTabs(props);\n\n  const ownerState = _extends({}, props, {\n    orientation,\n    direction\n  });\n\n  const classes = useUtilityClasses(ownerState);\n  const TabsRoot = (_ref = component != null ? component : components.Root) != null ? _ref : 'div';\n  const tabsRootProps = useSlotProps({\n    elementType: TabsRoot,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(TabsRoot, _extends({}, tabsRootProps, {\n    children: /*#__PURE__*/_jsx(Context.Provider, {\n      value: tabsContextValue,\n      children: children\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TabsUnstyled.propTypes\n/* remove-proptypes */\n= {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n\n  /**\n   * The components used for each slot inside the Tabs.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n\n  /**\n   * The props used for each slot inside the Tabs.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n\n  /**\n   * The default value. Use when the component is not controlled.\n   */\n  defaultValue: PropTypes.oneOfType([PropTypes.oneOf([false]), PropTypes.number, PropTypes.string]),\n\n  /**\n   * The direction of the text.\n   * @default 'ltr'\n   */\n  direction: PropTypes.oneOf(['ltr', 'rtl']),\n\n  /**\n   * Callback invoked when new value is being set.\n   */\n  onChange: PropTypes.func,\n\n  /**\n   * The component orientation (layout flow direction).\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n\n  /**\n   * If `true` the selected tab changes on focus. Otherwise it only\n   * changes on activation.\n   */\n  selectionFollowsFocus: PropTypes.bool,\n\n  /**\n   * The value of the currently selected `Tab`.\n   * If you don't want any selected `Tab`, you can set this prop to `false`.\n   */\n  value: PropTypes.oneOfType([PropTypes.oneOf([false]), PropTypes.number, PropTypes.string])\n} : void 0;\nexport default TabsUnstyled;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,cAAc,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,UAAU,EAAE,uBAAuB,CAAC;AACtK,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,YAAY,QAAQ,UAAU;AACvC,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,SAASC,2BAA2B,QAAQ,uBAAuB;AACnE,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,OAAO,MAAM,eAAe;AACnC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAE/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,WAAW;EAC5B,CAAC;EACD,OAAOR,cAAc,CAACS,KAAK,EAAER,2BAA2B,EAAE,CAAC,CAAC,CAAC;AAC/D,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,MAAMU,YAAY,GAAG,aAAad,KAAK,CAACe,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EACjE,IAAIC,IAAI;EAER,MAAM;MACJC,QAAQ;MACRR,WAAW,GAAG,YAAY;MAC1BS,SAAS,GAAG,KAAK;MACjBC,SAAS;MACTC,UAAU,GAAG,CAAC,CAAC;MACfC,eAAe,GAAG,CAAC;IACrB,CAAC,GAAGP,KAAK;IACHQ,KAAK,GAAG1B,6BAA6B,CAACkB,KAAK,EAAEjB,SAAS,CAAC;EAE7D,MAAM;IACJ0B;EACF,CAAC,GAAGpB,OAAO,CAACW,KAAK,CAAC;EAElB,MAAMN,UAAU,GAAGb,QAAQ,CAAC,CAAC,CAAC,EAAEmB,KAAK,EAAE;IACrCL,WAAW;IACXS;EACF,CAAC,CAAC;EAEF,MAAMM,OAAO,GAAGjB,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMiB,QAAQ,GAAG,CAACT,IAAI,GAAGG,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAGC,UAAU,CAACM,IAAI,KAAK,IAAI,GAAGV,IAAI,GAAG,KAAK;EAChG,MAAMW,aAAa,GAAG3B,YAAY,CAAC;IACjC4B,WAAW,EAAEH,QAAQ;IACrBI,iBAAiB,EAAER,eAAe,CAACV,IAAI;IACvCmB,sBAAsB,EAAER,KAAK;IAC7BS,eAAe,EAAE;MACfhB;IACF,CAAC;IACDP,UAAU;IACVwB,SAAS,EAAER,OAAO,CAACb;EACrB,CAAC,CAAC;EACF,OAAO,aAAaL,IAAI,CAACmB,QAAQ,EAAE9B,QAAQ,CAAC,CAAC,CAAC,EAAEgC,aAAa,EAAE;IAC7DV,QAAQ,EAAE,aAAaX,IAAI,CAACF,OAAO,CAAC6B,QAAQ,EAAE;MAC5CC,KAAK,EAAEX,gBAAgB;MACvBN,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFkB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzB,YAAY,CAAC0B;AACrD,yBACE;EACA;EACA;EACA;EACA;;EAEA;AACF;AACA;EACErB,QAAQ,EAAElB,SAAS,CAACwC,IAAI;EAExB;AACF;AACA;AACA;EACEpB,SAAS,EAAEpB,SAAS,CAAC6B,WAAW;EAEhC;AACF;AACA;AACA;AACA;EACER,UAAU,EAAErB,SAAS,CAACyC,KAAK,CAAC;IAC1Bd,IAAI,EAAE3B,SAAS,CAAC6B;EAClB,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACEP,eAAe,EAAEtB,SAAS,CAACyC,KAAK,CAAC;IAC/B7B,IAAI,EAAEZ,SAAS,CAAC0C,SAAS,CAAC,CAAC1C,SAAS,CAAC2C,IAAI,EAAE3C,SAAS,CAAC4C,MAAM,CAAC;EAC9D,CAAC,CAAC;EAEF;AACF;AACA;EACEC,YAAY,EAAE7C,SAAS,CAAC0C,SAAS,CAAC,CAAC1C,SAAS,CAAC8C,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE9C,SAAS,CAAC+C,MAAM,EAAE/C,SAAS,CAACgD,MAAM,CAAC,CAAC;EAEjG;AACF;AACA;AACA;EACE7B,SAAS,EAAEnB,SAAS,CAAC8C,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;EAE1C;AACF;AACA;EACEG,QAAQ,EAAEjD,SAAS,CAAC2C,IAAI;EAExB;AACF;AACA;AACA;EACEjC,WAAW,EAAEV,SAAS,CAAC8C,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;EAExD;AACF;AACA;AACA;EACEI,qBAAqB,EAAElD,SAAS,CAACmD,IAAI;EAErC;AACF;AACA;AACA;EACEhB,KAAK,EAAEnC,SAAS,CAAC0C,SAAS,CAAC,CAAC1C,SAAS,CAAC8C,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE9C,SAAS,CAAC+C,MAAM,EAAE/C,SAAS,CAACgD,MAAM,CAAC;AAC3F,CAAC,GAAG,KAAK,CAAC;AACV,eAAenC,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}