{"ast": null, "code": "import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getSwitchUnstyledUtilityClass(slot) {\n  return generateUtilityClass('MuiSwitch', slot);\n}\nconst switchUnstyledClasses = generateUtilityClasses('MuiSwitch', ['root', 'input', 'track', 'thumb', 'checked', 'disabled', 'focusVisible', 'readOnly']);\nexport default switchUnstyledClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getSwitchUnstyledUtilityClass", "slot", "switchUnstyledClasses"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/SwitchUnstyled/switchUnstyledClasses.js"], "sourcesContent": ["import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getSwitchUnstyledUtilityClass(slot) {\n  return generateUtilityClass('MuiSwitch', slot);\n}\nconst switchUnstyledClasses = generateUtilityClasses('MuiSwitch', ['root', 'input', 'track', 'thumb', 'checked', 'disabled', 'focusVisible', 'readOnly']);\nexport default switchUnstyledClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,yBAAyB;AAC1D,OAAOC,sBAAsB,MAAM,2BAA2B;AAC9D,OAAO,SAASC,6BAA6BA,CAACC,IAAI,EAAE;EAClD,OAAOH,oBAAoB,CAAC,WAAW,EAAEG,IAAI,CAAC;AAChD;AACA,MAAMC,qBAAqB,GAAGH,sBAAsB,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;AACzJ,eAAeG,qBAAqB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}