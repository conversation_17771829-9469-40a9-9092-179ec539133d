{"ast": null, "code": "var NAMESPACE = require(\"./conventions\").NAMESPACE;\n\n//[4]   \tNameStartChar\t   ::=   \t\":\" | [A-Z] | \"_\" | [a-z] | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x2FF] | [#x370-#x37D] | [#x37F-#x1FFF] | [#x200C-#x200D] | [#x2070-#x218F] | [#x2C00-#x2FEF] | [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n//[4a]   \tNameChar\t   ::=   \tNameStartChar | \"-\" | \".\" | [0-9] | #xB7 | [#x0300-#x036F] | [#x203F-#x2040]\n//[5]   \tName\t   ::=   \tNameStartChar (NameChar)*\nvar nameStartChar = /[A-Z_a-z\\xC0-\\xD6\\xD8-\\xF6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/; //\\u10000-\\uEFFFF\nvar nameChar = new RegExp(\"[\\\\-\\\\.0-9\" + nameStartChar.source.slice(1, -1) + \"\\\\u00B7\\\\u0300-\\\\u036F\\\\u203F-\\\\u2040]\");\nvar tagNamePattern = new RegExp('^' + nameStartChar.source + nameChar.source + '*(?:\\:' + nameStartChar.source + nameChar.source + '*)?$');\n//var tagNamePattern = /^[a-zA-Z_][\\w\\-\\.]*(?:\\:[a-zA-Z_][\\w\\-\\.]*)?$/\n//var handlers = 'resolveEntity,getExternalSubset,characters,endDocument,endElement,endPrefixMapping,ignorableWhitespace,processingInstruction,setDocumentLocator,skippedEntity,startDocument,startElement,startPrefixMapping,notationDecl,unparsedEntityDecl,error,fatalError,warning,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,comment,endCDATA,endDTD,endEntity,startCDATA,startDTD,startEntity'.split(',')\n\n//S_TAG,\tS_ATTR,\tS_EQ,\tS_ATTR_NOQUOT_VALUE\n//S_ATTR_SPACE,\tS_ATTR_END,\tS_TAG_SPACE, S_TAG_CLOSE\nvar S_TAG = 0; //tag name offerring\nvar S_ATTR = 1; //attr name offerring\nvar S_ATTR_SPACE = 2; //attr name end and space offer\nvar S_EQ = 3; //=space?\nvar S_ATTR_NOQUOT_VALUE = 4; //attr value(no quot value only)\nvar S_ATTR_END = 5; //attr value end and no space(quot end)\nvar S_TAG_SPACE = 6; //(attr value end || tag end ) && (space offer)\nvar S_TAG_CLOSE = 7; //closed el<el />\n\n/**\n * Creates an error that will not be caught by XMLReader aka the SAX parser.\n *\n * @param {string} message\n * @param {any?} locator Optional, can provide details about the location in the source\n * @constructor\n */\nfunction ParseError(message, locator) {\n  this.message = message;\n  this.locator = locator;\n  if (Error.captureStackTrace) Error.captureStackTrace(this, ParseError);\n}\nParseError.prototype = new Error();\nParseError.prototype.name = ParseError.name;\nfunction XMLReader() {}\nXMLReader.prototype = {\n  parse: function (source, defaultNSMap, entityMap) {\n    var domBuilder = this.domBuilder;\n    domBuilder.startDocument();\n    _copy(defaultNSMap, defaultNSMap = {});\n    parse(source, defaultNSMap, entityMap, domBuilder, this.errorHandler);\n    domBuilder.endDocument();\n  }\n};\nfunction parse(source, defaultNSMapCopy, entityMap, domBuilder, errorHandler) {\n  function fixedFromCharCode(code) {\n    // String.prototype.fromCharCode does not supports\n    // > 2 bytes unicode chars directly\n    if (code > 0xffff) {\n      code -= 0x10000;\n      var surrogate1 = 0xd800 + (code >> 10),\n        surrogate2 = 0xdc00 + (code & 0x3ff);\n      return String.fromCharCode(surrogate1, surrogate2);\n    } else {\n      return String.fromCharCode(code);\n    }\n  }\n  function entityReplacer(a) {\n    var k = a.slice(1, -1);\n    if (Object.hasOwnProperty.call(entityMap, k)) {\n      return entityMap[k];\n    } else if (k.charAt(0) === '#') {\n      return fixedFromCharCode(parseInt(k.substr(1).replace('x', '0x')));\n    } else {\n      errorHandler.error('entity not found:' + a);\n      return a;\n    }\n  }\n  function appendText(end) {\n    //has some bugs\n    if (end > start) {\n      var xt = source.substring(start, end).replace(/&#?\\w+;/g, entityReplacer);\n      locator && position(start);\n      domBuilder.characters(xt, 0, end - start);\n      start = end;\n    }\n  }\n  function position(p, m) {\n    while (p >= lineEnd && (m = linePattern.exec(source))) {\n      lineStart = m.index;\n      lineEnd = lineStart + m[0].length;\n      locator.lineNumber++;\n      //console.log('line++:',locator,startPos,endPos)\n    }\n\n    locator.columnNumber = p - lineStart + 1;\n  }\n  var lineStart = 0;\n  var lineEnd = 0;\n  var linePattern = /.*(?:\\r\\n?|\\n)|.*$/g;\n  var locator = domBuilder.locator;\n  var parseStack = [{\n    currentNSMap: defaultNSMapCopy\n  }];\n  var closeMap = {};\n  var start = 0;\n  while (true) {\n    try {\n      var tagStart = source.indexOf('<', start);\n      if (tagStart < 0) {\n        if (!source.substr(start).match(/^\\s*$/)) {\n          var doc = domBuilder.doc;\n          var text = doc.createTextNode(source.substr(start));\n          doc.appendChild(text);\n          domBuilder.currentElement = text;\n        }\n        return;\n      }\n      if (tagStart > start) {\n        appendText(tagStart);\n      }\n      switch (source.charAt(tagStart + 1)) {\n        case '/':\n          var end = source.indexOf('>', tagStart + 3);\n          var tagName = source.substring(tagStart + 2, end).replace(/[ \\t\\n\\r]+$/g, '');\n          var config = parseStack.pop();\n          if (end < 0) {\n            tagName = source.substring(tagStart + 2).replace(/[\\s<].*/, '');\n            errorHandler.error(\"end tag name: \" + tagName + ' is not complete:' + config.tagName);\n            end = tagStart + 1 + tagName.length;\n          } else if (tagName.match(/\\s</)) {\n            tagName = tagName.replace(/[\\s<].*/, '');\n            errorHandler.error(\"end tag name: \" + tagName + ' maybe not complete');\n            end = tagStart + 1 + tagName.length;\n          }\n          var localNSMap = config.localNSMap;\n          var endMatch = config.tagName == tagName;\n          var endIgnoreCaseMach = endMatch || config.tagName && config.tagName.toLowerCase() == tagName.toLowerCase();\n          if (endIgnoreCaseMach) {\n            domBuilder.endElement(config.uri, config.localName, tagName);\n            if (localNSMap) {\n              for (var prefix in localNSMap) {\n                if (Object.prototype.hasOwnProperty.call(localNSMap, prefix)) {\n                  domBuilder.endPrefixMapping(prefix);\n                }\n              }\n            }\n            if (!endMatch) {\n              errorHandler.fatalError(\"end tag name: \" + tagName + ' is not match the current start tagName:' + config.tagName); // No known test case\n            }\n          } else {\n            parseStack.push(config);\n          }\n          end++;\n          break;\n        // end elment\n        case '?':\n          // <?...?>\n          locator && position(tagStart);\n          end = parseInstruction(source, tagStart, domBuilder);\n          break;\n        case '!':\n          // <!doctype,<![CDATA,<!--\n          locator && position(tagStart);\n          end = parseDCC(source, tagStart, domBuilder, errorHandler);\n          break;\n        default:\n          locator && position(tagStart);\n          var el = new ElementAttributes();\n          var currentNSMap = parseStack[parseStack.length - 1].currentNSMap;\n          //elStartEnd\n          var end = parseElementStartPart(source, tagStart, el, currentNSMap, entityReplacer, errorHandler);\n          var len = el.length;\n          if (!el.closed && fixSelfClosed(source, end, el.tagName, closeMap)) {\n            el.closed = true;\n            if (!entityMap.nbsp) {\n              errorHandler.warning('unclosed xml attribute');\n            }\n          }\n          if (locator && len) {\n            var locator2 = copyLocator(locator, {});\n            //try{//attribute position fixed\n            for (var i = 0; i < len; i++) {\n              var a = el[i];\n              position(a.offset);\n              a.locator = copyLocator(locator, {});\n            }\n            domBuilder.locator = locator2;\n            if (appendElement(el, domBuilder, currentNSMap)) {\n              parseStack.push(el);\n            }\n            domBuilder.locator = locator;\n          } else {\n            if (appendElement(el, domBuilder, currentNSMap)) {\n              parseStack.push(el);\n            }\n          }\n          if (NAMESPACE.isHTML(el.uri) && !el.closed) {\n            end = parseHtmlSpecialContent(source, end, el.tagName, entityReplacer, domBuilder);\n          } else {\n            end++;\n          }\n      }\n    } catch (e) {\n      if (e instanceof ParseError) {\n        throw e;\n      }\n      errorHandler.error('element parse error: ' + e);\n      end = -1;\n    }\n    if (end > start) {\n      start = end;\n    } else {\n      //TODO: 这里有可能sax回退，有位置错误风险\n      appendText(Math.max(tagStart, start) + 1);\n    }\n  }\n}\nfunction copyLocator(f, t) {\n  t.lineNumber = f.lineNumber;\n  t.columnNumber = f.columnNumber;\n  return t;\n}\n\n/**\n * @see #appendElement(source,elStartEnd,el,selfClosed,entityReplacer,domBuilder,parseStack);\n * @return end of the elementStartPart(end of elementEndPart for selfClosed el)\n */\nfunction parseElementStartPart(source, start, el, currentNSMap, entityReplacer, errorHandler) {\n  /**\n   * @param {string} qname\n   * @param {string} value\n   * @param {number} startIndex\n   */\n  function addAttribute(qname, value, startIndex) {\n    if (el.attributeNames.hasOwnProperty(qname)) {\n      errorHandler.fatalError('Attribute ' + qname + ' redefined');\n    }\n    el.addValue(qname,\n    // @see https://www.w3.org/TR/xml/#AVNormalize\n    // since the xmldom sax parser does not \"interpret\" DTD the following is not implemented:\n    // - recursive replacement of (DTD) entity references\n    // - trimming and collapsing multiple spaces into a single one for attributes that are not of type CDATA\n    value.replace(/[\\t\\n\\r]/g, ' ').replace(/&#?\\w+;/g, entityReplacer), startIndex);\n  }\n  var attrName;\n  var value;\n  var p = ++start;\n  var s = S_TAG; //status\n  while (true) {\n    var c = source.charAt(p);\n    switch (c) {\n      case '=':\n        if (s === S_ATTR) {\n          //attrName\n          attrName = source.slice(start, p);\n          s = S_EQ;\n        } else if (s === S_ATTR_SPACE) {\n          s = S_EQ;\n        } else {\n          //fatalError: equal must after attrName or space after attrName\n          throw new Error('attribute equal must after attrName'); // No known test case\n        }\n\n        break;\n      case '\\'':\n      case '\"':\n        if (s === S_EQ || s === S_ATTR //|| s == S_ATTR_SPACE\n        ) {\n          //equal\n          if (s === S_ATTR) {\n            errorHandler.warning('attribute value must after \"=\"');\n            attrName = source.slice(start, p);\n          }\n          start = p + 1;\n          p = source.indexOf(c, start);\n          if (p > 0) {\n            value = source.slice(start, p);\n            addAttribute(attrName, value, start - 1);\n            s = S_ATTR_END;\n          } else {\n            //fatalError: no end quot match\n            throw new Error('attribute value no end \\'' + c + '\\' match');\n          }\n        } else if (s == S_ATTR_NOQUOT_VALUE) {\n          value = source.slice(start, p);\n          addAttribute(attrName, value, start);\n          errorHandler.warning('attribute \"' + attrName + '\" missed start quot(' + c + ')!!');\n          start = p + 1;\n          s = S_ATTR_END;\n        } else {\n          //fatalError: no equal before\n          throw new Error('attribute value must after \"=\"'); // No known test case\n        }\n\n        break;\n      case '/':\n        switch (s) {\n          case S_TAG:\n            el.setTagName(source.slice(start, p));\n          case S_ATTR_END:\n          case S_TAG_SPACE:\n          case S_TAG_CLOSE:\n            s = S_TAG_CLOSE;\n            el.closed = true;\n          case S_ATTR_NOQUOT_VALUE:\n          case S_ATTR:\n            break;\n          case S_ATTR_SPACE:\n            el.closed = true;\n            break;\n          //case S_EQ:\n          default:\n            throw new Error(\"attribute invalid close char('/')\");\n          // No known test case\n        }\n\n        break;\n      case '':\n        //end document\n        errorHandler.error('unexpected end of input');\n        if (s == S_TAG) {\n          el.setTagName(source.slice(start, p));\n        }\n        return p;\n      case '>':\n        switch (s) {\n          case S_TAG:\n            el.setTagName(source.slice(start, p));\n          case S_ATTR_END:\n          case S_TAG_SPACE:\n          case S_TAG_CLOSE:\n            break;\n          //normal\n          case S_ATTR_NOQUOT_VALUE: //Compatible state\n          case S_ATTR:\n            value = source.slice(start, p);\n            if (value.slice(-1) === '/') {\n              el.closed = true;\n              value = value.slice(0, -1);\n            }\n          case S_ATTR_SPACE:\n            if (s === S_ATTR_SPACE) {\n              value = attrName;\n            }\n            if (s == S_ATTR_NOQUOT_VALUE) {\n              errorHandler.warning('attribute \"' + value + '\" missed quot(\")!');\n              addAttribute(attrName, value, start);\n            } else {\n              if (!NAMESPACE.isHTML(currentNSMap['']) || !value.match(/^(?:disabled|checked|selected)$/i)) {\n                errorHandler.warning('attribute \"' + value + '\" missed value!! \"' + value + '\" instead!!');\n              }\n              addAttribute(value, value, start);\n            }\n            break;\n          case S_EQ:\n            throw new Error('attribute value missed!!');\n        }\n        //\t\t\tconsole.log(tagName,tagNamePattern,tagNamePattern.test(tagName))\n        return p;\n      /*xml space '\\x20' | #x9 | #xD | #xA; */\n      case '\\u0080':\n        c = ' ';\n      default:\n        if (c <= ' ') {\n          //space\n          switch (s) {\n            case S_TAG:\n              el.setTagName(source.slice(start, p)); //tagName\n              s = S_TAG_SPACE;\n              break;\n            case S_ATTR:\n              attrName = source.slice(start, p);\n              s = S_ATTR_SPACE;\n              break;\n            case S_ATTR_NOQUOT_VALUE:\n              var value = source.slice(start, p);\n              errorHandler.warning('attribute \"' + value + '\" missed quot(\")!!');\n              addAttribute(attrName, value, start);\n            case S_ATTR_END:\n              s = S_TAG_SPACE;\n              break;\n            //case S_TAG_SPACE:\n            //case S_EQ:\n            //case S_ATTR_SPACE:\n            //\tvoid();break;\n            //case S_TAG_CLOSE:\n            //ignore warning\n          }\n        } else {\n          //not space\n          //S_TAG,\tS_ATTR,\tS_EQ,\tS_ATTR_NOQUOT_VALUE\n          //S_ATTR_SPACE,\tS_ATTR_END,\tS_TAG_SPACE, S_TAG_CLOSE\n          switch (s) {\n            //case S_TAG:void();break;\n            //case S_ATTR:void();break;\n            //case S_ATTR_NOQUOT_VALUE:void();break;\n            case S_ATTR_SPACE:\n              var tagName = el.tagName;\n              if (!NAMESPACE.isHTML(currentNSMap['']) || !attrName.match(/^(?:disabled|checked|selected)$/i)) {\n                errorHandler.warning('attribute \"' + attrName + '\" missed value!! \"' + attrName + '\" instead2!!');\n              }\n              addAttribute(attrName, attrName, start);\n              start = p;\n              s = S_ATTR;\n              break;\n            case S_ATTR_END:\n              errorHandler.warning('attribute space is required\"' + attrName + '\"!!');\n            case S_TAG_SPACE:\n              s = S_ATTR;\n              start = p;\n              break;\n            case S_EQ:\n              s = S_ATTR_NOQUOT_VALUE;\n              start = p;\n              break;\n            case S_TAG_CLOSE:\n              throw new Error(\"elements closed character '/' and '>' must be connected to\");\n          }\n        }\n    } //end outer switch\n    //console.log('p++',p)\n    p++;\n  }\n}\n/**\n * @return true if has new namespace define\n */\nfunction appendElement(el, domBuilder, currentNSMap) {\n  var tagName = el.tagName;\n  var localNSMap = null;\n  //var currentNSMap = parseStack[parseStack.length-1].currentNSMap;\n  var i = el.length;\n  while (i--) {\n    var a = el[i];\n    var qName = a.qName;\n    var value = a.value;\n    var nsp = qName.indexOf(':');\n    if (nsp > 0) {\n      var prefix = a.prefix = qName.slice(0, nsp);\n      var localName = qName.slice(nsp + 1);\n      var nsPrefix = prefix === 'xmlns' && localName;\n    } else {\n      localName = qName;\n      prefix = null;\n      nsPrefix = qName === 'xmlns' && '';\n    }\n    //can not set prefix,because prefix !== ''\n    a.localName = localName;\n    //prefix == null for no ns prefix attribute\n    if (nsPrefix !== false) {\n      //hack!!\n      if (localNSMap == null) {\n        localNSMap = {};\n        //console.log(currentNSMap,0)\n        _copy(currentNSMap, currentNSMap = {});\n        //console.log(currentNSMap,1)\n      }\n\n      currentNSMap[nsPrefix] = localNSMap[nsPrefix] = value;\n      a.uri = NAMESPACE.XMLNS;\n      domBuilder.startPrefixMapping(nsPrefix, value);\n    }\n  }\n  var i = el.length;\n  while (i--) {\n    a = el[i];\n    var prefix = a.prefix;\n    if (prefix) {\n      //no prefix attribute has no namespace\n      if (prefix === 'xml') {\n        a.uri = NAMESPACE.XML;\n      }\n      if (prefix !== 'xmlns') {\n        a.uri = currentNSMap[prefix || ''];\n\n        //{console.log('###'+a.qName,domBuilder.locator.systemId+'',currentNSMap,a.uri)}\n      }\n    }\n  }\n\n  var nsp = tagName.indexOf(':');\n  if (nsp > 0) {\n    prefix = el.prefix = tagName.slice(0, nsp);\n    localName = el.localName = tagName.slice(nsp + 1);\n  } else {\n    prefix = null; //important!!\n    localName = el.localName = tagName;\n  }\n  //no prefix element has default namespace\n  var ns = el.uri = currentNSMap[prefix || ''];\n  domBuilder.startElement(ns, localName, tagName, el);\n  //endPrefixMapping and startPrefixMapping have not any help for dom builder\n  //localNSMap = null\n  if (el.closed) {\n    domBuilder.endElement(ns, localName, tagName);\n    if (localNSMap) {\n      for (prefix in localNSMap) {\n        if (Object.prototype.hasOwnProperty.call(localNSMap, prefix)) {\n          domBuilder.endPrefixMapping(prefix);\n        }\n      }\n    }\n  } else {\n    el.currentNSMap = currentNSMap;\n    el.localNSMap = localNSMap;\n    //parseStack.push(el);\n    return true;\n  }\n}\nfunction parseHtmlSpecialContent(source, elStartEnd, tagName, entityReplacer, domBuilder) {\n  if (/^(?:script|textarea)$/i.test(tagName)) {\n    var elEndStart = source.indexOf('</' + tagName + '>', elStartEnd);\n    var text = source.substring(elStartEnd + 1, elEndStart);\n    if (/[&<]/.test(text)) {\n      if (/^script$/i.test(tagName)) {\n        //if(!/\\]\\]>/.test(text)){\n        //lexHandler.startCDATA();\n        domBuilder.characters(text, 0, text.length);\n        //lexHandler.endCDATA();\n        return elEndStart;\n        //}\n      } //}else{//text area\n      text = text.replace(/&#?\\w+;/g, entityReplacer);\n      domBuilder.characters(text, 0, text.length);\n      return elEndStart;\n      //}\n    }\n  }\n\n  return elStartEnd + 1;\n}\nfunction fixSelfClosed(source, elStartEnd, tagName, closeMap) {\n  //if(tagName in closeMap){\n  var pos = closeMap[tagName];\n  if (pos == null) {\n    //console.log(tagName)\n    pos = source.lastIndexOf('</' + tagName + '>');\n    if (pos < elStartEnd) {\n      //忘记闭合\n      pos = source.lastIndexOf('</' + tagName);\n    }\n    closeMap[tagName] = pos;\n  }\n  return pos < elStartEnd;\n  //}\n}\n\nfunction _copy(source, target) {\n  for (var n in source) {\n    if (Object.prototype.hasOwnProperty.call(source, n)) {\n      target[n] = source[n];\n    }\n  }\n}\nfunction parseDCC(source, start, domBuilder, errorHandler) {\n  //sure start with '<!'\n  var next = source.charAt(start + 2);\n  switch (next) {\n    case '-':\n      if (source.charAt(start + 3) === '-') {\n        var end = source.indexOf('-->', start + 4);\n        //append comment source.substring(4,end)//<!--\n        if (end > start) {\n          domBuilder.comment(source, start + 4, end - start - 4);\n          return end + 3;\n        } else {\n          errorHandler.error(\"Unclosed comment\");\n          return -1;\n        }\n      } else {\n        //error\n        return -1;\n      }\n    default:\n      if (source.substr(start + 3, 6) == 'CDATA[') {\n        var end = source.indexOf(']]>', start + 9);\n        domBuilder.startCDATA();\n        domBuilder.characters(source, start + 9, end - start - 9);\n        domBuilder.endCDATA();\n        return end + 3;\n      }\n      //<!DOCTYPE\n      //startDTD(java.lang.String name, java.lang.String publicId, java.lang.String systemId)\n      var matchs = split(source, start);\n      var len = matchs.length;\n      if (len > 1 && /!doctype/i.test(matchs[0][0])) {\n        var name = matchs[1][0];\n        var pubid = false;\n        var sysid = false;\n        if (len > 3) {\n          if (/^public$/i.test(matchs[2][0])) {\n            pubid = matchs[3][0];\n            sysid = len > 4 && matchs[4][0];\n          } else if (/^system$/i.test(matchs[2][0])) {\n            sysid = matchs[3][0];\n          }\n        }\n        var lastMatch = matchs[len - 1];\n        domBuilder.startDTD(name, pubid, sysid);\n        domBuilder.endDTD();\n        return lastMatch.index + lastMatch[0].length;\n      }\n  }\n  return -1;\n}\nfunction parseInstruction(source, start, domBuilder) {\n  var end = source.indexOf('?>', start);\n  if (end) {\n    var match = source.substring(start, end).match(/^<\\?(\\S*)\\s*([\\s\\S]*?)\\s*$/);\n    if (match) {\n      var len = match[0].length;\n      domBuilder.processingInstruction(match[1], match[2]);\n      return end + 2;\n    } else {\n      //error\n      return -1;\n    }\n  }\n  return -1;\n}\nfunction ElementAttributes() {\n  this.attributeNames = {};\n}\nElementAttributes.prototype = {\n  setTagName: function (tagName) {\n    if (!tagNamePattern.test(tagName)) {\n      throw new Error('invalid tagName:' + tagName);\n    }\n    this.tagName = tagName;\n  },\n  addValue: function (qName, value, offset) {\n    if (!tagNamePattern.test(qName)) {\n      throw new Error('invalid attribute:' + qName);\n    }\n    this.attributeNames[qName] = this.length;\n    this[this.length++] = {\n      qName: qName,\n      value: value,\n      offset: offset\n    };\n  },\n  length: 0,\n  getLocalName: function (i) {\n    return this[i].localName;\n  },\n  getLocator: function (i) {\n    return this[i].locator;\n  },\n  getQName: function (i) {\n    return this[i].qName;\n  },\n  getURI: function (i) {\n    return this[i].uri;\n  },\n  getValue: function (i) {\n    return this[i].value;\n  }\n  //\t,getIndex:function(uri, localName)){\n  //\t\tif(localName){\n  //\n  //\t\t}else{\n  //\t\t\tvar qName = uri\n  //\t\t}\n  //\t},\n  //\tgetValue:function(){return this.getValue(this.getIndex.apply(this,arguments))},\n  //\tgetType:function(uri,localName){}\n  //\tgetType:function(i){},\n};\n\nfunction split(source, start) {\n  var match;\n  var buf = [];\n  var reg = /'[^']+'|\"[^\"]+\"|[^\\s<>\\/=]+=?|(\\/?\\s*>|<)/g;\n  reg.lastIndex = start;\n  reg.exec(source); //skip <\n  while (match = reg.exec(source)) {\n    buf.push(match);\n    if (match[1]) return buf;\n  }\n}\nexports.XMLReader = XMLReader;\nexports.ParseError = ParseError;", "map": {"version": 3, "names": ["NAMESPACE", "require", "nameStartChar", "nameChar", "RegExp", "source", "slice", "tagNamePattern", "S_TAG", "S_ATTR", "S_ATTR_SPACE", "S_EQ", "S_ATTR_NOQUOT_VALUE", "S_ATTR_END", "S_TAG_SPACE", "S_TAG_CLOSE", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message", "locator", "Error", "captureStackTrace", "prototype", "name", "XMLReader", "parse", "defaultNSMap", "entityMap", "domBuilder", "startDocument", "_copy", "<PERSON><PERSON><PERSON><PERSON>", "endDocument", "defaultNSMapCopy", "fixedFromCharCode", "code", "surrogate1", "surrogate2", "String", "fromCharCode", "entityReplacer", "a", "k", "Object", "hasOwnProperty", "call", "char<PERSON>t", "parseInt", "substr", "replace", "error", "appendText", "end", "start", "xt", "substring", "position", "characters", "p", "m", "lineEnd", "linePattern", "exec", "lineStart", "index", "length", "lineNumber", "columnNumber", "parseStack", "currentNSMap", "closeMap", "tagStart", "indexOf", "match", "doc", "text", "createTextNode", "append<PERSON><PERSON><PERSON>", "currentElement", "tagName", "config", "pop", "localNSMap", "endMatch", "endIgnoreCaseMach", "toLowerCase", "endElement", "uri", "localName", "prefix", "endPrefixMapping", "fatalError", "push", "parseInstruction", "parseDCC", "el", "ElementAttributes", "parseElementStartPart", "len", "closed", "fixSelfClosed", "nbsp", "warning", "locator2", "copyLocator", "i", "offset", "appendElement", "isHTML", "parseHtmlSpecialContent", "e", "Math", "max", "f", "t", "addAttribute", "qname", "value", "startIndex", "attributeNames", "addValue", "attrName", "s", "c", "setTagName", "qName", "nsp", "nsPrefix", "XMLNS", "startPrefixMapping", "XML", "ns", "startElement", "elStartEnd", "test", "elEndStart", "pos", "lastIndexOf", "target", "n", "next", "comment", "startCDATA", "endCDATA", "matchs", "split", "pubid", "sysid", "lastMatch", "startDTD", "endDTD", "processingInstruction", "getLocalName", "getLocator", "getQName", "getURI", "getValue", "buf", "reg", "lastIndex", "exports"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@xmldom/xmldom/lib/sax.js"], "sourcesContent": ["var NAMESPACE = require(\"./conventions\").NAMESPACE;\n\n//[4]   \tNameStartChar\t   ::=   \t\":\" | [A-Z] | \"_\" | [a-z] | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x2FF] | [#x370-#x37D] | [#x37F-#x1FFF] | [#x200C-#x200D] | [#x2070-#x218F] | [#x2C00-#x2FEF] | [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n//[4a]   \tNameChar\t   ::=   \tNameStartChar | \"-\" | \".\" | [0-9] | #xB7 | [#x0300-#x036F] | [#x203F-#x2040]\n//[5]   \tName\t   ::=   \tNameStartChar (NameChar)*\nvar nameStartChar = /[A-Z_a-z\\xC0-\\xD6\\xD8-\\xF6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]///\\u10000-\\uEFFFF\nvar nameChar = new RegExp(\"[\\\\-\\\\.0-9\"+nameStartChar.source.slice(1,-1)+\"\\\\u00B7\\\\u0300-\\\\u036F\\\\u203F-\\\\u2040]\");\nvar tagNamePattern = new RegExp('^'+nameStartChar.source+nameChar.source+'*(?:\\:'+nameStartChar.source+nameChar.source+'*)?$');\n//var tagNamePattern = /^[a-zA-Z_][\\w\\-\\.]*(?:\\:[a-zA-Z_][\\w\\-\\.]*)?$/\n//var handlers = 'resolveEntity,getExternalSubset,characters,endDocument,endElement,endPrefixMapping,ignorableWhitespace,processingInstruction,setDocumentLocator,skippedEntity,startDocument,startElement,startPrefixMapping,notationDecl,unparsedEntityDecl,error,fatalError,warning,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,comment,endCDATA,endDTD,endEntity,startCDATA,startDTD,startEntity'.split(',')\n\n//S_TAG,\tS_ATTR,\tS_EQ,\tS_ATTR_NOQUOT_VALUE\n//S_ATTR_SPACE,\tS_ATTR_END,\tS_TAG_SPACE, S_TAG_CLOSE\nvar S_TAG = 0;//tag name offerring\nvar S_ATTR = 1;//attr name offerring\nvar S_ATTR_SPACE=2;//attr name end and space offer\nvar S_EQ = 3;//=space?\nvar S_ATTR_NOQUOT_VALUE = 4;//attr value(no quot value only)\nvar S_ATTR_END = 5;//attr value end and no space(quot end)\nvar S_TAG_SPACE = 6;//(attr value end || tag end ) && (space offer)\nvar S_TAG_CLOSE = 7;//closed el<el />\n\n/**\n * Creates an error that will not be caught by XMLReader aka the SAX parser.\n *\n * @param {string} message\n * @param {any?} locator Optional, can provide details about the location in the source\n * @constructor\n */\nfunction ParseError(message, locator) {\n\tthis.message = message\n\tthis.locator = locator\n\tif(Error.captureStackTrace) Error.captureStackTrace(this, ParseError);\n}\nParseError.prototype = new Error();\nParseError.prototype.name = ParseError.name\n\nfunction XMLReader(){\n\n}\n\nXMLReader.prototype = {\n\tparse:function(source,defaultNSMap,entityMap){\n\t\tvar domBuilder = this.domBuilder;\n\t\tdomBuilder.startDocument();\n\t\t_copy(defaultNSMap ,defaultNSMap = {})\n\t\tparse(source,defaultNSMap,entityMap,\n\t\t\t\tdomBuilder,this.errorHandler);\n\t\tdomBuilder.endDocument();\n\t}\n}\nfunction parse(source,defaultNSMapCopy,entityMap,domBuilder,errorHandler){\n\tfunction fixedFromCharCode(code) {\n\t\t// String.prototype.fromCharCode does not supports\n\t\t// > 2 bytes unicode chars directly\n\t\tif (code > 0xffff) {\n\t\t\tcode -= 0x10000;\n\t\t\tvar surrogate1 = 0xd800 + (code >> 10)\n\t\t\t\t, surrogate2 = 0xdc00 + (code & 0x3ff);\n\n\t\t\treturn String.fromCharCode(surrogate1, surrogate2);\n\t\t} else {\n\t\t\treturn String.fromCharCode(code);\n\t\t}\n\t}\n\tfunction entityReplacer(a){\n\t\tvar k = a.slice(1,-1);\n\t\tif (Object.hasOwnProperty.call(entityMap, k)) {\n\t\t\treturn entityMap[k];\n\t\t}else if(k.charAt(0) === '#'){\n\t\t\treturn fixedFromCharCode(parseInt(k.substr(1).replace('x','0x')))\n\t\t}else{\n\t\t\terrorHandler.error('entity not found:'+a);\n\t\t\treturn a;\n\t\t}\n\t}\n\tfunction appendText(end){//has some bugs\n\t\tif(end>start){\n\t\t\tvar xt = source.substring(start,end).replace(/&#?\\w+;/g,entityReplacer);\n\t\t\tlocator&&position(start);\n\t\t\tdomBuilder.characters(xt,0,end-start);\n\t\t\tstart = end\n\t\t}\n\t}\n\tfunction position(p,m){\n\t\twhile(p>=lineEnd && (m = linePattern.exec(source))){\n\t\t\tlineStart = m.index;\n\t\t\tlineEnd = lineStart + m[0].length;\n\t\t\tlocator.lineNumber++;\n\t\t\t//console.log('line++:',locator,startPos,endPos)\n\t\t}\n\t\tlocator.columnNumber = p-lineStart+1;\n\t}\n\tvar lineStart = 0;\n\tvar lineEnd = 0;\n\tvar linePattern = /.*(?:\\r\\n?|\\n)|.*$/g\n\tvar locator = domBuilder.locator;\n\n\tvar parseStack = [{currentNSMap:defaultNSMapCopy}]\n\tvar closeMap = {};\n\tvar start = 0;\n\twhile(true){\n\t\ttry{\n\t\t\tvar tagStart = source.indexOf('<',start);\n\t\t\tif(tagStart<0){\n\t\t\t\tif(!source.substr(start).match(/^\\s*$/)){\n\t\t\t\t\tvar doc = domBuilder.doc;\n\t    \t\t\tvar text = doc.createTextNode(source.substr(start));\n\t    \t\t\tdoc.appendChild(text);\n\t    \t\t\tdomBuilder.currentElement = text;\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif(tagStart>start){\n\t\t\t\tappendText(tagStart);\n\t\t\t}\n\t\t\tswitch(source.charAt(tagStart+1)){\n\t\t\tcase '/':\n\t\t\t\tvar end = source.indexOf('>',tagStart+3);\n\t\t\t\tvar tagName = source.substring(tagStart + 2, end).replace(/[ \\t\\n\\r]+$/g, '');\n\t\t\t\tvar config = parseStack.pop();\n\t\t\t\tif(end<0){\n\n\t        \t\ttagName = source.substring(tagStart+2).replace(/[\\s<].*/,'');\n\t        \t\terrorHandler.error(\"end tag name: \"+tagName+' is not complete:'+config.tagName);\n\t        \t\tend = tagStart+1+tagName.length;\n\t        \t}else if(tagName.match(/\\s</)){\n\t        \t\ttagName = tagName.replace(/[\\s<].*/,'');\n\t        \t\terrorHandler.error(\"end tag name: \"+tagName+' maybe not complete');\n\t        \t\tend = tagStart+1+tagName.length;\n\t\t\t\t}\n\t\t\t\tvar localNSMap = config.localNSMap;\n\t\t\t\tvar endMatch = config.tagName == tagName;\n\t\t\t\tvar endIgnoreCaseMach = endMatch || config.tagName&&config.tagName.toLowerCase() == tagName.toLowerCase()\n\t\t        if(endIgnoreCaseMach){\n\t\t        \tdomBuilder.endElement(config.uri,config.localName,tagName);\n\t\t\t\t\tif(localNSMap){\n\t\t\t\t\t\tfor (var prefix in localNSMap) {\n\t\t\t\t\t\t\tif (Object.prototype.hasOwnProperty.call(localNSMap, prefix)) {\n\t\t\t\t\t\t\t\tdomBuilder.endPrefixMapping(prefix);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif(!endMatch){\n\t\t            \terrorHandler.fatalError(\"end tag name: \"+tagName+' is not match the current start tagName:'+config.tagName ); // No known test case\n\t\t\t\t\t}\n\t\t        }else{\n\t\t        \tparseStack.push(config)\n\t\t        }\n\n\t\t\t\tend++;\n\t\t\t\tbreak;\n\t\t\t\t// end elment\n\t\t\tcase '?':// <?...?>\n\t\t\t\tlocator&&position(tagStart);\n\t\t\t\tend = parseInstruction(source,tagStart,domBuilder);\n\t\t\t\tbreak;\n\t\t\tcase '!':// <!doctype,<![CDATA,<!--\n\t\t\t\tlocator&&position(tagStart);\n\t\t\t\tend = parseDCC(source,tagStart,domBuilder,errorHandler);\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tlocator&&position(tagStart);\n\t\t\t\tvar el = new ElementAttributes();\n\t\t\t\tvar currentNSMap = parseStack[parseStack.length-1].currentNSMap;\n\t\t\t\t//elStartEnd\n\t\t\t\tvar end = parseElementStartPart(source,tagStart,el,currentNSMap,entityReplacer,errorHandler);\n\t\t\t\tvar len = el.length;\n\n\n\t\t\t\tif(!el.closed && fixSelfClosed(source,end,el.tagName,closeMap)){\n\t\t\t\t\tel.closed = true;\n\t\t\t\t\tif(!entityMap.nbsp){\n\t\t\t\t\t\terrorHandler.warning('unclosed xml attribute');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif(locator && len){\n\t\t\t\t\tvar locator2 = copyLocator(locator,{});\n\t\t\t\t\t//try{//attribute position fixed\n\t\t\t\t\tfor(var i = 0;i<len;i++){\n\t\t\t\t\t\tvar a = el[i];\n\t\t\t\t\t\tposition(a.offset);\n\t\t\t\t\t\ta.locator = copyLocator(locator,{});\n\t\t\t\t\t}\n\t\t\t\t\tdomBuilder.locator = locator2\n\t\t\t\t\tif(appendElement(el,domBuilder,currentNSMap)){\n\t\t\t\t\t\tparseStack.push(el)\n\t\t\t\t\t}\n\t\t\t\t\tdomBuilder.locator = locator;\n\t\t\t\t}else{\n\t\t\t\t\tif(appendElement(el,domBuilder,currentNSMap)){\n\t\t\t\t\t\tparseStack.push(el)\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (NAMESPACE.isHTML(el.uri) && !el.closed) {\n\t\t\t\t\tend = parseHtmlSpecialContent(source,end,el.tagName,entityReplacer,domBuilder)\n\t\t\t\t} else {\n\t\t\t\t\tend++;\n\t\t\t\t}\n\t\t\t}\n\t\t}catch(e){\n\t\t\tif (e instanceof ParseError) {\n\t\t\t\tthrow e;\n\t\t\t}\n\t\t\terrorHandler.error('element parse error: '+e)\n\t\t\tend = -1;\n\t\t}\n\t\tif(end>start){\n\t\t\tstart = end;\n\t\t}else{\n\t\t\t//TODO: 这里有可能sax回退，有位置错误风险\n\t\t\tappendText(Math.max(tagStart,start)+1);\n\t\t}\n\t}\n}\nfunction copyLocator(f,t){\n\tt.lineNumber = f.lineNumber;\n\tt.columnNumber = f.columnNumber;\n\treturn t;\n}\n\n/**\n * @see #appendElement(source,elStartEnd,el,selfClosed,entityReplacer,domBuilder,parseStack);\n * @return end of the elementStartPart(end of elementEndPart for selfClosed el)\n */\nfunction parseElementStartPart(source,start,el,currentNSMap,entityReplacer,errorHandler){\n\n\t/**\n\t * @param {string} qname\n\t * @param {string} value\n\t * @param {number} startIndex\n\t */\n\tfunction addAttribute(qname, value, startIndex) {\n\t\tif (el.attributeNames.hasOwnProperty(qname)) {\n\t\t\terrorHandler.fatalError('Attribute ' + qname + ' redefined')\n\t\t}\n\t\tel.addValue(\n\t\t\tqname,\n\t\t\t// @see https://www.w3.org/TR/xml/#AVNormalize\n\t\t\t// since the xmldom sax parser does not \"interpret\" DTD the following is not implemented:\n\t\t\t// - recursive replacement of (DTD) entity references\n\t\t\t// - trimming and collapsing multiple spaces into a single one for attributes that are not of type CDATA\n\t\t\tvalue.replace(/[\\t\\n\\r]/g, ' ').replace(/&#?\\w+;/g, entityReplacer),\n\t\t\tstartIndex\n\t\t)\n\t}\n\tvar attrName;\n\tvar value;\n\tvar p = ++start;\n\tvar s = S_TAG;//status\n\twhile(true){\n\t\tvar c = source.charAt(p);\n\t\tswitch(c){\n\t\tcase '=':\n\t\t\tif(s === S_ATTR){//attrName\n\t\t\t\tattrName = source.slice(start,p);\n\t\t\t\ts = S_EQ;\n\t\t\t}else if(s === S_ATTR_SPACE){\n\t\t\t\ts = S_EQ;\n\t\t\t}else{\n\t\t\t\t//fatalError: equal must after attrName or space after attrName\n\t\t\t\tthrow new Error('attribute equal must after attrName'); // No known test case\n\t\t\t}\n\t\t\tbreak;\n\t\tcase '\\'':\n\t\tcase '\"':\n\t\t\tif(s === S_EQ || s === S_ATTR //|| s == S_ATTR_SPACE\n\t\t\t\t){//equal\n\t\t\t\tif(s === S_ATTR){\n\t\t\t\t\terrorHandler.warning('attribute value must after \"=\"')\n\t\t\t\t\tattrName = source.slice(start,p)\n\t\t\t\t}\n\t\t\t\tstart = p+1;\n\t\t\t\tp = source.indexOf(c,start)\n\t\t\t\tif(p>0){\n\t\t\t\t\tvalue = source.slice(start, p);\n\t\t\t\t\taddAttribute(attrName, value, start-1);\n\t\t\t\t\ts = S_ATTR_END;\n\t\t\t\t}else{\n\t\t\t\t\t//fatalError: no end quot match\n\t\t\t\t\tthrow new Error('attribute value no end \\''+c+'\\' match');\n\t\t\t\t}\n\t\t\t}else if(s == S_ATTR_NOQUOT_VALUE){\n\t\t\t\tvalue = source.slice(start, p);\n\t\t\t\taddAttribute(attrName, value, start);\n\t\t\t\terrorHandler.warning('attribute \"'+attrName+'\" missed start quot('+c+')!!');\n\t\t\t\tstart = p+1;\n\t\t\t\ts = S_ATTR_END\n\t\t\t}else{\n\t\t\t\t//fatalError: no equal before\n\t\t\t\tthrow new Error('attribute value must after \"=\"'); // No known test case\n\t\t\t}\n\t\t\tbreak;\n\t\tcase '/':\n\t\t\tswitch(s){\n\t\t\tcase S_TAG:\n\t\t\t\tel.setTagName(source.slice(start,p));\n\t\t\tcase S_ATTR_END:\n\t\t\tcase S_TAG_SPACE:\n\t\t\tcase S_TAG_CLOSE:\n\t\t\t\ts =S_TAG_CLOSE;\n\t\t\t\tel.closed = true;\n\t\t\tcase S_ATTR_NOQUOT_VALUE:\n\t\t\tcase S_ATTR:\n\t\t\t\tbreak;\n\t\t\t\tcase S_ATTR_SPACE:\n\t\t\t\t\tel.closed = true;\n\t\t\t\tbreak;\n\t\t\t//case S_EQ:\n\t\t\tdefault:\n\t\t\t\tthrow new Error(\"attribute invalid close char('/')\") // No known test case\n\t\t\t}\n\t\t\tbreak;\n\t\tcase ''://end document\n\t\t\terrorHandler.error('unexpected end of input');\n\t\t\tif(s == S_TAG){\n\t\t\t\tel.setTagName(source.slice(start,p));\n\t\t\t}\n\t\t\treturn p;\n\t\tcase '>':\n\t\t\tswitch(s){\n\t\t\tcase S_TAG:\n\t\t\t\tel.setTagName(source.slice(start,p));\n\t\t\tcase S_ATTR_END:\n\t\t\tcase S_TAG_SPACE:\n\t\t\tcase S_TAG_CLOSE:\n\t\t\t\tbreak;//normal\n\t\t\tcase S_ATTR_NOQUOT_VALUE://Compatible state\n\t\t\tcase S_ATTR:\n\t\t\t\tvalue = source.slice(start,p);\n\t\t\t\tif(value.slice(-1) === '/'){\n\t\t\t\t\tel.closed  = true;\n\t\t\t\t\tvalue = value.slice(0,-1)\n\t\t\t\t}\n\t\t\tcase S_ATTR_SPACE:\n\t\t\t\tif(s === S_ATTR_SPACE){\n\t\t\t\t\tvalue = attrName;\n\t\t\t\t}\n\t\t\t\tif(s == S_ATTR_NOQUOT_VALUE){\n\t\t\t\t\terrorHandler.warning('attribute \"'+value+'\" missed quot(\")!');\n\t\t\t\t\taddAttribute(attrName, value, start)\n\t\t\t\t}else{\n\t\t\t\t\tif(!NAMESPACE.isHTML(currentNSMap['']) || !value.match(/^(?:disabled|checked|selected)$/i)){\n\t\t\t\t\t\terrorHandler.warning('attribute \"'+value+'\" missed value!! \"'+value+'\" instead!!')\n\t\t\t\t\t}\n\t\t\t\t\taddAttribute(value, value, start)\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase S_EQ:\n\t\t\t\tthrow new Error('attribute value missed!!');\n\t\t\t}\n//\t\t\tconsole.log(tagName,tagNamePattern,tagNamePattern.test(tagName))\n\t\t\treturn p;\n\t\t/*xml space '\\x20' | #x9 | #xD | #xA; */\n\t\tcase '\\u0080':\n\t\t\tc = ' ';\n\t\tdefault:\n\t\t\tif(c<= ' '){//space\n\t\t\t\tswitch(s){\n\t\t\t\tcase S_TAG:\n\t\t\t\t\tel.setTagName(source.slice(start,p));//tagName\n\t\t\t\t\ts = S_TAG_SPACE;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_ATTR:\n\t\t\t\t\tattrName = source.slice(start,p)\n\t\t\t\t\ts = S_ATTR_SPACE;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_ATTR_NOQUOT_VALUE:\n\t\t\t\t\tvar value = source.slice(start, p);\n\t\t\t\t\terrorHandler.warning('attribute \"'+value+'\" missed quot(\")!!');\n\t\t\t\t\taddAttribute(attrName, value, start)\n\t\t\t\tcase S_ATTR_END:\n\t\t\t\t\ts = S_TAG_SPACE;\n\t\t\t\t\tbreak;\n\t\t\t\t//case S_TAG_SPACE:\n\t\t\t\t//case S_EQ:\n\t\t\t\t//case S_ATTR_SPACE:\n\t\t\t\t//\tvoid();break;\n\t\t\t\t//case S_TAG_CLOSE:\n\t\t\t\t\t//ignore warning\n\t\t\t\t}\n\t\t\t}else{//not space\n//S_TAG,\tS_ATTR,\tS_EQ,\tS_ATTR_NOQUOT_VALUE\n//S_ATTR_SPACE,\tS_ATTR_END,\tS_TAG_SPACE, S_TAG_CLOSE\n\t\t\t\tswitch(s){\n\t\t\t\t//case S_TAG:void();break;\n\t\t\t\t//case S_ATTR:void();break;\n\t\t\t\t//case S_ATTR_NOQUOT_VALUE:void();break;\n\t\t\t\tcase S_ATTR_SPACE:\n\t\t\t\t\tvar tagName =  el.tagName;\n\t\t\t\t\tif (!NAMESPACE.isHTML(currentNSMap['']) || !attrName.match(/^(?:disabled|checked|selected)$/i)) {\n\t\t\t\t\t\terrorHandler.warning('attribute \"'+attrName+'\" missed value!! \"'+attrName+'\" instead2!!')\n\t\t\t\t\t}\n\t\t\t\t\taddAttribute(attrName, attrName, start);\n\t\t\t\t\tstart = p;\n\t\t\t\t\ts = S_ATTR;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_ATTR_END:\n\t\t\t\t\terrorHandler.warning('attribute space is required\"'+attrName+'\"!!')\n\t\t\t\tcase S_TAG_SPACE:\n\t\t\t\t\ts = S_ATTR;\n\t\t\t\t\tstart = p;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_EQ:\n\t\t\t\t\ts = S_ATTR_NOQUOT_VALUE;\n\t\t\t\t\tstart = p;\n\t\t\t\t\tbreak;\n\t\t\t\tcase S_TAG_CLOSE:\n\t\t\t\t\tthrow new Error(\"elements closed character '/' and '>' must be connected to\");\n\t\t\t\t}\n\t\t\t}\n\t\t}//end outer switch\n\t\t//console.log('p++',p)\n\t\tp++;\n\t}\n}\n/**\n * @return true if has new namespace define\n */\nfunction appendElement(el,domBuilder,currentNSMap){\n\tvar tagName = el.tagName;\n\tvar localNSMap = null;\n\t//var currentNSMap = parseStack[parseStack.length-1].currentNSMap;\n\tvar i = el.length;\n\twhile(i--){\n\t\tvar a = el[i];\n\t\tvar qName = a.qName;\n\t\tvar value = a.value;\n\t\tvar nsp = qName.indexOf(':');\n\t\tif(nsp>0){\n\t\t\tvar prefix = a.prefix = qName.slice(0,nsp);\n\t\t\tvar localName = qName.slice(nsp+1);\n\t\t\tvar nsPrefix = prefix === 'xmlns' && localName\n\t\t}else{\n\t\t\tlocalName = qName;\n\t\t\tprefix = null\n\t\t\tnsPrefix = qName === 'xmlns' && ''\n\t\t}\n\t\t//can not set prefix,because prefix !== ''\n\t\ta.localName = localName ;\n\t\t//prefix == null for no ns prefix attribute\n\t\tif(nsPrefix !== false){//hack!!\n\t\t\tif(localNSMap == null){\n\t\t\t\tlocalNSMap = {}\n\t\t\t\t//console.log(currentNSMap,0)\n\t\t\t\t_copy(currentNSMap,currentNSMap={})\n\t\t\t\t//console.log(currentNSMap,1)\n\t\t\t}\n\t\t\tcurrentNSMap[nsPrefix] = localNSMap[nsPrefix] = value;\n\t\t\ta.uri = NAMESPACE.XMLNS\n\t\t\tdomBuilder.startPrefixMapping(nsPrefix, value)\n\t\t}\n\t}\n\tvar i = el.length;\n\twhile(i--){\n\t\ta = el[i];\n\t\tvar prefix = a.prefix;\n\t\tif(prefix){//no prefix attribute has no namespace\n\t\t\tif(prefix === 'xml'){\n\t\t\t\ta.uri = NAMESPACE.XML;\n\t\t\t}if(prefix !== 'xmlns'){\n\t\t\t\ta.uri = currentNSMap[prefix || '']\n\n\t\t\t\t//{console.log('###'+a.qName,domBuilder.locator.systemId+'',currentNSMap,a.uri)}\n\t\t\t}\n\t\t}\n\t}\n\tvar nsp = tagName.indexOf(':');\n\tif(nsp>0){\n\t\tprefix = el.prefix = tagName.slice(0,nsp);\n\t\tlocalName = el.localName = tagName.slice(nsp+1);\n\t}else{\n\t\tprefix = null;//important!!\n\t\tlocalName = el.localName = tagName;\n\t}\n\t//no prefix element has default namespace\n\tvar ns = el.uri = currentNSMap[prefix || ''];\n\tdomBuilder.startElement(ns,localName,tagName,el);\n\t//endPrefixMapping and startPrefixMapping have not any help for dom builder\n\t//localNSMap = null\n\tif(el.closed){\n\t\tdomBuilder.endElement(ns,localName,tagName);\n\t\tif(localNSMap){\n\t\t\tfor (prefix in localNSMap) {\n\t\t\t\tif (Object.prototype.hasOwnProperty.call(localNSMap, prefix)) {\n\t\t\t\t\tdomBuilder.endPrefixMapping(prefix);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}else{\n\t\tel.currentNSMap = currentNSMap;\n\t\tel.localNSMap = localNSMap;\n\t\t//parseStack.push(el);\n\t\treturn true;\n\t}\n}\nfunction parseHtmlSpecialContent(source,elStartEnd,tagName,entityReplacer,domBuilder){\n\tif(/^(?:script|textarea)$/i.test(tagName)){\n\t\tvar elEndStart =  source.indexOf('</'+tagName+'>',elStartEnd);\n\t\tvar text = source.substring(elStartEnd+1,elEndStart);\n\t\tif(/[&<]/.test(text)){\n\t\t\tif(/^script$/i.test(tagName)){\n\t\t\t\t//if(!/\\]\\]>/.test(text)){\n\t\t\t\t\t//lexHandler.startCDATA();\n\t\t\t\t\tdomBuilder.characters(text,0,text.length);\n\t\t\t\t\t//lexHandler.endCDATA();\n\t\t\t\t\treturn elEndStart;\n\t\t\t\t//}\n\t\t\t}//}else{//text area\n\t\t\t\ttext = text.replace(/&#?\\w+;/g,entityReplacer);\n\t\t\t\tdomBuilder.characters(text,0,text.length);\n\t\t\t\treturn elEndStart;\n\t\t\t//}\n\n\t\t}\n\t}\n\treturn elStartEnd+1;\n}\nfunction fixSelfClosed(source,elStartEnd,tagName,closeMap){\n\t//if(tagName in closeMap){\n\tvar pos = closeMap[tagName];\n\tif(pos == null){\n\t\t//console.log(tagName)\n\t\tpos =  source.lastIndexOf('</'+tagName+'>')\n\t\tif(pos<elStartEnd){//忘记闭合\n\t\t\tpos = source.lastIndexOf('</'+tagName)\n\t\t}\n\t\tcloseMap[tagName] =pos\n\t}\n\treturn pos<elStartEnd;\n\t//}\n}\n\nfunction _copy (source, target) {\n\tfor (var n in source) {\n\t\tif (Object.prototype.hasOwnProperty.call(source, n)) {\n\t\t\ttarget[n] = source[n];\n\t\t}\n\t}\n}\n\nfunction parseDCC(source,start,domBuilder,errorHandler){//sure start with '<!'\n\tvar next= source.charAt(start+2)\n\tswitch(next){\n\tcase '-':\n\t\tif(source.charAt(start + 3) === '-'){\n\t\t\tvar end = source.indexOf('-->',start+4);\n\t\t\t//append comment source.substring(4,end)//<!--\n\t\t\tif(end>start){\n\t\t\t\tdomBuilder.comment(source,start+4,end-start-4);\n\t\t\t\treturn end+3;\n\t\t\t}else{\n\t\t\t\terrorHandler.error(\"Unclosed comment\");\n\t\t\t\treturn -1;\n\t\t\t}\n\t\t}else{\n\t\t\t//error\n\t\t\treturn -1;\n\t\t}\n\tdefault:\n\t\tif(source.substr(start+3,6) == 'CDATA['){\n\t\t\tvar end = source.indexOf(']]>',start+9);\n\t\t\tdomBuilder.startCDATA();\n\t\t\tdomBuilder.characters(source,start+9,end-start-9);\n\t\t\tdomBuilder.endCDATA()\n\t\t\treturn end+3;\n\t\t}\n\t\t//<!DOCTYPE\n\t\t//startDTD(java.lang.String name, java.lang.String publicId, java.lang.String systemId)\n\t\tvar matchs = split(source,start);\n\t\tvar len = matchs.length;\n\t\tif(len>1 && /!doctype/i.test(matchs[0][0])){\n\t\t\tvar name = matchs[1][0];\n\t\t\tvar pubid = false;\n\t\t\tvar sysid = false;\n\t\t\tif(len>3){\n\t\t\t\tif(/^public$/i.test(matchs[2][0])){\n\t\t\t\t\tpubid = matchs[3][0];\n\t\t\t\t\tsysid = len>4 && matchs[4][0];\n\t\t\t\t}else if(/^system$/i.test(matchs[2][0])){\n\t\t\t\t\tsysid = matchs[3][0];\n\t\t\t\t}\n\t\t\t}\n\t\t\tvar lastMatch = matchs[len-1]\n\t\t\tdomBuilder.startDTD(name, pubid, sysid);\n\t\t\tdomBuilder.endDTD();\n\n\t\t\treturn lastMatch.index+lastMatch[0].length\n\t\t}\n\t}\n\treturn -1;\n}\n\n\n\nfunction parseInstruction(source,start,domBuilder){\n\tvar end = source.indexOf('?>',start);\n\tif(end){\n\t\tvar match = source.substring(start,end).match(/^<\\?(\\S*)\\s*([\\s\\S]*?)\\s*$/);\n\t\tif(match){\n\t\t\tvar len = match[0].length;\n\t\t\tdomBuilder.processingInstruction(match[1], match[2]) ;\n\t\t\treturn end+2;\n\t\t}else{//error\n\t\t\treturn -1;\n\t\t}\n\t}\n\treturn -1;\n}\n\nfunction ElementAttributes(){\n\tthis.attributeNames = {}\n}\nElementAttributes.prototype = {\n\tsetTagName:function(tagName){\n\t\tif(!tagNamePattern.test(tagName)){\n\t\t\tthrow new Error('invalid tagName:'+tagName)\n\t\t}\n\t\tthis.tagName = tagName\n\t},\n\taddValue:function(qName, value, offset) {\n\t\tif(!tagNamePattern.test(qName)){\n\t\t\tthrow new Error('invalid attribute:'+qName)\n\t\t}\n\t\tthis.attributeNames[qName] = this.length;\n\t\tthis[this.length++] = {qName:qName,value:value,offset:offset}\n\t},\n\tlength:0,\n\tgetLocalName:function(i){return this[i].localName},\n\tgetLocator:function(i){return this[i].locator},\n\tgetQName:function(i){return this[i].qName},\n\tgetURI:function(i){return this[i].uri},\n\tgetValue:function(i){return this[i].value}\n//\t,getIndex:function(uri, localName)){\n//\t\tif(localName){\n//\n//\t\t}else{\n//\t\t\tvar qName = uri\n//\t\t}\n//\t},\n//\tgetValue:function(){return this.getValue(this.getIndex.apply(this,arguments))},\n//\tgetType:function(uri,localName){}\n//\tgetType:function(i){},\n}\n\n\n\nfunction split(source,start){\n\tvar match;\n\tvar buf = [];\n\tvar reg = /'[^']+'|\"[^\"]+\"|[^\\s<>\\/=]+=?|(\\/?\\s*>|<)/g;\n\treg.lastIndex = start;\n\treg.exec(source);//skip <\n\twhile(match = reg.exec(source)){\n\t\tbuf.push(match);\n\t\tif(match[1])return buf;\n\t}\n}\n\nexports.XMLReader = XMLReader;\nexports.ParseError = ParseError;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC,CAACD,SAAS;;AAElD;AACA;AACA;AACA,IAAIE,aAAa,GAAG,kJAAkJ;AACtK,IAAIC,QAAQ,GAAG,IAAIC,MAAM,CAAC,YAAY,GAACF,aAAa,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,GAAC,wCAAwC,CAAC;AACjH,IAAIC,cAAc,GAAG,IAAIH,MAAM,CAAC,GAAG,GAACF,aAAa,CAACG,MAAM,GAACF,QAAQ,CAACE,MAAM,GAAC,QAAQ,GAACH,aAAa,CAACG,MAAM,GAACF,QAAQ,CAACE,MAAM,GAAC,MAAM,CAAC;AAC9H;AACA;;AAEA;AACA;AACA,IAAIG,KAAK,GAAG,CAAC,CAAC;AACd,IAAIC,MAAM,GAAG,CAAC,CAAC;AACf,IAAIC,YAAY,GAAC,CAAC,CAAC;AACnB,IAAIC,IAAI,GAAG,CAAC,CAAC;AACb,IAAIC,mBAAmB,GAAG,CAAC,CAAC;AAC5B,IAAIC,UAAU,GAAG,CAAC,CAAC;AACnB,IAAIC,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,WAAW,GAAG,CAAC,CAAC;;AAEpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,OAAO,EAAEC,OAAO,EAAE;EACrC,IAAI,CAACD,OAAO,GAAGA,OAAO;EACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;EACtB,IAAGC,KAAK,CAACC,iBAAiB,EAAED,KAAK,CAACC,iBAAiB,CAAC,IAAI,EAAEJ,UAAU,CAAC;AACtE;AACAA,UAAU,CAACK,SAAS,GAAG,IAAIF,KAAK,CAAC,CAAC;AAClCH,UAAU,CAACK,SAAS,CAACC,IAAI,GAAGN,UAAU,CAACM,IAAI;AAE3C,SAASC,SAASA,CAAA,EAAE,CAEpB;AAEAA,SAAS,CAACF,SAAS,GAAG;EACrBG,KAAK,EAAC,SAAAA,CAASnB,MAAM,EAACoB,YAAY,EAACC,SAAS,EAAC;IAC5C,IAAIC,UAAU,GAAG,IAAI,CAACA,UAAU;IAChCA,UAAU,CAACC,aAAa,CAAC,CAAC;IAC1BC,KAAK,CAACJ,YAAY,EAAEA,YAAY,GAAG,CAAC,CAAC,CAAC;IACtCD,KAAK,CAACnB,MAAM,EAACoB,YAAY,EAACC,SAAS,EACjCC,UAAU,EAAC,IAAI,CAACG,YAAY,CAAC;IAC/BH,UAAU,CAACI,WAAW,CAAC,CAAC;EACzB;AACD,CAAC;AACD,SAASP,KAAKA,CAACnB,MAAM,EAAC2B,gBAAgB,EAACN,SAAS,EAACC,UAAU,EAACG,YAAY,EAAC;EACxE,SAASG,iBAAiBA,CAACC,IAAI,EAAE;IAChC;IACA;IACA,IAAIA,IAAI,GAAG,MAAM,EAAE;MAClBA,IAAI,IAAI,OAAO;MACf,IAAIC,UAAU,GAAG,MAAM,IAAID,IAAI,IAAI,EAAE,CAAC;QACnCE,UAAU,GAAG,MAAM,IAAIF,IAAI,GAAG,KAAK,CAAC;MAEvC,OAAOG,MAAM,CAACC,YAAY,CAACH,UAAU,EAAEC,UAAU,CAAC;IACnD,CAAC,MAAM;MACN,OAAOC,MAAM,CAACC,YAAY,CAACJ,IAAI,CAAC;IACjC;EACD;EACA,SAASK,cAAcA,CAACC,CAAC,EAAC;IACzB,IAAIC,CAAC,GAAGD,CAAC,CAAClC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;IACrB,IAAIoC,MAAM,CAACC,cAAc,CAACC,IAAI,CAAClB,SAAS,EAAEe,CAAC,CAAC,EAAE;MAC7C,OAAOf,SAAS,CAACe,CAAC,CAAC;IACpB,CAAC,MAAK,IAAGA,CAAC,CAACI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAC;MAC5B,OAAOZ,iBAAiB,CAACa,QAAQ,CAACL,CAAC,CAACM,MAAM,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAC,IAAI,CAAC,CAAC,CAAC;IAClE,CAAC,MAAI;MACJlB,YAAY,CAACmB,KAAK,CAAC,mBAAmB,GAACT,CAAC,CAAC;MACzC,OAAOA,CAAC;IACT;EACD;EACA,SAASU,UAAUA,CAACC,GAAG,EAAC;IAAC;IACxB,IAAGA,GAAG,GAACC,KAAK,EAAC;MACZ,IAAIC,EAAE,GAAGhD,MAAM,CAACiD,SAAS,CAACF,KAAK,EAACD,GAAG,CAAC,CAACH,OAAO,CAAC,UAAU,EAACT,cAAc,CAAC;MACvErB,OAAO,IAAEqC,QAAQ,CAACH,KAAK,CAAC;MACxBzB,UAAU,CAAC6B,UAAU,CAACH,EAAE,EAAC,CAAC,EAACF,GAAG,GAACC,KAAK,CAAC;MACrCA,KAAK,GAAGD,GAAG;IACZ;EACD;EACA,SAASI,QAAQA,CAACE,CAAC,EAACC,CAAC,EAAC;IACrB,OAAMD,CAAC,IAAEE,OAAO,KAAKD,CAAC,GAAGE,WAAW,CAACC,IAAI,CAACxD,MAAM,CAAC,CAAC,EAAC;MAClDyD,SAAS,GAAGJ,CAAC,CAACK,KAAK;MACnBJ,OAAO,GAAGG,SAAS,GAAGJ,CAAC,CAAC,CAAC,CAAC,CAACM,MAAM;MACjC9C,OAAO,CAAC+C,UAAU,EAAE;MACpB;IACD;;IACA/C,OAAO,CAACgD,YAAY,GAAGT,CAAC,GAACK,SAAS,GAAC,CAAC;EACrC;EACA,IAAIA,SAAS,GAAG,CAAC;EACjB,IAAIH,OAAO,GAAG,CAAC;EACf,IAAIC,WAAW,GAAG,qBAAqB;EACvC,IAAI1C,OAAO,GAAGS,UAAU,CAACT,OAAO;EAEhC,IAAIiD,UAAU,GAAG,CAAC;IAACC,YAAY,EAACpC;EAAgB,CAAC,CAAC;EAClD,IAAIqC,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAIjB,KAAK,GAAG,CAAC;EACb,OAAM,IAAI,EAAC;IACV,IAAG;MACF,IAAIkB,QAAQ,GAAGjE,MAAM,CAACkE,OAAO,CAAC,GAAG,EAACnB,KAAK,CAAC;MACxC,IAAGkB,QAAQ,GAAC,CAAC,EAAC;QACb,IAAG,CAACjE,MAAM,CAAC0C,MAAM,CAACK,KAAK,CAAC,CAACoB,KAAK,CAAC,OAAO,CAAC,EAAC;UACvC,IAAIC,GAAG,GAAG9C,UAAU,CAAC8C,GAAG;UACrB,IAAIC,IAAI,GAAGD,GAAG,CAACE,cAAc,CAACtE,MAAM,CAAC0C,MAAM,CAACK,KAAK,CAAC,CAAC;UACnDqB,GAAG,CAACG,WAAW,CAACF,IAAI,CAAC;UACrB/C,UAAU,CAACkD,cAAc,GAAGH,IAAI;QACpC;QACA;MACD;MACA,IAAGJ,QAAQ,GAAClB,KAAK,EAAC;QACjBF,UAAU,CAACoB,QAAQ,CAAC;MACrB;MACA,QAAOjE,MAAM,CAACwC,MAAM,CAACyB,QAAQ,GAAC,CAAC,CAAC;QAChC,KAAK,GAAG;UACP,IAAInB,GAAG,GAAG9C,MAAM,CAACkE,OAAO,CAAC,GAAG,EAACD,QAAQ,GAAC,CAAC,CAAC;UACxC,IAAIQ,OAAO,GAAGzE,MAAM,CAACiD,SAAS,CAACgB,QAAQ,GAAG,CAAC,EAAEnB,GAAG,CAAC,CAACH,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;UAC7E,IAAI+B,MAAM,GAAGZ,UAAU,CAACa,GAAG,CAAC,CAAC;UAC7B,IAAG7B,GAAG,GAAC,CAAC,EAAC;YAEF2B,OAAO,GAAGzE,MAAM,CAACiD,SAAS,CAACgB,QAAQ,GAAC,CAAC,CAAC,CAACtB,OAAO,CAAC,SAAS,EAAC,EAAE,CAAC;YAC5DlB,YAAY,CAACmB,KAAK,CAAC,gBAAgB,GAAC6B,OAAO,GAAC,mBAAmB,GAACC,MAAM,CAACD,OAAO,CAAC;YAC/E3B,GAAG,GAAGmB,QAAQ,GAAC,CAAC,GAACQ,OAAO,CAACd,MAAM;UAChC,CAAC,MAAK,IAAGc,OAAO,CAACN,KAAK,CAAC,KAAK,CAAC,EAAC;YAC7BM,OAAO,GAAGA,OAAO,CAAC9B,OAAO,CAAC,SAAS,EAAC,EAAE,CAAC;YACvClB,YAAY,CAACmB,KAAK,CAAC,gBAAgB,GAAC6B,OAAO,GAAC,qBAAqB,CAAC;YAClE3B,GAAG,GAAGmB,QAAQ,GAAC,CAAC,GAACQ,OAAO,CAACd,MAAM;UACtC;UACA,IAAIiB,UAAU,GAAGF,MAAM,CAACE,UAAU;UAClC,IAAIC,QAAQ,GAAGH,MAAM,CAACD,OAAO,IAAIA,OAAO;UACxC,IAAIK,iBAAiB,GAAGD,QAAQ,IAAIH,MAAM,CAACD,OAAO,IAAEC,MAAM,CAACD,OAAO,CAACM,WAAW,CAAC,CAAC,IAAIN,OAAO,CAACM,WAAW,CAAC,CAAC;UACnG,IAAGD,iBAAiB,EAAC;YACpBxD,UAAU,CAAC0D,UAAU,CAACN,MAAM,CAACO,GAAG,EAACP,MAAM,CAACQ,SAAS,EAACT,OAAO,CAAC;YAChE,IAAGG,UAAU,EAAC;cACb,KAAK,IAAIO,MAAM,IAAIP,UAAU,EAAE;gBAC9B,IAAIvC,MAAM,CAACrB,SAAS,CAACsB,cAAc,CAACC,IAAI,CAACqC,UAAU,EAAEO,MAAM,CAAC,EAAE;kBAC7D7D,UAAU,CAAC8D,gBAAgB,CAACD,MAAM,CAAC;gBACpC;cACD;YACD;YACA,IAAG,CAACN,QAAQ,EAAC;cACHpD,YAAY,CAAC4D,UAAU,CAAC,gBAAgB,GAACZ,OAAO,GAAC,0CAA0C,GAACC,MAAM,CAACD,OAAQ,CAAC,CAAC,CAAC;YACxH;UACK,CAAC,MAAI;YACJX,UAAU,CAACwB,IAAI,CAACZ,MAAM,CAAC;UACxB;UAEN5B,GAAG,EAAE;UACL;QACA;QACD,KAAK,GAAG;UAAC;UACRjC,OAAO,IAAEqC,QAAQ,CAACe,QAAQ,CAAC;UAC3BnB,GAAG,GAAGyC,gBAAgB,CAACvF,MAAM,EAACiE,QAAQ,EAAC3C,UAAU,CAAC;UAClD;QACD,KAAK,GAAG;UAAC;UACRT,OAAO,IAAEqC,QAAQ,CAACe,QAAQ,CAAC;UAC3BnB,GAAG,GAAG0C,QAAQ,CAACxF,MAAM,EAACiE,QAAQ,EAAC3C,UAAU,EAACG,YAAY,CAAC;UACvD;QACD;UACCZ,OAAO,IAAEqC,QAAQ,CAACe,QAAQ,CAAC;UAC3B,IAAIwB,EAAE,GAAG,IAAIC,iBAAiB,CAAC,CAAC;UAChC,IAAI3B,YAAY,GAAGD,UAAU,CAACA,UAAU,CAACH,MAAM,GAAC,CAAC,CAAC,CAACI,YAAY;UAC/D;UACA,IAAIjB,GAAG,GAAG6C,qBAAqB,CAAC3F,MAAM,EAACiE,QAAQ,EAACwB,EAAE,EAAC1B,YAAY,EAAC7B,cAAc,EAACT,YAAY,CAAC;UAC5F,IAAImE,GAAG,GAAGH,EAAE,CAAC9B,MAAM;UAGnB,IAAG,CAAC8B,EAAE,CAACI,MAAM,IAAIC,aAAa,CAAC9F,MAAM,EAAC8C,GAAG,EAAC2C,EAAE,CAAChB,OAAO,EAACT,QAAQ,CAAC,EAAC;YAC9DyB,EAAE,CAACI,MAAM,GAAG,IAAI;YAChB,IAAG,CAACxE,SAAS,CAAC0E,IAAI,EAAC;cAClBtE,YAAY,CAACuE,OAAO,CAAC,wBAAwB,CAAC;YAC/C;UACD;UACA,IAAGnF,OAAO,IAAI+E,GAAG,EAAC;YACjB,IAAIK,QAAQ,GAAGC,WAAW,CAACrF,OAAO,EAAC,CAAC,CAAC,CAAC;YACtC;YACA,KAAI,IAAIsF,CAAC,GAAG,CAAC,EAACA,CAAC,GAACP,GAAG,EAACO,CAAC,EAAE,EAAC;cACvB,IAAIhE,CAAC,GAAGsD,EAAE,CAACU,CAAC,CAAC;cACbjD,QAAQ,CAACf,CAAC,CAACiE,MAAM,CAAC;cAClBjE,CAAC,CAACtB,OAAO,GAAGqF,WAAW,CAACrF,OAAO,EAAC,CAAC,CAAC,CAAC;YACpC;YACAS,UAAU,CAACT,OAAO,GAAGoF,QAAQ;YAC7B,IAAGI,aAAa,CAACZ,EAAE,EAACnE,UAAU,EAACyC,YAAY,CAAC,EAAC;cAC5CD,UAAU,CAACwB,IAAI,CAACG,EAAE,CAAC;YACpB;YACAnE,UAAU,CAACT,OAAO,GAAGA,OAAO;UAC7B,CAAC,MAAI;YACJ,IAAGwF,aAAa,CAACZ,EAAE,EAACnE,UAAU,EAACyC,YAAY,CAAC,EAAC;cAC5CD,UAAU,CAACwB,IAAI,CAACG,EAAE,CAAC;YACpB;UACD;UAEA,IAAI9F,SAAS,CAAC2G,MAAM,CAACb,EAAE,CAACR,GAAG,CAAC,IAAI,CAACQ,EAAE,CAACI,MAAM,EAAE;YAC3C/C,GAAG,GAAGyD,uBAAuB,CAACvG,MAAM,EAAC8C,GAAG,EAAC2C,EAAE,CAAChB,OAAO,EAACvC,cAAc,EAACZ,UAAU,CAAC;UAC/E,CAAC,MAAM;YACNwB,GAAG,EAAE;UACN;MACD;IACD,CAAC,QAAM0D,CAAC,EAAC;MACR,IAAIA,CAAC,YAAY7F,UAAU,EAAE;QAC5B,MAAM6F,CAAC;MACR;MACA/E,YAAY,CAACmB,KAAK,CAAC,uBAAuB,GAAC4D,CAAC,CAAC;MAC7C1D,GAAG,GAAG,CAAC,CAAC;IACT;IACA,IAAGA,GAAG,GAACC,KAAK,EAAC;MACZA,KAAK,GAAGD,GAAG;IACZ,CAAC,MAAI;MACJ;MACAD,UAAU,CAAC4D,IAAI,CAACC,GAAG,CAACzC,QAAQ,EAAClB,KAAK,CAAC,GAAC,CAAC,CAAC;IACvC;EACD;AACD;AACA,SAASmD,WAAWA,CAACS,CAAC,EAACC,CAAC,EAAC;EACxBA,CAAC,CAAChD,UAAU,GAAG+C,CAAC,CAAC/C,UAAU;EAC3BgD,CAAC,CAAC/C,YAAY,GAAG8C,CAAC,CAAC9C,YAAY;EAC/B,OAAO+C,CAAC;AACT;;AAEA;AACA;AACA;AACA;AACA,SAASjB,qBAAqBA,CAAC3F,MAAM,EAAC+C,KAAK,EAAC0C,EAAE,EAAC1B,YAAY,EAAC7B,cAAc,EAACT,YAAY,EAAC;EAEvF;AACD;AACA;AACA;AACA;EACC,SAASoF,YAAYA,CAACC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAE;IAC/C,IAAIvB,EAAE,CAACwB,cAAc,CAAC3E,cAAc,CAACwE,KAAK,CAAC,EAAE;MAC5CrF,YAAY,CAAC4D,UAAU,CAAC,YAAY,GAAGyB,KAAK,GAAG,YAAY,CAAC;IAC7D;IACArB,EAAE,CAACyB,QAAQ,CACVJ,KAAK;IACL;IACA;IACA;IACA;IACAC,KAAK,CAACpE,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,UAAU,EAAET,cAAc,CAAC,EACnE8E,UACD,CAAC;EACF;EACA,IAAIG,QAAQ;EACZ,IAAIJ,KAAK;EACT,IAAI3D,CAAC,GAAG,EAAEL,KAAK;EACf,IAAIqE,CAAC,GAAGjH,KAAK,CAAC;EACd,OAAM,IAAI,EAAC;IACV,IAAIkH,CAAC,GAAGrH,MAAM,CAACwC,MAAM,CAACY,CAAC,CAAC;IACxB,QAAOiE,CAAC;MACR,KAAK,GAAG;QACP,IAAGD,CAAC,KAAKhH,MAAM,EAAC;UAAC;UAChB+G,QAAQ,GAAGnH,MAAM,CAACC,KAAK,CAAC8C,KAAK,EAACK,CAAC,CAAC;UAChCgE,CAAC,GAAG9G,IAAI;QACT,CAAC,MAAK,IAAG8G,CAAC,KAAK/G,YAAY,EAAC;UAC3B+G,CAAC,GAAG9G,IAAI;QACT,CAAC,MAAI;UACJ;UACA,MAAM,IAAIQ,KAAK,CAAC,qCAAqC,CAAC,CAAC,CAAC;QACzD;;QACA;MACD,KAAK,IAAI;MACT,KAAK,GAAG;QACP,IAAGsG,CAAC,KAAK9G,IAAI,IAAI8G,CAAC,KAAKhH,MAAM,CAAC;QAAA,EAC5B;UAAC;UACF,IAAGgH,CAAC,KAAKhH,MAAM,EAAC;YACfqB,YAAY,CAACuE,OAAO,CAAC,gCAAgC,CAAC;YACtDmB,QAAQ,GAAGnH,MAAM,CAACC,KAAK,CAAC8C,KAAK,EAACK,CAAC,CAAC;UACjC;UACAL,KAAK,GAAGK,CAAC,GAAC,CAAC;UACXA,CAAC,GAAGpD,MAAM,CAACkE,OAAO,CAACmD,CAAC,EAACtE,KAAK,CAAC;UAC3B,IAAGK,CAAC,GAAC,CAAC,EAAC;YACN2D,KAAK,GAAG/G,MAAM,CAACC,KAAK,CAAC8C,KAAK,EAAEK,CAAC,CAAC;YAC9ByD,YAAY,CAACM,QAAQ,EAAEJ,KAAK,EAAEhE,KAAK,GAAC,CAAC,CAAC;YACtCqE,CAAC,GAAG5G,UAAU;UACf,CAAC,MAAI;YACJ;YACA,MAAM,IAAIM,KAAK,CAAC,2BAA2B,GAACuG,CAAC,GAAC,UAAU,CAAC;UAC1D;QACD,CAAC,MAAK,IAAGD,CAAC,IAAI7G,mBAAmB,EAAC;UACjCwG,KAAK,GAAG/G,MAAM,CAACC,KAAK,CAAC8C,KAAK,EAAEK,CAAC,CAAC;UAC9ByD,YAAY,CAACM,QAAQ,EAAEJ,KAAK,EAAEhE,KAAK,CAAC;UACpCtB,YAAY,CAACuE,OAAO,CAAC,aAAa,GAACmB,QAAQ,GAAC,sBAAsB,GAACE,CAAC,GAAC,KAAK,CAAC;UAC3EtE,KAAK,GAAGK,CAAC,GAAC,CAAC;UACXgE,CAAC,GAAG5G,UAAU;QACf,CAAC,MAAI;UACJ;UACA,MAAM,IAAIM,KAAK,CAAC,gCAAgC,CAAC,CAAC,CAAC;QACpD;;QACA;MACD,KAAK,GAAG;QACP,QAAOsG,CAAC;UACR,KAAKjH,KAAK;YACTsF,EAAE,CAAC6B,UAAU,CAACtH,MAAM,CAACC,KAAK,CAAC8C,KAAK,EAACK,CAAC,CAAC,CAAC;UACrC,KAAK5C,UAAU;UACf,KAAKC,WAAW;UAChB,KAAKC,WAAW;YACf0G,CAAC,GAAE1G,WAAW;YACd+E,EAAE,CAACI,MAAM,GAAG,IAAI;UACjB,KAAKtF,mBAAmB;UACxB,KAAKH,MAAM;YACV;UACA,KAAKC,YAAY;YAChBoF,EAAE,CAACI,MAAM,GAAG,IAAI;YACjB;UACD;UACA;YACC,MAAM,IAAI/E,KAAK,CAAC,mCAAmC,CAAC;UAAC;QACtD;;QACA;MACD,KAAK,EAAE;QAAC;QACPW,YAAY,CAACmB,KAAK,CAAC,yBAAyB,CAAC;QAC7C,IAAGwE,CAAC,IAAIjH,KAAK,EAAC;UACbsF,EAAE,CAAC6B,UAAU,CAACtH,MAAM,CAACC,KAAK,CAAC8C,KAAK,EAACK,CAAC,CAAC,CAAC;QACrC;QACA,OAAOA,CAAC;MACT,KAAK,GAAG;QACP,QAAOgE,CAAC;UACR,KAAKjH,KAAK;YACTsF,EAAE,CAAC6B,UAAU,CAACtH,MAAM,CAACC,KAAK,CAAC8C,KAAK,EAACK,CAAC,CAAC,CAAC;UACrC,KAAK5C,UAAU;UACf,KAAKC,WAAW;UAChB,KAAKC,WAAW;YACf;UAAM;UACP,KAAKH,mBAAmB,CAAC;UACzB,KAAKH,MAAM;YACV2G,KAAK,GAAG/G,MAAM,CAACC,KAAK,CAAC8C,KAAK,EAACK,CAAC,CAAC;YAC7B,IAAG2D,KAAK,CAAC9G,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAC;cAC1BwF,EAAE,CAACI,MAAM,GAAI,IAAI;cACjBkB,KAAK,GAAGA,KAAK,CAAC9G,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YAC1B;UACD,KAAKI,YAAY;YAChB,IAAG+G,CAAC,KAAK/G,YAAY,EAAC;cACrB0G,KAAK,GAAGI,QAAQ;YACjB;YACA,IAAGC,CAAC,IAAI7G,mBAAmB,EAAC;cAC3BkB,YAAY,CAACuE,OAAO,CAAC,aAAa,GAACe,KAAK,GAAC,mBAAmB,CAAC;cAC7DF,YAAY,CAACM,QAAQ,EAAEJ,KAAK,EAAEhE,KAAK,CAAC;YACrC,CAAC,MAAI;cACJ,IAAG,CAACpD,SAAS,CAAC2G,MAAM,CAACvC,YAAY,CAAC,EAAE,CAAC,CAAC,IAAI,CAACgD,KAAK,CAAC5C,KAAK,CAAC,kCAAkC,CAAC,EAAC;gBAC1F1C,YAAY,CAACuE,OAAO,CAAC,aAAa,GAACe,KAAK,GAAC,oBAAoB,GAACA,KAAK,GAAC,aAAa,CAAC;cACnF;cACAF,YAAY,CAACE,KAAK,EAAEA,KAAK,EAAEhE,KAAK,CAAC;YAClC;YACA;UACD,KAAKzC,IAAI;YACR,MAAM,IAAIQ,KAAK,CAAC,0BAA0B,CAAC;QAC5C;QACH;QACG,OAAOsC,CAAC;MACT;MACA,KAAK,QAAQ;QACZiE,CAAC,GAAG,GAAG;MACR;QACC,IAAGA,CAAC,IAAG,GAAG,EAAC;UAAC;UACX,QAAOD,CAAC;YACR,KAAKjH,KAAK;cACTsF,EAAE,CAAC6B,UAAU,CAACtH,MAAM,CAACC,KAAK,CAAC8C,KAAK,EAACK,CAAC,CAAC,CAAC,CAAC;cACrCgE,CAAC,GAAG3G,WAAW;cACf;YACD,KAAKL,MAAM;cACV+G,QAAQ,GAAGnH,MAAM,CAACC,KAAK,CAAC8C,KAAK,EAACK,CAAC,CAAC;cAChCgE,CAAC,GAAG/G,YAAY;cAChB;YACD,KAAKE,mBAAmB;cACvB,IAAIwG,KAAK,GAAG/G,MAAM,CAACC,KAAK,CAAC8C,KAAK,EAAEK,CAAC,CAAC;cAClC3B,YAAY,CAACuE,OAAO,CAAC,aAAa,GAACe,KAAK,GAAC,oBAAoB,CAAC;cAC9DF,YAAY,CAACM,QAAQ,EAAEJ,KAAK,EAAEhE,KAAK,CAAC;YACrC,KAAKvC,UAAU;cACd4G,CAAC,GAAG3G,WAAW;cACf;YACD;YACA;YACA;YACA;YACA;YACC;UACD;QACD,CAAC,MAAI;UAAC;UACT;UACA;UACI,QAAO2G,CAAC;YACR;YACA;YACA;YACA,KAAK/G,YAAY;cAChB,IAAIoE,OAAO,GAAIgB,EAAE,CAAChB,OAAO;cACzB,IAAI,CAAC9E,SAAS,CAAC2G,MAAM,CAACvC,YAAY,CAAC,EAAE,CAAC,CAAC,IAAI,CAACoD,QAAQ,CAAChD,KAAK,CAAC,kCAAkC,CAAC,EAAE;gBAC/F1C,YAAY,CAACuE,OAAO,CAAC,aAAa,GAACmB,QAAQ,GAAC,oBAAoB,GAACA,QAAQ,GAAC,cAAc,CAAC;cAC1F;cACAN,YAAY,CAACM,QAAQ,EAAEA,QAAQ,EAAEpE,KAAK,CAAC;cACvCA,KAAK,GAAGK,CAAC;cACTgE,CAAC,GAAGhH,MAAM;cACV;YACD,KAAKI,UAAU;cACdiB,YAAY,CAACuE,OAAO,CAAC,8BAA8B,GAACmB,QAAQ,GAAC,KAAK,CAAC;YACpE,KAAK1G,WAAW;cACf2G,CAAC,GAAGhH,MAAM;cACV2C,KAAK,GAAGK,CAAC;cACT;YACD,KAAK9C,IAAI;cACR8G,CAAC,GAAG7G,mBAAmB;cACvBwC,KAAK,GAAGK,CAAC;cACT;YACD,KAAK1C,WAAW;cACf,MAAM,IAAII,KAAK,CAAC,4DAA4D,CAAC;UAC9E;QACD;IACD,CAAC;IACD;IACAsC,CAAC,EAAE;EACJ;AACD;AACA;AACA;AACA;AACA,SAASiD,aAAaA,CAACZ,EAAE,EAACnE,UAAU,EAACyC,YAAY,EAAC;EACjD,IAAIU,OAAO,GAAGgB,EAAE,CAAChB,OAAO;EACxB,IAAIG,UAAU,GAAG,IAAI;EACrB;EACA,IAAIuB,CAAC,GAAGV,EAAE,CAAC9B,MAAM;EACjB,OAAMwC,CAAC,EAAE,EAAC;IACT,IAAIhE,CAAC,GAAGsD,EAAE,CAACU,CAAC,CAAC;IACb,IAAIoB,KAAK,GAAGpF,CAAC,CAACoF,KAAK;IACnB,IAAIR,KAAK,GAAG5E,CAAC,CAAC4E,KAAK;IACnB,IAAIS,GAAG,GAAGD,KAAK,CAACrD,OAAO,CAAC,GAAG,CAAC;IAC5B,IAAGsD,GAAG,GAAC,CAAC,EAAC;MACR,IAAIrC,MAAM,GAAGhD,CAAC,CAACgD,MAAM,GAAGoC,KAAK,CAACtH,KAAK,CAAC,CAAC,EAACuH,GAAG,CAAC;MAC1C,IAAItC,SAAS,GAAGqC,KAAK,CAACtH,KAAK,CAACuH,GAAG,GAAC,CAAC,CAAC;MAClC,IAAIC,QAAQ,GAAGtC,MAAM,KAAK,OAAO,IAAID,SAAS;IAC/C,CAAC,MAAI;MACJA,SAAS,GAAGqC,KAAK;MACjBpC,MAAM,GAAG,IAAI;MACbsC,QAAQ,GAAGF,KAAK,KAAK,OAAO,IAAI,EAAE;IACnC;IACA;IACApF,CAAC,CAAC+C,SAAS,GAAGA,SAAS;IACvB;IACA,IAAGuC,QAAQ,KAAK,KAAK,EAAC;MAAC;MACtB,IAAG7C,UAAU,IAAI,IAAI,EAAC;QACrBA,UAAU,GAAG,CAAC,CAAC;QACf;QACApD,KAAK,CAACuC,YAAY,EAACA,YAAY,GAAC,CAAC,CAAC,CAAC;QACnC;MACD;;MACAA,YAAY,CAAC0D,QAAQ,CAAC,GAAG7C,UAAU,CAAC6C,QAAQ,CAAC,GAAGV,KAAK;MACrD5E,CAAC,CAAC8C,GAAG,GAAGtF,SAAS,CAAC+H,KAAK;MACvBpG,UAAU,CAACqG,kBAAkB,CAACF,QAAQ,EAAEV,KAAK,CAAC;IAC/C;EACD;EACA,IAAIZ,CAAC,GAAGV,EAAE,CAAC9B,MAAM;EACjB,OAAMwC,CAAC,EAAE,EAAC;IACThE,CAAC,GAAGsD,EAAE,CAACU,CAAC,CAAC;IACT,IAAIhB,MAAM,GAAGhD,CAAC,CAACgD,MAAM;IACrB,IAAGA,MAAM,EAAC;MAAC;MACV,IAAGA,MAAM,KAAK,KAAK,EAAC;QACnBhD,CAAC,CAAC8C,GAAG,GAAGtF,SAAS,CAACiI,GAAG;MACtB;MAAC,IAAGzC,MAAM,KAAK,OAAO,EAAC;QACtBhD,CAAC,CAAC8C,GAAG,GAAGlB,YAAY,CAACoB,MAAM,IAAI,EAAE,CAAC;;QAElC;MACD;IACD;EACD;;EACA,IAAIqC,GAAG,GAAG/C,OAAO,CAACP,OAAO,CAAC,GAAG,CAAC;EAC9B,IAAGsD,GAAG,GAAC,CAAC,EAAC;IACRrC,MAAM,GAAGM,EAAE,CAACN,MAAM,GAAGV,OAAO,CAACxE,KAAK,CAAC,CAAC,EAACuH,GAAG,CAAC;IACzCtC,SAAS,GAAGO,EAAE,CAACP,SAAS,GAAGT,OAAO,CAACxE,KAAK,CAACuH,GAAG,GAAC,CAAC,CAAC;EAChD,CAAC,MAAI;IACJrC,MAAM,GAAG,IAAI,CAAC;IACdD,SAAS,GAAGO,EAAE,CAACP,SAAS,GAAGT,OAAO;EACnC;EACA;EACA,IAAIoD,EAAE,GAAGpC,EAAE,CAACR,GAAG,GAAGlB,YAAY,CAACoB,MAAM,IAAI,EAAE,CAAC;EAC5C7D,UAAU,CAACwG,YAAY,CAACD,EAAE,EAAC3C,SAAS,EAACT,OAAO,EAACgB,EAAE,CAAC;EAChD;EACA;EACA,IAAGA,EAAE,CAACI,MAAM,EAAC;IACZvE,UAAU,CAAC0D,UAAU,CAAC6C,EAAE,EAAC3C,SAAS,EAACT,OAAO,CAAC;IAC3C,IAAGG,UAAU,EAAC;MACb,KAAKO,MAAM,IAAIP,UAAU,EAAE;QAC1B,IAAIvC,MAAM,CAACrB,SAAS,CAACsB,cAAc,CAACC,IAAI,CAACqC,UAAU,EAAEO,MAAM,CAAC,EAAE;UAC7D7D,UAAU,CAAC8D,gBAAgB,CAACD,MAAM,CAAC;QACpC;MACD;IACD;EACD,CAAC,MAAI;IACJM,EAAE,CAAC1B,YAAY,GAAGA,YAAY;IAC9B0B,EAAE,CAACb,UAAU,GAAGA,UAAU;IAC1B;IACA,OAAO,IAAI;EACZ;AACD;AACA,SAAS2B,uBAAuBA,CAACvG,MAAM,EAAC+H,UAAU,EAACtD,OAAO,EAACvC,cAAc,EAACZ,UAAU,EAAC;EACpF,IAAG,wBAAwB,CAAC0G,IAAI,CAACvD,OAAO,CAAC,EAAC;IACzC,IAAIwD,UAAU,GAAIjI,MAAM,CAACkE,OAAO,CAAC,IAAI,GAACO,OAAO,GAAC,GAAG,EAACsD,UAAU,CAAC;IAC7D,IAAI1D,IAAI,GAAGrE,MAAM,CAACiD,SAAS,CAAC8E,UAAU,GAAC,CAAC,EAACE,UAAU,CAAC;IACpD,IAAG,MAAM,CAACD,IAAI,CAAC3D,IAAI,CAAC,EAAC;MACpB,IAAG,WAAW,CAAC2D,IAAI,CAACvD,OAAO,CAAC,EAAC;QAC5B;QACC;QACAnD,UAAU,CAAC6B,UAAU,CAACkB,IAAI,EAAC,CAAC,EAACA,IAAI,CAACV,MAAM,CAAC;QACzC;QACA,OAAOsE,UAAU;QAClB;MACD,CAAC;MACA5D,IAAI,GAAGA,IAAI,CAAC1B,OAAO,CAAC,UAAU,EAACT,cAAc,CAAC;MAC9CZ,UAAU,CAAC6B,UAAU,CAACkB,IAAI,EAAC,CAAC,EAACA,IAAI,CAACV,MAAM,CAAC;MACzC,OAAOsE,UAAU;MAClB;IAED;EACD;;EACA,OAAOF,UAAU,GAAC,CAAC;AACpB;AACA,SAASjC,aAAaA,CAAC9F,MAAM,EAAC+H,UAAU,EAACtD,OAAO,EAACT,QAAQ,EAAC;EACzD;EACA,IAAIkE,GAAG,GAAGlE,QAAQ,CAACS,OAAO,CAAC;EAC3B,IAAGyD,GAAG,IAAI,IAAI,EAAC;IACd;IACAA,GAAG,GAAIlI,MAAM,CAACmI,WAAW,CAAC,IAAI,GAAC1D,OAAO,GAAC,GAAG,CAAC;IAC3C,IAAGyD,GAAG,GAACH,UAAU,EAAC;MAAC;MAClBG,GAAG,GAAGlI,MAAM,CAACmI,WAAW,CAAC,IAAI,GAAC1D,OAAO,CAAC;IACvC;IACAT,QAAQ,CAACS,OAAO,CAAC,GAAEyD,GAAG;EACvB;EACA,OAAOA,GAAG,GAACH,UAAU;EACrB;AACD;;AAEA,SAASvG,KAAKA,CAAExB,MAAM,EAAEoI,MAAM,EAAE;EAC/B,KAAK,IAAIC,CAAC,IAAIrI,MAAM,EAAE;IACrB,IAAIqC,MAAM,CAACrB,SAAS,CAACsB,cAAc,CAACC,IAAI,CAACvC,MAAM,EAAEqI,CAAC,CAAC,EAAE;MACpDD,MAAM,CAACC,CAAC,CAAC,GAAGrI,MAAM,CAACqI,CAAC,CAAC;IACtB;EACD;AACD;AAEA,SAAS7C,QAAQA,CAACxF,MAAM,EAAC+C,KAAK,EAACzB,UAAU,EAACG,YAAY,EAAC;EAAC;EACvD,IAAI6G,IAAI,GAAEtI,MAAM,CAACwC,MAAM,CAACO,KAAK,GAAC,CAAC,CAAC;EAChC,QAAOuF,IAAI;IACX,KAAK,GAAG;MACP,IAAGtI,MAAM,CAACwC,MAAM,CAACO,KAAK,GAAG,CAAC,CAAC,KAAK,GAAG,EAAC;QACnC,IAAID,GAAG,GAAG9C,MAAM,CAACkE,OAAO,CAAC,KAAK,EAACnB,KAAK,GAAC,CAAC,CAAC;QACvC;QACA,IAAGD,GAAG,GAACC,KAAK,EAAC;UACZzB,UAAU,CAACiH,OAAO,CAACvI,MAAM,EAAC+C,KAAK,GAAC,CAAC,EAACD,GAAG,GAACC,KAAK,GAAC,CAAC,CAAC;UAC9C,OAAOD,GAAG,GAAC,CAAC;QACb,CAAC,MAAI;UACJrB,YAAY,CAACmB,KAAK,CAAC,kBAAkB,CAAC;UACtC,OAAO,CAAC,CAAC;QACV;MACD,CAAC,MAAI;QACJ;QACA,OAAO,CAAC,CAAC;MACV;IACD;MACC,IAAG5C,MAAM,CAAC0C,MAAM,CAACK,KAAK,GAAC,CAAC,EAAC,CAAC,CAAC,IAAI,QAAQ,EAAC;QACvC,IAAID,GAAG,GAAG9C,MAAM,CAACkE,OAAO,CAAC,KAAK,EAACnB,KAAK,GAAC,CAAC,CAAC;QACvCzB,UAAU,CAACkH,UAAU,CAAC,CAAC;QACvBlH,UAAU,CAAC6B,UAAU,CAACnD,MAAM,EAAC+C,KAAK,GAAC,CAAC,EAACD,GAAG,GAACC,KAAK,GAAC,CAAC,CAAC;QACjDzB,UAAU,CAACmH,QAAQ,CAAC,CAAC;QACrB,OAAO3F,GAAG,GAAC,CAAC;MACb;MACA;MACA;MACA,IAAI4F,MAAM,GAAGC,KAAK,CAAC3I,MAAM,EAAC+C,KAAK,CAAC;MAChC,IAAI6C,GAAG,GAAG8C,MAAM,CAAC/E,MAAM;MACvB,IAAGiC,GAAG,GAAC,CAAC,IAAI,WAAW,CAACoC,IAAI,CAACU,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC;QAC1C,IAAIzH,IAAI,GAAGyH,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,IAAIE,KAAK,GAAG,KAAK;QACjB,IAAIC,KAAK,GAAG,KAAK;QACjB,IAAGjD,GAAG,GAAC,CAAC,EAAC;UACR,IAAG,WAAW,CAACoC,IAAI,CAACU,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC;YACjCE,KAAK,GAAGF,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpBG,KAAK,GAAGjD,GAAG,GAAC,CAAC,IAAI8C,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,MAAK,IAAG,WAAW,CAACV,IAAI,CAACU,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC;YACvCG,KAAK,GAAGH,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB;QACD;QACA,IAAII,SAAS,GAAGJ,MAAM,CAAC9C,GAAG,GAAC,CAAC,CAAC;QAC7BtE,UAAU,CAACyH,QAAQ,CAAC9H,IAAI,EAAE2H,KAAK,EAAEC,KAAK,CAAC;QACvCvH,UAAU,CAAC0H,MAAM,CAAC,CAAC;QAEnB,OAAOF,SAAS,CAACpF,KAAK,GAACoF,SAAS,CAAC,CAAC,CAAC,CAACnF,MAAM;MAC3C;EACD;EACA,OAAO,CAAC,CAAC;AACV;AAIA,SAAS4B,gBAAgBA,CAACvF,MAAM,EAAC+C,KAAK,EAACzB,UAAU,EAAC;EACjD,IAAIwB,GAAG,GAAG9C,MAAM,CAACkE,OAAO,CAAC,IAAI,EAACnB,KAAK,CAAC;EACpC,IAAGD,GAAG,EAAC;IACN,IAAIqB,KAAK,GAAGnE,MAAM,CAACiD,SAAS,CAACF,KAAK,EAACD,GAAG,CAAC,CAACqB,KAAK,CAAC,4BAA4B,CAAC;IAC3E,IAAGA,KAAK,EAAC;MACR,IAAIyB,GAAG,GAAGzB,KAAK,CAAC,CAAC,CAAC,CAACR,MAAM;MACzBrC,UAAU,CAAC2H,qBAAqB,CAAC9E,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;MACpD,OAAOrB,GAAG,GAAC,CAAC;IACb,CAAC,MAAI;MAAC;MACL,OAAO,CAAC,CAAC;IACV;EACD;EACA,OAAO,CAAC,CAAC;AACV;AAEA,SAAS4C,iBAAiBA,CAAA,EAAE;EAC3B,IAAI,CAACuB,cAAc,GAAG,CAAC,CAAC;AACzB;AACAvB,iBAAiB,CAAC1E,SAAS,GAAG;EAC7BsG,UAAU,EAAC,SAAAA,CAAS7C,OAAO,EAAC;IAC3B,IAAG,CAACvE,cAAc,CAAC8H,IAAI,CAACvD,OAAO,CAAC,EAAC;MAChC,MAAM,IAAI3D,KAAK,CAAC,kBAAkB,GAAC2D,OAAO,CAAC;IAC5C;IACA,IAAI,CAACA,OAAO,GAAGA,OAAO;EACvB,CAAC;EACDyC,QAAQ,EAAC,SAAAA,CAASK,KAAK,EAAER,KAAK,EAAEX,MAAM,EAAE;IACvC,IAAG,CAAClG,cAAc,CAAC8H,IAAI,CAACT,KAAK,CAAC,EAAC;MAC9B,MAAM,IAAIzG,KAAK,CAAC,oBAAoB,GAACyG,KAAK,CAAC;IAC5C;IACA,IAAI,CAACN,cAAc,CAACM,KAAK,CAAC,GAAG,IAAI,CAAC5D,MAAM;IACxC,IAAI,CAAC,IAAI,CAACA,MAAM,EAAE,CAAC,GAAG;MAAC4D,KAAK,EAACA,KAAK;MAACR,KAAK,EAACA,KAAK;MAACX,MAAM,EAACA;IAAM,CAAC;EAC9D,CAAC;EACDzC,MAAM,EAAC,CAAC;EACRuF,YAAY,EAAC,SAAAA,CAAS/C,CAAC,EAAC;IAAC,OAAO,IAAI,CAACA,CAAC,CAAC,CAACjB,SAAS;EAAA,CAAC;EAClDiE,UAAU,EAAC,SAAAA,CAAShD,CAAC,EAAC;IAAC,OAAO,IAAI,CAACA,CAAC,CAAC,CAACtF,OAAO;EAAA,CAAC;EAC9CuI,QAAQ,EAAC,SAAAA,CAASjD,CAAC,EAAC;IAAC,OAAO,IAAI,CAACA,CAAC,CAAC,CAACoB,KAAK;EAAA,CAAC;EAC1C8B,MAAM,EAAC,SAAAA,CAASlD,CAAC,EAAC;IAAC,OAAO,IAAI,CAACA,CAAC,CAAC,CAAClB,GAAG;EAAA,CAAC;EACtCqE,QAAQ,EAAC,SAAAA,CAASnD,CAAC,EAAC;IAAC,OAAO,IAAI,CAACA,CAAC,CAAC,CAACY,KAAK;EAAA;EAC1C;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA,CAAC;;AAID,SAAS4B,KAAKA,CAAC3I,MAAM,EAAC+C,KAAK,EAAC;EAC3B,IAAIoB,KAAK;EACT,IAAIoF,GAAG,GAAG,EAAE;EACZ,IAAIC,GAAG,GAAG,4CAA4C;EACtDA,GAAG,CAACC,SAAS,GAAG1G,KAAK;EACrByG,GAAG,CAAChG,IAAI,CAACxD,MAAM,CAAC,CAAC;EACjB,OAAMmE,KAAK,GAAGqF,GAAG,CAAChG,IAAI,CAACxD,MAAM,CAAC,EAAC;IAC9BuJ,GAAG,CAACjE,IAAI,CAACnB,KAAK,CAAC;IACf,IAAGA,KAAK,CAAC,CAAC,CAAC,EAAC,OAAOoF,GAAG;EACvB;AACD;AAEAG,OAAO,CAACxI,SAAS,GAAGA,SAAS;AAC7BwI,OAAO,CAAC/I,UAAU,GAAGA,UAAU"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}