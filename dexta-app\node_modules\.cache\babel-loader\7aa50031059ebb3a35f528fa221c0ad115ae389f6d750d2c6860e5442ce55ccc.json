{"ast": null, "code": "import { shallowEqual } from '@react-dnd/shallowequal';\nimport { isRef } from './isRef.js';\nimport { wrapConnectorHooks } from './wrapConnectorHooks.js';\nexport class SourceConnector {\n  receiveHandlerId(newHandlerId) {\n    if (this.handlerId === newHandlerId) {\n      return;\n    }\n    this.handlerId = newHandlerId;\n    this.reconnect();\n  }\n  get connectTarget() {\n    return this.dragSource;\n  }\n  get dragSourceOptions() {\n    return this.dragSourceOptionsInternal;\n  }\n  set dragSourceOptions(options) {\n    this.dragSourceOptionsInternal = options;\n  }\n  get dragPreviewOptions() {\n    return this.dragPreviewOptionsInternal;\n  }\n  set dragPreviewOptions(options) {\n    this.dragPreviewOptionsInternal = options;\n  }\n  reconnect() {\n    const didChange = this.reconnectDragSource();\n    this.reconnectDragPreview(didChange);\n  }\n  reconnectDragSource() {\n    const dragSource = this.dragSource;\n    // if nothing has changed then don't resubscribe\n    const didChange = this.didHandlerIdChange() || this.didConnectedDragSourceChange() || this.didDragSourceOptionsChange();\n    if (didChange) {\n      this.disconnectDragSource();\n    }\n    if (!this.handlerId) {\n      return didChange;\n    }\n    if (!dragSource) {\n      this.lastConnectedDragSource = dragSource;\n      return didChange;\n    }\n    if (didChange) {\n      this.lastConnectedHandlerId = this.handlerId;\n      this.lastConnectedDragSource = dragSource;\n      this.lastConnectedDragSourceOptions = this.dragSourceOptions;\n      this.dragSourceUnsubscribe = this.backend.connectDragSource(this.handlerId, dragSource, this.dragSourceOptions);\n    }\n    return didChange;\n  }\n  reconnectDragPreview(forceDidChange = false) {\n    const dragPreview = this.dragPreview;\n    // if nothing has changed then don't resubscribe\n    const didChange = forceDidChange || this.didHandlerIdChange() || this.didConnectedDragPreviewChange() || this.didDragPreviewOptionsChange();\n    if (didChange) {\n      this.disconnectDragPreview();\n    }\n    if (!this.handlerId) {\n      return;\n    }\n    if (!dragPreview) {\n      this.lastConnectedDragPreview = dragPreview;\n      return;\n    }\n    if (didChange) {\n      this.lastConnectedHandlerId = this.handlerId;\n      this.lastConnectedDragPreview = dragPreview;\n      this.lastConnectedDragPreviewOptions = this.dragPreviewOptions;\n      this.dragPreviewUnsubscribe = this.backend.connectDragPreview(this.handlerId, dragPreview, this.dragPreviewOptions);\n    }\n  }\n  didHandlerIdChange() {\n    return this.lastConnectedHandlerId !== this.handlerId;\n  }\n  didConnectedDragSourceChange() {\n    return this.lastConnectedDragSource !== this.dragSource;\n  }\n  didConnectedDragPreviewChange() {\n    return this.lastConnectedDragPreview !== this.dragPreview;\n  }\n  didDragSourceOptionsChange() {\n    return !shallowEqual(this.lastConnectedDragSourceOptions, this.dragSourceOptions);\n  }\n  didDragPreviewOptionsChange() {\n    return !shallowEqual(this.lastConnectedDragPreviewOptions, this.dragPreviewOptions);\n  }\n  disconnectDragSource() {\n    if (this.dragSourceUnsubscribe) {\n      this.dragSourceUnsubscribe();\n      this.dragSourceUnsubscribe = undefined;\n    }\n  }\n  disconnectDragPreview() {\n    if (this.dragPreviewUnsubscribe) {\n      this.dragPreviewUnsubscribe();\n      this.dragPreviewUnsubscribe = undefined;\n      this.dragPreviewNode = null;\n      this.dragPreviewRef = null;\n    }\n  }\n  get dragSource() {\n    return this.dragSourceNode || this.dragSourceRef && this.dragSourceRef.current;\n  }\n  get dragPreview() {\n    return this.dragPreviewNode || this.dragPreviewRef && this.dragPreviewRef.current;\n  }\n  clearDragSource() {\n    this.dragSourceNode = null;\n    this.dragSourceRef = null;\n  }\n  clearDragPreview() {\n    this.dragPreviewNode = null;\n    this.dragPreviewRef = null;\n  }\n  constructor(backend) {\n    this.hooks = wrapConnectorHooks({\n      dragSource: (node, options) => {\n        this.clearDragSource();\n        this.dragSourceOptions = options || null;\n        if (isRef(node)) {\n          this.dragSourceRef = node;\n        } else {\n          this.dragSourceNode = node;\n        }\n        this.reconnectDragSource();\n      },\n      dragPreview: (node, options) => {\n        this.clearDragPreview();\n        this.dragPreviewOptions = options || null;\n        if (isRef(node)) {\n          this.dragPreviewRef = node;\n        } else {\n          this.dragPreviewNode = node;\n        }\n        this.reconnectDragPreview();\n      }\n    });\n    this.handlerId = null;\n    // The drop target may either be attached via ref or connect function\n    this.dragSourceRef = null;\n    this.dragSourceOptionsInternal = null;\n    // The drag preview may either be attached via ref or connect function\n    this.dragPreviewRef = null;\n    this.dragPreviewOptionsInternal = null;\n    this.lastConnectedHandlerId = null;\n    this.lastConnectedDragSource = null;\n    this.lastConnectedDragSourceOptions = null;\n    this.lastConnectedDragPreview = null;\n    this.lastConnectedDragPreviewOptions = null;\n    this.backend = backend;\n  }\n}", "map": {"version": 3, "names": ["shallowEqual", "isRef", "wrapConnectorHooks", "SourceConnector", "receiveHandlerId", "newHandlerId", "handlerId", "reconnect", "connectTarget", "dragSource", "dragSourceOptions", "dragSourceOptionsInternal", "options", "dragPreviewOptions", "dragPreviewOptionsInternal", "<PERSON><PERSON><PERSON><PERSON>", "reconnectDragSource", "reconnectDragPreview", "didHandlerIdChange", "didConnectedDragSourceChange", "didDragSourceOptionsChange", "disconnectDragSource", "lastConnectedDragSource", "lastConnectedHandlerId", "lastConnectedDragSourceOptions", "dragSourceUnsubscribe", "backend", "connectDragSource", "forceDidChange", "dragPreview", "didConnectedDragPreviewChange", "didDragPreviewOptionsChange", "disconnectDragPreview", "lastConnectedDragPreview", "lastConnectedDragPreviewOptions", "dragPreviewUnsubscribe", "connectDragPreview", "undefined", "dragPreviewNode", "dragPreviewRef", "dragSourceNode", "dragSourceRef", "current", "clearDragSource", "clearDragPreview", "constructor", "hooks", "node"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\react-dnd\\src\\internals\\SourceConnector.ts"], "sourcesContent": ["import { shallowEqual } from '@react-dnd/shallowequal'\nimport type { Backend, Identifier, Unsubscribe } from 'dnd-core'\nimport type { ReactElement, Ref, RefObject } from 'react'\n\nimport type { DragPreviewOptions, DragSourceOptions } from '../types/index.js'\nimport { isRef } from './isRef.js'\nimport { wrapConnectorHooks } from './wrapConnectorHooks.js'\n\nexport interface Connector {\n\thooks: any\n\tconnectTarget: any\n\treceiveHandlerId(handlerId: Identifier | null): void\n\treconnect(): void\n}\n\nexport class SourceConnector implements Connector {\n\tpublic hooks = wrapConnectorHooks({\n\t\tdragSource: (\n\t\t\tnode: Element | ReactElement | Ref<any>,\n\t\t\toptions?: DragSourceOptions,\n\t\t) => {\n\t\t\tthis.clearDragSource()\n\t\t\tthis.dragSourceOptions = options || null\n\t\t\tif (isRef(node)) {\n\t\t\t\tthis.dragSourceRef = node as RefObject<any>\n\t\t\t} else {\n\t\t\t\tthis.dragSourceNode = node\n\t\t\t}\n\t\t\tthis.reconnectDragSource()\n\t\t},\n\t\tdragPreview: (node: any, options?: DragPreviewOptions) => {\n\t\t\tthis.clearDragPreview()\n\t\t\tthis.dragPreviewOptions = options || null\n\t\t\tif (isRef(node)) {\n\t\t\t\tthis.dragPreviewRef = node\n\t\t\t} else {\n\t\t\t\tthis.dragPreviewNode = node\n\t\t\t}\n\t\t\tthis.reconnectDragPreview()\n\t\t},\n\t})\n\tprivate handlerId: Identifier | null = null\n\n\t// The drop target may either be attached via ref or connect function\n\tprivate dragSourceRef: RefObject<any> | null = null\n\tprivate dragSourceNode: any\n\tprivate dragSourceOptionsInternal: DragSourceOptions | null = null\n\tprivate dragSourceUnsubscribe: Unsubscribe | undefined\n\n\t// The drag preview may either be attached via ref or connect function\n\tprivate dragPreviewRef: RefObject<any> | null = null\n\tprivate dragPreviewNode: any\n\tprivate dragPreviewOptionsInternal: DragPreviewOptions | null = null\n\tprivate dragPreviewUnsubscribe: Unsubscribe | undefined\n\n\tprivate lastConnectedHandlerId: Identifier | null = null\n\tprivate lastConnectedDragSource: any = null\n\tprivate lastConnectedDragSourceOptions: any = null\n\tprivate lastConnectedDragPreview: any = null\n\tprivate lastConnectedDragPreviewOptions: any = null\n\n\tprivate readonly backend: Backend\n\n\tpublic constructor(backend: Backend) {\n\t\tthis.backend = backend\n\t}\n\n\tpublic receiveHandlerId(newHandlerId: Identifier | null): void {\n\t\tif (this.handlerId === newHandlerId) {\n\t\t\treturn\n\t\t}\n\n\t\tthis.handlerId = newHandlerId\n\t\tthis.reconnect()\n\t}\n\n\tpublic get connectTarget(): any {\n\t\treturn this.dragSource\n\t}\n\n\tpublic get dragSourceOptions(): DragSourceOptions | null {\n\t\treturn this.dragSourceOptionsInternal\n\t}\n\tpublic set dragSourceOptions(options: DragSourceOptions | null) {\n\t\tthis.dragSourceOptionsInternal = options\n\t}\n\n\tpublic get dragPreviewOptions(): DragPreviewOptions | null {\n\t\treturn this.dragPreviewOptionsInternal\n\t}\n\n\tpublic set dragPreviewOptions(options: DragPreviewOptions | null) {\n\t\tthis.dragPreviewOptionsInternal = options\n\t}\n\n\tpublic reconnect(): void {\n\t\tconst didChange = this.reconnectDragSource()\n\t\tthis.reconnectDragPreview(didChange)\n\t}\n\n\tprivate reconnectDragSource(): boolean {\n\t\tconst dragSource = this.dragSource\n\t\t// if nothing has changed then don't resubscribe\n\t\tconst didChange =\n\t\t\tthis.didHandlerIdChange() ||\n\t\t\tthis.didConnectedDragSourceChange() ||\n\t\t\tthis.didDragSourceOptionsChange()\n\n\t\tif (didChange) {\n\t\t\tthis.disconnectDragSource()\n\t\t}\n\n\t\tif (!this.handlerId) {\n\t\t\treturn didChange\n\t\t}\n\t\tif (!dragSource) {\n\t\t\tthis.lastConnectedDragSource = dragSource\n\t\t\treturn didChange\n\t\t}\n\n\t\tif (didChange) {\n\t\t\tthis.lastConnectedHandlerId = this.handlerId\n\t\t\tthis.lastConnectedDragSource = dragSource\n\t\t\tthis.lastConnectedDragSourceOptions = this.dragSourceOptions\n\t\t\tthis.dragSourceUnsubscribe = this.backend.connectDragSource(\n\t\t\t\tthis.handlerId,\n\t\t\t\tdragSource,\n\t\t\t\tthis.dragSourceOptions,\n\t\t\t)\n\t\t}\n\t\treturn didChange\n\t}\n\n\tprivate reconnectDragPreview(forceDidChange = false): void {\n\t\tconst dragPreview = this.dragPreview\n\t\t// if nothing has changed then don't resubscribe\n\t\tconst didChange =\n\t\t\tforceDidChange ||\n\t\t\tthis.didHandlerIdChange() ||\n\t\t\tthis.didConnectedDragPreviewChange() ||\n\t\t\tthis.didDragPreviewOptionsChange()\n\n\t\tif (didChange) {\n\t\t\tthis.disconnectDragPreview()\n\t\t}\n\n\t\tif (!this.handlerId) {\n\t\t\treturn\n\t\t}\n\t\tif (!dragPreview) {\n\t\t\tthis.lastConnectedDragPreview = dragPreview\n\t\t\treturn\n\t\t}\n\n\t\tif (didChange) {\n\t\t\tthis.lastConnectedHandlerId = this.handlerId\n\t\t\tthis.lastConnectedDragPreview = dragPreview\n\t\t\tthis.lastConnectedDragPreviewOptions = this.dragPreviewOptions\n\t\t\tthis.dragPreviewUnsubscribe = this.backend.connectDragPreview(\n\t\t\t\tthis.handlerId,\n\t\t\t\tdragPreview,\n\t\t\t\tthis.dragPreviewOptions,\n\t\t\t)\n\t\t}\n\t}\n\n\tprivate didHandlerIdChange(): boolean {\n\t\treturn this.lastConnectedHandlerId !== this.handlerId\n\t}\n\n\tprivate didConnectedDragSourceChange(): boolean {\n\t\treturn this.lastConnectedDragSource !== this.dragSource\n\t}\n\n\tprivate didConnectedDragPreviewChange(): boolean {\n\t\treturn this.lastConnectedDragPreview !== this.dragPreview\n\t}\n\n\tprivate didDragSourceOptionsChange(): boolean {\n\t\treturn !shallowEqual(\n\t\t\tthis.lastConnectedDragSourceOptions,\n\t\t\tthis.dragSourceOptions,\n\t\t)\n\t}\n\n\tprivate didDragPreviewOptionsChange(): boolean {\n\t\treturn !shallowEqual(\n\t\t\tthis.lastConnectedDragPreviewOptions,\n\t\t\tthis.dragPreviewOptions,\n\t\t)\n\t}\n\n\tpublic disconnectDragSource() {\n\t\tif (this.dragSourceUnsubscribe) {\n\t\t\tthis.dragSourceUnsubscribe()\n\t\t\tthis.dragSourceUnsubscribe = undefined\n\t\t}\n\t}\n\n\tpublic disconnectDragPreview() {\n\t\tif (this.dragPreviewUnsubscribe) {\n\t\t\tthis.dragPreviewUnsubscribe()\n\t\t\tthis.dragPreviewUnsubscribe = undefined\n\t\t\tthis.dragPreviewNode = null\n\t\t\tthis.dragPreviewRef = null\n\t\t}\n\t}\n\n\tprivate get dragSource() {\n\t\treturn (\n\t\t\tthis.dragSourceNode || (this.dragSourceRef && this.dragSourceRef.current)\n\t\t)\n\t}\n\n\tprivate get dragPreview() {\n\t\treturn (\n\t\t\tthis.dragPreviewNode ||\n\t\t\t(this.dragPreviewRef && this.dragPreviewRef.current)\n\t\t)\n\t}\n\n\tprivate clearDragSource() {\n\t\tthis.dragSourceNode = null\n\t\tthis.dragSourceRef = null\n\t}\n\n\tprivate clearDragPreview() {\n\t\tthis.dragPreviewNode = null\n\t\tthis.dragPreviewRef = null\n\t}\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,yBAAyB;AAKtD,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,kBAAkB,QAAQ,yBAAyB;AAS5D,OAAO,MAAMC,eAAe;EAoD3BC,gBAAuBA,CAACC,YAA+B,EAAQ;IAC9D,IAAI,IAAI,CAACC,SAAS,KAAKD,YAAY,EAAE;MACpC;;IAGD,IAAI,CAACC,SAAS,GAAGD,YAAY;IAC7B,IAAI,CAACE,SAAS,EAAE;;EAGjB,IAAWC,aAAaA,CAAA,EAAQ;IAC/B,OAAO,IAAI,CAACC,UAAU;;EAGvB,IAAWC,iBAAiBA,CAAA,EAA6B;IACxD,OAAO,IAAI,CAACC,yBAAyB;;EAEtC,IAAWD,iBAAiBA,CAACE,OAAiC,EAAE;IAC/D,IAAI,CAACD,yBAAyB,GAAGC,OAAO;;EAGzC,IAAWC,kBAAkBA,CAAA,EAA8B;IAC1D,OAAO,IAAI,CAACC,0BAA0B;;EAGvC,IAAWD,kBAAkBA,CAACD,OAAkC,EAAE;IACjE,IAAI,CAACE,0BAA0B,GAAGF,OAAO;;EAG1CL,SAAgBA,CAAA,EAAS;IACxB,MAAMQ,SAAS,GAAG,IAAI,CAACC,mBAAmB,EAAE;IAC5C,IAAI,CAACC,oBAAoB,CAACF,SAAS,CAAC;;EAGrCC,mBAA2BA,CAAA,EAAY;IACtC,MAAMP,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC;IACA,MAAMM,SAAS,GACd,IAAI,CAACG,kBAAkB,EAAE,IACzB,IAAI,CAACC,4BAA4B,EAAE,IACnC,IAAI,CAACC,0BAA0B,EAAE;IAElC,IAAIL,SAAS,EAAE;MACd,IAAI,CAACM,oBAAoB,EAAE;;IAG5B,IAAI,CAAC,IAAI,CAACf,SAAS,EAAE;MACpB,OAAOS,SAAS;;IAEjB,IAAI,CAACN,UAAU,EAAE;MAChB,IAAI,CAACa,uBAAuB,GAAGb,UAAU;MACzC,OAAOM,SAAS;;IAGjB,IAAIA,SAAS,EAAE;MACd,IAAI,CAACQ,sBAAsB,GAAG,IAAI,CAACjB,SAAS;MAC5C,IAAI,CAACgB,uBAAuB,GAAGb,UAAU;MACzC,IAAI,CAACe,8BAA8B,GAAG,IAAI,CAACd,iBAAiB;MAC5D,IAAI,CAACe,qBAAqB,GAAG,IAAI,CAACC,OAAO,CAACC,iBAAiB,CAC1D,IAAI,CAACrB,SAAS,EACdG,UAAU,EACV,IAAI,CAACC,iBAAiB,CACtB;;IAEF,OAAOK,SAAS;;EAGjBE,oBAA4BA,CAACW,cAAc,GAAG,KAAK,EAAQ;IAC1D,MAAMC,WAAW,GAAG,IAAI,CAACA,WAAW;IACpC;IACA,MAAMd,SAAS,GACda,cAAc,IACd,IAAI,CAACV,kBAAkB,EAAE,IACzB,IAAI,CAACY,6BAA6B,EAAE,IACpC,IAAI,CAACC,2BAA2B,EAAE;IAEnC,IAAIhB,SAAS,EAAE;MACd,IAAI,CAACiB,qBAAqB,EAAE;;IAG7B,IAAI,CAAC,IAAI,CAAC1B,SAAS,EAAE;MACpB;;IAED,IAAI,CAACuB,WAAW,EAAE;MACjB,IAAI,CAACI,wBAAwB,GAAGJ,WAAW;MAC3C;;IAGD,IAAId,SAAS,EAAE;MACd,IAAI,CAACQ,sBAAsB,GAAG,IAAI,CAACjB,SAAS;MAC5C,IAAI,CAAC2B,wBAAwB,GAAGJ,WAAW;MAC3C,IAAI,CAACK,+BAA+B,GAAG,IAAI,CAACrB,kBAAkB;MAC9D,IAAI,CAACsB,sBAAsB,GAAG,IAAI,CAACT,OAAO,CAACU,kBAAkB,CAC5D,IAAI,CAAC9B,SAAS,EACduB,WAAW,EACX,IAAI,CAAChB,kBAAkB,CACvB;;;EAIHK,kBAA0BA,CAAA,EAAY;IACrC,OAAO,IAAI,CAACK,sBAAsB,KAAK,IAAI,CAACjB,SAAS;;EAGtDa,4BAAoCA,CAAA,EAAY;IAC/C,OAAO,IAAI,CAACG,uBAAuB,KAAK,IAAI,CAACb,UAAU;;EAGxDqB,6BAAqCA,CAAA,EAAY;IAChD,OAAO,IAAI,CAACG,wBAAwB,KAAK,IAAI,CAACJ,WAAW;;EAG1DT,0BAAkCA,CAAA,EAAY;IAC7C,OAAO,CAACpB,YAAY,CACnB,IAAI,CAACwB,8BAA8B,EACnC,IAAI,CAACd,iBAAiB,CACtB;;EAGFqB,2BAAmCA,CAAA,EAAY;IAC9C,OAAO,CAAC/B,YAAY,CACnB,IAAI,CAACkC,+BAA+B,EACpC,IAAI,CAACrB,kBAAkB,CACvB;;EAGFQ,oBAA2BA,CAAA,EAAG;IAC7B,IAAI,IAAI,CAACI,qBAAqB,EAAE;MAC/B,IAAI,CAACA,qBAAqB,EAAE;MAC5B,IAAI,CAACA,qBAAqB,GAAGY,SAAS;;;EAIxCL,qBAA4BA,CAAA,EAAG;IAC9B,IAAI,IAAI,CAACG,sBAAsB,EAAE;MAChC,IAAI,CAACA,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,GAAGE,SAAS;MACvC,IAAI,CAACC,eAAe,GAAG,IAAI;MAC3B,IAAI,CAACC,cAAc,GAAG,IAAI;;;EAI5B,IAAY9B,UAAUA,CAAA,EAAG;IACxB,OACC,IAAI,CAAC+B,cAAc,IAAK,IAAI,CAACC,aAAa,IAAI,IAAI,CAACA,aAAa,CAACC,OAAO;;EAI1E,IAAYb,WAAWA,CAAA,EAAG;IACzB,OACC,IAAI,CAACS,eAAe,IACnB,IAAI,CAACC,cAAc,IAAI,IAAI,CAACA,cAAc,CAACG,OAAO;;EAIrDC,eAAuBA,CAAA,EAAG;IACzB,IAAI,CAACH,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,aAAa,GAAG,IAAI;;EAG1BG,gBAAwBA,CAAA,EAAG;IAC1B,IAAI,CAACN,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,cAAc,GAAG,IAAI;;EArK3BM,YAAmBnB,OAAgB,EAAE;IA/CrC,KAAOoB,KAAK,GAAG5C,kBAAkB,CAAC;MACjCO,UAAU,EAAEA,CACXsC,IAAuC,EACvCnC,OAA2B,KACvB;QACJ,IAAI,CAAC+B,eAAe,EAAE;QACtB,IAAI,CAACjC,iBAAiB,GAAGE,OAAO,IAAI,IAAI;QACxC,IAAIX,KAAK,CAAC8C,IAAI,CAAC,EAAE;UAChB,IAAI,CAACN,aAAa,GAAGM,IAAI;SACzB,MAAM;UACN,IAAI,CAACP,cAAc,GAAGO,IAAI;;QAE3B,IAAI,CAAC/B,mBAAmB,EAAE;OAC1B;MACDa,WAAW,EAAEA,CAACkB,IAAS,EAAEnC,OAA4B,KAAK;QACzD,IAAI,CAACgC,gBAAgB,EAAE;QACvB,IAAI,CAAC/B,kBAAkB,GAAGD,OAAO,IAAI,IAAI;QACzC,IAAIX,KAAK,CAAC8C,IAAI,CAAC,EAAE;UAChB,IAAI,CAACR,cAAc,GAAGQ,IAAI;SAC1B,MAAM;UACN,IAAI,CAACT,eAAe,GAAGS,IAAI;;QAE5B,IAAI,CAAC9B,oBAAoB,EAAE;;KAE5B,CAAC;IACF,KAAQX,SAAS,GAAsB,IAAI;IAE3C;IACA,KAAQmC,aAAa,GAA0B,IAAI;IAEnD,KAAQ9B,yBAAyB,GAA6B,IAAI;IAGlE;IACA,KAAQ4B,cAAc,GAA0B,IAAI;IAEpD,KAAQzB,0BAA0B,GAA8B,IAAI;IAGpE,KAAQS,sBAAsB,GAAsB,IAAI;IACxD,KAAQD,uBAAuB,GAAQ,IAAI;IAC3C,KAAQE,8BAA8B,GAAQ,IAAI;IAClD,KAAQS,wBAAwB,GAAQ,IAAI;IAC5C,KAAQC,+BAA+B,GAAQ,IAAI;IAKlD,IAAI,CAACR,OAAO,GAAGA,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}