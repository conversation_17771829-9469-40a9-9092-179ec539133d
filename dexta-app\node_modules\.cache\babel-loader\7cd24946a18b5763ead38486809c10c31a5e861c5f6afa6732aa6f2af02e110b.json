{"ast": null, "code": "import isPrototype from './_isPrototype.js';\nimport nativeKeys from './_nativeKeys.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * The base implementation of `_.keys` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction baseKeys(object) {\n  if (!isPrototype(object)) {\n    return nativeKeys(object);\n  }\n  var result = [];\n  for (var key in Object(object)) {\n    if (hasOwnProperty.call(object, key) && key != 'constructor') {\n      result.push(key);\n    }\n  }\n  return result;\n}\nexport default baseKeys;", "map": {"version": 3, "names": ["isPrototype", "nativeKeys", "objectProto", "Object", "prototype", "hasOwnProperty", "baseKeys", "object", "result", "key", "call", "push"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/lodash-es/_baseKeys.js"], "sourcesContent": ["import isPrototype from './_isPrototype.js';\nimport nativeKeys from './_nativeKeys.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * The base implementation of `_.keys` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction baseKeys(object) {\n  if (!isPrototype(object)) {\n    return nativeKeys(object);\n  }\n  var result = [];\n  for (var key in Object(object)) {\n    if (hasOwnProperty.call(object, key) && key != 'constructor') {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\nexport default baseKeys;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,UAAU,MAAM,kBAAkB;;AAEzC;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,MAAM,EAAE;EACxB,IAAI,CAACP,WAAW,CAACO,MAAM,CAAC,EAAE;IACxB,OAAON,UAAU,CAACM,MAAM,CAAC;EAC3B;EACA,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,GAAG,IAAIN,MAAM,CAACI,MAAM,CAAC,EAAE;IAC9B,IAAIF,cAAc,CAACK,IAAI,CAACH,MAAM,EAAEE,GAAG,CAAC,IAAIA,GAAG,IAAI,aAAa,EAAE;MAC5DD,MAAM,CAACG,IAAI,CAACF,GAAG,CAAC;IAClB;EACF;EACA,OAAOD,MAAM;AACf;AAEA,eAAeF,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}