{"ast": null, "code": "import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getOptionGroupUnstyledUtilityClass(slot) {\n  return generateUtilityClass('MuiOptionGroupUnstyled', slot);\n}\nconst optionGroupUnstyledClasses = generateUtilityClasses('MuiOptionGroupUnstyled', ['root', 'label', 'list']);\nexport default optionGroupUnstyledClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getOptionGroupUnstyledUtilityClass", "slot", "optionGroupUnstyledClasses"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/OptionGroupUnstyled/optionGroupUnstyledClasses.js"], "sourcesContent": ["import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getOptionGroupUnstyledUtilityClass(slot) {\n  return generateUtilityClass('MuiOptionGroupUnstyled', slot);\n}\nconst optionGroupUnstyledClasses = generateUtilityClasses('MuiOptionGroupUnstyled', ['root', 'label', 'list']);\nexport default optionGroupUnstyledClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,yBAAyB;AAC1D,OAAOC,sBAAsB,MAAM,2BAA2B;AAC9D,OAAO,SAASC,kCAAkCA,CAACC,IAAI,EAAE;EACvD,OAAOH,oBAAoB,CAAC,wBAAwB,EAAEG,IAAI,CAAC;AAC7D;AACA,MAAMC,0BAA0B,GAAGH,sBAAsB,CAAC,wBAAwB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AAC9G,eAAeG,0BAA0B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}