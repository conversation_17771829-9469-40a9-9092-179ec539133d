{"ast": null, "code": "/* eslint-disable */\n// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\nexport default typeof window != 'undefined' && window.Math == Math ? window : typeof self != 'undefined' && self.Math == Math ? self : Function('return this')();", "map": {"version": 3, "names": ["window", "Math", "self", "Function"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/utils/esm/ponyfillGlobal/ponyfillGlobal.js"], "sourcesContent": ["/* eslint-disable */\n// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\nexport default typeof window != 'undefined' && window.Math == Math ? window : typeof self != 'undefined' && self.Math == Math ? self : Function('return this')();"], "mappings": "AAAA;AACA;AACA,eAAe,OAAOA,MAAM,IAAI,WAAW,IAAIA,MAAM,CAACC,IAAI,IAAIA,IAAI,GAAGD,MAAM,GAAG,OAAOE,IAAI,IAAI,WAAW,IAAIA,IAAI,CAACD,IAAI,IAAIA,IAAI,GAAGC,IAAI,GAAGC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}