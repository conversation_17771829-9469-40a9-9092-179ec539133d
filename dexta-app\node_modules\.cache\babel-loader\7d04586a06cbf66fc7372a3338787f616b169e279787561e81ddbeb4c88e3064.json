{"ast": null, "code": "import { intersection } from './js_utils.js';\nexport const NONE = [];\nexport const ALL = [];\nNONE.__IS_NONE__ = true;\nALL.__IS_ALL__ = true;\n/**\n * Determines if the given handler IDs are dirty or not.\n *\n * @param dirtyIds The set of dirty handler ids\n * @param handlerIds The set of handler ids to check\n */\nexport function areDirty(dirtyIds, handlerIds) {\n  if (dirtyIds === NONE) {\n    return false;\n  }\n  if (dirtyIds === ALL || typeof handlerIds === 'undefined') {\n    return true;\n  }\n  const commonIds = intersection(handlerIds, dirtyIds);\n  return commonIds.length > 0;\n}", "map": {"version": 3, "names": ["intersection", "NONE", "ALL", "__IS_NONE__", "__IS_ALL__", "areDirty", "dirtyIds", "handlerIds", "commonIds", "length"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\dnd-core\\src\\utils\\dirtiness.ts"], "sourcesContent": ["import { intersection } from './js_utils.js'\n\nexport const NONE: string[] = []\nexport const ALL: string[] = []\n// Add these flags for debug\n;(NONE as any).__IS_NONE__ = true\n;(ALL as any).__IS_ALL__ = true\n\n/**\n * Determines if the given handler IDs are dirty or not.\n *\n * @param dirtyIds The set of dirty handler ids\n * @param handlerIds The set of handler ids to check\n */\nexport function areDirty(\n\tdirtyIds: string[],\n\thandlerIds: string[] | undefined,\n): boolean {\n\tif (dirtyIds === NONE) {\n\t\treturn false\n\t}\n\n\tif (dirtyIds === ALL || typeof handlerIds === 'undefined') {\n\t\treturn true\n\t}\n\n\tconst commonIds = intersection(handlerIds, dirtyIds)\n\treturn commonIds.length > 0\n}\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,eAAe;AAE5C,OAAO,MAAMC,IAAI,GAAa,EAAE;AAChC,OAAO,MAAMC,GAAG,GAAa,EAAE;AAE9BD,IAAK,CAASE,WAAW,GAAG,IAAI;AAChCD,GAAI,CAASE,UAAU,GAAG,IAAI;AAE/B;;;;;;AAMA,OAAO,SAASC,QAAQA,CACvBC,QAAkB,EAClBC,UAAgC,EACtB;EACV,IAAID,QAAQ,KAAKL,IAAI,EAAE;IACtB,OAAO,KAAK;;EAGb,IAAIK,QAAQ,KAAKJ,GAAG,IAAI,OAAOK,UAAU,KAAK,WAAW,EAAE;IAC1D,OAAO,IAAI;;EAGZ,MAAMC,SAAS,GAAGR,YAAY,CAACO,UAAU,EAAED,QAAQ,CAAC;EACpD,OAAOE,SAAS,CAACC,MAAM,GAAG,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}