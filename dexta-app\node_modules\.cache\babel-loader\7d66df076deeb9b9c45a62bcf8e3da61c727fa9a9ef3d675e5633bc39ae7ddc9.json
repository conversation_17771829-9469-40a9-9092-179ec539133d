{"ast": null, "code": "exports.removeElement = function (elem) {\n  if (elem.prev) elem.prev.next = elem.next;\n  if (elem.next) elem.next.prev = elem.prev;\n  if (elem.parent) {\n    var childs = elem.parent.children;\n    childs.splice(childs.lastIndexOf(elem), 1);\n  }\n};\nexports.replaceElement = function (elem, replacement) {\n  var prev = replacement.prev = elem.prev;\n  if (prev) {\n    prev.next = replacement;\n  }\n  var next = replacement.next = elem.next;\n  if (next) {\n    next.prev = replacement;\n  }\n  var parent = replacement.parent = elem.parent;\n  if (parent) {\n    var childs = parent.children;\n    childs[childs.lastIndexOf(elem)] = replacement;\n  }\n};\nexports.appendChild = function (elem, child) {\n  child.parent = elem;\n  if (elem.children.push(child) !== 1) {\n    var sibling = elem.children[elem.children.length - 2];\n    sibling.next = child;\n    child.prev = sibling;\n    child.next = null;\n  }\n};\nexports.append = function (elem, next) {\n  var parent = elem.parent,\n    currNext = elem.next;\n  next.next = currNext;\n  next.prev = elem;\n  elem.next = next;\n  next.parent = parent;\n  if (currNext) {\n    currNext.prev = next;\n    if (parent) {\n      var childs = parent.children;\n      childs.splice(childs.lastIndexOf(currNext), 0, next);\n    }\n  } else if (parent) {\n    parent.children.push(next);\n  }\n};\nexports.prepend = function (elem, prev) {\n  var parent = elem.parent;\n  if (parent) {\n    var childs = parent.children;\n    childs.splice(childs.lastIndexOf(elem), 0, prev);\n  }\n  if (elem.prev) {\n    elem.prev.next = prev;\n  }\n  prev.parent = parent;\n  prev.prev = elem.prev;\n  prev.next = elem;\n  elem.prev = prev;\n};", "map": {"version": 3, "names": ["exports", "removeElement", "elem", "prev", "next", "parent", "childs", "children", "splice", "lastIndexOf", "replaceElement", "replacement", "append<PERSON><PERSON><PERSON>", "child", "push", "sibling", "length", "append", "currNext", "prepend"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/domutils/lib/manipulation.js"], "sourcesContent": ["exports.removeElement = function(elem){\n\tif(elem.prev) elem.prev.next = elem.next;\n\tif(elem.next) elem.next.prev = elem.prev;\n\n\tif(elem.parent){\n\t\tvar childs = elem.parent.children;\n\t\tchilds.splice(childs.lastIndexOf(elem), 1);\n\t}\n};\n\nexports.replaceElement = function(elem, replacement){\n\tvar prev = replacement.prev = elem.prev;\n\tif(prev){\n\t\tprev.next = replacement;\n\t}\n\n\tvar next = replacement.next = elem.next;\n\tif(next){\n\t\tnext.prev = replacement;\n\t}\n\n\tvar parent = replacement.parent = elem.parent;\n\tif(parent){\n\t\tvar childs = parent.children;\n\t\tchilds[childs.lastIndexOf(elem)] = replacement;\n\t}\n};\n\nexports.appendChild = function(elem, child){\n\tchild.parent = elem;\n\n\tif(elem.children.push(child) !== 1){\n\t\tvar sibling = elem.children[elem.children.length - 2];\n\t\tsibling.next = child;\n\t\tchild.prev = sibling;\n\t\tchild.next = null;\n\t}\n};\n\nexports.append = function(elem, next){\n\tvar parent = elem.parent,\n\t\tcurrNext = elem.next;\n\n\tnext.next = currNext;\n\tnext.prev = elem;\n\telem.next = next;\n\tnext.parent = parent;\n\n\tif(currNext){\n\t\tcurrNext.prev = next;\n\t\tif(parent){\n\t\t\tvar childs = parent.children;\n\t\t\tchilds.splice(childs.lastIndexOf(currNext), 0, next);\n\t\t}\n\t} else if(parent){\n\t\tparent.children.push(next);\n\t}\n};\n\nexports.prepend = function(elem, prev){\n\tvar parent = elem.parent;\n\tif(parent){\n\t\tvar childs = parent.children;\n\t\tchilds.splice(childs.lastIndexOf(elem), 0, prev);\n\t}\n\n\tif(elem.prev){\n\t\telem.prev.next = prev;\n\t}\n\t\n\tprev.parent = parent;\n\tprev.prev = elem.prev;\n\tprev.next = elem;\n\telem.prev = prev;\n};\n\n\n"], "mappings": "AAAAA,OAAO,CAACC,aAAa,GAAG,UAASC,IAAI,EAAC;EACrC,IAAGA,IAAI,CAACC,IAAI,EAAED,IAAI,CAACC,IAAI,CAACC,IAAI,GAAGF,IAAI,CAACE,IAAI;EACxC,IAAGF,IAAI,CAACE,IAAI,EAAEF,IAAI,CAACE,IAAI,CAACD,IAAI,GAAGD,IAAI,CAACC,IAAI;EAExC,IAAGD,IAAI,CAACG,MAAM,EAAC;IACd,IAAIC,MAAM,GAAGJ,IAAI,CAACG,MAAM,CAACE,QAAQ;IACjCD,MAAM,CAACE,MAAM,CAACF,MAAM,CAACG,WAAW,CAACP,IAAI,CAAC,EAAE,CAAC,CAAC;EAC3C;AACD,CAAC;AAEDF,OAAO,CAACU,cAAc,GAAG,UAASR,IAAI,EAAES,WAAW,EAAC;EACnD,IAAIR,IAAI,GAAGQ,WAAW,CAACR,IAAI,GAAGD,IAAI,CAACC,IAAI;EACvC,IAAGA,IAAI,EAAC;IACPA,IAAI,CAACC,IAAI,GAAGO,WAAW;EACxB;EAEA,IAAIP,IAAI,GAAGO,WAAW,CAACP,IAAI,GAAGF,IAAI,CAACE,IAAI;EACvC,IAAGA,IAAI,EAAC;IACPA,IAAI,CAACD,IAAI,GAAGQ,WAAW;EACxB;EAEA,IAAIN,MAAM,GAAGM,WAAW,CAACN,MAAM,GAAGH,IAAI,CAACG,MAAM;EAC7C,IAAGA,MAAM,EAAC;IACT,IAAIC,MAAM,GAAGD,MAAM,CAACE,QAAQ;IAC5BD,MAAM,CAACA,MAAM,CAACG,WAAW,CAACP,IAAI,CAAC,CAAC,GAAGS,WAAW;EAC/C;AACD,CAAC;AAEDX,OAAO,CAACY,WAAW,GAAG,UAASV,IAAI,EAAEW,KAAK,EAAC;EAC1CA,KAAK,CAACR,MAAM,GAAGH,IAAI;EAEnB,IAAGA,IAAI,CAACK,QAAQ,CAACO,IAAI,CAACD,KAAK,CAAC,KAAK,CAAC,EAAC;IAClC,IAAIE,OAAO,GAAGb,IAAI,CAACK,QAAQ,CAACL,IAAI,CAACK,QAAQ,CAACS,MAAM,GAAG,CAAC,CAAC;IACrDD,OAAO,CAACX,IAAI,GAAGS,KAAK;IACpBA,KAAK,CAACV,IAAI,GAAGY,OAAO;IACpBF,KAAK,CAACT,IAAI,GAAG,IAAI;EAClB;AACD,CAAC;AAEDJ,OAAO,CAACiB,MAAM,GAAG,UAASf,IAAI,EAAEE,IAAI,EAAC;EACpC,IAAIC,MAAM,GAAGH,IAAI,CAACG,MAAM;IACvBa,QAAQ,GAAGhB,IAAI,CAACE,IAAI;EAErBA,IAAI,CAACA,IAAI,GAAGc,QAAQ;EACpBd,IAAI,CAACD,IAAI,GAAGD,IAAI;EAChBA,IAAI,CAACE,IAAI,GAAGA,IAAI;EAChBA,IAAI,CAACC,MAAM,GAAGA,MAAM;EAEpB,IAAGa,QAAQ,EAAC;IACXA,QAAQ,CAACf,IAAI,GAAGC,IAAI;IACpB,IAAGC,MAAM,EAAC;MACT,IAAIC,MAAM,GAAGD,MAAM,CAACE,QAAQ;MAC5BD,MAAM,CAACE,MAAM,CAACF,MAAM,CAACG,WAAW,CAACS,QAAQ,CAAC,EAAE,CAAC,EAAEd,IAAI,CAAC;IACrD;EACD,CAAC,MAAM,IAAGC,MAAM,EAAC;IAChBA,MAAM,CAACE,QAAQ,CAACO,IAAI,CAACV,IAAI,CAAC;EAC3B;AACD,CAAC;AAEDJ,OAAO,CAACmB,OAAO,GAAG,UAASjB,IAAI,EAAEC,IAAI,EAAC;EACrC,IAAIE,MAAM,GAAGH,IAAI,CAACG,MAAM;EACxB,IAAGA,MAAM,EAAC;IACT,IAAIC,MAAM,GAAGD,MAAM,CAACE,QAAQ;IAC5BD,MAAM,CAACE,MAAM,CAACF,MAAM,CAACG,WAAW,CAACP,IAAI,CAAC,EAAE,CAAC,EAAEC,IAAI,CAAC;EACjD;EAEA,IAAGD,IAAI,CAACC,IAAI,EAAC;IACZD,IAAI,CAACC,IAAI,CAACC,IAAI,GAAGD,IAAI;EACtB;EAEAA,IAAI,CAACE,MAAM,GAAGA,MAAM;EACpBF,IAAI,CAACA,IAAI,GAAGD,IAAI,CAACC,IAAI;EACrBA,IAAI,CAACC,IAAI,GAAGF,IAAI;EAChBA,IAAI,CAACC,IAAI,GAAGA,IAAI;AACjB,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}