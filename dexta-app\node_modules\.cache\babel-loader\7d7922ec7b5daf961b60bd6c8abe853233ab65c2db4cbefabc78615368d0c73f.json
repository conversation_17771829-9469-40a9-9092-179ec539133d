{"ast": null, "code": "import { __values } from \"tslib\";\nimport { EMPTY } from '../observable/empty';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function takeLast(count) {\n  return count <= 0 ? function () {\n    return EMPTY;\n  } : operate(function (source, subscriber) {\n    var buffer = [];\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      buffer.push(value);\n      count < buffer.length && buffer.shift();\n    }, function () {\n      var e_1, _a;\n      try {\n        for (var buffer_1 = __values(buffer), buffer_1_1 = buffer_1.next(); !buffer_1_1.done; buffer_1_1 = buffer_1.next()) {\n          var value = buffer_1_1.value;\n          subscriber.next(value);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (buffer_1_1 && !buffer_1_1.done && (_a = buffer_1.return)) _a.call(buffer_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n      subscriber.complete();\n    }, undefined, function () {\n      buffer = null;\n    }));\n  });\n}", "map": {"version": 3, "names": ["EMPTY", "operate", "createOperatorSubscriber", "takeLast", "count", "source", "subscriber", "buffer", "subscribe", "value", "push", "length", "shift", "buffer_1", "__values", "buffer_1_1", "next", "done", "complete", "undefined"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\takeLast.ts"], "sourcesContent": ["import { EMPTY } from '../observable/empty';\nimport { MonoTypeOperatorFunction } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/**\n * Waits for the source to complete, then emits the last N values from the source,\n * as specified by the `count` argument.\n *\n * ![](takeLast.png)\n *\n * `takeLast` results in an observable that will hold values up to `count` values in memory,\n * until the source completes. It then pushes all values in memory to the consumer, in the\n * order they were received from the source, then notifies the consumer that it is\n * complete.\n *\n * If for some reason the source completes before the `count` supplied to `takeLast` is reached,\n * all values received until that point are emitted, and then completion is notified.\n *\n * **Warning**: Using `takeLast` with an observable that never completes will result\n * in an observable that never emits a value.\n *\n * ## Example\n *\n * Take the last 3 values of an Observable with many values\n *\n * ```ts\n * import { range, takeLast } from 'rxjs';\n *\n * const many = range(1, 100);\n * const lastThree = many.pipe(takeLast(3));\n * lastThree.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link take}\n * @see {@link takeUntil}\n * @see {@link takeWhile}\n * @see {@link skip}\n *\n * @param count The maximum number of values to emit from the end of\n * the sequence of values emitted by the source Observable.\n * @return A function that returns an Observable that emits at most the last\n * `count` values emitted by the source Observable.\n */\nexport function takeLast<T>(count: number): MonoTypeOperatorFunction<T> {\n  return count <= 0\n    ? () => EMPTY\n    : operate((source, subscriber) => {\n        // This buffer will hold the values we are going to emit\n        // when the source completes. Since we only want to take the\n        // last N values, we can't emit until we're sure we're not getting\n        // any more values.\n        let buffer: T[] = [];\n        source.subscribe(\n          createOperatorSubscriber(\n            subscriber,\n            (value) => {\n              // Add the most recent value onto the end of our buffer.\n              buffer.push(value);\n              // If our buffer is now larger than the number of values we\n              // want to take, we remove the oldest value from the buffer.\n              count < buffer.length && buffer.shift();\n            },\n            () => {\n              // The source completed, we now know what are last values\n              // are, emit them in the order they were received.\n              for (const value of buffer) {\n                subscriber.next(value);\n              }\n              subscriber.complete();\n            },\n            // Errors are passed through to the consumer\n            undefined,\n            () => {\n              // During finalization release the values in our buffer.\n              buffer = null!;\n            }\n          )\n        );\n      });\n}\n"], "mappings": ";AAAA,SAASA,KAAK,QAAQ,qBAAqB;AAE3C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAyC/D,OAAM,SAAUC,QAAQA,CAAIC,KAAa;EACvC,OAAOA,KAAK,IAAI,CAAC,GACb;IAAM,OAAAJ,KAAK;EAAL,CAAK,GACXC,OAAO,CAAC,UAACI,MAAM,EAAEC,UAAU;IAKzB,IAAIC,MAAM,GAAQ,EAAE;IACpBF,MAAM,CAACG,SAAS,CACdN,wBAAwB,CACtBI,UAAU,EACV,UAACG,KAAK;MAEJF,MAAM,CAACG,IAAI,CAACD,KAAK,CAAC;MAGlBL,KAAK,GAAGG,MAAM,CAACI,MAAM,IAAIJ,MAAM,CAACK,KAAK,EAAE;IACzC,CAAC,EACD;;;QAGE,KAAoB,IAAAC,QAAA,GAAAC,QAAA,CAAAP,MAAM,GAAAQ,UAAA,GAAAF,QAAA,CAAAG,IAAA,KAAAD,UAAA,CAAAE,IAAA,EAAAF,UAAA,GAAAF,QAAA,CAAAG,IAAA,IAAE;UAAvB,IAAMP,KAAK,GAAAM,UAAA,CAAAN,KAAA;UACdH,UAAU,CAACU,IAAI,CAACP,KAAK,CAAC;;;;;;;;;;;;;MAExBH,UAAU,CAACY,QAAQ,EAAE;IACvB,CAAC,EAEDC,SAAS,EACT;MAEEZ,MAAM,GAAG,IAAK;IAChB,CAAC,CACF,CACF;EACH,CAAC,CAAC;AACR"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}