{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar decode_json_1 = __importDefault(require(\"./maps/decode.json\"));\n// Adapted from https://github.com/mathiasbynens/he/blob/master/src/he.js#L94-L119\nvar fromCodePoint =\n// eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\nString.fromCodePoint || function (codePoint) {\n  var output = \"\";\n  if (codePoint > 0xffff) {\n    codePoint -= 0x10000;\n    output += String.fromCharCode(codePoint >>> 10 & 0x3ff | 0xd800);\n    codePoint = 0xdc00 | codePoint & 0x3ff;\n  }\n  output += String.fromCharCode(codePoint);\n  return output;\n};\nfunction decodeCodePoint(codePoint) {\n  if (codePoint >= 0xd800 && codePoint <= 0xdfff || codePoint > 0x10ffff) {\n    return \"\\uFFFD\";\n  }\n  if (codePoint in decode_json_1.default) {\n    codePoint = decode_json_1.default[codePoint];\n  }\n  return fromCodePoint(codePoint);\n}\nexports.default = decodeCodePoint;", "map": {"version": 3, "names": ["__importDefault", "mod", "__esModule", "Object", "defineProperty", "exports", "value", "decode_json_1", "require", "fromCodePoint", "String", "codePoint", "output", "fromCharCode", "decodeCodePoint", "default"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/entities/lib/decode_codepoint.js"], "sourcesContent": ["\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar decode_json_1 = __importDefault(require(\"./maps/decode.json\"));\n// Adapted from https://github.com/mathiasbynens/he/blob/master/src/he.js#L94-L119\nvar fromCodePoint = \n// eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\nString.fromCodePoint ||\n    function (codePoint) {\n        var output = \"\";\n        if (codePoint > 0xffff) {\n            codePoint -= 0x10000;\n            output += String.fromCharCode(((codePoint >>> 10) & 0x3ff) | 0xd800);\n            codePoint = 0xdc00 | (codePoint & 0x3ff);\n        }\n        output += String.fromCharCode(codePoint);\n        return output;\n    };\nfunction decodeCodePoint(codePoint) {\n    if ((codePoint >= 0xd800 && codePoint <= 0xdfff) || codePoint > 0x10ffff) {\n        return \"\\uFFFD\";\n    }\n    if (codePoint in decode_json_1.default) {\n        codePoint = decode_json_1.default[codePoint];\n    }\n    return fromCodePoint(codePoint);\n}\nexports.default = decodeCodePoint;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAI,IAAI,IAAI,IAAI,CAACA,eAAe,IAAK,UAAUC,GAAG,EAAE;EACnE,OAAQA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAID,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAC7D,CAAC;AACDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7D,IAAIC,aAAa,GAAGP,eAAe,CAACQ,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAClE;AACA,IAAIC,aAAa;AACjB;AACAC,MAAM,CAACD,aAAa,IAChB,UAAUE,SAAS,EAAE;EACjB,IAAIC,MAAM,GAAG,EAAE;EACf,IAAID,SAAS,GAAG,MAAM,EAAE;IACpBA,SAAS,IAAI,OAAO;IACpBC,MAAM,IAAIF,MAAM,CAACG,YAAY,CAAGF,SAAS,KAAK,EAAE,GAAI,KAAK,GAAI,MAAM,CAAC;IACpEA,SAAS,GAAG,MAAM,GAAIA,SAAS,GAAG,KAAM;EAC5C;EACAC,MAAM,IAAIF,MAAM,CAACG,YAAY,CAACF,SAAS,CAAC;EACxC,OAAOC,MAAM;AACjB,CAAC;AACL,SAASE,eAAeA,CAACH,SAAS,EAAE;EAChC,IAAKA,SAAS,IAAI,MAAM,IAAIA,SAAS,IAAI,MAAM,IAAKA,SAAS,GAAG,QAAQ,EAAE;IACtE,OAAO,QAAQ;EACnB;EACA,IAAIA,SAAS,IAAIJ,aAAa,CAACQ,OAAO,EAAE;IACpCJ,SAAS,GAAGJ,aAAa,CAACQ,OAAO,CAACJ,SAAS,CAAC;EAChD;EACA,OAAOF,aAAa,CAACE,SAAS,CAAC;AACnC;AACAN,OAAO,CAACU,OAAO,GAAGD,eAAe"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}