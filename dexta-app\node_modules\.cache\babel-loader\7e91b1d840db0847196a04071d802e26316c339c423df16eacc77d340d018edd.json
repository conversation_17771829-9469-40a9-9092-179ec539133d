{"ast": null, "code": "function ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nimport Metadata from './metadata.js';\nimport isPossibleNumber from './isPossible.js';\nimport isValidNumber from './isValid.js'; // import checkNumberLength from './helpers/checkNumberLength.js'\n\nimport getNumberType from './helpers/getNumberType.js';\nimport getPossibleCountriesForNumber from './helpers/getPossibleCountriesForNumber.js';\nimport formatNumber from './format.js';\nvar USE_NON_GEOGRAPHIC_COUNTRY_CODE = false;\nvar PhoneNumber = /*#__PURE__*/function () {\n  /**\r\n   * @param  {string} countryOrCountryCallingCode\r\n   * @param  {string} nationalNumber\r\n   * @param  {object} metadata — Metadata JSON\r\n   * @return {PhoneNumber}\r\n   */\n  function PhoneNumber(countryOrCountryCallingCode, nationalNumber, metadata) {\n    _classCallCheck(this, PhoneNumber);\n    if (!countryOrCountryCallingCode) {\n      throw new TypeError('`country` or `countryCallingCode` not passed');\n    }\n    if (!nationalNumber) {\n      throw new TypeError('`nationalNumber` not passed');\n    }\n    if (!metadata) {\n      throw new TypeError('`metadata` not passed');\n    }\n    var _getCountryAndCountry = getCountryAndCountryCallingCode(countryOrCountryCallingCode, metadata),\n      country = _getCountryAndCountry.country,\n      countryCallingCode = _getCountryAndCountry.countryCallingCode;\n    this.country = country;\n    this.countryCallingCode = countryCallingCode;\n    this.nationalNumber = nationalNumber;\n    this.number = '+' + this.countryCallingCode + this.nationalNumber; // Exclude `metadata` property output from `PhoneNumber.toString()`\n    // so that it doesn't clutter the console output of Node.js.\n    // Previously, when Node.js did `console.log(new PhoneNumber(...))`,\n    // it would output the whole internal structure of the `metadata` object.\n\n    this.getMetadata = function () {\n      return metadata;\n    };\n  }\n  _createClass(PhoneNumber, [{\n    key: \"setExt\",\n    value: function setExt(ext) {\n      this.ext = ext;\n    }\n  }, {\n    key: \"getPossibleCountries\",\n    value: function getPossibleCountries() {\n      if (this.country) {\n        return [this.country];\n      }\n      return getPossibleCountriesForNumber(this.countryCallingCode, this.nationalNumber, this.getMetadata());\n    }\n  }, {\n    key: \"isPossible\",\n    value: function isPossible() {\n      return isPossibleNumber(this, {\n        v2: true\n      }, this.getMetadata());\n    }\n  }, {\n    key: \"isValid\",\n    value: function isValid() {\n      return isValidNumber(this, {\n        v2: true\n      }, this.getMetadata());\n    }\n  }, {\n    key: \"isNonGeographic\",\n    value: function isNonGeographic() {\n      var metadata = new Metadata(this.getMetadata());\n      return metadata.isNonGeographicCallingCode(this.countryCallingCode);\n    }\n  }, {\n    key: \"isEqual\",\n    value: function isEqual(phoneNumber) {\n      return this.number === phoneNumber.number && this.ext === phoneNumber.ext;\n    } // This function was originally meant to be an equivalent for `validatePhoneNumberLength()`,\n    // but later it was found out that it doesn't include the possible `TOO_SHORT` result\n    // returned from `parsePhoneNumberWithError()` in the original `validatePhoneNumberLength()`,\n    // so eventually I simply commented out this method from the `PhoneNumber` class\n    // and just left the `validatePhoneNumberLength()` function, even though that one would require\n    // and additional step to also validate the actual country / calling code of the phone number.\n    // validateLength() {\n    // \tconst metadata = new Metadata(this.getMetadata())\n    // \tmetadata.selectNumberingPlan(this.countryCallingCode)\n    // \tconst result = checkNumberLength(this.nationalNumber, metadata)\n    // \tif (result !== 'IS_POSSIBLE') {\n    // \t\treturn result\n    // \t}\n    // }\n  }, {\n    key: \"getType\",\n    value: function getType() {\n      return getNumberType(this, {\n        v2: true\n      }, this.getMetadata());\n    }\n  }, {\n    key: \"format\",\n    value: function format(_format, options) {\n      return formatNumber(this, _format, options ? _objectSpread(_objectSpread({}, options), {}, {\n        v2: true\n      }) : {\n        v2: true\n      }, this.getMetadata());\n    }\n  }, {\n    key: \"formatNational\",\n    value: function formatNational(options) {\n      return this.format('NATIONAL', options);\n    }\n  }, {\n    key: \"formatInternational\",\n    value: function formatInternational(options) {\n      return this.format('INTERNATIONAL', options);\n    }\n  }, {\n    key: \"getURI\",\n    value: function getURI(options) {\n      return this.format('RFC3966', options);\n    }\n  }]);\n  return PhoneNumber;\n}();\nexport { PhoneNumber as default };\nvar isCountryCode = function isCountryCode(value) {\n  return /^[A-Z]{2}$/.test(value);\n};\nfunction getCountryAndCountryCallingCode(countryOrCountryCallingCode, metadataJson) {\n  var country;\n  var countryCallingCode;\n  var metadata = new Metadata(metadataJson); // If country code is passed then derive `countryCallingCode` from it.\n  // Also store the country code as `.country`.\n\n  if (isCountryCode(countryOrCountryCallingCode)) {\n    country = countryOrCountryCallingCode;\n    metadata.selectNumberingPlan(country);\n    countryCallingCode = metadata.countryCallingCode();\n  } else {\n    countryCallingCode = countryOrCountryCallingCode;\n    /* istanbul ignore if */\n\n    if (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\n      if (metadata.isNonGeographicCallingCode(countryCallingCode)) {\n        country = '001';\n      }\n    }\n  }\n  return {\n    country: country,\n    countryCallingCode: countryCallingCode\n  };\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "isPossibleNumber", "isValidNumber", "getNumberType", "getPossibleCountriesForNumber", "formatNumber", "USE_NON_GEOGRAPHIC_COUNTRY_CODE", "PhoneNumber", "countryOrCountryCallingCode", "nationalNumber", "metadata", "_classCallCheck", "TypeError", "_getCountryAndCountry", "getCountryAndCountryCallingCode", "country", "countryCallingCode", "number", "getMetadata", "setExt", "ext", "getPossibleCountries", "isPossible", "v2", "<PERSON><PERSON><PERSON><PERSON>", "isNonGeographic", "isNonGeographicCallingCode", "isEqual", "phoneNumber", "getType", "format", "_format", "options", "_objectSpread", "formatNational", "formatInternational", "getURI", "isCountryCode", "value", "test", "metadataJson", "selectNumberingPlan"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\PhoneNumber.js"], "sourcesContent": ["import Metadata from './metadata.js'\r\nimport isPossibleNumber from './isPossible.js'\r\nimport isValidNumber from './isValid.js'\r\n// import checkNumberLength from './helpers/checkNumberLength.js'\r\nimport getNumberType from './helpers/getNumberType.js'\r\nimport getPossibleCountriesForNumber from './helpers/getPossibleCountriesForNumber.js'\r\nimport formatNumber from './format.js'\r\n\r\nconst USE_NON_GEOGRAPHIC_COUNTRY_CODE = false\r\n\r\nexport default class PhoneNumber {\r\n\t/**\r\n\t * @param  {string} countryOrCountryCallingCode\r\n\t * @param  {string} nationalNumber\r\n\t * @param  {object} metadata — Metadata JSON\r\n\t * @return {PhoneNumber}\r\n\t */\r\n\tconstructor(countryOrCountryCallingCode, nationalNumber, metadata) {\r\n\t\tif (!countryOrCountryCallingCode) {\r\n\t\t\tthrow new TypeError('`country` or `countryCallingCode` not passed')\r\n\t\t}\r\n\t\tif (!nationalNumber) {\r\n\t\t\tthrow new TypeError('`nationalNumber` not passed')\r\n\t\t}\r\n\t\tif (!metadata) {\r\n\t\t\tthrow new TypeError('`metadata` not passed')\r\n\t\t}\r\n\t\tconst { country, countryCallingCode } = getCountryAndCountryCallingCode(\r\n\t\t\tcountryOrCountryCallingCode,\r\n\t\t\tmetadata\r\n\t\t)\r\n\t\tthis.country = country\r\n\t\tthis.countryCallingCode = countryCallingCode\r\n\t\tthis.nationalNumber = nationalNumber\r\n\t\tthis.number = '+' + this.countryCallingCode + this.nationalNumber\r\n\t\t// Exclude `metadata` property output from `PhoneNumber.toString()`\r\n\t\t// so that it doesn't clutter the console output of Node.js.\r\n\t\t// Previously, when Node.js did `console.log(new PhoneNumber(...))`,\r\n\t\t// it would output the whole internal structure of the `metadata` object.\r\n\t\tthis.getMetadata = () => metadata\r\n\t}\r\n\r\n\tsetExt(ext) {\r\n\t\tthis.ext = ext\r\n\t}\r\n\r\n\tgetPossibleCountries() {\r\n\t\tif (this.country) {\r\n\t\t\treturn [this.country]\r\n\t\t}\r\n\t\treturn getPossibleCountriesForNumber(\r\n\t\t\tthis.countryCallingCode,\r\n\t\t\tthis.nationalNumber,\r\n\t\t\tthis.getMetadata()\r\n\t\t)\r\n\t}\r\n\r\n\tisPossible() {\r\n\t\treturn isPossibleNumber(this, { v2: true }, this.getMetadata())\r\n\t}\r\n\r\n\tisValid() {\r\n\t\treturn isValidNumber(this, { v2: true }, this.getMetadata())\r\n\t}\r\n\r\n\tisNonGeographic() {\r\n\t\tconst metadata = new Metadata(this.getMetadata())\r\n\t\treturn metadata.isNonGeographicCallingCode(this.countryCallingCode)\r\n\t}\r\n\r\n\tisEqual(phoneNumber) {\r\n\t\treturn this.number === phoneNumber.number && this.ext === phoneNumber.ext\r\n\t}\r\n\r\n\t// This function was originally meant to be an equivalent for `validatePhoneNumberLength()`,\r\n\t// but later it was found out that it doesn't include the possible `TOO_SHORT` result\r\n\t// returned from `parsePhoneNumberWithError()` in the original `validatePhoneNumberLength()`,\r\n\t// so eventually I simply commented out this method from the `PhoneNumber` class\r\n\t// and just left the `validatePhoneNumberLength()` function, even though that one would require\r\n\t// and additional step to also validate the actual country / calling code of the phone number.\r\n\t// validateLength() {\r\n\t// \tconst metadata = new Metadata(this.getMetadata())\r\n\t// \tmetadata.selectNumberingPlan(this.countryCallingCode)\r\n\t// \tconst result = checkNumberLength(this.nationalNumber, metadata)\r\n\t// \tif (result !== 'IS_POSSIBLE') {\r\n\t// \t\treturn result\r\n\t// \t}\r\n\t// }\r\n\r\n\tgetType() {\r\n\t\treturn getNumberType(this, { v2: true }, this.getMetadata())\r\n\t}\r\n\r\n\tformat(format, options) {\r\n\t\treturn formatNumber(\r\n\t\t\tthis,\r\n\t\t\tformat,\r\n\t\t\toptions ? { ...options, v2: true } : { v2: true },\r\n\t\t\tthis.getMetadata()\r\n\t\t)\r\n\t}\r\n\r\n\tformatNational(options) {\r\n\t\treturn this.format('NATIONAL', options)\r\n\t}\r\n\r\n\tformatInternational(options) {\r\n\t\treturn this.format('INTERNATIONAL', options)\r\n\t}\r\n\r\n\tgetURI(options) {\r\n\t\treturn this.format('RFC3966', options)\r\n\t}\r\n}\r\n\r\nconst isCountryCode = (value) => /^[A-Z]{2}$/.test(value)\r\n\r\nfunction getCountryAndCountryCallingCode(countryOrCountryCallingCode, metadataJson) {\r\n\tlet country\r\n\tlet countryCallingCode\r\n\r\n\tconst metadata = new Metadata(metadataJson)\r\n\t// If country code is passed then derive `countryCallingCode` from it.\r\n\t// Also store the country code as `.country`.\r\n\tif (isCountryCode(countryOrCountryCallingCode)) {\r\n\t\tcountry = countryOrCountryCallingCode\r\n\t\tmetadata.selectNumberingPlan(country)\r\n\t\tcountryCallingCode = metadata.countryCallingCode()\r\n\t} else {\r\n\t\tcountryCallingCode = countryOrCountryCallingCode\r\n\t\t/* istanbul ignore if */\r\n\t\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\t\tif (metadata.isNonGeographicCallingCode(countryCallingCode)) {\r\n\t\t\t\tcountry = '001'\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn {\r\n\t\tcountry,\r\n\t\tcountryCallingCode\r\n\t}\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,QAAP,MAAqB,eAArB;AACA,OAAOC,gBAAP,MAA6B,iBAA7B;AACA,OAAOC,aAAP,MAA0B,cAA1B,C,CACA;;AACA,OAAOC,aAAP,MAA0B,4BAA1B;AACA,OAAOC,6BAAP,MAA0C,4CAA1C;AACA,OAAOC,YAAP,MAAyB,aAAzB;AAEA,IAAMC,+BAA+B,GAAG,KAAxC;IAEqBC,W;EACpB;AACD;AACA;AACA;AACA;AACA;EACC,SAAAA,YAAYC,2BAAZ,EAAyCC,cAAzC,EAAyDC,QAAzD,EAAmE;IAAAC,eAAA,OAAAJ,WAAA;IAClE,IAAI,CAACC,2BAAL,EAAkC;MACjC,MAAM,IAAII,SAAJ,CAAc,8CAAd,CAAN;IACA;IACD,IAAI,CAACH,cAAL,EAAqB;MACpB,MAAM,IAAIG,SAAJ,CAAc,6BAAd,CAAN;IACA;IACD,IAAI,CAACF,QAAL,EAAe;MACd,MAAM,IAAIE,SAAJ,CAAc,uBAAd,CAAN;IACA;IACD,IAAAC,qBAAA,GAAwCC,+BAA+B,CACtEN,2BADsE,EAEtEE,QAFsE,CAAvE;MAAQK,OAAR,GAAAF,qBAAA,CAAQE,OAAR;MAAiBC,kBAAjB,GAAAH,qBAAA,CAAiBG,kBAAjB;IAIA,KAAKD,OAAL,GAAeA,OAAf;IACA,KAAKC,kBAAL,GAA0BA,kBAA1B;IACA,KAAKP,cAAL,GAAsBA,cAAtB;IACA,KAAKQ,MAAL,GAAc,MAAM,KAAKD,kBAAX,GAAgC,KAAKP,cAAnD,CAjBkE,CAkBlE;IACA;IACA;IACA;;IACA,KAAKS,WAAL,GAAmB;MAAA,OAAMR,QAAN;IAAA,CAAnB;EACA;;;WAED,SAAAS,OAAOC,GAAP,EAAY;MACX,KAAKA,GAAL,GAAWA,GAAX;IACA;;;WAED,SAAAC,qBAAA,EAAuB;MACtB,IAAI,KAAKN,OAAT,EAAkB;QACjB,OAAO,CAAC,KAAKA,OAAN,CAAP;MACA;MACD,OAAOX,6BAA6B,CACnC,KAAKY,kBAD8B,EAEnC,KAAKP,cAF8B,EAGnC,KAAKS,WAAL,EAHmC,CAApC;IAKA;;;WAED,SAAAI,WAAA,EAAa;MACZ,OAAOrB,gBAAgB,CAAC,IAAD,EAAO;QAAEsB,EAAE,EAAE;MAAN,CAAP,EAAqB,KAAKL,WAAL,EAArB,CAAvB;IACA;;;WAED,SAAAM,QAAA,EAAU;MACT,OAAOtB,aAAa,CAAC,IAAD,EAAO;QAAEqB,EAAE,EAAE;MAAN,CAAP,EAAqB,KAAKL,WAAL,EAArB,CAApB;IACA;;;WAED,SAAAO,gBAAA,EAAkB;MACjB,IAAMf,QAAQ,GAAG,IAAIV,QAAJ,CAAa,KAAKkB,WAAL,EAAb,CAAjB;MACA,OAAOR,QAAQ,CAACgB,0BAAT,CAAoC,KAAKV,kBAAzC,CAAP;IACA;;;WAED,SAAAW,QAAQC,WAAR,EAAqB;MACpB,OAAO,KAAKX,MAAL,KAAgBW,WAAW,CAACX,MAA5B,IAAsC,KAAKG,GAAL,KAAaQ,WAAW,CAACR,GAAtE;IACA,C,CAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;WAEA,SAAAS,QAAA,EAAU;MACT,OAAO1B,aAAa,CAAC,IAAD,EAAO;QAAEoB,EAAE,EAAE;MAAN,CAAP,EAAqB,KAAKL,WAAL,EAArB,CAApB;IACA;;;WAED,SAAAY,OAAOC,OAAP,EAAeC,OAAf,EAAwB;MACvB,OAAO3B,YAAY,CAClB,IADkB,EAElB0B,OAFkB,EAGlBC,OAAO,GAAAC,aAAA,CAAAA,aAAA,KAAQD,OAAR;QAAiBT,EAAE,EAAE;MAArB,KAA8B;QAAEA,EAAE,EAAE;MAAN,CAHnB,EAIlB,KAAKL,WAAL,EAJkB,CAAnB;IAMA;;;WAED,SAAAgB,eAAeF,OAAf,EAAwB;MACvB,OAAO,KAAKF,MAAL,CAAY,UAAZ,EAAwBE,OAAxB,CAAP;IACA;;;WAED,SAAAG,oBAAoBH,OAApB,EAA6B;MAC5B,OAAO,KAAKF,MAAL,CAAY,eAAZ,EAA6BE,OAA7B,CAAP;IACA;;;WAED,SAAAI,OAAOJ,OAAP,EAAgB;MACf,OAAO,KAAKF,MAAL,CAAY,SAAZ,EAAuBE,OAAvB,CAAP;IACA;;;;SAtGmBzB,W;AAyGrB,IAAM8B,aAAa,GAAG,SAAhBA,aAAgBA,CAACC,KAAD;EAAA,OAAW,aAAaC,IAAb,CAAkBD,KAAlB,CAAX;AAAA,CAAtB;AAEA,SAASxB,+BAATA,CAAyCN,2BAAzC,EAAsEgC,YAAtE,EAAoF;EACnF,IAAIzB,OAAJ;EACA,IAAIC,kBAAJ;EAEA,IAAMN,QAAQ,GAAG,IAAIV,QAAJ,CAAawC,YAAb,CAAjB,CAJmF,CAKnF;EACA;;EACA,IAAIH,aAAa,CAAC7B,2BAAD,CAAjB,EAAgD;IAC/CO,OAAO,GAAGP,2BAAV;IACAE,QAAQ,CAAC+B,mBAAT,CAA6B1B,OAA7B;IACAC,kBAAkB,GAAGN,QAAQ,CAACM,kBAAT,EAArB;EACA,CAJD,MAIO;IACNA,kBAAkB,GAAGR,2BAArB;IACA;;IACA,IAAIF,+BAAJ,EAAqC;MACpC,IAAII,QAAQ,CAACgB,0BAAT,CAAoCV,kBAApC,CAAJ,EAA6D;QAC5DD,OAAO,GAAG,KAAV;MACA;IACD;EACD;EAED,OAAO;IACNA,OAAO,EAAPA,OADM;IAENC,kBAAkB,EAAlBA;EAFM,CAAP;AAIA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}