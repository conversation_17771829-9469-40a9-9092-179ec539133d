{"ast": null, "code": "export const ADD_SOURCE = 'dnd-core/ADD_SOURCE';\nexport const ADD_TARGET = 'dnd-core/ADD_TARGET';\nexport const REMOVE_SOURCE = 'dnd-core/REMOVE_SOURCE';\nexport const REMOVE_TARGET = 'dnd-core/REMOVE_TARGET';\nexport function addSource(sourceId) {\n  return {\n    type: ADD_SOURCE,\n    payload: {\n      sourceId\n    }\n  };\n}\nexport function addTarget(targetId) {\n  return {\n    type: ADD_TARGET,\n    payload: {\n      targetId\n    }\n  };\n}\nexport function removeSource(sourceId) {\n  return {\n    type: REMOVE_SOURCE,\n    payload: {\n      sourceId\n    }\n  };\n}\nexport function removeTarget(targetId) {\n  return {\n    type: REMOVE_TARGET,\n    payload: {\n      targetId\n    }\n  };\n}", "map": {"version": 3, "names": ["ADD_SOURCE", "ADD_TARGET", "REMOVE_SOURCE", "REMOVE_TARGET", "addSource", "sourceId", "type", "payload", "addTarget", "targetId", "removeSource", "remove<PERSON>arget"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\dnd-core\\src\\actions\\registry.ts"], "sourcesContent": ["import type { Action, SourceIdPayload, TargetIdPayload } from '../interfaces.js'\n\nexport const ADD_SOURCE = 'dnd-core/ADD_SOURCE'\nexport const ADD_TARGET = 'dnd-core/ADD_TARGET'\nexport const REMOVE_SOURCE = 'dnd-core/REMOVE_SOURCE'\nexport const REMOVE_TARGET = 'dnd-core/REMOVE_TARGET'\n\nexport function addSource(sourceId: string): Action<SourceIdPayload> {\n\treturn {\n\t\ttype: ADD_SOURCE,\n\t\tpayload: {\n\t\t\tsourceId,\n\t\t},\n\t}\n}\n\nexport function addTarget(targetId: string): Action<TargetIdPayload> {\n\treturn {\n\t\ttype: ADD_TARGET,\n\t\tpayload: {\n\t\t\ttargetId,\n\t\t},\n\t}\n}\n\nexport function removeSource(sourceId: string): Action<SourceIdPayload> {\n\treturn {\n\t\ttype: REMOVE_SOURCE,\n\t\tpayload: {\n\t\t\tsourceId,\n\t\t},\n\t}\n}\n\nexport function removeTarget(targetId: string): Action<TargetIdPayload> {\n\treturn {\n\t\ttype: REMOVE_TARGET,\n\t\tpayload: {\n\t\t\ttargetId,\n\t\t},\n\t}\n}\n"], "mappings": "AAEA,OAAO,MAAMA,UAAU,GAAG,qBAAqB;AAC/C,OAAO,MAAMC,UAAU,GAAG,qBAAqB;AAC/C,OAAO,MAAMC,aAAa,GAAG,wBAAwB;AACrD,OAAO,MAAMC,aAAa,GAAG,wBAAwB;AAErD,OAAO,SAASC,SAASA,CAACC,QAAgB,EAA2B;EACpE,OAAO;IACNC,IAAI,EAAEN,UAAU;IAChBO,OAAO,EAAE;MACRF;;GAED;;AAGF,OAAO,SAASG,SAASA,CAACC,QAAgB,EAA2B;EACpE,OAAO;IACNH,IAAI,EAAEL,UAAU;IAChBM,OAAO,EAAE;MACRE;;GAED;;AAGF,OAAO,SAASC,YAAYA,CAACL,QAAgB,EAA2B;EACvE,OAAO;IACNC,IAAI,EAAEJ,aAAa;IACnBK,OAAO,EAAE;MACRF;;GAED;;AAGF,OAAO,SAASM,YAAYA,CAACF,QAAgB,EAA2B;EACvE,OAAO;IACNH,IAAI,EAAEH,aAAa;IACnBI,OAAO,EAAE;MACRE;;GAED"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}