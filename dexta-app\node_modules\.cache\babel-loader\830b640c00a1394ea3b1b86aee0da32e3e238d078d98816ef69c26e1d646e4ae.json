{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n/**\n * List of boolean attributes\n * These attributes should have their React attribute value set to be the same as their name\n * E.g. <input disabled> = <input disabled>\n *      <input disabled=\"\"> = <input disabled>\n *      <input disabled=\"disabled\"> = <input disabled>\n * @type {Array}\n */\nexports.default = ['allowfullScreen', 'async', 'autoplay', 'capture', 'checked', 'controls', 'default', 'defer', 'disabled', 'formnovalidate', 'hidden', 'loop', 'multiple', 'muted', 'novalidate', 'open', 'playsinline', 'readonly', 'required', 'reversed', 'scoped', 'seamless', 'selected', 'itemscope'];", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/react-html-parser/lib/dom/attributes/BooleanAttributes.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n/**\n * List of boolean attributes\n * These attributes should have their React attribute value set to be the same as their name\n * E.g. <input disabled> = <input disabled>\n *      <input disabled=\"\"> = <input disabled>\n *      <input disabled=\"disabled\"> = <input disabled>\n * @type {Array}\n */\nexports.default = ['allowfullScreen', 'async', 'autoplay', 'capture', 'checked', 'controls', 'default', 'defer', 'disabled', 'formnovalidate', 'hidden', 'loop', 'multiple', 'muted', 'novalidate', 'open', 'playsinline', 'readonly', 'required', 'reversed', 'scoped', 'seamless', 'selected', 'itemscope'];"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAD,OAAO,CAACE,OAAO,GAAG,CAAC,iBAAiB,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}