{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nimport isValidNumber from '../isValid.js';\nimport parseDigits from '../helpers/parseDigits.js';\nimport matchPhoneNumberStringAgainstPhoneNumber from './matchPhoneNumberStringAgainstPhoneNumber.js';\nimport Metadata from '../metadata.js';\nimport getCountryByCallingCode from '../helpers/getCountryByCallingCode.js';\nimport { chooseFormatForNumber } from '../format.js';\nimport { startsWith, endsWith } from './util.js';\n/**\r\n * Leniency when finding potential phone numbers in text segments\r\n * The levels here are ordered in increasing strictness.\r\n */\n\nexport default {\n  /**\r\n   * Phone numbers accepted are \"possible\", but not necessarily \"valid\".\r\n   */\n  POSSIBLE: function POSSIBLE(phoneNumber, _ref) {\n    var candidate = _ref.candidate,\n      metadata = _ref.metadata;\n    return true;\n  },\n  /**\r\n   * Phone numbers accepted are \"possible\" and \"valid\".\r\n   * Numbers written in national format must have their national-prefix\r\n   * present if it is usually written for a number of this type.\r\n   */\n  VALID: function VALID(phoneNumber, _ref2) {\n    var candidate = _ref2.candidate,\n      defaultCountry = _ref2.defaultCountry,\n      metadata = _ref2.metadata;\n    if (!phoneNumber.isValid() || !containsOnlyValidXChars(phoneNumber, candidate, metadata)) {\n      return false;\n    } // Skipped for simplicity.\n    // return isNationalPrefixPresentIfRequired(phoneNumber, { defaultCountry, metadata })\n\n    return true;\n  },\n  /**\r\n   * Phone numbers accepted are \"valid\" and\r\n   * are grouped in a possible way for this locale. For example, a US number written as\r\n   * \"65 02 53 00 00\" and \"650253 0000\" are not accepted at this leniency level, whereas\r\n   * \"************\", \"650 2530000\" or \"6502530000\" are.\r\n   * Numbers with more than one '/' symbol in the national significant number\r\n   * are also dropped at this level.\r\n   *\r\n   * Warning: This level might result in lower coverage especially for regions outside of\r\n   * country code \"+1\". If you are not sure about which level to use,\r\n   * email the <NAME_EMAIL>.\r\n   */\n  STRICT_GROUPING: function STRICT_GROUPING(phoneNumber, _ref3) {\n    var candidate = _ref3.candidate,\n      defaultCountry = _ref3.defaultCountry,\n      metadata = _ref3.metadata,\n      regExpCache = _ref3.regExpCache;\n    if (!phoneNumber.isValid() || !containsOnlyValidXChars(phoneNumber, candidate, metadata) || containsMoreThanOneSlashInNationalNumber(phoneNumber, candidate) || !isNationalPrefixPresentIfRequired(phoneNumber, {\n      defaultCountry: defaultCountry,\n      metadata: metadata\n    })) {\n      return false;\n    }\n    return checkNumberGroupingIsValid(phoneNumber, candidate, metadata, allNumberGroupsRemainGrouped, regExpCache);\n  },\n  /**\r\n   * Phone numbers accepted are \"valid\" and are grouped in the same way\r\n   * that we would have formatted it, or as a single block.\r\n   * For example, a US number written as \"650 2530000\" is not accepted\r\n   * at this leniency level, whereas \"************\" or \"6502530000\" are.\r\n   * Numbers with more than one '/' symbol are also dropped at this level.\r\n   *\r\n   * Warning: This level might result in lower coverage especially for regions outside of\r\n   * country code \"+1\". If you are not sure about which level to use, email the discussion group\r\n   * <EMAIL>.\r\n   */\n  EXACT_GROUPING: function EXACT_GROUPING(phoneNumber, _ref4) {\n    var candidate = _ref4.candidate,\n      defaultCountry = _ref4.defaultCountry,\n      metadata = _ref4.metadata,\n      regExpCache = _ref4.regExpCache;\n    if (!phoneNumber.isValid() || !containsOnlyValidXChars(phoneNumber, candidate, metadata) || containsMoreThanOneSlashInNationalNumber(phoneNumber, candidate) || !isNationalPrefixPresentIfRequired(phoneNumber, {\n      defaultCountry: defaultCountry,\n      metadata: metadata\n    })) {\n      return false;\n    }\n    return checkNumberGroupingIsValid(phoneNumber, candidate, metadata, allNumberGroupsAreExactlyPresent, regExpCache);\n  }\n};\nfunction containsOnlyValidXChars(phoneNumber, candidate, metadata) {\n  // The characters 'x' and 'X' can be (1) a carrier code, in which case they always precede the\n  // national significant number or (2) an extension sign, in which case they always precede the\n  // extension number. We assume a carrier code is more than 1 digit, so the first case has to\n  // have more than 1 consecutive 'x' or 'X', whereas the second case can only have exactly 1 'x'\n  // or 'X'. We ignore the character if it appears as the last character of the string.\n  for (var index = 0; index < candidate.length - 1; index++) {\n    var charAtIndex = candidate.charAt(index);\n    if (charAtIndex === 'x' || charAtIndex === 'X') {\n      var charAtNextIndex = candidate.charAt(index + 1);\n      if (charAtNextIndex === 'x' || charAtNextIndex === 'X') {\n        // This is the carrier code case, in which the 'X's always precede the national\n        // significant number.\n        index++;\n        if (matchPhoneNumberStringAgainstPhoneNumber(candidate.substring(index), phoneNumber, metadata) !== 'NSN_MATCH') {\n          return false;\n        } // This is the extension sign case, in which the 'x' or 'X' should always precede the\n        // extension number.\n      } else {\n        var ext = parseDigits(candidate.substring(index));\n        if (ext) {\n          if (phoneNumber.ext !== ext) {\n            return false;\n          }\n        } else {\n          if (phoneNumber.ext) {\n            return false;\n          }\n        }\n      }\n    }\n  }\n  return true;\n}\nfunction isNationalPrefixPresentIfRequired(phoneNumber, _ref5) {\n  var defaultCountry = _ref5.defaultCountry,\n    _metadata = _ref5.metadata;\n\n  // First, check how we deduced the country code. If it was written in international format, then\n  // the national prefix is not required.\n  if (phoneNumber.__countryCallingCodeSource !== 'FROM_DEFAULT_COUNTRY') {\n    return true;\n  }\n  var metadata = new Metadata(_metadata);\n  metadata.selectNumberingPlan(phoneNumber.countryCallingCode);\n  var phoneNumberRegion = phoneNumber.country || getCountryByCallingCode(phoneNumber.countryCallingCode, {\n    nationalNumber: phoneNumber.nationalNumber,\n    defaultCountry: defaultCountry,\n    metadata: metadata\n  }); // Check if a national prefix should be present when formatting this number.\n\n  var nationalNumber = phoneNumber.nationalNumber;\n  var format = chooseFormatForNumber(metadata.numberingPlan.formats(), nationalNumber); // To do this, we check that a national prefix formatting rule was present\n  // and that it wasn't just the first-group symbol ($1) with punctuation.\n\n  if (format.nationalPrefixFormattingRule()) {\n    if (metadata.numberingPlan.nationalPrefixIsOptionalWhenFormattingInNationalFormat()) {\n      // The national-prefix is optional in these cases, so we don't need to check if it was present.\n      return true;\n    }\n    if (!format.usesNationalPrefix()) {\n      // National Prefix not needed for this number.\n      return true;\n    }\n    return Boolean(phoneNumber.nationalPrefix);\n  }\n  return true;\n}\nexport function containsMoreThanOneSlashInNationalNumber(phoneNumber, candidate) {\n  var firstSlashInBodyIndex = candidate.indexOf('/');\n  if (firstSlashInBodyIndex < 0) {\n    // No slashes, this is okay.\n    return false;\n  } // Now look for a second one.\n\n  var secondSlashInBodyIndex = candidate.indexOf('/', firstSlashInBodyIndex + 1);\n  if (secondSlashInBodyIndex < 0) {\n    // Only one slash, this is okay.\n    return false;\n  } // If the first slash is after the country calling code, this is permitted.\n\n  var candidateHasCountryCode = phoneNumber.__countryCallingCodeSource === 'FROM_NUMBER_WITH_PLUS_SIGN' || phoneNumber.__countryCallingCodeSource === 'FROM_NUMBER_WITHOUT_PLUS_SIGN';\n  if (candidateHasCountryCode && parseDigits(candidate.substring(0, firstSlashInBodyIndex)) === phoneNumber.countryCallingCode) {\n    // Any more slashes and this is illegal.\n    return candidate.slice(secondSlashInBodyIndex + 1).indexOf('/') >= 0;\n  }\n  return true;\n}\nfunction checkNumberGroupingIsValid(number, candidate, metadata, checkGroups, regExpCache) {\n  throw new Error('This part of code hasn\\'t been ported');\n  var normalizedCandidate = normalizeDigits(candidate, true\n  /* keep non-digits */);\n\n  var formattedNumberGroups = getNationalNumberGroups(metadata, number, null);\n  if (checkGroups(metadata, number, normalizedCandidate, formattedNumberGroups)) {\n    return true;\n  } // If this didn't pass, see if there are any alternate formats that match, and try them instead.\n\n  var alternateFormats = MetadataManager.getAlternateFormatsForCountry(number.getCountryCode());\n  var nationalSignificantNumber = util.getNationalSignificantNumber(number);\n  if (alternateFormats) {\n    for (var _iterator = _createForOfIteratorHelperLoose(alternateFormats.numberFormats()), _step; !(_step = _iterator()).done;) {\n      var alternateFormat = _step.value;\n      if (alternateFormat.leadingDigitsPatterns().length > 0) {\n        // There is only one leading digits pattern for alternate formats.\n        var leadingDigitsRegExp = regExpCache.getPatternForRegExp('^' + alternateFormat.leadingDigitsPatterns()[0]);\n        if (!leadingDigitsRegExp.test(nationalSignificantNumber)) {\n          // Leading digits don't match; try another one.\n          continue;\n        }\n      }\n      formattedNumberGroups = getNationalNumberGroups(metadata, number, alternateFormat);\n      if (checkGroups(metadata, number, normalizedCandidate, formattedNumberGroups)) {\n        return true;\n      }\n    }\n  }\n  return false;\n}\n/**\r\n * Helper method to get the national-number part of a number, formatted without any national\r\n * prefix, and return it as a set of digit blocks that would be formatted together following\r\n * standard formatting rules.\r\n */\n\nfunction getNationalNumberGroups(metadata, number, formattingPattern) {\n  throw new Error('This part of code hasn\\'t been ported');\n  if (formattingPattern) {\n    // We format the NSN only, and split that according to the separator.\n    var nationalSignificantNumber = util.getNationalSignificantNumber(number);\n    return util.formatNsnUsingPattern(nationalSignificantNumber, formattingPattern, 'RFC3966', metadata).split('-');\n  } // This will be in the format +CC-DG1-DG2-DGX;ext=EXT where DG1..DGX represents groups of digits.\n\n  var rfc3966Format = formatNumber(number, 'RFC3966', metadata); // We remove the extension part from the formatted string before splitting it into different\n  // groups.\n\n  var endIndex = rfc3966Format.indexOf(';');\n  if (endIndex < 0) {\n    endIndex = rfc3966Format.length;\n  } // The country-code will have a '-' following it.\n\n  var startIndex = rfc3966Format.indexOf('-') + 1;\n  return rfc3966Format.slice(startIndex, endIndex).split('-');\n}\nfunction allNumberGroupsAreExactlyPresent(metadata, number, normalizedCandidate, formattedNumberGroups) {\n  throw new Error('This part of code hasn\\'t been ported');\n  var candidateGroups = normalizedCandidate.split(NON_DIGITS_PATTERN); // Set this to the last group, skipping it if the number has an extension.\n\n  var candidateNumberGroupIndex = number.hasExtension() ? candidateGroups.length - 2 : candidateGroups.length - 1; // First we check if the national significant number is formatted as a block.\n  // We use contains and not equals, since the national significant number may be present with\n  // a prefix such as a national number prefix, or the country code itself.\n\n  if (candidateGroups.length == 1 || candidateGroups[candidateNumberGroupIndex].contains(util.getNationalSignificantNumber(number))) {\n    return true;\n  } // Starting from the end, go through in reverse, excluding the first group, and check the\n  // candidate and number groups are the same.\n\n  var formattedNumberGroupIndex = formattedNumberGroups.length - 1;\n  while (formattedNumberGroupIndex > 0 && candidateNumberGroupIndex >= 0) {\n    if (candidateGroups[candidateNumberGroupIndex] !== formattedNumberGroups[formattedNumberGroupIndex]) {\n      return false;\n    }\n    formattedNumberGroupIndex--;\n    candidateNumberGroupIndex--;\n  } // Now check the first group. There may be a national prefix at the start, so we only check\n  // that the candidate group ends with the formatted number group.\n\n  return candidateNumberGroupIndex >= 0 && endsWith(candidateGroups[candidateNumberGroupIndex], formattedNumberGroups[0]);\n}\nfunction allNumberGroupsRemainGrouped(metadata, number, normalizedCandidate, formattedNumberGroups) {\n  throw new Error('This part of code hasn\\'t been ported');\n  var fromIndex = 0;\n  if (number.getCountryCodeSource() !== CountryCodeSource.FROM_DEFAULT_COUNTRY) {\n    // First skip the country code if the normalized candidate contained it.\n    var countryCode = String(number.getCountryCode());\n    fromIndex = normalizedCandidate.indexOf(countryCode) + countryCode.length();\n  } // Check each group of consecutive digits are not broken into separate groupings in the\n  // {@code normalizedCandidate} string.\n\n  for (var i = 0; i < formattedNumberGroups.length; i++) {\n    // Fails if the substring of {@code normalizedCandidate} starting from {@code fromIndex}\n    // doesn't contain the consecutive digits in formattedNumberGroups[i].\n    fromIndex = normalizedCandidate.indexOf(formattedNumberGroups[i], fromIndex);\n    if (fromIndex < 0) {\n      return false;\n    } // Moves {@code fromIndex} forward.\n\n    fromIndex += formattedNumberGroups[i].length();\n    if (i == 0 && fromIndex < normalizedCandidate.length()) {\n      // We are at the position right after the NDC. We get the region used for formatting\n      // information based on the country code in the phone number, rather than the number itself,\n      // as we do not need to distinguish between different countries with the same country\n      // calling code and this is faster.\n      var region = util.getRegionCodeForCountryCode(number.getCountryCode());\n      if (util.getNddPrefixForRegion(region, true) != null && Character.isDigit(normalizedCandidate.charAt(fromIndex))) {\n        // This means there is no formatting symbol after the NDC. In this case, we only\n        // accept the number if there is no formatting symbol at all in the number, except\n        // for extensions. This is only important for countries with national prefixes.\n        var nationalSignificantNumber = util.getNationalSignificantNumber(number);\n        return startsWith(normalizedCandidate.slice(fromIndex - formattedNumberGroups[i].length), nationalSignificantNumber);\n      }\n    }\n  } // The check here makes sure that we haven't mistakenly already used the extension to\n  // match the last group of the subscriber number. Note the extension cannot have\n  // formatting in-between digits.\n\n  return normalizedCandidate.slice(fromIndex).contains(number.getExtension());\n}", "map": {"version": 3, "names": ["isValidNumber", "parseDigits", "matchPhoneNumberStringAgainstPhoneNumber", "<PERSON><PERSON><PERSON>", "getCountryByCallingCode", "chooseFormatForNumber", "startsWith", "endsWith", "POSSIBLE", "phoneNumber", "_ref", "candidate", "metadata", "VALID", "_ref2", "defaultCountry", "<PERSON><PERSON><PERSON><PERSON>", "containsOnlyValidXChars", "STRICT_GROUPING", "_ref3", "regExpCache", "containsMoreThanOneSlashInNationalNumber", "isNationalPrefixPresentIfRequired", "checkNumberGroupingIsValid", "allNumberGroupsRemainGrouped", "EXACT_GROUPING", "_ref4", "allNumberGroupsAreExactlyPresent", "index", "length", "charAtIndex", "char<PERSON>t", "charAtNextIndex", "substring", "ext", "_ref5", "_metadata", "__countryCallingCodeSource", "selectNumberingPlan", "countryCallingCode", "phoneNumberRegion", "country", "nationalNumber", "format", "numberingPlan", "formats", "nationalPrefixFormattingRule", "nationalPrefixIsOptionalWhenFormattingInNationalFormat", "usesNationalPrefix", "Boolean", "nationalPrefix", "firstSlashInBodyIndex", "indexOf", "secondSlashInBodyIndex", "candidateHasCountryCode", "slice", "number", "checkGroups", "Error", "normalizedCandidate", "normalizeDigits", "formattedNumberGroups", "getNationalNumberGroups", "alternateFormats", "MetadataManager", "getAlternateFormatsForCountry", "getCountryCode", "nationalSignificantNumber", "util", "getNationalSignificantNumber", "_iterator", "_createForOfIteratorHelperLoose", "numberFormats", "_step", "done", "alternateFormat", "value", "leadingDigitsPatterns", "leadingDigitsRegExp", "getPatternForRegExp", "test", "formattingPattern", "formatNsnUsingPattern", "split", "rfc3966Format", "formatNumber", "endIndex", "startIndex", "candidateGroups", "NON_DIGITS_PATTERN", "candidateNumberGroupIndex", "hasExtension", "contains", "formattedNumberGroupIndex", "fromIndex", "getCountryCodeSource", "CountryCodeSource", "FROM_DEFAULT_COUNTRY", "countryCode", "String", "i", "region", "getRegionCodeForCountryCode", "getNddPrefixForRegion", "Character", "isDigit", "getExtension"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\findNumbers\\Leniency.js"], "sourcesContent": ["import isValidNumber from '../isValid.js'\r\nimport parseDigits from '../helpers/parseDigits.js'\r\nimport matchPhoneNumberStringAgainstPhoneNumber from './matchPhoneNumberStringAgainstPhoneNumber.js'\r\nimport Metadata from '../metadata.js'\r\nimport getCountryByCallingCode from '../helpers/getCountryByCallingCode.js'\r\nimport { chooseFormatForNumber } from '../format.js'\r\n\r\nimport {\r\n\tstartsWith,\r\n\tendsWith\r\n} from './util.js'\r\n\r\n/**\r\n * Leniency when finding potential phone numbers in text segments\r\n * The levels here are ordered in increasing strictness.\r\n */\r\nexport default\r\n{\r\n\t/**\r\n\t * Phone numbers accepted are \"possible\", but not necessarily \"valid\".\r\n\t */\r\n\tPOSSIBLE(phoneNumber, { candidate, metadata })\r\n\t{\r\n\t\treturn true\r\n\t},\r\n\r\n\t/**\r\n\t * Phone numbers accepted are \"possible\" and \"valid\".\r\n\t * Numbers written in national format must have their national-prefix\r\n\t * present if it is usually written for a number of this type.\r\n\t */\r\n\tVALID(phoneNumber, { candidate, defaultCountry, metadata })\r\n\t{\r\n\t\tif (\r\n\t\t\t!phoneNumber.isValid() ||\r\n\t\t\t!containsOnlyValidXChars(phoneNumber, candidate, metadata)\r\n\t\t)\r\n\t\t{\r\n\t\t\treturn false\r\n\t\t}\r\n\r\n\t\t// Skipped for simplicity.\r\n\t\t// return isNationalPrefixPresentIfRequired(phoneNumber, { defaultCountry, metadata })\r\n\t\treturn true\r\n\t},\r\n\r\n\t/**\r\n\t * Phone numbers accepted are \"valid\" and\r\n\t * are grouped in a possible way for this locale. For example, a US number written as\r\n\t * \"65 02 53 00 00\" and \"650253 0000\" are not accepted at this leniency level, whereas\r\n\t * \"************\", \"650 2530000\" or \"6502530000\" are.\r\n\t * Numbers with more than one '/' symbol in the national significant number\r\n\t * are also dropped at this level.\r\n\t *\r\n\t * Warning: This level might result in lower coverage especially for regions outside of\r\n\t * country code \"+1\". If you are not sure about which level to use,\r\n\t * email the <NAME_EMAIL>.\r\n\t */\r\n\tSTRICT_GROUPING(phoneNumber, { candidate, defaultCountry, metadata, regExpCache })\r\n\t{\r\n\t\tif (\r\n\t\t\t!phoneNumber.isValid() ||\r\n\t\t\t!containsOnlyValidXChars(phoneNumber, candidate, metadata) ||\r\n\t\t\tcontainsMoreThanOneSlashInNationalNumber(phoneNumber, candidate) ||\r\n\t\t\t!isNationalPrefixPresentIfRequired(phoneNumber, { defaultCountry, metadata })\r\n\t\t)\r\n\t\t{\r\n\t\t\treturn false\r\n\t\t}\r\n\r\n\t\treturn checkNumberGroupingIsValid\r\n\t\t(\r\n\t\t\tphoneNumber,\r\n\t\t\tcandidate,\r\n\t\t\tmetadata,\r\n\t\t\tallNumberGroupsRemainGrouped,\r\n\t\t\tregExpCache\r\n\t\t)\r\n\t},\r\n\r\n\t/**\r\n\t * Phone numbers accepted are \"valid\" and are grouped in the same way\r\n\t * that we would have formatted it, or as a single block.\r\n\t * For example, a US number written as \"650 2530000\" is not accepted\r\n\t * at this leniency level, whereas \"************\" or \"6502530000\" are.\r\n\t * Numbers with more than one '/' symbol are also dropped at this level.\r\n\t *\r\n\t * Warning: This level might result in lower coverage especially for regions outside of\r\n\t * country code \"+1\". If you are not sure about which level to use, email the discussion group\r\n\t * <EMAIL>.\r\n\t */\r\n\tEXACT_GROUPING(phoneNumber, { candidate, defaultCountry, metadata, regExpCache })\r\n\t{\r\n\t\tif (\r\n\t\t\t!phoneNumber.isValid() ||\r\n\t\t\t!containsOnlyValidXChars(phoneNumber, candidate, metadata) ||\r\n\t\t\tcontainsMoreThanOneSlashInNationalNumber(phoneNumber, candidate) ||\r\n\t\t\t!isNationalPrefixPresentIfRequired(phoneNumber, { defaultCountry, metadata })\r\n\t\t)\r\n\t\t{\r\n\t\t\treturn false\r\n\t\t}\r\n\r\n\t\treturn checkNumberGroupingIsValid\r\n\t\t(\r\n\t\t\tphoneNumber,\r\n\t\t\tcandidate,\r\n\t\t\tmetadata,\r\n\t\t\tallNumberGroupsAreExactlyPresent,\r\n\t\t\tregExpCache\r\n\t\t)\r\n\t}\r\n}\r\n\r\nfunction containsOnlyValidXChars(phoneNumber, candidate, metadata)\r\n{\r\n\t// The characters 'x' and 'X' can be (1) a carrier code, in which case they always precede the\r\n\t// national significant number or (2) an extension sign, in which case they always precede the\r\n\t// extension number. We assume a carrier code is more than 1 digit, so the first case has to\r\n\t// have more than 1 consecutive 'x' or 'X', whereas the second case can only have exactly 1 'x'\r\n\t// or 'X'. We ignore the character if it appears as the last character of the string.\r\n\tfor (let index = 0; index < candidate.length - 1; index++)\r\n\t{\r\n\t\tconst charAtIndex = candidate.charAt(index)\r\n\r\n\t\tif (charAtIndex === 'x' || charAtIndex === 'X')\r\n\t\t{\r\n\t\t\tconst charAtNextIndex = candidate.charAt(index + 1)\r\n\r\n\t\t\tif (charAtNextIndex === 'x' || charAtNextIndex === 'X')\r\n\t\t\t{\r\n\t\t\t\t// This is the carrier code case, in which the 'X's always precede the national\r\n\t\t\t\t// significant number.\r\n\t\t\t\tindex++\r\n\t\t\t\tif (matchPhoneNumberStringAgainstPhoneNumber(candidate.substring(index), phoneNumber, metadata) !== 'NSN_MATCH')\r\n\t\t\t\t{\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\t// This is the extension sign case, in which the 'x' or 'X' should always precede the\r\n\t\t\t\t// extension number.\r\n\t\t\t}\r\n\t\t\telse {\r\n\t\t\t\tconst ext = parseDigits(candidate.substring(index))\r\n\t\t\t\tif (ext) {\r\n\t\t\t\t\tif (phoneNumber.ext !== ext)  {\r\n\t\t\t\t\t\treturn false\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (phoneNumber.ext) {\r\n\t\t\t\t\t\treturn false\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn true\r\n}\r\n\r\nfunction isNationalPrefixPresentIfRequired(phoneNumber, { defaultCountry, metadata: _metadata })\r\n{\r\n\t// First, check how we deduced the country code. If it was written in international format, then\r\n\t// the national prefix is not required.\r\n\tif (phoneNumber.__countryCallingCodeSource !== 'FROM_DEFAULT_COUNTRY')\r\n\t{\r\n\t\treturn true\r\n\t}\r\n\r\n\tconst metadata = new Metadata(_metadata)\r\n\tmetadata.selectNumberingPlan(phoneNumber.countryCallingCode)\r\n\r\n\tconst phoneNumberRegion = phoneNumber.country || getCountryByCallingCode(phoneNumber.countryCallingCode, {\r\n\t\tnationalNumber: phoneNumber.nationalNumber,\r\n\t\tdefaultCountry,\r\n\t\tmetadata\r\n\t})\r\n\r\n\t// Check if a national prefix should be present when formatting this number.\r\n\tconst nationalNumber = phoneNumber.nationalNumber\r\n\tconst format = chooseFormatForNumber(metadata.numberingPlan.formats(), nationalNumber)\r\n\r\n\t// To do this, we check that a national prefix formatting rule was present\r\n\t// and that it wasn't just the first-group symbol ($1) with punctuation.\r\n\tif (format.nationalPrefixFormattingRule())\r\n\t{\r\n\t\tif (metadata.numberingPlan.nationalPrefixIsOptionalWhenFormattingInNationalFormat())\r\n\t\t{\r\n\t\t\t// The national-prefix is optional in these cases, so we don't need to check if it was present.\r\n\t\t\treturn true\r\n\t\t}\r\n\r\n\t\tif (!format.usesNationalPrefix())\r\n\t\t{\r\n\t\t\t// National Prefix not needed for this number.\r\n\t\t\treturn true\r\n\t\t}\r\n\r\n\t\treturn Boolean(phoneNumber.nationalPrefix)\r\n\t}\r\n\r\n\treturn true\r\n}\r\n\r\nexport function containsMoreThanOneSlashInNationalNumber(phoneNumber, candidate)\r\n{\r\n\tconst firstSlashInBodyIndex = candidate.indexOf('/')\r\n\tif (firstSlashInBodyIndex < 0)\r\n\t{\r\n\t\t// No slashes, this is okay.\r\n\t\treturn false\r\n\t}\r\n\r\n\t// Now look for a second one.\r\n\tconst secondSlashInBodyIndex = candidate.indexOf('/', firstSlashInBodyIndex + 1)\r\n\tif (secondSlashInBodyIndex < 0)\r\n\t{\r\n\t\t// Only one slash, this is okay.\r\n\t\treturn false\r\n\t}\r\n\r\n\t// If the first slash is after the country calling code, this is permitted.\r\n\tconst candidateHasCountryCode =\r\n\t\t\tphoneNumber.__countryCallingCodeSource === 'FROM_NUMBER_WITH_PLUS_SIGN' ||\r\n\t\t\tphoneNumber.__countryCallingCodeSource === 'FROM_NUMBER_WITHOUT_PLUS_SIGN'\r\n\r\n\tif (candidateHasCountryCode && parseDigits(candidate.substring(0, firstSlashInBodyIndex)) === phoneNumber.countryCallingCode)\r\n\t{\r\n\t\t// Any more slashes and this is illegal.\r\n\t\treturn candidate.slice(secondSlashInBodyIndex + 1).indexOf('/') >= 0\r\n\t}\r\n\r\n\treturn true\r\n}\r\n\r\nfunction checkNumberGroupingIsValid(\r\n\tnumber,\r\n\tcandidate,\r\n\tmetadata,\r\n\tcheckGroups,\r\n\tregExpCache\r\n) {\r\n\tthrow new Error('This part of code hasn\\'t been ported')\r\n\r\n\tconst normalizedCandidate = normalizeDigits(candidate, true /* keep non-digits */)\r\n\tlet formattedNumberGroups = getNationalNumberGroups(metadata, number, null)\r\n\tif (checkGroups(metadata, number, normalizedCandidate, formattedNumberGroups)) {\r\n\t\treturn true\r\n\t}\r\n\r\n\t// If this didn't pass, see if there are any alternate formats that match, and try them instead.\r\n\tconst alternateFormats = MetadataManager.getAlternateFormatsForCountry(number.getCountryCode())\r\n\tconst nationalSignificantNumber = util.getNationalSignificantNumber(number)\r\n\r\n\tif (alternateFormats) {\r\n\t\tfor (const alternateFormat of alternateFormats.numberFormats()) {\r\n\t\t\tif (alternateFormat.leadingDigitsPatterns().length > 0) {\r\n\t\t\t\t// There is only one leading digits pattern for alternate formats.\r\n\t\t\t\tconst leadingDigitsRegExp = regExpCache.getPatternForRegExp('^' + alternateFormat.leadingDigitsPatterns()[0])\r\n\t\t\t\tif (!leadingDigitsRegExp.test(nationalSignificantNumber)) {\r\n\t\t\t\t\t// Leading digits don't match; try another one.\r\n\t\t\t\t\tcontinue\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tformattedNumberGroups = getNationalNumberGroups(metadata, number, alternateFormat)\r\n\t\t\tif (checkGroups(metadata, number, normalizedCandidate, formattedNumberGroups)) {\r\n\t\t\t\treturn true\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn false\r\n}\r\n\r\n/**\r\n * Helper method to get the national-number part of a number, formatted without any national\r\n * prefix, and return it as a set of digit blocks that would be formatted together following\r\n * standard formatting rules.\r\n */\r\nfunction getNationalNumberGroups(\r\n\tmetadata,\r\n\tnumber,\r\n\tformattingPattern\r\n) {\r\n\tthrow new Error('This part of code hasn\\'t been ported')\r\n\r\n\tif (formattingPattern) {\r\n\t\t// We format the NSN only, and split that according to the separator.\r\n\t\tconst nationalSignificantNumber = util.getNationalSignificantNumber(number)\r\n\t\treturn util.formatNsnUsingPattern(nationalSignificantNumber,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tformattingPattern, 'RFC3966', metadata).split('-')\r\n\t}\r\n\r\n\t// This will be in the format +CC-DG1-DG2-DGX;ext=EXT where DG1..DGX represents groups of digits.\r\n\tconst rfc3966Format = formatNumber(number, 'RFC3966', metadata)\r\n\r\n\t// We remove the extension part from the formatted string before splitting it into different\r\n\t// groups.\r\n\tlet endIndex = rfc3966Format.indexOf(';')\r\n\tif (endIndex < 0) {\r\n\t\tendIndex = rfc3966Format.length\r\n\t}\r\n\r\n\t// The country-code will have a '-' following it.\r\n\tconst startIndex = rfc3966Format.indexOf('-') + 1\r\n\treturn rfc3966Format.slice(startIndex, endIndex).split('-')\r\n}\r\n\r\nfunction allNumberGroupsAreExactlyPresent\r\n(\r\n\tmetadata,\r\n\tnumber,\r\n\tnormalizedCandidate,\r\n\tformattedNumberGroups\r\n)\r\n{\r\n\tthrow new Error('This part of code hasn\\'t been ported')\r\n\r\n\tconst candidateGroups = normalizedCandidate.split(NON_DIGITS_PATTERN)\r\n\r\n\t// Set this to the last group, skipping it if the number has an extension.\r\n\tlet candidateNumberGroupIndex =\r\n\t\t\tnumber.hasExtension() ? candidateGroups.length - 2 : candidateGroups.length - 1\r\n\r\n\t// First we check if the national significant number is formatted as a block.\r\n\t// We use contains and not equals, since the national significant number may be present with\r\n\t// a prefix such as a national number prefix, or the country code itself.\r\n\tif (candidateGroups.length == 1\r\n\t\t\t|| candidateGroups[candidateNumberGroupIndex].contains(\r\n\t\t\t\t\tutil.getNationalSignificantNumber(number)))\r\n\t{\r\n\t\treturn true\r\n\t}\r\n\r\n\t// Starting from the end, go through in reverse, excluding the first group, and check the\r\n\t// candidate and number groups are the same.\r\n\tlet formattedNumberGroupIndex = (formattedNumberGroups.length - 1)\r\n\twhile (formattedNumberGroupIndex > 0 && candidateNumberGroupIndex >= 0)\r\n\t{\r\n\t\tif (candidateGroups[candidateNumberGroupIndex] !== formattedNumberGroups[formattedNumberGroupIndex])\r\n\t\t{\r\n\t\t\treturn false\r\n\t\t}\r\n\t\tformattedNumberGroupIndex--\r\n\t\tcandidateNumberGroupIndex--\r\n\t}\r\n\r\n\t// Now check the first group. There may be a national prefix at the start, so we only check\r\n\t// that the candidate group ends with the formatted number group.\r\n\treturn (candidateNumberGroupIndex >= 0\r\n\t\t\t&& endsWith(candidateGroups[candidateNumberGroupIndex], formattedNumberGroups[0]))\r\n}\r\n\r\n\r\nfunction allNumberGroupsRemainGrouped\r\n(\r\n\tmetadata,\r\n\tnumber,\r\n\tnormalizedCandidate,\r\n\tformattedNumberGroups\r\n)\r\n{\r\n\tthrow new Error('This part of code hasn\\'t been ported')\r\n\r\n\tlet fromIndex = 0\r\n\tif (number.getCountryCodeSource() !== CountryCodeSource.FROM_DEFAULT_COUNTRY)\r\n\t{\r\n\t\t// First skip the country code if the normalized candidate contained it.\r\n\t\tconst countryCode = String(number.getCountryCode())\r\n\t\tfromIndex = normalizedCandidate.indexOf(countryCode) + countryCode.length()\r\n\t}\r\n\r\n\t// Check each group of consecutive digits are not broken into separate groupings in the\r\n\t// {@code normalizedCandidate} string.\r\n\tfor (let i = 0; i < formattedNumberGroups.length; i++)\r\n\t{\r\n\t\t// Fails if the substring of {@code normalizedCandidate} starting from {@code fromIndex}\r\n\t\t// doesn't contain the consecutive digits in formattedNumberGroups[i].\r\n\t\tfromIndex = normalizedCandidate.indexOf(formattedNumberGroups[i], fromIndex)\r\n\t\tif (fromIndex < 0) {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\t// Moves {@code fromIndex} forward.\r\n\t\tfromIndex += formattedNumberGroups[i].length()\r\n\t\tif (i == 0 && fromIndex < normalizedCandidate.length())\r\n\t\t{\r\n\t\t\t// We are at the position right after the NDC. We get the region used for formatting\r\n\t\t\t// information based on the country code in the phone number, rather than the number itself,\r\n\t\t\t// as we do not need to distinguish between different countries with the same country\r\n\t\t\t// calling code and this is faster.\r\n\t\t\tconst region = util.getRegionCodeForCountryCode(number.getCountryCode())\r\n\t\t\tif (util.getNddPrefixForRegion(region, true) != null\r\n\t\t\t\t\t&& Character.isDigit(normalizedCandidate.charAt(fromIndex))) {\r\n\t\t\t\t// This means there is no formatting symbol after the NDC. In this case, we only\r\n\t\t\t\t// accept the number if there is no formatting symbol at all in the number, except\r\n\t\t\t\t// for extensions. This is only important for countries with national prefixes.\r\n\t\t\t\tconst nationalSignificantNumber = util.getNationalSignificantNumber(number)\r\n\t\t\t\treturn startsWith\r\n\t\t\t\t(\r\n\t\t\t\t\tnormalizedCandidate.slice(fromIndex - formattedNumberGroups[i].length),\r\n\t\t\t\t\t nationalSignificantNumber\r\n\t\t\t\t)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// The check here makes sure that we haven't mistakenly already used the extension to\r\n\t// match the last group of the subscriber number. Note the extension cannot have\r\n\t// formatting in-between digits.\r\n\treturn normalizedCandidate.slice(fromIndex).contains(number.getExtension())\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,aAAP,MAA0B,eAA1B;AACA,OAAOC,WAAP,MAAwB,2BAAxB;AACA,OAAOC,wCAAP,MAAqD,+CAArD;AACA,OAAOC,QAAP,MAAqB,gBAArB;AACA,OAAOC,uBAAP,MAAoC,uCAApC;AACA,SAASC,qBAAT,QAAsC,cAAtC;AAEA,SACCC,UADD,EAECC,QAFD,QAGO,WAHP;AAKA;AACA;AACA;AACA;;AACA,eACA;EACC;AACD;AACA;EACCC,QAJD,WAAAA,SAIUC,WAJV,EAAAC,IAAA,EAKC;IAAA,IADwBC,SACxB,GAAAD,IAAA,CADwBC,SACxB;MADmCC,QACnC,GAAAF,IAAA,CADmCE,QACnC;IACC,OAAO,IAAP;EACA,CAPF;EASC;AACD;AACA;AACA;AACA;EACCC,KAdD,WAAAA,MAcOJ,WAdP,EAAAK,KAAA,EAeC;IAAA,IADqBH,SACrB,GAAAG,KAAA,CADqBH,SACrB;MADgCI,cAChC,GAAAD,KAAA,CADgCC,cAChC;MADgDH,QAChD,GAAAE,KAAA,CADgDF,QAChD;IACC,IACC,CAACH,WAAW,CAACO,OAAZ,EAAD,IACA,CAACC,uBAAuB,CAACR,WAAD,EAAcE,SAAd,EAAyBC,QAAzB,CAFzB,EAIA;MACC,OAAO,KAAP;IACA,CAPF,CASC;IACA;;IACA,OAAO,IAAP;EACA,CA3BF;EA6BC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACCM,eAzCD,WAAAA,gBAyCiBT,WAzCjB,EAAAU,KAAA,EA0CC;IAAA,IAD+BR,SAC/B,GAAAQ,KAAA,CAD+BR,SAC/B;MAD0CI,cAC1C,GAAAI,KAAA,CAD0CJ,cAC1C;MAD0DH,QAC1D,GAAAO,KAAA,CAD0DP,QAC1D;MADoEQ,WACpE,GAAAD,KAAA,CADoEC,WACpE;IACC,IACC,CAACX,WAAW,CAACO,OAAZ,EAAD,IACA,CAACC,uBAAuB,CAACR,WAAD,EAAcE,SAAd,EAAyBC,QAAzB,CADxB,IAEAS,wCAAwC,CAACZ,WAAD,EAAcE,SAAd,CAFxC,IAGA,CAACW,iCAAiC,CAACb,WAAD,EAAc;MAAEM,cAAc,EAAdA,cAAF;MAAkBH,QAAQ,EAARA;IAAlB,CAAd,CAJnC,EAMA;MACC,OAAO,KAAP;IACA;IAED,OAAOW,0BAA0B,CAEhCd,WAFgC,EAGhCE,SAHgC,EAIhCC,QAJgC,EAKhCY,4BALgC,EAMhCJ,WANgC,CAAjC;EAQA,CA7DF;EA+DC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACCK,cA1ED,WAAAA,eA0EgBhB,WA1EhB,EAAAiB,KAAA,EA2EC;IAAA,IAD8Bf,SAC9B,GAAAe,KAAA,CAD8Bf,SAC9B;MADyCI,cACzC,GAAAW,KAAA,CADyCX,cACzC;MADyDH,QACzD,GAAAc,KAAA,CADyDd,QACzD;MADmEQ,WACnE,GAAAM,KAAA,CADmEN,WACnE;IACC,IACC,CAACX,WAAW,CAACO,OAAZ,EAAD,IACA,CAACC,uBAAuB,CAACR,WAAD,EAAcE,SAAd,EAAyBC,QAAzB,CADxB,IAEAS,wCAAwC,CAACZ,WAAD,EAAcE,SAAd,CAFxC,IAGA,CAACW,iCAAiC,CAACb,WAAD,EAAc;MAAEM,cAAc,EAAdA,cAAF;MAAkBH,QAAQ,EAARA;IAAlB,CAAd,CAJnC,EAMA;MACC,OAAO,KAAP;IACA;IAED,OAAOW,0BAA0B,CAEhCd,WAFgC,EAGhCE,SAHgC,EAIhCC,QAJgC,EAKhCe,gCALgC,EAMhCP,WANgC,CAAjC;EAQA;AA9FF,CADA;AAkGA,SAASH,uBAATA,CAAiCR,WAAjC,EAA8CE,SAA9C,EAAyDC,QAAzD,EACA;EACC;EACA;EACA;EACA;EACA;EACA,KAAK,IAAIgB,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGjB,SAAS,CAACkB,MAAV,GAAmB,CAA/C,EAAkDD,KAAK,EAAvD,EACA;IACC,IAAME,WAAW,GAAGnB,SAAS,CAACoB,MAAV,CAAiBH,KAAjB,CAApB;IAEA,IAAIE,WAAW,KAAK,GAAhB,IAAuBA,WAAW,KAAK,GAA3C,EACA;MACC,IAAME,eAAe,GAAGrB,SAAS,CAACoB,MAAV,CAAiBH,KAAK,GAAG,CAAzB,CAAxB;MAEA,IAAII,eAAe,KAAK,GAApB,IAA2BA,eAAe,KAAK,GAAnD,EACA;QACC;QACA;QACAJ,KAAK;QACL,IAAI1B,wCAAwC,CAACS,SAAS,CAACsB,SAAV,CAAoBL,KAApB,CAAD,EAA6BnB,WAA7B,EAA0CG,QAA1C,CAAxC,KAAgG,WAApG,EACA;UACC,OAAO,KAAP;QACA,CAPF,CAQC;QACA;MACA,CAXD,MAYK;QACJ,IAAMsB,GAAG,GAAGjC,WAAW,CAACU,SAAS,CAACsB,SAAV,CAAoBL,KAApB,CAAD,CAAvB;QACA,IAAIM,GAAJ,EAAS;UACR,IAAIzB,WAAW,CAACyB,GAAZ,KAAoBA,GAAxB,EAA8B;YAC7B,OAAO,KAAP;UACA;QACD,CAJD,MAIO;UACN,IAAIzB,WAAW,CAACyB,GAAhB,EAAqB;YACpB,OAAO,KAAP;UACA;QACD;MACD;IACD;EACD;EAED,OAAO,IAAP;AACA;AAED,SAASZ,iCAATA,CAA2Cb,WAA3C,EAAA0B,KAAA,EACA;EAAA,IAD0DpB,cAC1D,GAAAoB,KAAA,CAD0DpB,cAC1D;IADoFqB,SACpF,GAAAD,KAAA,CAD0EvB,QAC1E;;EACC;EACA;EACA,IAAIH,WAAW,CAAC4B,0BAAZ,KAA2C,sBAA/C,EACA;IACC,OAAO,IAAP;EACA;EAED,IAAMzB,QAAQ,GAAG,IAAIT,QAAJ,CAAaiC,SAAb,CAAjB;EACAxB,QAAQ,CAAC0B,mBAAT,CAA6B7B,WAAW,CAAC8B,kBAAzC;EAEA,IAAMC,iBAAiB,GAAG/B,WAAW,CAACgC,OAAZ,IAAuBrC,uBAAuB,CAACK,WAAW,CAAC8B,kBAAb,EAAiC;IACxGG,cAAc,EAAEjC,WAAW,CAACiC,cAD4E;IAExG3B,cAAc,EAAdA,cAFwG;IAGxGH,QAAQ,EAARA;EAHwG,CAAjC,CAAxE,CAXD,CAiBC;;EACA,IAAM8B,cAAc,GAAGjC,WAAW,CAACiC,cAAnC;EACA,IAAMC,MAAM,GAAGtC,qBAAqB,CAACO,QAAQ,CAACgC,aAAT,CAAuBC,OAAvB,EAAD,EAAmCH,cAAnC,CAApC,CAnBD,CAqBC;EACA;;EACA,IAAIC,MAAM,CAACG,4BAAP,EAAJ,EACA;IACC,IAAIlC,QAAQ,CAACgC,aAAT,CAAuBG,sDAAvB,EAAJ,EACA;MACC;MACA,OAAO,IAAP;IACA;IAED,IAAI,CAACJ,MAAM,CAACK,kBAAP,EAAL,EACA;MACC;MACA,OAAO,IAAP;IACA;IAED,OAAOC,OAAO,CAACxC,WAAW,CAACyC,cAAb,CAAd;EACA;EAED,OAAO,IAAP;AACA;AAED,OAAO,SAAS7B,wCAATA,CAAkDZ,WAAlD,EAA+DE,SAA/D,EACP;EACC,IAAMwC,qBAAqB,GAAGxC,SAAS,CAACyC,OAAV,CAAkB,GAAlB,CAA9B;EACA,IAAID,qBAAqB,GAAG,CAA5B,EACA;IACC;IACA,OAAO,KAAP;EACA,CANF,CAQC;;EACA,IAAME,sBAAsB,GAAG1C,SAAS,CAACyC,OAAV,CAAkB,GAAlB,EAAuBD,qBAAqB,GAAG,CAA/C,CAA/B;EACA,IAAIE,sBAAsB,GAAG,CAA7B,EACA;IACC;IACA,OAAO,KAAP;EACA,CAdF,CAgBC;;EACA,IAAMC,uBAAuB,GAC3B7C,WAAW,CAAC4B,0BAAZ,KAA2C,4BAA3C,IACA5B,WAAW,CAAC4B,0BAAZ,KAA2C,+BAF7C;EAIA,IAAIiB,uBAAuB,IAAIrD,WAAW,CAACU,SAAS,CAACsB,SAAV,CAAoB,CAApB,EAAuBkB,qBAAvB,CAAD,CAAX,KAA+D1C,WAAW,CAAC8B,kBAA1G,EACA;IACC;IACA,OAAO5B,SAAS,CAAC4C,KAAV,CAAgBF,sBAAsB,GAAG,CAAzC,EAA4CD,OAA5C,CAAoD,GAApD,KAA4D,CAAnE;EACA;EAED,OAAO,IAAP;AACA;AAED,SAAS7B,0BAATA,CACCiC,MADD,EAEC7C,SAFD,EAGCC,QAHD,EAIC6C,WAJD,EAKCrC,WALD,EAME;EACD,MAAM,IAAIsC,KAAJ,CAAU,uCAAV,CAAN;EAEA,IAAMC,mBAAmB,GAAGC,eAAe,CAACjD,SAAD,EAAY;EAAK,qBAAjB,CAA3C;;EACA,IAAIkD,qBAAqB,GAAGC,uBAAuB,CAAClD,QAAD,EAAW4C,MAAX,EAAmB,IAAnB,CAAnD;EACA,IAAIC,WAAW,CAAC7C,QAAD,EAAW4C,MAAX,EAAmBG,mBAAnB,EAAwCE,qBAAxC,CAAf,EAA+E;IAC9E,OAAO,IAAP;EACA,CAPA,CASD;;EACA,IAAME,gBAAgB,GAAGC,eAAe,CAACC,6BAAhB,CAA8CT,MAAM,CAACU,cAAP,EAA9C,CAAzB;EACA,IAAMC,yBAAyB,GAAGC,IAAI,CAACC,4BAAL,CAAkCb,MAAlC,CAAlC;EAEA,IAAIO,gBAAJ,EAAsB;IACrB,SAAAO,SAAA,GAAAC,+BAAA,CAA8BR,gBAAgB,CAACS,aAAjB,EAA9B,GAAAC,KAAA,IAAAA,KAAA,GAAAH,SAAA,IAAAI,IAAA,GAAgE;MAAA,IAArDC,eAAqD,GAAAF,KAAA,CAAAG,KAAA;MAC/D,IAAID,eAAe,CAACE,qBAAhB,GAAwChD,MAAxC,GAAiD,CAArD,EAAwD;QACvD;QACA,IAAMiD,mBAAmB,GAAG1D,WAAW,CAAC2D,mBAAZ,CAAgC,MAAMJ,eAAe,CAACE,qBAAhB,GAAwC,CAAxC,CAAtC,CAA5B;QACA,IAAI,CAACC,mBAAmB,CAACE,IAApB,CAAyBb,yBAAzB,CAAL,EAA0D;UACzD;UACA;QACA;MACD;MACDN,qBAAqB,GAAGC,uBAAuB,CAAClD,QAAD,EAAW4C,MAAX,EAAmBmB,eAAnB,CAA/C;MACA,IAAIlB,WAAW,CAAC7C,QAAD,EAAW4C,MAAX,EAAmBG,mBAAnB,EAAwCE,qBAAxC,CAAf,EAA+E;QAC9E,OAAO,IAAP;MACA;IACD;EACD;EAED,OAAO,KAAP;AACA;AAED;AACA;AACA;AACA;AACA;;AACA,SAASC,uBAATA,CACClD,QADD,EAEC4C,MAFD,EAGCyB,iBAHD,EAIE;EACD,MAAM,IAAIvB,KAAJ,CAAU,uCAAV,CAAN;EAEA,IAAIuB,iBAAJ,EAAuB;IACtB;IACA,IAAMd,yBAAyB,GAAGC,IAAI,CAACC,4BAAL,CAAkCb,MAAlC,CAAlC;IACA,OAAOY,IAAI,CAACc,qBAAL,CAA2Bf,yBAA3B,EACUc,iBADV,EAC6B,SAD7B,EACwCrE,QADxC,EACkDuE,KADlD,CACwD,GADxD,CAAP;EAEA,CARA,CAUD;;EACA,IAAMC,aAAa,GAAGC,YAAY,CAAC7B,MAAD,EAAS,SAAT,EAAoB5C,QAApB,CAAlC,CAXC,CAaD;EACA;;EACA,IAAI0E,QAAQ,GAAGF,aAAa,CAAChC,OAAd,CAAsB,GAAtB,CAAf;EACA,IAAIkC,QAAQ,GAAG,CAAf,EAAkB;IACjBA,QAAQ,GAAGF,aAAa,CAACvD,MAAzB;EACA,CAlBA,CAoBD;;EACA,IAAM0D,UAAU,GAAGH,aAAa,CAAChC,OAAd,CAAsB,GAAtB,IAA6B,CAAhD;EACA,OAAOgC,aAAa,CAAC7B,KAAd,CAAoBgC,UAApB,EAAgCD,QAAhC,EAA0CH,KAA1C,CAAgD,GAAhD,CAAP;AACA;AAED,SAASxD,gCAATA,CAECf,QAFD,EAGC4C,MAHD,EAICG,mBAJD,EAKCE,qBALD,EAOA;EACC,MAAM,IAAIH,KAAJ,CAAU,uCAAV,CAAN;EAEA,IAAM8B,eAAe,GAAG7B,mBAAmB,CAACwB,KAApB,CAA0BM,kBAA1B,CAAxB,CAHD,CAKC;;EACA,IAAIC,yBAAyB,GAC3BlC,MAAM,CAACmC,YAAP,KAAwBH,eAAe,CAAC3D,MAAhB,GAAyB,CAAjD,GAAqD2D,eAAe,CAAC3D,MAAhB,GAAyB,CADhF,CAND,CASC;EACA;EACA;;EACA,IAAI2D,eAAe,CAAC3D,MAAhB,IAA0B,CAA1B,IACC2D,eAAe,CAACE,yBAAD,CAAf,CAA2CE,QAA3C,CACDxB,IAAI,CAACC,4BAAL,CAAkCb,MAAlC,CADC,CADL,EAGA;IACC,OAAO,IAAP;EACA,CAjBF,CAmBC;EACA;;EACA,IAAIqC,yBAAyB,GAAIhC,qBAAqB,CAAChC,MAAtB,GAA+B,CAAhE;EACA,OAAOgE,yBAAyB,GAAG,CAA5B,IAAiCH,yBAAyB,IAAI,CAArE,EACA;IACC,IAAIF,eAAe,CAACE,yBAAD,CAAf,KAA+C7B,qBAAqB,CAACgC,yBAAD,CAAxE,EACA;MACC,OAAO,KAAP;IACA;IACDA,yBAAyB;IACzBH,yBAAyB;EACzB,CA9BF,CAgCC;EACA;;EACA,OAAQA,yBAAyB,IAAI,CAA7B,IACHnF,QAAQ,CAACiF,eAAe,CAACE,yBAAD,CAAhB,EAA6C7B,qBAAqB,CAAC,CAAD,CAAlE,CADb;AAEA;AAGD,SAASrC,4BAATA,CAECZ,QAFD,EAGC4C,MAHD,EAICG,mBAJD,EAKCE,qBALD,EAOA;EACC,MAAM,IAAIH,KAAJ,CAAU,uCAAV,CAAN;EAEA,IAAIoC,SAAS,GAAG,CAAhB;EACA,IAAItC,MAAM,CAACuC,oBAAP,OAAkCC,iBAAiB,CAACC,oBAAxD,EACA;IACC;IACA,IAAMC,WAAW,GAAGC,MAAM,CAAC3C,MAAM,CAACU,cAAP,EAAD,CAA1B;IACA4B,SAAS,GAAGnC,mBAAmB,CAACP,OAApB,CAA4B8C,WAA5B,IAA2CA,WAAW,CAACrE,MAAZ,EAAvD;EACA,CATF,CAWC;EACA;;EACA,KAAK,IAAIuE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGvC,qBAAqB,CAAChC,MAA1C,EAAkDuE,CAAC,EAAnD,EACA;IACC;IACA;IACAN,SAAS,GAAGnC,mBAAmB,CAACP,OAApB,CAA4BS,qBAAqB,CAACuC,CAAD,CAAjD,EAAsDN,SAAtD,CAAZ;IACA,IAAIA,SAAS,GAAG,CAAhB,EAAmB;MAClB,OAAO,KAAP;IACA,CANF,CAOC;;IACAA,SAAS,IAAIjC,qBAAqB,CAACuC,CAAD,CAArB,CAAyBvE,MAAzB,EAAb;IACA,IAAIuE,CAAC,IAAI,CAAL,IAAUN,SAAS,GAAGnC,mBAAmB,CAAC9B,MAApB,EAA1B,EACA;MACC;MACA;MACA;MACA;MACA,IAAMwE,MAAM,GAAGjC,IAAI,CAACkC,2BAAL,CAAiC9C,MAAM,CAACU,cAAP,EAAjC,CAAf;MACA,IAAIE,IAAI,CAACmC,qBAAL,CAA2BF,MAA3B,EAAmC,IAAnC,KAA4C,IAA5C,IACCG,SAAS,CAACC,OAAV,CAAkB9C,mBAAmB,CAAC5B,MAApB,CAA2B+D,SAA3B,CAAlB,CADL,EAC+D;QAC9D;QACA;QACA;QACA,IAAM3B,yBAAyB,GAAGC,IAAI,CAACC,4BAAL,CAAkCb,MAAlC,CAAlC;QACA,OAAOlD,UAAU,CAEhBqD,mBAAmB,CAACJ,KAApB,CAA0BuC,SAAS,GAAGjC,qBAAqB,CAACuC,CAAD,CAArB,CAAyBvE,MAA/D,CAFgB,EAGfsC,yBAHe,CAAjB;MAKA;IACD;EACD,CA3CF,CA6CC;EACA;EACA;;EACA,OAAOR,mBAAmB,CAACJ,KAApB,CAA0BuC,SAA1B,EAAqCF,QAArC,CAA8CpC,MAAM,CAACkD,YAAP,EAA9C,CAAP;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}