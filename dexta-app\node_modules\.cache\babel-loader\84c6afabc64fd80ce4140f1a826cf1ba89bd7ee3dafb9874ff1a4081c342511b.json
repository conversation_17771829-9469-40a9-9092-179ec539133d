{"ast": null, "code": "/*\n  Module dependencies\n*/\nvar ElementType = require('domelementtype');\nvar entities = require('entities');\n\n/* mixed-case SVG and MathML tags & attributes\n   recognized by the HTML parser, see\n   https://html.spec.whatwg.org/multipage/parsing.html#parsing-main-inforeign\n*/\nvar foreignNames = require('./foreignNames.json');\nforeignNames.elementNames.__proto__ = null; /* use as a simple dictionary */\nforeignNames.attributeNames.__proto__ = null;\nvar unencodedElements = {\n  __proto__: null,\n  style: true,\n  script: true,\n  xmp: true,\n  iframe: true,\n  noembed: true,\n  noframes: true,\n  plaintext: true,\n  noscript: true\n};\n\n/*\n  Format attributes\n*/\nfunction formatAttrs(attributes, opts) {\n  if (!attributes) return;\n  var output = '';\n  var value;\n\n  // Loop through the attributes\n  for (var key in attributes) {\n    value = attributes[key];\n    if (output) {\n      output += ' ';\n    }\n    if (opts.xmlMode === 'foreign') {\n      /* fix up mixed-case attribute names */\n      key = foreignNames.attributeNames[key] || key;\n    }\n    output += key;\n    if (value !== null && value !== '' || opts.xmlMode) {\n      output += '=\"' + (opts.decodeEntities ? entities.encodeXML(value) : value.replace(/\\\"/g, '&quot;')) + '\"';\n    }\n  }\n  return output;\n}\n\n/*\n  Self-enclosing tags (stolen from node-htmlparser)\n*/\nvar singleTag = {\n  __proto__: null,\n  area: true,\n  base: true,\n  basefont: true,\n  br: true,\n  col: true,\n  command: true,\n  embed: true,\n  frame: true,\n  hr: true,\n  img: true,\n  input: true,\n  isindex: true,\n  keygen: true,\n  link: true,\n  meta: true,\n  param: true,\n  source: true,\n  track: true,\n  wbr: true\n};\nvar render = module.exports = function (dom, opts) {\n  if (!Array.isArray(dom) && !dom.cheerio) dom = [dom];\n  opts = opts || {};\n  var output = '';\n  for (var i = 0; i < dom.length; i++) {\n    var elem = dom[i];\n    if (elem.type === 'root') output += render(elem.children, opts);else if (ElementType.isTag(elem)) output += renderTag(elem, opts);else if (elem.type === ElementType.Directive) output += renderDirective(elem);else if (elem.type === ElementType.Comment) output += renderComment(elem);else if (elem.type === ElementType.CDATA) output += renderCdata(elem);else output += renderText(elem, opts);\n  }\n  return output;\n};\nvar foreignModeIntegrationPoints = ['mi', 'mo', 'mn', 'ms', 'mtext', 'annotation-xml', 'foreignObject', 'desc', 'title'];\nfunction renderTag(elem, opts) {\n  // Handle SVG / MathML in HTML\n  if (opts.xmlMode === 'foreign') {\n    /* fix up mixed-case element names */\n    elem.name = foreignNames.elementNames[elem.name] || elem.name;\n    /* exit foreign mode at integration points */\n    if (elem.parent && foreignModeIntegrationPoints.indexOf(elem.parent.name) >= 0) opts = Object.assign({}, opts, {\n      xmlMode: false\n    });\n  }\n  if (!opts.xmlMode && ['svg', 'math'].indexOf(elem.name) >= 0) {\n    opts = Object.assign({}, opts, {\n      xmlMode: 'foreign'\n    });\n  }\n  var tag = '<' + elem.name;\n  var attribs = formatAttrs(elem.attribs, opts);\n  if (attribs) {\n    tag += ' ' + attribs;\n  }\n  if (opts.xmlMode && (!elem.children || elem.children.length === 0)) {\n    tag += '/>';\n  } else {\n    tag += '>';\n    if (elem.children) {\n      tag += render(elem.children, opts);\n    }\n    if (!singleTag[elem.name] || opts.xmlMode) {\n      tag += '</' + elem.name + '>';\n    }\n  }\n  return tag;\n}\nfunction renderDirective(elem) {\n  return '<' + elem.data + '>';\n}\nfunction renderText(elem, opts) {\n  var data = elem.data || '';\n\n  // if entities weren't decoded, no need to encode them back\n  if (opts.decodeEntities && !(elem.parent && elem.parent.name in unencodedElements)) {\n    data = entities.encodeXML(data);\n  }\n  return data;\n}\nfunction renderCdata(elem) {\n  return '<![CDATA[' + elem.children[0].data + ']]>';\n}\nfunction renderComment(elem) {\n  return '<!--' + elem.data + '-->';\n}", "map": {"version": 3, "names": ["ElementType", "require", "entities", "foreignNames", "elementNames", "__proto__", "attributeNames", "unencodedElements", "style", "script", "xmp", "iframe", "noembed", "noframes", "plaintext", "noscript", "formatAttrs", "attributes", "opts", "output", "value", "key", "xmlMode", "decodeEntities", "encodeXML", "replace", "singleTag", "area", "base", "basefont", "br", "col", "command", "embed", "frame", "hr", "img", "input", "isindex", "keygen", "link", "meta", "param", "source", "track", "wbr", "render", "module", "exports", "dom", "Array", "isArray", "cheerio", "i", "length", "elem", "type", "children", "isTag", "renderTag", "Directive", "renderDirective", "Comment", "renderComment", "CDATA", "renderCdata", "renderText", "foreignModeIntegrationPoints", "name", "parent", "indexOf", "Object", "assign", "tag", "attribs", "data"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/dom-serializer/index.js"], "sourcesContent": ["/*\n  Module dependencies\n*/\nvar ElementType = require('domelementtype');\nvar entities = require('entities');\n\n/* mixed-case SVG and MathML tags & attributes\n   recognized by the HTML parser, see\n   https://html.spec.whatwg.org/multipage/parsing.html#parsing-main-inforeign\n*/\nvar foreignNames = require('./foreignNames.json');\nforeignNames.elementNames.__proto__ = null; /* use as a simple dictionary */\nforeignNames.attributeNames.__proto__ = null;\n\nvar unencodedElements = {\n  __proto__: null,\n  style: true,\n  script: true,\n  xmp: true,\n  iframe: true,\n  noembed: true,\n  noframes: true,\n  plaintext: true,\n  noscript: true\n};\n\n/*\n  Format attributes\n*/\nfunction formatAttrs(attributes, opts) {\n  if (!attributes) return;\n\n  var output = '';\n  var value;\n\n  // Loop through the attributes\n  for (var key in attributes) {\n    value = attributes[key];\n    if (output) {\n      output += ' ';\n    }\n\n    if (opts.xmlMode === 'foreign') {\n      /* fix up mixed-case attribute names */\n      key = foreignNames.attributeNames[key] || key;\n    }\n    output += key;\n    if ((value !== null && value !== '') || opts.xmlMode) {\n      output +=\n        '=\"' +\n        (opts.decodeEntities\n          ? entities.encodeXML(value)\n          : value.replace(/\\\"/g, '&quot;')) +\n        '\"';\n    }\n  }\n\n  return output;\n}\n\n/*\n  Self-enclosing tags (stolen from node-htmlparser)\n*/\nvar singleTag = {\n  __proto__: null,\n  area: true,\n  base: true,\n  basefont: true,\n  br: true,\n  col: true,\n  command: true,\n  embed: true,\n  frame: true,\n  hr: true,\n  img: true,\n  input: true,\n  isindex: true,\n  keygen: true,\n  link: true,\n  meta: true,\n  param: true,\n  source: true,\n  track: true,\n  wbr: true\n};\n\nvar render = (module.exports = function(dom, opts) {\n  if (!Array.isArray(dom) && !dom.cheerio) dom = [dom];\n  opts = opts || {};\n\n  var output = '';\n\n  for (var i = 0; i < dom.length; i++) {\n    var elem = dom[i];\n\n    if (elem.type === 'root') output += render(elem.children, opts);\n    else if (ElementType.isTag(elem)) output += renderTag(elem, opts);\n    else if (elem.type === ElementType.Directive)\n      output += renderDirective(elem);\n    else if (elem.type === ElementType.Comment) output += renderComment(elem);\n    else if (elem.type === ElementType.CDATA) output += renderCdata(elem);\n    else output += renderText(elem, opts);\n  }\n\n  return output;\n});\n\nvar foreignModeIntegrationPoints = [\n  'mi',\n  'mo',\n  'mn',\n  'ms',\n  'mtext',\n  'annotation-xml',\n  'foreignObject',\n  'desc',\n  'title'\n];\n\nfunction renderTag(elem, opts) {\n  // Handle SVG / MathML in HTML\n  if (opts.xmlMode === 'foreign') {\n    /* fix up mixed-case element names */\n    elem.name = foreignNames.elementNames[elem.name] || elem.name;\n    /* exit foreign mode at integration points */\n    if (\n      elem.parent &&\n      foreignModeIntegrationPoints.indexOf(elem.parent.name) >= 0\n    )\n      opts = Object.assign({}, opts, { xmlMode: false });\n  }\n  if (!opts.xmlMode && ['svg', 'math'].indexOf(elem.name) >= 0) {\n    opts = Object.assign({}, opts, { xmlMode: 'foreign' });\n  }\n\n  var tag = '<' + elem.name;\n  var attribs = formatAttrs(elem.attribs, opts);\n\n  if (attribs) {\n    tag += ' ' + attribs;\n  }\n\n  if (opts.xmlMode && (!elem.children || elem.children.length === 0)) {\n    tag += '/>';\n  } else {\n    tag += '>';\n    if (elem.children) {\n      tag += render(elem.children, opts);\n    }\n\n    if (!singleTag[elem.name] || opts.xmlMode) {\n      tag += '</' + elem.name + '>';\n    }\n  }\n\n  return tag;\n}\n\nfunction renderDirective(elem) {\n  return '<' + elem.data + '>';\n}\n\nfunction renderText(elem, opts) {\n  var data = elem.data || '';\n\n  // if entities weren't decoded, no need to encode them back\n  if (\n    opts.decodeEntities &&\n    !(elem.parent && elem.parent.name in unencodedElements)\n  ) {\n    data = entities.encodeXML(data);\n  }\n\n  return data;\n}\n\nfunction renderCdata(elem) {\n  return '<![CDATA[' + elem.children[0].data + ']]>';\n}\n\nfunction renderComment(elem) {\n  return '<!--' + elem.data + '-->';\n}\n"], "mappings": "AAAA;AACA;AACA;AACA,IAAIA,WAAW,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AAC3C,IAAIC,QAAQ,GAAGD,OAAO,CAAC,UAAU,CAAC;;AAElC;AACA;AACA;AACA;AACA,IAAIE,YAAY,GAAGF,OAAO,CAAC,qBAAqB,CAAC;AACjDE,YAAY,CAACC,YAAY,CAACC,SAAS,GAAG,IAAI,CAAC,CAAC;AAC5CF,YAAY,CAACG,cAAc,CAACD,SAAS,GAAG,IAAI;AAE5C,IAAIE,iBAAiB,GAAG;EACtBF,SAAS,EAAE,IAAI;EACfG,KAAK,EAAE,IAAI;EACXC,MAAM,EAAE,IAAI;EACZC,GAAG,EAAE,IAAI;EACTC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE,IAAI;EACbC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE;AACZ,CAAC;;AAED;AACA;AACA;AACA,SAASC,WAAWA,CAACC,UAAU,EAAEC,IAAI,EAAE;EACrC,IAAI,CAACD,UAAU,EAAE;EAEjB,IAAIE,MAAM,GAAG,EAAE;EACf,IAAIC,KAAK;;EAET;EACA,KAAK,IAAIC,GAAG,IAAIJ,UAAU,EAAE;IAC1BG,KAAK,GAAGH,UAAU,CAACI,GAAG,CAAC;IACvB,IAAIF,MAAM,EAAE;MACVA,MAAM,IAAI,GAAG;IACf;IAEA,IAAID,IAAI,CAACI,OAAO,KAAK,SAAS,EAAE;MAC9B;MACAD,GAAG,GAAGlB,YAAY,CAACG,cAAc,CAACe,GAAG,CAAC,IAAIA,GAAG;IAC/C;IACAF,MAAM,IAAIE,GAAG;IACb,IAAKD,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,EAAE,IAAKF,IAAI,CAACI,OAAO,EAAE;MACpDH,MAAM,IACJ,IAAI,IACHD,IAAI,CAACK,cAAc,GAChBrB,QAAQ,CAACsB,SAAS,CAACJ,KAAK,CAAC,GACzBA,KAAK,CAACK,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,GACnC,GAAG;IACP;EACF;EAEA,OAAON,MAAM;AACf;;AAEA;AACA;AACA;AACA,IAAIO,SAAS,GAAG;EACdrB,SAAS,EAAE,IAAI;EACfsB,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,IAAI;EACdC,EAAE,EAAE,IAAI;EACRC,GAAG,EAAE,IAAI;EACTC,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE,IAAI;EACXC,KAAK,EAAE,IAAI;EACXC,EAAE,EAAE,IAAI;EACRC,GAAG,EAAE,IAAI;EACTC,KAAK,EAAE,IAAI;EACXC,OAAO,EAAE,IAAI;EACbC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,MAAM,EAAE,IAAI;EACZC,KAAK,EAAE,IAAI;EACXC,GAAG,EAAE;AACP,CAAC;AAED,IAAIC,MAAM,GAAIC,MAAM,CAACC,OAAO,GAAG,UAASC,GAAG,EAAE/B,IAAI,EAAE;EACjD,IAAI,CAACgC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,IAAI,CAACA,GAAG,CAACG,OAAO,EAAEH,GAAG,GAAG,CAACA,GAAG,CAAC;EACpD/B,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;EAEjB,IAAIC,MAAM,GAAG,EAAE;EAEf,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,IAAIE,IAAI,GAAGN,GAAG,CAACI,CAAC,CAAC;IAEjB,IAAIE,IAAI,CAACC,IAAI,KAAK,MAAM,EAAErC,MAAM,IAAI2B,MAAM,CAACS,IAAI,CAACE,QAAQ,EAAEvC,IAAI,CAAC,CAAC,KAC3D,IAAIlB,WAAW,CAAC0D,KAAK,CAACH,IAAI,CAAC,EAAEpC,MAAM,IAAIwC,SAAS,CAACJ,IAAI,EAAErC,IAAI,CAAC,CAAC,KAC7D,IAAIqC,IAAI,CAACC,IAAI,KAAKxD,WAAW,CAAC4D,SAAS,EAC1CzC,MAAM,IAAI0C,eAAe,CAACN,IAAI,CAAC,CAAC,KAC7B,IAAIA,IAAI,CAACC,IAAI,KAAKxD,WAAW,CAAC8D,OAAO,EAAE3C,MAAM,IAAI4C,aAAa,CAACR,IAAI,CAAC,CAAC,KACrE,IAAIA,IAAI,CAACC,IAAI,KAAKxD,WAAW,CAACgE,KAAK,EAAE7C,MAAM,IAAI8C,WAAW,CAACV,IAAI,CAAC,CAAC,KACjEpC,MAAM,IAAI+C,UAAU,CAACX,IAAI,EAAErC,IAAI,CAAC;EACvC;EAEA,OAAOC,MAAM;AACf,CAAE;AAEF,IAAIgD,4BAA4B,GAAG,CACjC,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,gBAAgB,EAChB,eAAe,EACf,MAAM,EACN,OAAO,CACR;AAED,SAASR,SAASA,CAACJ,IAAI,EAAErC,IAAI,EAAE;EAC7B;EACA,IAAIA,IAAI,CAACI,OAAO,KAAK,SAAS,EAAE;IAC9B;IACAiC,IAAI,CAACa,IAAI,GAAGjE,YAAY,CAACC,YAAY,CAACmD,IAAI,CAACa,IAAI,CAAC,IAAIb,IAAI,CAACa,IAAI;IAC7D;IACA,IACEb,IAAI,CAACc,MAAM,IACXF,4BAA4B,CAACG,OAAO,CAACf,IAAI,CAACc,MAAM,CAACD,IAAI,CAAC,IAAI,CAAC,EAE3DlD,IAAI,GAAGqD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEtD,IAAI,EAAE;MAAEI,OAAO,EAAE;IAAM,CAAC,CAAC;EACtD;EACA,IAAI,CAACJ,IAAI,CAACI,OAAO,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAACgD,OAAO,CAACf,IAAI,CAACa,IAAI,CAAC,IAAI,CAAC,EAAE;IAC5DlD,IAAI,GAAGqD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEtD,IAAI,EAAE;MAAEI,OAAO,EAAE;IAAU,CAAC,CAAC;EACxD;EAEA,IAAImD,GAAG,GAAG,GAAG,GAAGlB,IAAI,CAACa,IAAI;EACzB,IAAIM,OAAO,GAAG1D,WAAW,CAACuC,IAAI,CAACmB,OAAO,EAAExD,IAAI,CAAC;EAE7C,IAAIwD,OAAO,EAAE;IACXD,GAAG,IAAI,GAAG,GAAGC,OAAO;EACtB;EAEA,IAAIxD,IAAI,CAACI,OAAO,KAAK,CAACiC,IAAI,CAACE,QAAQ,IAAIF,IAAI,CAACE,QAAQ,CAACH,MAAM,KAAK,CAAC,CAAC,EAAE;IAClEmB,GAAG,IAAI,IAAI;EACb,CAAC,MAAM;IACLA,GAAG,IAAI,GAAG;IACV,IAAIlB,IAAI,CAACE,QAAQ,EAAE;MACjBgB,GAAG,IAAI3B,MAAM,CAACS,IAAI,CAACE,QAAQ,EAAEvC,IAAI,CAAC;IACpC;IAEA,IAAI,CAACQ,SAAS,CAAC6B,IAAI,CAACa,IAAI,CAAC,IAAIlD,IAAI,CAACI,OAAO,EAAE;MACzCmD,GAAG,IAAI,IAAI,GAAGlB,IAAI,CAACa,IAAI,GAAG,GAAG;IAC/B;EACF;EAEA,OAAOK,GAAG;AACZ;AAEA,SAASZ,eAAeA,CAACN,IAAI,EAAE;EAC7B,OAAO,GAAG,GAAGA,IAAI,CAACoB,IAAI,GAAG,GAAG;AAC9B;AAEA,SAAST,UAAUA,CAACX,IAAI,EAAErC,IAAI,EAAE;EAC9B,IAAIyD,IAAI,GAAGpB,IAAI,CAACoB,IAAI,IAAI,EAAE;;EAE1B;EACA,IACEzD,IAAI,CAACK,cAAc,IACnB,EAAEgC,IAAI,CAACc,MAAM,IAAId,IAAI,CAACc,MAAM,CAACD,IAAI,IAAI7D,iBAAiB,CAAC,EACvD;IACAoE,IAAI,GAAGzE,QAAQ,CAACsB,SAAS,CAACmD,IAAI,CAAC;EACjC;EAEA,OAAOA,IAAI;AACb;AAEA,SAASV,WAAWA,CAACV,IAAI,EAAE;EACzB,OAAO,WAAW,GAAGA,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC,CAACkB,IAAI,GAAG,KAAK;AACpD;AAEA,SAASZ,aAAaA,CAACR,IAAI,EAAE;EAC3B,OAAO,MAAM,GAAGA,IAAI,CAACoB,IAAI,GAAG,KAAK;AACnC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}