{"ast": null, "code": "import applyInternationalSeparatorStyle from './applyInternationalSeparatorStyle.js'; // This was originally set to $1 but there are some countries for which the\n// first group is not used in the national pattern (e.g. Argentina) so the $1\n// group does not match correctly. Therefore, we use `\\d`, so that the first\n// group actually used in the pattern will be matched.\n\nexport var FIRST_GROUP_PATTERN = /(\\$\\d)/;\nexport default function formatNationalNumberUsingFormat(number, format, _ref) {\n  var useInternationalFormat = _ref.useInternationalFormat,\n    withNationalPrefix = _ref.withNationalPrefix,\n    carrierCode = _ref.carrierCode,\n    metadata = _ref.metadata;\n  var formattedNumber = number.replace(new RegExp(format.pattern()), useInternationalFormat ? format.internationalFormat() :\n  // This library doesn't use `domestic_carrier_code_formatting_rule`,\n  // because that one is only used when formatting phone numbers\n  // for dialing from a mobile phone, and this is not a dialing library.\n  // carrierCode && format.domesticCarrierCodeFormattingRule()\n  // \t// First, replace the $CC in the formatting rule with the desired carrier code.\n  // \t// Then, replace the $FG in the formatting rule with the first group\n  // \t// and the carrier code combined in the appropriate way.\n  // \t? format.format().replace(FIRST_GROUP_PATTERN, format.domesticCarrierCodeFormattingRule().replace('$CC', carrierCode))\n  // \t: (\n  // \t\twithNationalPrefix && format.nationalPrefixFormattingRule()\n  // \t\t\t? format.format().replace(FIRST_GROUP_PATTERN, format.nationalPrefixFormattingRule())\n  // \t\t\t: format.format()\n  // \t)\n  withNationalPrefix && format.nationalPrefixFormattingRule() ? format.format().replace(FIRST_GROUP_PATTERN, format.nationalPrefixFormattingRule()) : format.format());\n  if (useInternationalFormat) {\n    return applyInternationalSeparatorStyle(formattedNumber);\n  }\n  return formattedNumber;\n}", "map": {"version": 3, "names": ["applyInternationalSeparatorStyle", "FIRST_GROUP_PATTERN", "formatNationalNumberUsingFormat", "number", "format", "_ref", "useInternationalFormat", "withNationalPrefix", "carrierCode", "metadata", "formattedNumber", "replace", "RegExp", "pattern", "internationalFormat", "nationalPrefixFormattingRule"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\helpers\\formatNationalNumberUsingFormat.js"], "sourcesContent": ["import applyInternationalSeparatorStyle from './applyInternationalSeparatorStyle.js'\r\n\r\n// This was originally set to $1 but there are some countries for which the\r\n// first group is not used in the national pattern (e.g. Argentina) so the $1\r\n// group does not match correctly. Therefore, we use `\\d`, so that the first\r\n// group actually used in the pattern will be matched.\r\nexport const FIRST_GROUP_PATTERN = /(\\$\\d)/\r\n\r\nexport default function formatNationalNumberUsingFormat(\r\n\tnumber,\r\n\tformat,\r\n\t{\r\n\t\tuseInternationalFormat,\r\n\t\twithNationalPrefix,\r\n\t\tcarrierCode,\r\n\t\tmetadata\r\n\t}\r\n) {\r\n\tconst formattedNumber = number.replace(\r\n\t\tnew RegExp(format.pattern()),\r\n\t\tuseInternationalFormat\r\n\t\t\t? format.internationalFormat()\r\n\t\t\t: (\r\n\t\t\t\t// This library doesn't use `domestic_carrier_code_formatting_rule`,\r\n\t\t\t\t// because that one is only used when formatting phone numbers\r\n\t\t\t\t// for dialing from a mobile phone, and this is not a dialing library.\r\n\t\t\t\t// carrierCode && format.domesticCarrierCodeFormattingRule()\r\n\t\t\t\t// \t// First, replace the $CC in the formatting rule with the desired carrier code.\r\n\t\t\t\t// \t// Then, replace the $FG in the formatting rule with the first group\r\n\t\t\t\t// \t// and the carrier code combined in the appropriate way.\r\n\t\t\t\t// \t? format.format().replace(FIRST_GROUP_PATTERN, format.domesticCarrierCodeFormattingRule().replace('$CC', carrierCode))\r\n\t\t\t\t// \t: (\r\n\t\t\t\t// \t\twithNationalPrefix && format.nationalPrefixFormattingRule()\r\n\t\t\t\t// \t\t\t? format.format().replace(FIRST_GROUP_PATTERN, format.nationalPrefixFormattingRule())\r\n\t\t\t\t// \t\t\t: format.format()\r\n\t\t\t\t// \t)\r\n\t\t\t\twithNationalPrefix && format.nationalPrefixFormattingRule()\r\n\t\t\t\t\t? format.format().replace(FIRST_GROUP_PATTERN, format.nationalPrefixFormattingRule())\r\n\t\t\t\t\t: format.format()\r\n\t\t\t)\r\n\t)\r\n\tif (useInternationalFormat) {\r\n\t\treturn applyInternationalSeparatorStyle(formattedNumber)\r\n\t}\r\n\treturn formattedNumber\r\n}"], "mappings": "AAAA,OAAOA,gCAAP,MAA6C,uCAA7C,C,CAEA;AACA;AACA;AACA;;AACA,OAAO,IAAMC,mBAAmB,GAAG,QAA5B;AAEP,eAAe,SAASC,+BAATA,CACdC,MADc,EAEdC,MAFc,EAAAC,IAAA,EASb;EAAA,IALAC,sBAKA,GAAAD,IAAA,CALAC,sBAKA;IAJAC,kBAIA,GAAAF,IAAA,CAJAE,kBAIA;IAHAC,WAGA,GAAAH,IAAA,CAHAG,WAGA;IAFAC,QAEA,GAAAJ,IAAA,CAFAI,QAEA;EACD,IAAMC,eAAe,GAAGP,MAAM,CAACQ,OAAP,CACvB,IAAIC,MAAJ,CAAWR,MAAM,CAACS,OAAP,EAAX,CADuB,EAEvBP,sBAAsB,GACnBF,MAAM,CAACU,mBAAP,EADmB;EAGpB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAP,kBAAkB,IAAIH,MAAM,CAACW,4BAAP,EAAtB,GACGX,MAAM,CAACA,MAAP,GAAgBO,OAAhB,CAAwBV,mBAAxB,EAA6CG,MAAM,CAACW,4BAAP,EAA7C,CADH,GAEGX,MAAM,CAACA,MAAP,EApBkB,CAAxB;EAuBA,IAAIE,sBAAJ,EAA4B;IAC3B,OAAON,gCAAgC,CAACU,eAAD,CAAvC;EACA;EACD,OAAOA,eAAP;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}