{"ast": null, "code": "import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getTabPanelUnstyledUtilityClass(slot) {\n  return generateUtilityClass('TabPanelUnstyled', slot);\n}\nconst tabPanelUnstyledClasses = generateUtilityClasses('TabPanelUnstyled', ['root', 'hidden']);\nexport default tabPanelUnstyledClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTabPanelUnstyledUtilityClass", "slot", "tabPanelUnstyledClasses"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/TabPanelUnstyled/tabPanelUnstyledClasses.js"], "sourcesContent": ["import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getTabPanelUnstyledUtilityClass(slot) {\n  return generateUtilityClass('TabPanelUnstyled', slot);\n}\nconst tabPanelUnstyledClasses = generateUtilityClasses('TabPanelUnstyled', ['root', 'hidden']);\nexport default tabPanelUnstyledClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,yBAAyB;AAC1D,OAAOC,sBAAsB,MAAM,2BAA2B;AAC9D,OAAO,SAASC,+BAA+BA,CAACC,IAAI,EAAE;EACpD,OAAOH,oBAAoB,CAAC,kBAAkB,EAAEG,IAAI,CAAC;AACvD;AACA,MAAMC,uBAAuB,GAAGH,sBAAsB,CAAC,kBAAkB,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC9F,eAAeG,uBAAuB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}