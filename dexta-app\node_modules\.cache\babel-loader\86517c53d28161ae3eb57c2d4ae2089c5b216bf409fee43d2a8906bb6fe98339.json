{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { sample } from './sample';\nimport { interval } from '../observable/interval';\nexport function sampleTime(period, scheduler) {\n  if (scheduler === void 0) {\n    scheduler = asyncScheduler;\n  }\n  return sample(interval(period, scheduler));\n}", "map": {"version": 3, "names": ["asyncScheduler", "sample", "interval", "sampleTime", "period", "scheduler"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\sampleTime.ts"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { MonoTypeOperatorFunction, SchedulerLike } from '../types';\nimport { sample } from './sample';\nimport { interval } from '../observable/interval';\n\n/**\n * Emits the most recently emitted value from the source Observable within\n * periodic time intervals.\n *\n * <span class=\"informal\">Samples the source Observable at periodic time\n * intervals, emitting what it samples.</span>\n *\n * ![](sampleTime.png)\n *\n * `sampleTime` periodically looks at the source Observable and emits whichever\n * value it has most recently emitted since the previous sampling, unless the\n * source has not emitted anything since the previous sampling. The sampling\n * happens periodically in time every `period` milliseconds (or the time unit\n * defined by the optional `scheduler` argument). The sampling starts as soon as\n * the output Observable is subscribed.\n *\n * ## Example\n *\n * Every second, emit the most recent click at most once\n *\n * ```ts\n * import { fromEvent, sampleTime } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(sampleTime(1000));\n *\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link auditTime}\n * @see {@link debounceTime}\n * @see {@link delay}\n * @see {@link sample}\n * @see {@link throttleTime}\n *\n * @param period The sampling period expressed in milliseconds or the time unit\n * determined internally by the optional `scheduler`.\n * @param scheduler The {@link SchedulerLike} to use for managing the timers\n * that handle the sampling.\n * @return A function that returns an Observable that emits the results of\n * sampling the values emitted by the source Observable at the specified time\n * interval.\n */\nexport function sampleTime<T>(period: number, scheduler: SchedulerLike = asyncScheduler): MonoTypeOperatorFunction<T> {\n  return sample(interval(period, scheduler));\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,oBAAoB;AAEnD,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,QAAQ,QAAQ,wBAAwB;AA6CjD,OAAM,SAAUC,UAAUA,CAAIC,MAAc,EAAEC,SAAyC;EAAzC,IAAAA,SAAA;IAAAA,SAAA,GAAAL,cAAyC;EAAA;EACrF,OAAOC,MAAM,CAACC,QAAQ,CAACE,MAAM,EAAEC,SAAS,CAAC,CAAC;AAC5C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}