{"ast": null, "code": "export var isArrayLike = function (x) {\n  return x && typeof x.length === 'number' && typeof x !== 'function';\n};", "map": {"version": 3, "names": ["isArrayLike", "x", "length"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\util\\isArrayLike.ts"], "sourcesContent": ["export const isArrayLike = (<T>(x: any): x is ArrayLike<T> => x && typeof x.length === 'number' && typeof x !== 'function');"], "mappings": "AAAA,OAAO,IAAMA,WAAW,GAAI,SAAAA,CAAIC,CAAM;EAAwB,OAAAA,CAAC,IAAI,OAAOA,CAAC,CAACC,MAAM,KAAK,QAAQ,IAAI,OAAOD,CAAC,KAAK,UAAU;AAA5D,CAA6D"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}