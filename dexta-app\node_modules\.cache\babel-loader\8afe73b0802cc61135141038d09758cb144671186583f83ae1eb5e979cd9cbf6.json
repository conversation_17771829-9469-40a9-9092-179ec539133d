{"ast": null, "code": "import { Observable } from '../Observable';\nexport function fromSubscribable(subscribable) {\n  return new Observable(function (subscriber) {\n    return subscribable.subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["Observable", "fromSubscribable", "subscribable", "subscriber", "subscribe"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\observable\\fromSubscribable.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { Subscribable } from '../types';\n\n/**\n * Used to convert a subscribable to an observable.\n *\n * Currently, this is only used within internals.\n *\n * TODO: Discuss ObservableInput supporting \"Subscribable\".\n * https://github.com/ReactiveX/rxjs/issues/5909\n *\n * @param subscribable A subscribable\n */\nexport function fromSubscribable<T>(subscribable: Subscribable<T>) {\n  return new Observable((subscriber: Subscriber<T>) => subscribable.subscribe(subscriber));\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAc1C,OAAM,SAAUC,gBAAgBA,CAAIC,YAA6B;EAC/D,OAAO,IAAIF,UAAU,CAAC,UAACG,UAAyB;IAAK,OAAAD,YAAY,CAACE,SAAS,CAACD,UAAU,CAAC;EAAlC,CAAkC,CAAC;AAC1F"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}