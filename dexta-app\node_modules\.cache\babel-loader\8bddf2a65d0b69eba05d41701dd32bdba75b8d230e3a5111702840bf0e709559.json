{"ast": null, "code": "var ElementType = require(\"domelementtype\");\nvar isTag = exports.isTag = ElementType.isTag;\nexports.testElement = function (options, element) {\n  for (var key in options) {\n    if (!options.hasOwnProperty(key)) ;else if (key === \"tag_name\") {\n      if (!isTag(element) || !options.tag_name(element.name)) {\n        return false;\n      }\n    } else if (key === \"tag_type\") {\n      if (!options.tag_type(element.type)) return false;\n    } else if (key === \"tag_contains\") {\n      if (isTag(element) || !options.tag_contains(element.data)) {\n        return false;\n      }\n    } else if (!element.attribs || !options[key](element.attribs[key])) {\n      return false;\n    }\n  }\n  return true;\n};\nvar Checks = {\n  tag_name: function (name) {\n    if (typeof name === \"function\") {\n      return function (elem) {\n        return isTag(elem) && name(elem.name);\n      };\n    } else if (name === \"*\") {\n      return isTag;\n    } else {\n      return function (elem) {\n        return isTag(elem) && elem.name === name;\n      };\n    }\n  },\n  tag_type: function (type) {\n    if (typeof type === \"function\") {\n      return function (elem) {\n        return type(elem.type);\n      };\n    } else {\n      return function (elem) {\n        return elem.type === type;\n      };\n    }\n  },\n  tag_contains: function (data) {\n    if (typeof data === \"function\") {\n      return function (elem) {\n        return !isTag(elem) && data(elem.data);\n      };\n    } else {\n      return function (elem) {\n        return !isTag(elem) && elem.data === data;\n      };\n    }\n  }\n};\nfunction getAttribCheck(attrib, value) {\n  if (typeof value === \"function\") {\n    return function (elem) {\n      return elem.attribs && value(elem.attribs[attrib]);\n    };\n  } else {\n    return function (elem) {\n      return elem.attribs && elem.attribs[attrib] === value;\n    };\n  }\n}\nfunction combineFuncs(a, b) {\n  return function (elem) {\n    return a(elem) || b(elem);\n  };\n}\nexports.getElements = function (options, element, recurse, limit) {\n  var funcs = Object.keys(options).map(function (key) {\n    var value = options[key];\n    return key in Checks ? Checks[key](value) : getAttribCheck(key, value);\n  });\n  return funcs.length === 0 ? [] : this.filter(funcs.reduce(combineFuncs), element, recurse, limit);\n};\nexports.getElementById = function (id, element, recurse) {\n  if (!Array.isArray(element)) element = [element];\n  return this.findOne(getAttribCheck(\"id\", id), element, recurse !== false);\n};\nexports.getElementsByTagName = function (name, element, recurse, limit) {\n  return this.filter(Checks.tag_name(name), element, recurse, limit);\n};\nexports.getElementsByTagType = function (type, element, recurse, limit) {\n  return this.filter(Checks.tag_type(type), element, recurse, limit);\n};", "map": {"version": 3, "names": ["ElementType", "require", "isTag", "exports", "testElement", "options", "element", "key", "hasOwnProperty", "tag_name", "name", "tag_type", "type", "tag_contains", "data", "attribs", "Checks", "elem", "getAttribCheck", "attrib", "value", "combineFuncs", "a", "b", "getElements", "recurse", "limit", "funcs", "Object", "keys", "map", "length", "filter", "reduce", "getElementById", "id", "Array", "isArray", "findOne", "getElementsByTagName", "getElementsByTagType"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/domutils/lib/legacy.js"], "sourcesContent": ["var ElementType = require(\"domelementtype\");\nvar isTag = exports.isTag = ElementType.isTag;\n\nexports.testElement = function(options, element){\n\tfor(var key in options){\n\t\tif(!options.hasOwnProperty(key));\n\t\telse if(key === \"tag_name\"){\n\t\t\tif(!isTag(element) || !options.tag_name(element.name)){\n\t\t\t\treturn false;\n\t\t\t}\n\t\t} else if(key === \"tag_type\"){\n\t\t\tif(!options.tag_type(element.type)) return false;\n\t\t} else if(key === \"tag_contains\"){\n\t\t\tif(isTag(element) || !options.tag_contains(element.data)){\n\t\t\t\treturn false;\n\t\t\t}\n\t\t} else if(!element.attribs || !options[key](element.attribs[key])){\n\t\t\treturn false;\n\t\t}\n\t}\n\treturn true;\n};\n\nvar Checks = {\n\ttag_name: function(name){\n\t\tif(typeof name === \"function\"){\n\t\t\treturn function(elem){ return isTag(elem) && name(elem.name); };\n\t\t} else if(name === \"*\"){\n\t\t\treturn isTag;\n\t\t} else {\n\t\t\treturn function(elem){ return isTag(elem) && elem.name === name; };\n\t\t}\n\t},\n\ttag_type: function(type){\n\t\tif(typeof type === \"function\"){\n\t\t\treturn function(elem){ return type(elem.type); };\n\t\t} else {\n\t\t\treturn function(elem){ return elem.type === type; };\n\t\t}\n\t},\n\ttag_contains: function(data){\n\t\tif(typeof data === \"function\"){\n\t\t\treturn function(elem){ return !isTag(elem) && data(elem.data); };\n\t\t} else {\n\t\t\treturn function(elem){ return !isTag(elem) && elem.data === data; };\n\t\t}\n\t}\n};\n\nfunction getAttribCheck(attrib, value){\n\tif(typeof value === \"function\"){\n\t\treturn function(elem){ return elem.attribs && value(elem.attribs[attrib]); };\n\t} else {\n\t\treturn function(elem){ return elem.attribs && elem.attribs[attrib] === value; };\n\t}\n}\n\nfunction combineFuncs(a, b){\n\treturn function(elem){\n\t\treturn a(elem) || b(elem);\n\t};\n}\n\nexports.getElements = function(options, element, recurse, limit){\n\tvar funcs = Object.keys(options).map(function(key){\n\t\tvar value = options[key];\n\t\treturn key in Checks ? Checks[key](value) : getAttribCheck(key, value);\n\t});\n\n\treturn funcs.length === 0 ? [] : this.filter(\n\t\tfuncs.reduce(combineFuncs),\n\t\telement, recurse, limit\n\t);\n};\n\nexports.getElementById = function(id, element, recurse){\n\tif(!Array.isArray(element)) element = [element];\n\treturn this.findOne(getAttribCheck(\"id\", id), element, recurse !== false);\n};\n\nexports.getElementsByTagName = function(name, element, recurse, limit){\n\treturn this.filter(Checks.tag_name(name), element, recurse, limit);\n};\n\nexports.getElementsByTagType = function(type, element, recurse, limit){\n\treturn this.filter(Checks.tag_type(type), element, recurse, limit);\n};\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,gBAAgB,CAAC;AAC3C,IAAIC,KAAK,GAAGC,OAAO,CAACD,KAAK,GAAGF,WAAW,CAACE,KAAK;AAE7CC,OAAO,CAACC,WAAW,GAAG,UAASC,OAAO,EAAEC,OAAO,EAAC;EAC/C,KAAI,IAAIC,GAAG,IAAIF,OAAO,EAAC;IACtB,IAAG,CAACA,OAAO,CAACG,cAAc,CAACD,GAAG,CAAC,EAAC,CAAC,KAC5B,IAAGA,GAAG,KAAK,UAAU,EAAC;MAC1B,IAAG,CAACL,KAAK,CAACI,OAAO,CAAC,IAAI,CAACD,OAAO,CAACI,QAAQ,CAACH,OAAO,CAACI,IAAI,CAAC,EAAC;QACrD,OAAO,KAAK;MACb;IACD,CAAC,MAAM,IAAGH,GAAG,KAAK,UAAU,EAAC;MAC5B,IAAG,CAACF,OAAO,CAACM,QAAQ,CAACL,OAAO,CAACM,IAAI,CAAC,EAAE,OAAO,KAAK;IACjD,CAAC,MAAM,IAAGL,GAAG,KAAK,cAAc,EAAC;MAChC,IAAGL,KAAK,CAACI,OAAO,CAAC,IAAI,CAACD,OAAO,CAACQ,YAAY,CAACP,OAAO,CAACQ,IAAI,CAAC,EAAC;QACxD,OAAO,KAAK;MACb;IACD,CAAC,MAAM,IAAG,CAACR,OAAO,CAACS,OAAO,IAAI,CAACV,OAAO,CAACE,GAAG,CAAC,CAACD,OAAO,CAACS,OAAO,CAACR,GAAG,CAAC,CAAC,EAAC;MACjE,OAAO,KAAK;IACb;EACD;EACA,OAAO,IAAI;AACZ,CAAC;AAED,IAAIS,MAAM,GAAG;EACZP,QAAQ,EAAE,SAAAA,CAASC,IAAI,EAAC;IACvB,IAAG,OAAOA,IAAI,KAAK,UAAU,EAAC;MAC7B,OAAO,UAASO,IAAI,EAAC;QAAE,OAAOf,KAAK,CAACe,IAAI,CAAC,IAAIP,IAAI,CAACO,IAAI,CAACP,IAAI,CAAC;MAAE,CAAC;IAChE,CAAC,MAAM,IAAGA,IAAI,KAAK,GAAG,EAAC;MACtB,OAAOR,KAAK;IACb,CAAC,MAAM;MACN,OAAO,UAASe,IAAI,EAAC;QAAE,OAAOf,KAAK,CAACe,IAAI,CAAC,IAAIA,IAAI,CAACP,IAAI,KAAKA,IAAI;MAAE,CAAC;IACnE;EACD,CAAC;EACDC,QAAQ,EAAE,SAAAA,CAASC,IAAI,EAAC;IACvB,IAAG,OAAOA,IAAI,KAAK,UAAU,EAAC;MAC7B,OAAO,UAASK,IAAI,EAAC;QAAE,OAAOL,IAAI,CAACK,IAAI,CAACL,IAAI,CAAC;MAAE,CAAC;IACjD,CAAC,MAAM;MACN,OAAO,UAASK,IAAI,EAAC;QAAE,OAAOA,IAAI,CAACL,IAAI,KAAKA,IAAI;MAAE,CAAC;IACpD;EACD,CAAC;EACDC,YAAY,EAAE,SAAAA,CAASC,IAAI,EAAC;IAC3B,IAAG,OAAOA,IAAI,KAAK,UAAU,EAAC;MAC7B,OAAO,UAASG,IAAI,EAAC;QAAE,OAAO,CAACf,KAAK,CAACe,IAAI,CAAC,IAAIH,IAAI,CAACG,IAAI,CAACH,IAAI,CAAC;MAAE,CAAC;IACjE,CAAC,MAAM;MACN,OAAO,UAASG,IAAI,EAAC;QAAE,OAAO,CAACf,KAAK,CAACe,IAAI,CAAC,IAAIA,IAAI,CAACH,IAAI,KAAKA,IAAI;MAAE,CAAC;IACpE;EACD;AACD,CAAC;AAED,SAASI,cAAcA,CAACC,MAAM,EAAEC,KAAK,EAAC;EACrC,IAAG,OAAOA,KAAK,KAAK,UAAU,EAAC;IAC9B,OAAO,UAASH,IAAI,EAAC;MAAE,OAAOA,IAAI,CAACF,OAAO,IAAIK,KAAK,CAACH,IAAI,CAACF,OAAO,CAACI,MAAM,CAAC,CAAC;IAAE,CAAC;EAC7E,CAAC,MAAM;IACN,OAAO,UAASF,IAAI,EAAC;MAAE,OAAOA,IAAI,CAACF,OAAO,IAAIE,IAAI,CAACF,OAAO,CAACI,MAAM,CAAC,KAAKC,KAAK;IAAE,CAAC;EAChF;AACD;AAEA,SAASC,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAC;EAC1B,OAAO,UAASN,IAAI,EAAC;IACpB,OAAOK,CAAC,CAACL,IAAI,CAAC,IAAIM,CAAC,CAACN,IAAI,CAAC;EAC1B,CAAC;AACF;AAEAd,OAAO,CAACqB,WAAW,GAAG,UAASnB,OAAO,EAAEC,OAAO,EAAEmB,OAAO,EAAEC,KAAK,EAAC;EAC/D,IAAIC,KAAK,GAAGC,MAAM,CAACC,IAAI,CAACxB,OAAO,CAAC,CAACyB,GAAG,CAAC,UAASvB,GAAG,EAAC;IACjD,IAAIa,KAAK,GAAGf,OAAO,CAACE,GAAG,CAAC;IACxB,OAAOA,GAAG,IAAIS,MAAM,GAAGA,MAAM,CAACT,GAAG,CAAC,CAACa,KAAK,CAAC,GAAGF,cAAc,CAACX,GAAG,EAAEa,KAAK,CAAC;EACvE,CAAC,CAAC;EAEF,OAAOO,KAAK,CAACI,MAAM,KAAK,CAAC,GAAG,EAAE,GAAG,IAAI,CAACC,MAAM,CAC3CL,KAAK,CAACM,MAAM,CAACZ,YAAY,CAAC,EAC1Bf,OAAO,EAAEmB,OAAO,EAAEC,KACnB,CAAC;AACF,CAAC;AAEDvB,OAAO,CAAC+B,cAAc,GAAG,UAASC,EAAE,EAAE7B,OAAO,EAAEmB,OAAO,EAAC;EACtD,IAAG,CAACW,KAAK,CAACC,OAAO,CAAC/B,OAAO,CAAC,EAAEA,OAAO,GAAG,CAACA,OAAO,CAAC;EAC/C,OAAO,IAAI,CAACgC,OAAO,CAACpB,cAAc,CAAC,IAAI,EAAEiB,EAAE,CAAC,EAAE7B,OAAO,EAAEmB,OAAO,KAAK,KAAK,CAAC;AAC1E,CAAC;AAEDtB,OAAO,CAACoC,oBAAoB,GAAG,UAAS7B,IAAI,EAAEJ,OAAO,EAAEmB,OAAO,EAAEC,KAAK,EAAC;EACrE,OAAO,IAAI,CAACM,MAAM,CAAChB,MAAM,CAACP,QAAQ,CAACC,IAAI,CAAC,EAAEJ,OAAO,EAAEmB,OAAO,EAAEC,KAAK,CAAC;AACnE,CAAC;AAEDvB,OAAO,CAACqC,oBAAoB,GAAG,UAAS5B,IAAI,EAAEN,OAAO,EAAEmB,OAAO,EAAEC,KAAK,EAAC;EACrE,OAAO,IAAI,CAACM,MAAM,CAAChB,MAAM,CAACL,QAAQ,CAACC,IAAI,CAAC,EAAEN,OAAO,EAAEmB,OAAO,EAAEC,KAAK,CAAC;AACnE,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}