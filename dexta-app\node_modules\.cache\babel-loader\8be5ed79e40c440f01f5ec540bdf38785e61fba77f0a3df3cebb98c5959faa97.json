{"ast": null, "code": "// Copy-pasted from `PhoneNumberMatcher.js`.\nimport { PLUS_CHARS } from '../constants.js';\nimport { limit } from './util.js';\nimport { isLatinLetter, isInvalidPunctuationSymbol } from './utf-8.js';\nvar OPENING_PARENS = \"(\\\\[\\uFF08\\uFF3B\";\nvar CLOSING_PARENS = \")\\\\]\\uFF09\\uFF3D\";\nvar NON_PARENS = \"[^\".concat(OPENING_PARENS).concat(CLOSING_PARENS, \"]\");\nexport var LEAD_CLASS = \"[\".concat(OPENING_PARENS).concat(PLUS_CHARS, \"]\"); // Punctuation that may be at the start of a phone number - brackets and plus signs.\n\nvar LEAD_CLASS_LEADING = new RegExp('^' + LEAD_CLASS); // Limit on the number of pairs of brackets in a phone number.\n\nvar BRACKET_PAIR_LIMIT = limit(0, 3);\n/**\r\n * <PERSON><PERSON> to check that brackets match. Opening brackets should be closed within a phone number.\r\n * This also checks that there is something inside the brackets. Having no brackets at all is also\r\n * fine.\r\n *\r\n * An opening bracket at the beginning may not be closed, but subsequent ones should be.  It's\r\n * also possible that the leading bracket was dropped, so we shouldn't be surprised if we see a\r\n * closing bracket first. We limit the sets of brackets in a phone number to four.\r\n */\n\nvar MATCHING_BRACKETS_ENTIRE = new RegExp('^' + \"(?:[\" + OPENING_PARENS + \"])?\" + \"(?:\" + NON_PARENS + \"+\" + \"[\" + CLOSING_PARENS + \"])?\" + NON_PARENS + \"+\" + \"(?:[\" + OPENING_PARENS + \"]\" + NON_PARENS + \"+[\" + CLOSING_PARENS + \"])\" + BRACKET_PAIR_LIMIT + NON_PARENS + \"*\" + '$');\n/**\r\n * Matches strings that look like publication pages. Example:\r\n * <pre>Computing Complete Answers to Queries in the Presence of Limited Access Patterns.\r\n * Chen Li. VLDB J. 12(3): 211-227 (2003).</pre>\r\n *\r\n * The string \"211-227 (2003)\" is not a telephone number.\r\n */\n\nvar PUB_PAGES = /\\d{1,5}-+\\d{1,5}\\s{0,4}\\(\\d{1,4}/;\nexport default function isValidCandidate(candidate, offset, text, leniency) {\n  // Check the candidate doesn't contain any formatting\n  // which would indicate that it really isn't a phone number.\n  if (!MATCHING_BRACKETS_ENTIRE.test(candidate) || PUB_PAGES.test(candidate)) {\n    return;\n  } // If leniency is set to VALID or stricter, we also want to skip numbers that are surrounded\n  // by Latin alphabetic characters, to skip cases like abc8005001234 or 8005001234def.\n\n  if (leniency !== 'POSSIBLE') {\n    // If the candidate is not at the start of the text,\n    // and does not start with phone-number punctuation,\n    // check the previous character.\n    if (offset > 0 && !LEAD_CLASS_LEADING.test(candidate)) {\n      var previousChar = text[offset - 1]; // We return null if it is a latin letter or an invalid punctuation symbol.\n\n      if (isInvalidPunctuationSymbol(previousChar) || isLatinLetter(previousChar)) {\n        return false;\n      }\n    }\n    var lastCharIndex = offset + candidate.length;\n    if (lastCharIndex < text.length) {\n      var nextChar = text[lastCharIndex];\n      if (isInvalidPunctuationSymbol(nextChar) || isLatinLetter(nextChar)) {\n        return false;\n      }\n    }\n  }\n  return true;\n}", "map": {"version": 3, "names": ["PLUS_CHARS", "limit", "isLatinLetter", "isInvalidPunctuationSymbol", "OPENING_PARENS", "CLOSING_PARENS", "NON_PARENS", "concat", "LEAD_CLASS", "LEAD_CLASS_LEADING", "RegExp", "BRACKET_PAIR_LIMIT", "MATCHING_BRACKETS_ENTIRE", "PUB_PAGES", "isValidCandidate", "candidate", "offset", "text", "leniency", "test", "previousChar", "lastCharIndex", "length", "nextChar"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\findNumbers\\isValidCandidate.js"], "sourcesContent": ["// Copy-pasted from `PhoneNumberMatcher.js`.\r\n\r\nimport { PLUS_CHARS } from '../constants.js'\r\nimport { limit } from './util.js'\r\n\r\nimport {\r\n\tisLatinLetter,\r\n\tisInvalidPunctuationSymbol\r\n} from './utf-8.js'\r\n\r\nconst OPENING_PARENS = '(\\\\[\\uFF08\\uFF3B'\r\nconst CLOSING_PARENS = ')\\\\]\\uFF09\\uFF3D'\r\nconst NON_PARENS = `[^${OPENING_PARENS}${CLOSING_PARENS}]`\r\n\r\nexport const LEAD_CLASS = `[${OPENING_PARENS}${PLUS_CHARS}]`\r\n\r\n// Punctuation that may be at the start of a phone number - brackets and plus signs.\r\nconst LEAD_CLASS_LEADING = new RegExp('^' + LEAD_CLASS)\r\n\r\n// Limit on the number of pairs of brackets in a phone number.\r\nconst BRACKET_PAIR_LIMIT = limit(0, 3)\r\n\r\n/**\r\n * Pattern to check that brackets match. Opening brackets should be closed within a phone number.\r\n * This also checks that there is something inside the brackets. Having no brackets at all is also\r\n * fine.\r\n *\r\n * An opening bracket at the beginning may not be closed, but subsequent ones should be.  It's\r\n * also possible that the leading bracket was dropped, so we shouldn't be surprised if we see a\r\n * closing bracket first. We limit the sets of brackets in a phone number to four.\r\n */\r\nconst MATCHING_BRACKETS_ENTIRE = new RegExp\r\n(\r\n\t'^'\r\n\t+ \"(?:[\" + OPENING_PARENS + \"])?\" + \"(?:\" + NON_PARENS + \"+\" + \"[\" + CLOSING_PARENS + \"])?\"\r\n\t+ NON_PARENS + \"+\"\r\n\t+ \"(?:[\" + OPENING_PARENS + \"]\" + NON_PARENS + \"+[\" + CLOSING_PARENS + \"])\" + BRACKET_PAIR_LIMIT\r\n\t+ NON_PARENS + \"*\"\r\n\t+ '$'\r\n)\r\n\r\n/**\r\n * Matches strings that look like publication pages. Example:\r\n * <pre>Computing Complete Answers to Queries in the Presence of Limited Access Patterns.\r\n * Chen Li. VLDB J. 12(3): 211-227 (2003).</pre>\r\n *\r\n * The string \"211-227 (2003)\" is not a telephone number.\r\n */\r\nconst PUB_PAGES = /\\d{1,5}-+\\d{1,5}\\s{0,4}\\(\\d{1,4}/\r\n\r\nexport default function isValidCandidate(candidate, offset, text, leniency)\r\n{\r\n\t// Check the candidate doesn't contain any formatting\r\n\t// which would indicate that it really isn't a phone number.\r\n\tif (!MATCHING_BRACKETS_ENTIRE.test(candidate) || PUB_PAGES.test(candidate)) {\r\n\t\treturn\r\n\t}\r\n\r\n\t// If leniency is set to VALID or stricter, we also want to skip numbers that are surrounded\r\n\t// by Latin alphabetic characters, to skip cases like abc8005001234 or 8005001234def.\r\n\tif (leniency !== 'POSSIBLE')\r\n\t{\r\n\t\t// If the candidate is not at the start of the text,\r\n\t\t// and does not start with phone-number punctuation,\r\n\t\t// check the previous character.\r\n\t\tif (offset > 0 && !LEAD_CLASS_LEADING.test(candidate))\r\n\t\t{\r\n\t\t\tconst previousChar = text[offset - 1]\r\n\t\t\t// We return null if it is a latin letter or an invalid punctuation symbol.\r\n\t\t\tif (isInvalidPunctuationSymbol(previousChar) || isLatinLetter(previousChar)) {\r\n\t\t\t\treturn false\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tconst lastCharIndex = offset + candidate.length\r\n\t\tif (lastCharIndex < text.length)\r\n\t\t{\r\n\t\t\tconst nextChar = text[lastCharIndex]\r\n\t\t\tif (isInvalidPunctuationSymbol(nextChar) || isLatinLetter(nextChar)) {\r\n\t\t\t\treturn false\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn true\r\n}"], "mappings": "AAAA;AAEA,SAASA,UAAT,QAA2B,iBAA3B;AACA,SAASC,KAAT,QAAsB,WAAtB;AAEA,SACCC,aADD,EAECC,0BAFD,QAGO,YAHP;AAKA,IAAMC,cAAc,GAAG,kBAAvB;AACA,IAAMC,cAAc,GAAG,kBAAvB;AACA,IAAMC,UAAU,QAAAC,MAAA,CAAQH,cAAR,EAAAG,MAAA,CAAyBF,cAAzB,MAAhB;AAEA,OAAO,IAAMG,UAAU,OAAAD,MAAA,CAAOH,cAAP,EAAAG,MAAA,CAAwBP,UAAxB,MAAhB,C,CAEP;;AACA,IAAMS,kBAAkB,GAAG,IAAIC,MAAJ,CAAW,MAAMF,UAAjB,CAA3B,C,CAEA;;AACA,IAAMG,kBAAkB,GAAGV,KAAK,CAAC,CAAD,EAAI,CAAJ,CAAhC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAMW,wBAAwB,GAAG,IAAIF,MAAJ,CAEhC,MACE,MADF,GACWN,cADX,GAC4B,KAD5B,GACoC,KADpC,GAC4CE,UAD5C,GACyD,GADzD,GAC+D,GAD/D,GACqED,cADrE,GACsF,KADtF,GAEEC,UAFF,GAEe,GAFf,GAGE,MAHF,GAGWF,cAHX,GAG4B,GAH5B,GAGkCE,UAHlC,GAG+C,IAH/C,GAGsDD,cAHtD,GAGuE,IAHvE,GAG8EM,kBAH9E,GAIEL,UAJF,GAIe,GAJf,GAKE,GAP8B,CAAjC;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAMO,SAAS,GAAG,kCAAlB;AAEA,eAAe,SAASC,gBAATA,CAA0BC,SAA1B,EAAqCC,MAArC,EAA6CC,IAA7C,EAAmDC,QAAnD,EACf;EACC;EACA;EACA,IAAI,CAACN,wBAAwB,CAACO,IAAzB,CAA8BJ,SAA9B,CAAD,IAA6CF,SAAS,CAACM,IAAV,CAAeJ,SAAf,CAAjD,EAA4E;IAC3E;EACA,CALF,CAOC;EACA;;EACA,IAAIG,QAAQ,KAAK,UAAjB,EACA;IACC;IACA;IACA;IACA,IAAIF,MAAM,GAAG,CAAT,IAAc,CAACP,kBAAkB,CAACU,IAAnB,CAAwBJ,SAAxB,CAAnB,EACA;MACC,IAAMK,YAAY,GAAGH,IAAI,CAACD,MAAM,GAAG,CAAV,CAAzB,CADD,CAEC;;MACA,IAAIb,0BAA0B,CAACiB,YAAD,CAA1B,IAA4ClB,aAAa,CAACkB,YAAD,CAA7D,EAA6E;QAC5E,OAAO,KAAP;MACA;IACD;IAED,IAAMC,aAAa,GAAGL,MAAM,GAAGD,SAAS,CAACO,MAAzC;IACA,IAAID,aAAa,GAAGJ,IAAI,CAACK,MAAzB,EACA;MACC,IAAMC,QAAQ,GAAGN,IAAI,CAACI,aAAD,CAArB;MACA,IAAIlB,0BAA0B,CAACoB,QAAD,CAA1B,IAAwCrB,aAAa,CAACqB,QAAD,CAAzD,EAAqE;QACpE,OAAO,KAAP;MACA;IACD;EACD;EAED,OAAO,IAAP;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}