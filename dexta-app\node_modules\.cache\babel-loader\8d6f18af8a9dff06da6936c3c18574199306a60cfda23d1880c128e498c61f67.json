{"ast": null, "code": "import { zip } from '../observable/zip';\nimport { joinAllInternals } from './joinAllInternals';\nexport function zipAll(project) {\n  return joinAllInternals(zip, project);\n}", "map": {"version": 3, "names": ["zip", "joinAllInternals", "zipAll", "project"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\zipAll.ts"], "sourcesContent": ["import { OperatorFunction, ObservableInput } from '../types';\nimport { zip } from '../observable/zip';\nimport { joinAllInternals } from './joinAllInternals';\n\n/**\n * Collects all observable inner sources from the source, once the source completes,\n * it will subscribe to all inner sources, combining their values by index and emitting\n * them.\n *\n * @see {@link zipWith}\n * @see {@link zip}\n */\nexport function zipAll<T>(): OperatorFunction<ObservableInput<T>, T[]>;\nexport function zipAll<T>(): OperatorFunction<any, T[]>;\nexport function zipAll<T, R>(project: (...values: T[]) => R): OperatorFunction<ObservableInput<T>, R>;\nexport function zipAll<R>(project: (...values: Array<any>) => R): OperatorFunction<any, R>;\n\nexport function zipAll<T, R>(project?: (...values: T[]) => R) {\n  return joinAllInternals(zip, project);\n}\n"], "mappings": "AACA,SAASA,GAAG,QAAQ,mBAAmB;AACvC,SAASC,gBAAgB,QAAQ,oBAAoB;AAerD,OAAM,SAAUC,MAAMA,CAAOC,OAA+B;EAC1D,OAAOF,gBAAgB,CAACD,GAAG,EAAEG,OAAO,CAAC;AACvC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}