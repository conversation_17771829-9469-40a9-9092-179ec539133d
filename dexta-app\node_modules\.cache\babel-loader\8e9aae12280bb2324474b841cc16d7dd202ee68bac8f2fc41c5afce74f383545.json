{"ast": null, "code": "// removeSubsets\n// Given an array of nodes, remove any member that is contained by another.\nexports.removeSubsets = function (nodes) {\n  var idx = nodes.length,\n    node,\n    ancestor,\n    replace;\n\n  // Check if each node (or one of its ancestors) is already contained in the\n  // array.\n  while (--idx > -1) {\n    node = ancestor = nodes[idx];\n\n    // Temporarily remove the node under consideration\n    nodes[idx] = null;\n    replace = true;\n    while (ancestor) {\n      if (nodes.indexOf(ancestor) > -1) {\n        replace = false;\n        nodes.splice(idx, 1);\n        break;\n      }\n      ancestor = ancestor.parent;\n    }\n\n    // If the node has been found to be unique, re-insert it.\n    if (replace) {\n      nodes[idx] = node;\n    }\n  }\n  return nodes;\n};\n\n// Source: http://dom.spec.whatwg.org/#dom-node-comparedocumentposition\nvar POSITION = {\n  DISCONNECTED: 1,\n  PRECEDING: 2,\n  FOLLOWING: 4,\n  CONTAINS: 8,\n  CONTAINED_BY: 16\n};\n\n// Compare the position of one node against another node in any other document.\n// The return value is a bitmask with the following values:\n//\n// document order:\n// > There is an ordering, document order, defined on all the nodes in the\n// > document corresponding to the order in which the first character of the\n// > XML representation of each node occurs in the XML representation of the\n// > document after expansion of general entities. Thus, the document element\n// > node will be the first node. Element nodes occur before their children.\n// > Thus, document order orders element nodes in order of the occurrence of\n// > their start-tag in the XML (after expansion of entities). The attribute\n// > nodes of an element occur after the element and before its children. The\n// > relative order of attribute nodes is implementation-dependent./\n// Source:\n// http://www.w3.org/TR/DOM-Level-3-Core/glossary.html#dt-document-order\n//\n// @argument {Node} nodaA The first node to use in the comparison\n// @argument {Node} nodeB The second node to use in the comparison\n//\n// @return {Number} A bitmask describing the input nodes' relative position.\n//         See http://dom.spec.whatwg.org/#dom-node-comparedocumentposition for\n//         a description of these values.\nvar comparePos = exports.compareDocumentPosition = function (nodeA, nodeB) {\n  var aParents = [];\n  var bParents = [];\n  var current, sharedParent, siblings, aSibling, bSibling, idx;\n  if (nodeA === nodeB) {\n    return 0;\n  }\n  current = nodeA;\n  while (current) {\n    aParents.unshift(current);\n    current = current.parent;\n  }\n  current = nodeB;\n  while (current) {\n    bParents.unshift(current);\n    current = current.parent;\n  }\n  idx = 0;\n  while (aParents[idx] === bParents[idx]) {\n    idx++;\n  }\n  if (idx === 0) {\n    return POSITION.DISCONNECTED;\n  }\n  sharedParent = aParents[idx - 1];\n  siblings = sharedParent.children;\n  aSibling = aParents[idx];\n  bSibling = bParents[idx];\n  if (siblings.indexOf(aSibling) > siblings.indexOf(bSibling)) {\n    if (sharedParent === nodeB) {\n      return POSITION.FOLLOWING | POSITION.CONTAINED_BY;\n    }\n    return POSITION.FOLLOWING;\n  } else {\n    if (sharedParent === nodeA) {\n      return POSITION.PRECEDING | POSITION.CONTAINS;\n    }\n    return POSITION.PRECEDING;\n  }\n};\n\n// Sort an array of nodes based on their relative position in the document and\n// remove any duplicate nodes. If the array contains nodes that do not belong\n// to the same document, sort order is unspecified.\n//\n// @argument {Array} nodes Array of DOM nodes\n//\n// @returns {Array} collection of unique nodes, sorted in document order\nexports.uniqueSort = function (nodes) {\n  var idx = nodes.length,\n    node,\n    position;\n  nodes = nodes.slice();\n  while (--idx > -1) {\n    node = nodes[idx];\n    position = nodes.indexOf(node);\n    if (position > -1 && position < idx) {\n      nodes.splice(idx, 1);\n    }\n  }\n  nodes.sort(function (a, b) {\n    var relative = comparePos(a, b);\n    if (relative & POSITION.PRECEDING) {\n      return -1;\n    } else if (relative & POSITION.FOLLOWING) {\n      return 1;\n    }\n    return 0;\n  });\n  return nodes;\n};", "map": {"version": 3, "names": ["exports", "removeSubsets", "nodes", "idx", "length", "node", "ancestor", "replace", "indexOf", "splice", "parent", "POSITION", "DISCONNECTED", "PRECEDING", "FOLLOWING", "CONTAINS", "CONTAINED_BY", "comparePos", "compareDocumentPosition", "nodeA", "nodeB", "aParents", "bPare<PERSON>", "current", "sharedParent", "siblings", "a<PERSON><PERSON>ling", "b<PERSON><PERSON>ling", "unshift", "children", "uniqueSort", "position", "slice", "sort", "a", "b", "relative"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/domutils/lib/helpers.js"], "sourcesContent": ["// removeSubsets\n// Given an array of nodes, remove any member that is contained by another.\nexports.removeSubsets = function(nodes) {\n\tvar idx = nodes.length, node, ancestor, replace;\n\n\t// Check if each node (or one of its ancestors) is already contained in the\n\t// array.\n\twhile (--idx > -1) {\n\t\tnode = ancestor = nodes[idx];\n\n\t\t// Temporarily remove the node under consideration\n\t\tnodes[idx] = null;\n\t\treplace = true;\n\n\t\twhile (ancestor) {\n\t\t\tif (nodes.indexOf(ancestor) > -1) {\n\t\t\t\treplace = false;\n\t\t\t\tnodes.splice(idx, 1);\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tancestor = ancestor.parent;\n\t\t}\n\n\t\t// If the node has been found to be unique, re-insert it.\n\t\tif (replace) {\n\t\t\tnodes[idx] = node;\n\t\t}\n\t}\n\n\treturn nodes;\n};\n\n// Source: http://dom.spec.whatwg.org/#dom-node-comparedocumentposition\nvar POSITION = {\n\tDISCONNECTED: 1,\n\tPRECEDING: 2,\n\tFOLLOWING: 4,\n\tCONTAINS: 8,\n\tCONTAINED_BY: 16\n};\n\n// Compare the position of one node against another node in any other document.\n// The return value is a bitmask with the following values:\n//\n// document order:\n// > There is an ordering, document order, defined on all the nodes in the\n// > document corresponding to the order in which the first character of the\n// > XML representation of each node occurs in the XML representation of the\n// > document after expansion of general entities. Thus, the document element\n// > node will be the first node. Element nodes occur before their children.\n// > Thus, document order orders element nodes in order of the occurrence of\n// > their start-tag in the XML (after expansion of entities). The attribute\n// > nodes of an element occur after the element and before its children. The\n// > relative order of attribute nodes is implementation-dependent./\n// Source:\n// http://www.w3.org/TR/DOM-Level-3-Core/glossary.html#dt-document-order\n//\n// @argument {Node} nodaA The first node to use in the comparison\n// @argument {Node} nodeB The second node to use in the comparison\n//\n// @return {Number} A bitmask describing the input nodes' relative position.\n//         See http://dom.spec.whatwg.org/#dom-node-comparedocumentposition for\n//         a description of these values.\nvar comparePos = exports.compareDocumentPosition = function(nodeA, nodeB) {\n\tvar aParents = [];\n\tvar bParents = [];\n\tvar current, sharedParent, siblings, aSibling, bSibling, idx;\n\n\tif (nodeA === nodeB) {\n\t\treturn 0;\n\t}\n\n\tcurrent = nodeA;\n\twhile (current) {\n\t\taParents.unshift(current);\n\t\tcurrent = current.parent;\n\t}\n\tcurrent = nodeB;\n\twhile (current) {\n\t\tbParents.unshift(current);\n\t\tcurrent = current.parent;\n\t}\n\n\tidx = 0;\n\twhile (aParents[idx] === bParents[idx]) {\n\t\tidx++;\n\t}\n\n\tif (idx === 0) {\n\t\treturn POSITION.DISCONNECTED;\n\t}\n\n\tsharedParent = aParents[idx - 1];\n\tsiblings = sharedParent.children;\n\taSibling = aParents[idx];\n\tbSibling = bParents[idx];\n\n\tif (siblings.indexOf(aSibling) > siblings.indexOf(bSibling)) {\n\t\tif (sharedParent === nodeB) {\n\t\t\treturn POSITION.FOLLOWING | POSITION.CONTAINED_BY;\n\t\t}\n\t\treturn POSITION.FOLLOWING;\n\t} else {\n\t\tif (sharedParent === nodeA) {\n\t\t\treturn POSITION.PRECEDING | POSITION.CONTAINS;\n\t\t}\n\t\treturn POSITION.PRECEDING;\n\t}\n};\n\n// Sort an array of nodes based on their relative position in the document and\n// remove any duplicate nodes. If the array contains nodes that do not belong\n// to the same document, sort order is unspecified.\n//\n// @argument {Array} nodes Array of DOM nodes\n//\n// @returns {Array} collection of unique nodes, sorted in document order\nexports.uniqueSort = function(nodes) {\n\tvar idx = nodes.length, node, position;\n\n\tnodes = nodes.slice();\n\n\twhile (--idx > -1) {\n\t\tnode = nodes[idx];\n\t\tposition = nodes.indexOf(node);\n\t\tif (position > -1 && position < idx) {\n\t\t\tnodes.splice(idx, 1);\n\t\t}\n\t}\n\tnodes.sort(function(a, b) {\n\t\tvar relative = comparePos(a, b);\n\t\tif (relative & POSITION.PRECEDING) {\n\t\t\treturn -1;\n\t\t} else if (relative & POSITION.FOLLOWING) {\n\t\t\treturn 1;\n\t\t}\n\t\treturn 0;\n\t});\n\n\treturn nodes;\n};\n"], "mappings": "AAAA;AACA;AACAA,OAAO,CAACC,aAAa,GAAG,UAASC,KAAK,EAAE;EACvC,IAAIC,GAAG,GAAGD,KAAK,CAACE,MAAM;IAAEC,IAAI;IAAEC,QAAQ;IAAEC,OAAO;;EAE/C;EACA;EACA,OAAO,EAAEJ,GAAG,GAAG,CAAC,CAAC,EAAE;IAClBE,IAAI,GAAGC,QAAQ,GAAGJ,KAAK,CAACC,GAAG,CAAC;;IAE5B;IACAD,KAAK,CAACC,GAAG,CAAC,GAAG,IAAI;IACjBI,OAAO,GAAG,IAAI;IAEd,OAAOD,QAAQ,EAAE;MAChB,IAAIJ,KAAK,CAACM,OAAO,CAACF,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;QACjCC,OAAO,GAAG,KAAK;QACfL,KAAK,CAACO,MAAM,CAACN,GAAG,EAAE,CAAC,CAAC;QACpB;MACD;MACAG,QAAQ,GAAGA,QAAQ,CAACI,MAAM;IAC3B;;IAEA;IACA,IAAIH,OAAO,EAAE;MACZL,KAAK,CAACC,GAAG,CAAC,GAAGE,IAAI;IAClB;EACD;EAEA,OAAOH,KAAK;AACb,CAAC;;AAED;AACA,IAAIS,QAAQ,GAAG;EACdC,YAAY,EAAE,CAAC;EACfC,SAAS,EAAE,CAAC;EACZC,SAAS,EAAE,CAAC;EACZC,QAAQ,EAAE,CAAC;EACXC,YAAY,EAAE;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAGjB,OAAO,CAACkB,uBAAuB,GAAG,UAASC,KAAK,EAAEC,KAAK,EAAE;EACzE,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAIC,OAAO,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAExB,GAAG;EAE5D,IAAIgB,KAAK,KAAKC,KAAK,EAAE;IACpB,OAAO,CAAC;EACT;EAEAG,OAAO,GAAGJ,KAAK;EACf,OAAOI,OAAO,EAAE;IACfF,QAAQ,CAACO,OAAO,CAACL,OAAO,CAAC;IACzBA,OAAO,GAAGA,OAAO,CAACb,MAAM;EACzB;EACAa,OAAO,GAAGH,KAAK;EACf,OAAOG,OAAO,EAAE;IACfD,QAAQ,CAACM,OAAO,CAACL,OAAO,CAAC;IACzBA,OAAO,GAAGA,OAAO,CAACb,MAAM;EACzB;EAEAP,GAAG,GAAG,CAAC;EACP,OAAOkB,QAAQ,CAAClB,GAAG,CAAC,KAAKmB,QAAQ,CAACnB,GAAG,CAAC,EAAE;IACvCA,GAAG,EAAE;EACN;EAEA,IAAIA,GAAG,KAAK,CAAC,EAAE;IACd,OAAOQ,QAAQ,CAACC,YAAY;EAC7B;EAEAY,YAAY,GAAGH,QAAQ,CAAClB,GAAG,GAAG,CAAC,CAAC;EAChCsB,QAAQ,GAAGD,YAAY,CAACK,QAAQ;EAChCH,QAAQ,GAAGL,QAAQ,CAAClB,GAAG,CAAC;EACxBwB,QAAQ,GAAGL,QAAQ,CAACnB,GAAG,CAAC;EAExB,IAAIsB,QAAQ,CAACjB,OAAO,CAACkB,QAAQ,CAAC,GAAGD,QAAQ,CAACjB,OAAO,CAACmB,QAAQ,CAAC,EAAE;IAC5D,IAAIH,YAAY,KAAKJ,KAAK,EAAE;MAC3B,OAAOT,QAAQ,CAACG,SAAS,GAAGH,QAAQ,CAACK,YAAY;IAClD;IACA,OAAOL,QAAQ,CAACG,SAAS;EAC1B,CAAC,MAAM;IACN,IAAIU,YAAY,KAAKL,KAAK,EAAE;MAC3B,OAAOR,QAAQ,CAACE,SAAS,GAAGF,QAAQ,CAACI,QAAQ;IAC9C;IACA,OAAOJ,QAAQ,CAACE,SAAS;EAC1B;AACD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACAb,OAAO,CAAC8B,UAAU,GAAG,UAAS5B,KAAK,EAAE;EACpC,IAAIC,GAAG,GAAGD,KAAK,CAACE,MAAM;IAAEC,IAAI;IAAE0B,QAAQ;EAEtC7B,KAAK,GAAGA,KAAK,CAAC8B,KAAK,CAAC,CAAC;EAErB,OAAO,EAAE7B,GAAG,GAAG,CAAC,CAAC,EAAE;IAClBE,IAAI,GAAGH,KAAK,CAACC,GAAG,CAAC;IACjB4B,QAAQ,GAAG7B,KAAK,CAACM,OAAO,CAACH,IAAI,CAAC;IAC9B,IAAI0B,QAAQ,GAAG,CAAC,CAAC,IAAIA,QAAQ,GAAG5B,GAAG,EAAE;MACpCD,KAAK,CAACO,MAAM,CAACN,GAAG,EAAE,CAAC,CAAC;IACrB;EACD;EACAD,KAAK,CAAC+B,IAAI,CAAC,UAASC,CAAC,EAAEC,CAAC,EAAE;IACzB,IAAIC,QAAQ,GAAGnB,UAAU,CAACiB,CAAC,EAAEC,CAAC,CAAC;IAC/B,IAAIC,QAAQ,GAAGzB,QAAQ,CAACE,SAAS,EAAE;MAClC,OAAO,CAAC,CAAC;IACV,CAAC,MAAM,IAAIuB,QAAQ,GAAGzB,QAAQ,CAACG,SAAS,EAAE;MACzC,OAAO,CAAC;IACT;IACA,OAAO,CAAC;EACT,CAAC,CAAC;EAEF,OAAOZ,KAAK;AACb,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}