{"ast": null, "code": "import template_formatter from './templateFormatter.js'; // Formats `value` value preserving `caret` at the same character.\n//\n// `{ value, caret }` attribute is the result of `parse()` function call.\n//\n// Returns `{ text, caret }` where the new `caret` is the caret position\n// inside `text` text corresponding to the original `caret` position inside `value`.\n//\n// `formatter(value)` is a function returning `{ text, template }`.\n//\n// `text` is the `value` value formatted using `template`.\n// It may either cut off the non-filled right part of the `template`\n// or it may fill the non-filled character placeholders\n// in the right part of the `template` with `spacer`\n// which is a space (' ') character by default.\n//\n// `template` is the template used to format the `value`.\n// It can be either a full-length template or a partial template.\n//\n// `formatter` can also be a string — a `template`\n// where character placeholders are denoted by 'x'es.\n// In this case `formatter` function is automatically created.\n//\n// Example:\n//\n// `value` is '880',\n// `caret` is `2` (before the first `0`)\n//\n// `formatter` is `'880' =>\n//   { text: '8 (80 )', template: 'x (xxx) xxx-xx-xx' }`\n//\n// The result is `{ text: '8 (80 )', caret: 4 }`.\n//\n\nexport default function format(value, caret, formatter) {\n  if (typeof formatter === 'string') {\n    formatter = template_formatter(formatter);\n  }\n  var _ref = formatter(value) || {},\n    text = _ref.text,\n    template = _ref.template;\n  if (text === undefined) {\n    text = value;\n  }\n  if (template) {\n    if (caret === undefined) {\n      caret = text.length;\n    } else {\n      var index = 0;\n      var found = false;\n      var possibly_last_input_character_index = -1;\n      while (index < text.length && index < template.length) {\n        // Character placeholder found\n        if (text[index] !== template[index]) {\n          if (caret === 0) {\n            found = true;\n            caret = index;\n            break;\n          }\n          possibly_last_input_character_index = index;\n          caret--;\n        }\n        index++;\n      } // If the caret was positioned after last input character,\n      // then the text caret index is just after the last input character.\n\n      if (!found) {\n        caret = possibly_last_input_character_index + 1;\n      }\n    }\n  }\n  return {\n    text: text,\n    caret: caret\n  };\n}", "map": {"version": 3, "names": ["template_formatter", "format", "value", "caret", "formatter", "_ref", "text", "template", "undefined", "length", "index", "found", "possibly_last_input_character_index"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\input-format\\source\\format.js"], "sourcesContent": ["import template_formatter from './templateFormatter.js'\r\n\r\n// Formats `value` value preserving `caret` at the same character.\r\n//\r\n// `{ value, caret }` attribute is the result of `parse()` function call.\r\n//\r\n// Returns `{ text, caret }` where the new `caret` is the caret position\r\n// inside `text` text corresponding to the original `caret` position inside `value`.\r\n//\r\n// `formatter(value)` is a function returning `{ text, template }`.\r\n//\r\n// `text` is the `value` value formatted using `template`.\r\n// It may either cut off the non-filled right part of the `template`\r\n// or it may fill the non-filled character placeholders\r\n// in the right part of the `template` with `spacer`\r\n// which is a space (' ') character by default.\r\n//\r\n// `template` is the template used to format the `value`.\r\n// It can be either a full-length template or a partial template.\r\n//\r\n// `formatter` can also be a string — a `template`\r\n// where character placeholders are denoted by 'x'es.\r\n// In this case `formatter` function is automatically created.\r\n//\r\n// Example:\r\n//\r\n// `value` is '880',\r\n// `caret` is `2` (before the first `0`)\r\n//\r\n// `formatter` is `'880' =>\r\n//   { text: '8 (80 )', template: 'x (xxx) xxx-xx-xx' }`\r\n//\r\n// The result is `{ text: '8 (80 )', caret: 4 }`.\r\n//\r\nexport default function format(value, caret, formatter)\r\n{\r\n\tif (typeof formatter === 'string')\r\n\t{\r\n\t\tformatter = template_formatter(formatter)\r\n\t}\r\n\r\n\tlet { text, template } = formatter(value) || {}\r\n\r\n\tif (text === undefined)\r\n\t{\r\n\t\t text = value\r\n\t}\r\n\r\n\tif (template)\r\n\t{\r\n\t\tif (caret === undefined)\r\n\t\t{\r\n\t\t\tcaret = text.length\r\n\t\t}\r\n\t\telse\r\n\t\t{\r\n\t\t\tlet index = 0\r\n\t\t\tlet found = false\r\n\r\n\t\t\tlet possibly_last_input_character_index = -1\r\n\r\n\t\t\twhile (index < text.length && index < template.length)\r\n\t\t\t{\r\n\t\t\t\t// Character placeholder found\r\n\t\t\t\tif (text[index] !== template[index])\r\n\t\t\t\t{\r\n\t\t\t\t\tif (caret === 0)\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tfound = true\r\n\t\t\t\t\t\tcaret = index\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tpossibly_last_input_character_index = index\r\n\r\n\t\t\t\t\tcaret--\r\n\t\t\t\t}\r\n\r\n\t\t\t\tindex++\r\n\t\t\t}\r\n\r\n\t\t\t// If the caret was positioned after last input character,\r\n\t\t\t// then the text caret index is just after the last input character.\r\n\t\t\tif (!found)\r\n\t\t\t{\r\n\t\t\t\tcaret = possibly_last_input_character_index + 1\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn { text, caret }\r\n}"], "mappings": "AAAA,OAAOA,kBAAP,MAA+B,wBAA/B,C,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASC,MAATA,CAAgBC,KAAhB,EAAuBC,KAAvB,EAA8BC,SAA9B,EACf;EACC,IAAI,OAAOA,SAAP,KAAqB,QAAzB,EACA;IACCA,SAAS,GAAGJ,kBAAkB,CAACI,SAAD,CAA9B;EACA;EAED,IAAAC,IAAA,GAAyBD,SAAS,CAACF,KAAD,CAAT,IAAoB,EAA7C;IAAMI,IAAN,GAAAD,IAAA,CAAMC,IAAN;IAAYC,QAAZ,GAAAF,IAAA,CAAYE,QAAZ;EAEA,IAAID,IAAI,KAAKE,SAAb,EACA;IACEF,IAAI,GAAGJ,KAAP;EACD;EAED,IAAIK,QAAJ,EACA;IACC,IAAIJ,KAAK,KAAKK,SAAd,EACA;MACCL,KAAK,GAAGG,IAAI,CAACG,MAAb;IACA,CAHD,MAKA;MACC,IAAIC,KAAK,GAAG,CAAZ;MACA,IAAIC,KAAK,GAAG,KAAZ;MAEA,IAAIC,mCAAmC,GAAG,CAAC,CAA3C;MAEA,OAAOF,KAAK,GAAGJ,IAAI,CAACG,MAAb,IAAuBC,KAAK,GAAGH,QAAQ,CAACE,MAA/C,EACA;QACC;QACA,IAAIH,IAAI,CAACI,KAAD,CAAJ,KAAgBH,QAAQ,CAACG,KAAD,CAA5B,EACA;UACC,IAAIP,KAAK,KAAK,CAAd,EACA;YACCQ,KAAK,GAAG,IAAR;YACAR,KAAK,GAAGO,KAAR;YACA;UACA;UAEDE,mCAAmC,GAAGF,KAAtC;UAEAP,KAAK;QACL;QAEDO,KAAK;MACL,CAxBF,CA0BC;MACA;;MACA,IAAI,CAACC,KAAL,EACA;QACCR,KAAK,GAAGS,mCAAmC,GAAG,CAA9C;MACA;IACD;EACD;EAED,OAAO;IAAEN,IAAI,EAAJA,IAAF;IAAQH,KAAK,EAALA;EAAR,CAAP;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}