{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function find(predicate, thisArg) {\n  return operate(createFind(predicate, thisArg, 'value'));\n}\nexport function createFind(predicate, thisArg, emit) {\n  var findIndex = emit === 'index';\n  return function (source, subscriber) {\n    var index = 0;\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      var i = index++;\n      if (predicate.call(thisArg, value, i, source)) {\n        subscriber.next(findIndex ? i : value);\n        subscriber.complete();\n      }\n    }, function () {\n      subscriber.next(findIndex ? -1 : undefined);\n      subscriber.complete();\n    }));\n  };\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "find", "predicate", "thisArg", "createFind", "emit", "findIndex", "source", "subscriber", "index", "subscribe", "value", "i", "call", "next", "complete", "undefined"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\find.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { Subscriber } from '../Subscriber';\nimport { OperatorFunction, TruthyTypesOf } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\nexport function find<T>(predicate: BooleanConstructor): OperatorFunction<T, TruthyTypesOf<T>>;\n/** @deprecated Use a closure instead of a `thisArg`. Signatures accepting a `thisArg` will be removed in v8. */\nexport function find<T, S extends T, A>(\n  predicate: (this: A, value: T, index: number, source: Observable<T>) => value is S,\n  thisArg: A\n): OperatorFunction<T, S | undefined>;\nexport function find<T, S extends T>(\n  predicate: (value: T, index: number, source: Observable<T>) => value is S\n): OperatorFunction<T, S | undefined>;\n/** @deprecated Use a closure instead of a `thisArg`. Signatures accepting a `thisArg` will be removed in v8. */\nexport function find<T, A>(\n  predicate: (this: A, value: T, index: number, source: Observable<T>) => boolean,\n  thisArg: A\n): OperatorFunction<T, T | undefined>;\nexport function find<T>(predicate: (value: T, index: number, source: Observable<T>) => boolean): OperatorFunction<T, T | undefined>;\n/**\n * Emits only the first value emitted by the source Observable that meets some\n * condition.\n *\n * <span class=\"informal\">Finds the first value that passes some test and emits\n * that.</span>\n *\n * ![](find.png)\n *\n * `find` searches for the first item in the source Observable that matches the\n * specified condition embodied by the `predicate`, and returns the first\n * occurrence in the source. Unlike {@link first}, the `predicate` is required\n * in `find`, and does not emit an error if a valid value is not found\n * (emits `undefined` instead).\n *\n * ## Example\n *\n * Find and emit the first click that happens on a DIV element\n *\n * ```ts\n * import { fromEvent, find } from 'rxjs';\n *\n * const div = document.createElement('div');\n * div.style.cssText = 'width: 200px; height: 200px; background: #09c;';\n * document.body.appendChild(div);\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(find(ev => (<HTMLElement>ev.target).tagName === 'DIV'));\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link filter}\n * @see {@link first}\n * @see {@link findIndex}\n * @see {@link take}\n *\n * @param predicate A function called with each item to test for condition matching.\n * @param thisArg An optional argument to determine the value of `this` in the\n * `predicate` function.\n * @return A function that returns an Observable that emits the first item that\n * matches the condition.\n */\nexport function find<T>(\n  predicate: (value: T, index: number, source: Observable<T>) => boolean,\n  thisArg?: any\n): OperatorFunction<T, T | undefined> {\n  return operate(createFind(predicate, thisArg, 'value'));\n}\n\nexport function createFind<T>(\n  predicate: (value: T, index: number, source: Observable<T>) => boolean,\n  thisArg: any,\n  emit: 'value' | 'index'\n) {\n  const findIndex = emit === 'index';\n  return (source: Observable<T>, subscriber: Subscriber<any>) => {\n    let index = 0;\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (value) => {\n          const i = index++;\n          if (predicate.call(thisArg, value, i, source)) {\n            subscriber.next(findIndex ? i : value);\n            subscriber.complete();\n          }\n        },\n        () => {\n          subscriber.next(findIndex ? -1 : undefined);\n          subscriber.complete();\n        }\n      )\n    );\n  };\n}\n"], "mappings": "AAGA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AA2D/D,OAAM,SAAUC,IAAIA,CAClBC,SAAsE,EACtEC,OAAa;EAEb,OAAOJ,OAAO,CAACK,UAAU,CAACF,SAAS,EAAEC,OAAO,EAAE,OAAO,CAAC,CAAC;AACzD;AAEA,OAAM,SAAUC,UAAUA,CACxBF,SAAsE,EACtEC,OAAY,EACZE,IAAuB;EAEvB,IAAMC,SAAS,GAAGD,IAAI,KAAK,OAAO;EAClC,OAAO,UAACE,MAAqB,EAAEC,UAA2B;IACxD,IAAIC,KAAK,GAAG,CAAC;IACbF,MAAM,CAACG,SAAS,CACdV,wBAAwB,CACtBQ,UAAU,EACV,UAACG,KAAK;MACJ,IAAMC,CAAC,GAAGH,KAAK,EAAE;MACjB,IAAIP,SAAS,CAACW,IAAI,CAACV,OAAO,EAAEQ,KAAK,EAAEC,CAAC,EAAEL,MAAM,CAAC,EAAE;QAC7CC,UAAU,CAACM,IAAI,CAACR,SAAS,GAAGM,CAAC,GAAGD,KAAK,CAAC;QACtCH,UAAU,CAACO,QAAQ,EAAE;;IAEzB,CAAC,EACD;MACEP,UAAU,CAACM,IAAI,CAACR,SAAS,GAAG,CAAC,CAAC,GAAGU,SAAS,CAAC;MAC3CR,UAAU,CAACO,QAAQ,EAAE;IACvB,CAAC,CACF,CACF;EACH,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}