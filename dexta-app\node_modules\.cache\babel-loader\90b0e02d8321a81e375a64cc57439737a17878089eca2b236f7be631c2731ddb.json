{"ast": null, "code": "// DOM-Level-1-compliant structure\nvar NodePrototype = require('./node');\nvar ElementPrototype = module.exports = Object.create(NodePrototype);\nvar domLvl1 = {\n  tagName: \"name\"\n};\nObject.keys(domLvl1).forEach(function (key) {\n  var shorthand = domLvl1[key];\n  Object.defineProperty(ElementPrototype, key, {\n    get: function () {\n      return this[shorthand] || null;\n    },\n    set: function (val) {\n      this[shorthand] = val;\n      return val;\n    }\n  });\n});", "map": {"version": 3, "names": ["NodePrototype", "require", "ElementPrototype", "module", "exports", "Object", "create", "domLvl1", "tagName", "keys", "for<PERSON>ach", "key", "shorthand", "defineProperty", "get", "set", "val"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/htmlparser2/node_modules/domhandler/lib/element.js"], "sourcesContent": ["// DOM-Level-1-compliant structure\nvar NodePrototype = require('./node');\nvar ElementPrototype = module.exports = Object.create(NodePrototype);\n\nvar domLvl1 = {\n\ttagName: \"name\"\n};\n\nObject.keys(domLvl1).forEach(function(key) {\n\tvar shorthand = domLvl1[key];\n\tObject.defineProperty(ElementPrototype, key, {\n\t\tget: function() {\n\t\t\treturn this[shorthand] || null;\n\t\t},\n\t\tset: function(val) {\n\t\t\tthis[shorthand] = val;\n\t\t\treturn val;\n\t\t}\n\t});\n});\n"], "mappings": "AAAA;AACA,IAAIA,aAAa,GAAGC,OAAO,CAAC,QAAQ,CAAC;AACrC,IAAIC,gBAAgB,GAAGC,MAAM,CAACC,OAAO,GAAGC,MAAM,CAACC,MAAM,CAACN,aAAa,CAAC;AAEpE,IAAIO,OAAO,GAAG;EACbC,OAAO,EAAE;AACV,CAAC;AAEDH,MAAM,CAACI,IAAI,CAACF,OAAO,CAAC,CAACG,OAAO,CAAC,UAASC,GAAG,EAAE;EAC1C,IAAIC,SAAS,GAAGL,OAAO,CAACI,GAAG,CAAC;EAC5BN,MAAM,CAACQ,cAAc,CAACX,gBAAgB,EAAES,GAAG,EAAE;IAC5CG,GAAG,EAAE,SAAAA,CAAA,EAAW;MACf,OAAO,IAAI,CAACF,SAAS,CAAC,IAAI,IAAI;IAC/B,CAAC;IACDG,GAAG,EAAE,SAAAA,CAASC,GAAG,EAAE;MAClB,IAAI,CAACJ,SAAS,CAAC,GAAGI,GAAG;MACrB,OAAOA,GAAG;IACX;EACD,CAAC,CAAC;AACH,CAAC,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}