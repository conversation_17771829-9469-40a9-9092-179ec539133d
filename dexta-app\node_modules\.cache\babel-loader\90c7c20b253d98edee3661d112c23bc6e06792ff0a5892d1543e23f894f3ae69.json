{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"getRootProps\"];\nimport { useTabContext, getTabId, getPanelId } from '../TabsUnstyled';\nimport { useButton } from '../ButtonUnstyled';\nconst useTab = parameters => {\n  var _getPanelId, _getTabId;\n  const {\n    value: valueProp,\n    onChange,\n    onClick,\n    onFocus\n  } = parameters;\n  const _useButton = useButton(parameters),\n    {\n      getRootProps: getRootPropsButton\n    } = _useButton,\n    otherButtonProps = _objectWithoutPropertiesLoose(_useButton, _excluded);\n  const context = useTabContext();\n  if (context === null) {\n    throw new Error('No TabContext provided');\n  }\n  const value = valueProp != null ? valueProp : 0;\n  const selected = context.value === value;\n  const selectionFollowsFocus = context.selectionFollowsFocus;\n  const a11yAttributes = {\n    role: 'tab',\n    'aria-controls': (_getPanelId = getPanelId(context, value)) != null ? _getPanelId : undefined,\n    id: (_getTabId = getTabId(context, value)) != null ? _getTabId : undefined,\n    'aria-selected': selected,\n    disabled: otherButtonProps.disabled\n  };\n  const createHandleFocus = otherHandlers => event => {\n    var _otherHandlers$onFocu;\n    (_otherHandlers$onFocu = otherHandlers.onFocus) == null ? void 0 : _otherHandlers$onFocu.call(otherHandlers, event);\n    if (event.defaultPrevented) {\n      return;\n    }\n    if (selectionFollowsFocus && !selected) {\n      if (onChange) {\n        onChange(event, value);\n      }\n      context.onSelected(event, value);\n    }\n    if (onFocus) {\n      onFocus(event);\n    }\n  };\n  const createHandleClick = otherHandlers => event => {\n    var _otherHandlers$onClic;\n    (_otherHandlers$onClic = otherHandlers.onClick) == null ? void 0 : _otherHandlers$onClic.call(otherHandlers, event);\n    if (event.defaultPrevented) {\n      return;\n    }\n    if (!selected) {\n      if (onChange) {\n        onChange(event, value);\n      }\n      context.onSelected(event, value);\n    }\n    if (onClick) {\n      onClick(event);\n    }\n  };\n  const getRootProps = (otherHandlers = {}) => {\n    const buttonResolvedProps = getRootPropsButton(_extends({}, otherHandlers, {\n      onClick: createHandleClick(otherHandlers),\n      onFocus: createHandleFocus(otherHandlers)\n    }));\n    return _extends({}, buttonResolvedProps, a11yAttributes);\n  };\n  return _extends({\n    getRootProps\n  }, otherButtonProps, {\n    selected\n  });\n};\nexport default useTab;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "useTabContext", "getTabId", "getPanelId", "useButton", "useTab", "parameters", "_getPanelId", "_getTabId", "value", "valueProp", "onChange", "onClick", "onFocus", "_useButton", "getRootProps", "getRootPropsButton", "otherButtonProps", "context", "Error", "selected", "selectionFollowsFocus", "a11yAttributes", "role", "undefined", "id", "disabled", "createHandleFocus", "otherHandlers", "event", "_otherHandlers$onFocu", "call", "defaultPrevented", "onSelected", "createHandleClick", "_otherHandlers$onClic", "buttonResolvedProps"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/TabUnstyled/useTab.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"getRootProps\"];\nimport { useTabContext, getTabId, getPanelId } from '../TabsUnstyled';\nimport { useButton } from '../ButtonUnstyled';\n\nconst useTab = parameters => {\n  var _getPanelId, _getTabId;\n\n  const {\n    value: valueProp,\n    onChange,\n    onClick,\n    onFocus\n  } = parameters;\n\n  const _useButton = useButton(parameters),\n        {\n    getRootProps: getRootPropsButton\n  } = _useButton,\n        otherButtonProps = _objectWithoutPropertiesLoose(_useButton, _excluded);\n\n  const context = useTabContext();\n\n  if (context === null) {\n    throw new Error('No TabContext provided');\n  }\n\n  const value = valueProp != null ? valueProp : 0;\n  const selected = context.value === value;\n  const selectionFollowsFocus = context.selectionFollowsFocus;\n  const a11yAttributes = {\n    role: 'tab',\n    'aria-controls': (_getPanelId = getPanelId(context, value)) != null ? _getPanelId : undefined,\n    id: (_getTabId = getTabId(context, value)) != null ? _getTabId : undefined,\n    'aria-selected': selected,\n    disabled: otherButtonProps.disabled\n  };\n\n  const createHandleFocus = otherHandlers => event => {\n    var _otherHandlers$onFocu;\n\n    (_otherHandlers$onFocu = otherHandlers.onFocus) == null ? void 0 : _otherHandlers$onFocu.call(otherHandlers, event);\n\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    if (selectionFollowsFocus && !selected) {\n      if (onChange) {\n        onChange(event, value);\n      }\n\n      context.onSelected(event, value);\n    }\n\n    if (onFocus) {\n      onFocus(event);\n    }\n  };\n\n  const createHandleClick = otherHandlers => event => {\n    var _otherHandlers$onClic;\n\n    (_otherHandlers$onClic = otherHandlers.onClick) == null ? void 0 : _otherHandlers$onClic.call(otherHandlers, event);\n\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    if (!selected) {\n      if (onChange) {\n        onChange(event, value);\n      }\n\n      context.onSelected(event, value);\n    }\n\n    if (onClick) {\n      onClick(event);\n    }\n  };\n\n  const getRootProps = (otherHandlers = {}) => {\n    const buttonResolvedProps = getRootPropsButton(_extends({}, otherHandlers, {\n      onClick: createHandleClick(otherHandlers),\n      onFocus: createHandleFocus(otherHandlers)\n    }));\n    return _extends({}, buttonResolvedProps, a11yAttributes);\n  };\n\n  return _extends({\n    getRootProps\n  }, otherButtonProps, {\n    selected\n  });\n};\n\nexport default useTab;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,cAAc,CAAC;AAClC,SAASC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,iBAAiB;AACrE,SAASC,SAAS,QAAQ,mBAAmB;AAE7C,MAAMC,MAAM,GAAGC,UAAU,IAAI;EAC3B,IAAIC,WAAW,EAAEC,SAAS;EAE1B,MAAM;IACJC,KAAK,EAAEC,SAAS;IAChBC,QAAQ;IACRC,OAAO;IACPC;EACF,CAAC,GAAGP,UAAU;EAEd,MAAMQ,UAAU,GAAGV,SAAS,CAACE,UAAU,CAAC;IAClC;MACJS,YAAY,EAAEC;IAChB,CAAC,GAAGF,UAAU;IACRG,gBAAgB,GAAGlB,6BAA6B,CAACe,UAAU,EAAEd,SAAS,CAAC;EAE7E,MAAMkB,OAAO,GAAGjB,aAAa,CAAC,CAAC;EAE/B,IAAIiB,OAAO,KAAK,IAAI,EAAE;IACpB,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;EAC3C;EAEA,MAAMV,KAAK,GAAGC,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAG,CAAC;EAC/C,MAAMU,QAAQ,GAAGF,OAAO,CAACT,KAAK,KAAKA,KAAK;EACxC,MAAMY,qBAAqB,GAAGH,OAAO,CAACG,qBAAqB;EAC3D,MAAMC,cAAc,GAAG;IACrBC,IAAI,EAAE,KAAK;IACX,eAAe,EAAE,CAAChB,WAAW,GAAGJ,UAAU,CAACe,OAAO,EAAET,KAAK,CAAC,KAAK,IAAI,GAAGF,WAAW,GAAGiB,SAAS;IAC7FC,EAAE,EAAE,CAACjB,SAAS,GAAGN,QAAQ,CAACgB,OAAO,EAAET,KAAK,CAAC,KAAK,IAAI,GAAGD,SAAS,GAAGgB,SAAS;IAC1E,eAAe,EAAEJ,QAAQ;IACzBM,QAAQ,EAAET,gBAAgB,CAACS;EAC7B,CAAC;EAED,MAAMC,iBAAiB,GAAGC,aAAa,IAAIC,KAAK,IAAI;IAClD,IAAIC,qBAAqB;IAEzB,CAACA,qBAAqB,GAAGF,aAAa,CAACf,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGiB,qBAAqB,CAACC,IAAI,CAACH,aAAa,EAAEC,KAAK,CAAC;IAEnH,IAAIA,KAAK,CAACG,gBAAgB,EAAE;MAC1B;IACF;IAEA,IAAIX,qBAAqB,IAAI,CAACD,QAAQ,EAAE;MACtC,IAAIT,QAAQ,EAAE;QACZA,QAAQ,CAACkB,KAAK,EAAEpB,KAAK,CAAC;MACxB;MAEAS,OAAO,CAACe,UAAU,CAACJ,KAAK,EAAEpB,KAAK,CAAC;IAClC;IAEA,IAAII,OAAO,EAAE;MACXA,OAAO,CAACgB,KAAK,CAAC;IAChB;EACF,CAAC;EAED,MAAMK,iBAAiB,GAAGN,aAAa,IAAIC,KAAK,IAAI;IAClD,IAAIM,qBAAqB;IAEzB,CAACA,qBAAqB,GAAGP,aAAa,CAAChB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGuB,qBAAqB,CAACJ,IAAI,CAACH,aAAa,EAAEC,KAAK,CAAC;IAEnH,IAAIA,KAAK,CAACG,gBAAgB,EAAE;MAC1B;IACF;IAEA,IAAI,CAACZ,QAAQ,EAAE;MACb,IAAIT,QAAQ,EAAE;QACZA,QAAQ,CAACkB,KAAK,EAAEpB,KAAK,CAAC;MACxB;MAEAS,OAAO,CAACe,UAAU,CAACJ,KAAK,EAAEpB,KAAK,CAAC;IAClC;IAEA,IAAIG,OAAO,EAAE;MACXA,OAAO,CAACiB,KAAK,CAAC;IAChB;EACF,CAAC;EAED,MAAMd,YAAY,GAAGA,CAACa,aAAa,GAAG,CAAC,CAAC,KAAK;IAC3C,MAAMQ,mBAAmB,GAAGpB,kBAAkB,CAAClB,QAAQ,CAAC,CAAC,CAAC,EAAE8B,aAAa,EAAE;MACzEhB,OAAO,EAAEsB,iBAAiB,CAACN,aAAa,CAAC;MACzCf,OAAO,EAAEc,iBAAiB,CAACC,aAAa;IAC1C,CAAC,CAAC,CAAC;IACH,OAAO9B,QAAQ,CAAC,CAAC,CAAC,EAAEsC,mBAAmB,EAAEd,cAAc,CAAC;EAC1D,CAAC;EAED,OAAOxB,QAAQ,CAAC;IACdiB;EACF,CAAC,EAAEE,gBAAgB,EAAE;IACnBG;EACF,CAAC,CAAC;AACJ,CAAC;AAED,eAAef,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}