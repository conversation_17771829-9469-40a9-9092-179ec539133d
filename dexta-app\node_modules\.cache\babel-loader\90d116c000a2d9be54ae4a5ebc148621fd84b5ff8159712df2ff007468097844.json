{"ast": null, "code": "function l() {\n  return {\n    before({\n      doc: e,\n      d: o\n    }) {\n      o.style(e.documentElement, \"overflow\", \"hidden\");\n    }\n  };\n}\nexport { l as preventScroll };", "map": {"version": 3, "names": ["l", "before", "doc", "e", "d", "o", "style", "documentElement", "preventScroll"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@headlessui/react/dist/hooks/document-overflow/prevent-scroll.js"], "sourcesContent": ["function l(){return{before({doc:e,d:o}){o.style(e.documentElement,\"overflow\",\"hidden\")}}}export{l as preventScroll};\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,OAAM;IAACC,MAAMA,CAAC;MAACC,GAAG,EAACC,CAAC;MAACC,CAAC,EAACC;IAAC,CAAC,EAAC;MAACA,CAAC,CAACC,KAAK,CAACH,CAAC,CAACI,eAAe,EAAC,UAAU,EAAC,QAAQ,CAAC;IAAA;EAAC,CAAC;AAAA;AAAC,SAAOP,CAAC,IAAIQ,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}