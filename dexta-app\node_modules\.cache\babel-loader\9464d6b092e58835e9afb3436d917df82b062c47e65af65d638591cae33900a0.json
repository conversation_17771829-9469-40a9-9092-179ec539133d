{"ast": null, "code": "import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getMenuItemUnstyledUtilityClass(slot) {\n  return generateUtilityClass('MuiMenuItemUnstyled', slot);\n}\nconst menuItemUnstyledClasses = generateUtilityClasses('MuiMenuItemUnstyled', ['root', 'disabled', 'focusVisible']);\nexport default menuItemUnstyledClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getMenuItemUnstyledUtilityClass", "slot", "menuItemUnstyledClasses"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/MenuItemUnstyled/menuItemUnstyledClasses.js"], "sourcesContent": ["import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getMenuItemUnstyledUtilityClass(slot) {\n  return generateUtilityClass('MuiMenuItemUnstyled', slot);\n}\nconst menuItemUnstyledClasses = generateUtilityClasses('MuiMenuItemUnstyled', ['root', 'disabled', 'focusVisible']);\nexport default menuItemUnstyledClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,yBAAyB;AAC1D,OAAOC,sBAAsB,MAAM,2BAA2B;AAC9D,OAAO,SAASC,+BAA+BA,CAACC,IAAI,EAAE;EACpD,OAAOH,oBAAoB,CAAC,qBAAqB,EAAEG,IAAI,CAAC;AAC1D;AACA,MAAMC,uBAAuB,GAAGH,sBAAsB,CAAC,qBAAqB,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;AACnH,eAAeG,uBAAuB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}