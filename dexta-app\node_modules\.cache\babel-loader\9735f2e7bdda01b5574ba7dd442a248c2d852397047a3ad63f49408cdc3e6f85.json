{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n};\nexports.default = generatePropsFromAttributes;\nvar _htmlAttributesToReact = require('./htmlAttributesToReact');\nvar _htmlAttributesToReact2 = _interopRequireDefault(_htmlAttributesToReact);\nvar _inlineStyleToObject = require('./inlineStyleToObject');\nvar _inlineStyleToObject2 = _interopRequireDefault(_inlineStyleToObject);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\n\n/**\n * Generates props for a React element from an object of HTML attributes\n *\n * @param {Object} attributes The HTML attributes\n * @param {String} key The key to give the react element\n */\nfunction generatePropsFromAttributes(attributes, key) {\n  // generate props\n  var props = _extends({}, (0, _htmlAttributesToReact2.default)(attributes), {\n    key: key\n  });\n\n  // if there is an inline/string style prop then convert it to a React style object\n  // otherwise, it is invalid and omitted\n  if (typeof props.style === 'string' || props.style instanceof String) {\n    props.style = (0, _inlineStyleToObject2.default)(props.style);\n  } else {\n    delete props.style;\n  }\n  return props;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_extends", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "default", "generatePropsFromAttributes", "_htmlAttributesToReact", "require", "_htmlAttributesToReact2", "_interopRequireDefault", "_inlineStyleToObject", "_inlineStyleToObject2", "obj", "__esModule", "attributes", "props", "style", "String"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/react-html-parser/lib/utils/generatePropsFromAttributes.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };\n\nexports.default = generatePropsFromAttributes;\n\nvar _htmlAttributesToReact = require('./htmlAttributesToReact');\n\nvar _htmlAttributesToReact2 = _interopRequireDefault(_htmlAttributesToReact);\n\nvar _inlineStyleToObject = require('./inlineStyleToObject');\n\nvar _inlineStyleToObject2 = _interopRequireDefault(_inlineStyleToObject);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * Generates props for a React element from an object of HTML attributes\n *\n * @param {Object} attributes The HTML attributes\n * @param {String} key The key to give the react element\n */\nfunction generatePropsFromAttributes(attributes, key) {\n\n  // generate props\n  var props = _extends({}, (0, _htmlAttributesToReact2.default)(attributes), { key: key });\n\n  // if there is an inline/string style prop then convert it to a React style object\n  // otherwise, it is invalid and omitted\n  if (typeof props.style === 'string' || props.style instanceof String) {\n    props.style = (0, _inlineStyleToObject2.default)(props.style);\n  } else {\n    delete props.style;\n  }\n\n  return props;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AAEF,IAAIC,QAAQ,GAAGJ,MAAM,CAACK,MAAM,IAAI,UAAUC,MAAM,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IAAE,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;IAAE,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;MAAE,IAAIV,MAAM,CAACY,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;QAAEL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;MAAE;IAAE;EAAE;EAAE,OAAOL,MAAM;AAAE,CAAC;AAEhQJ,OAAO,CAACa,OAAO,GAAGC,2BAA2B;AAE7C,IAAIC,sBAAsB,GAAGC,OAAO,CAAC,yBAAyB,CAAC;AAE/D,IAAIC,uBAAuB,GAAGC,sBAAsB,CAACH,sBAAsB,CAAC;AAE5E,IAAII,oBAAoB,GAAGH,OAAO,CAAC,uBAAuB,CAAC;AAE3D,IAAII,qBAAqB,GAAGF,sBAAsB,CAACC,oBAAoB,CAAC;AAExE,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAER,OAAO,EAAEQ;EAAI,CAAC;AAAE;;AAE9F;AACA;AACA;AACA;AACA;AACA;AACA,SAASP,2BAA2BA,CAACS,UAAU,EAAEd,GAAG,EAAE;EAEpD;EACA,IAAIe,KAAK,GAAGtB,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEe,uBAAuB,CAACJ,OAAO,EAAEU,UAAU,CAAC,EAAE;IAAEd,GAAG,EAAEA;EAAI,CAAC,CAAC;;EAExF;EACA;EACA,IAAI,OAAOe,KAAK,CAACC,KAAK,KAAK,QAAQ,IAAID,KAAK,CAACC,KAAK,YAAYC,MAAM,EAAE;IACpEF,KAAK,CAACC,KAAK,GAAG,CAAC,CAAC,EAAEL,qBAAqB,CAACP,OAAO,EAAEW,KAAK,CAACC,KAAK,CAAC;EAC/D,CAAC,MAAM;IACL,OAAOD,KAAK,CAACC,KAAK;EACpB;EAEA,OAAOD,KAAK;AACd"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}