{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { Action } from './Action';\nimport { intervalProvider } from './intervalProvider';\nimport { arrRemove } from '../util/arrRemove';\nvar AsyncAction = function (_super) {\n  __extends(AsyncAction, _super);\n  function AsyncAction(scheduler, work) {\n    var _this = _super.call(this, scheduler, work) || this;\n    _this.scheduler = scheduler;\n    _this.work = work;\n    _this.pending = false;\n    return _this;\n  }\n  AsyncAction.prototype.schedule = function (state, delay) {\n    var _a;\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (this.closed) {\n      return this;\n    }\n    this.state = state;\n    var id = this.id;\n    var scheduler = this.scheduler;\n    if (id != null) {\n      this.id = this.recycleAsyncId(scheduler, id, delay);\n    }\n    this.pending = true;\n    this.delay = delay;\n    this.id = (_a = this.id) !== null && _a !== void 0 ? _a : this.requestAsyncId(scheduler, this.id, delay);\n    return this;\n  };\n  AsyncAction.prototype.requestAsyncId = function (scheduler, _id, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    return intervalProvider.setInterval(scheduler.flush.bind(scheduler, this), delay);\n  };\n  AsyncAction.prototype.recycleAsyncId = function (_scheduler, id, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (delay != null && this.delay === delay && this.pending === false) {\n      return id;\n    }\n    if (id != null) {\n      intervalProvider.clearInterval(id);\n    }\n    return undefined;\n  };\n  AsyncAction.prototype.execute = function (state, delay) {\n    if (this.closed) {\n      return new Error('executing a cancelled action');\n    }\n    this.pending = false;\n    var error = this._execute(state, delay);\n    if (error) {\n      return error;\n    } else if (this.pending === false && this.id != null) {\n      this.id = this.recycleAsyncId(this.scheduler, this.id, null);\n    }\n  };\n  AsyncAction.prototype._execute = function (state, _delay) {\n    var errored = false;\n    var errorValue;\n    try {\n      this.work(state);\n    } catch (e) {\n      errored = true;\n      errorValue = e ? e : new Error('Scheduled action threw falsy error');\n    }\n    if (errored) {\n      this.unsubscribe();\n      return errorValue;\n    }\n  };\n  AsyncAction.prototype.unsubscribe = function () {\n    if (!this.closed) {\n      var _a = this,\n        id = _a.id,\n        scheduler = _a.scheduler;\n      var actions = scheduler.actions;\n      this.work = this.state = this.scheduler = null;\n      this.pending = false;\n      arrRemove(actions, this);\n      if (id != null) {\n        this.id = this.recycleAsyncId(scheduler, id, null);\n      }\n      this.delay = null;\n      _super.prototype.unsubscribe.call(this);\n    }\n  };\n  return AsyncAction;\n}(Action);\nexport { AsyncAction };", "map": {"version": 3, "names": ["Action", "intervalProvider", "arr<PERSON><PERSON><PERSON>", "AsyncAction", "_super", "__extends", "scheduler", "work", "_this", "call", "pending", "prototype", "schedule", "state", "delay", "closed", "id", "recycleAsyncId", "_a", "requestAsyncId", "_id", "setInterval", "flush", "bind", "_scheduler", "clearInterval", "undefined", "execute", "Error", "error", "_execute", "_delay", "errored", "errorValue", "e", "unsubscribe", "actions"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\scheduler\\AsyncAction.ts"], "sourcesContent": ["import { Action } from './Action';\nimport { SchedulerAction } from '../types';\nimport { Subscription } from '../Subscription';\nimport { AsyncScheduler } from './AsyncScheduler';\nimport { intervalProvider } from './intervalProvider';\nimport { arrRemove } from '../util/arrRemove';\nimport { TimerHandle } from './timerHandle';\n\nexport class AsyncAction<T> extends Action<T> {\n  public id: TimerHandle | undefined;\n  public state?: T;\n  // @ts-ignore: Property has no initializer and is not definitely assigned\n  public delay: number;\n  protected pending: boolean = false;\n\n  constructor(protected scheduler: AsyncScheduler, protected work: (this: SchedulerAction<T>, state?: T) => void) {\n    super(scheduler, work);\n  }\n\n  public schedule(state?: T, delay: number = 0): Subscription {\n    if (this.closed) {\n      return this;\n    }\n\n    // Always replace the current state with the new state.\n    this.state = state;\n\n    const id = this.id;\n    const scheduler = this.scheduler;\n\n    //\n    // Important implementation note:\n    //\n    // Actions only execute once by default, unless rescheduled from within the\n    // scheduled callback. This allows us to implement single and repeat\n    // actions via the same code path, without adding API surface area, as well\n    // as mimic traditional recursion but across asynchronous boundaries.\n    //\n    // However, JS runtimes and timers distinguish between intervals achieved by\n    // serial `setTimeout` calls vs. a single `setInterval` call. An interval of\n    // serial `setTimeout` calls can be individually delayed, which delays\n    // scheduling the next `setTimeout`, and so on. `setInterval` attempts to\n    // guarantee the interval callback will be invoked more precisely to the\n    // interval period, regardless of load.\n    //\n    // Therefore, we use `setInterval` to schedule single and repeat actions.\n    // If the action reschedules itself with the same delay, the interval is not\n    // canceled. If the action doesn't reschedule, or reschedules with a\n    // different delay, the interval will be canceled after scheduled callback\n    // execution.\n    //\n    if (id != null) {\n      this.id = this.recycleAsyncId(scheduler, id, delay);\n    }\n\n    // Set the pending flag indicating that this action has been scheduled, or\n    // has recursively rescheduled itself.\n    this.pending = true;\n\n    this.delay = delay;\n    // If this action has already an async Id, don't request a new one.\n    this.id = this.id ?? this.requestAsyncId(scheduler, this.id, delay);\n\n    return this;\n  }\n\n  protected requestAsyncId(scheduler: AsyncScheduler, _id?: TimerHandle, delay: number = 0): TimerHandle {\n    return intervalProvider.setInterval(scheduler.flush.bind(scheduler, this), delay);\n  }\n\n  protected recycleAsyncId(_scheduler: AsyncScheduler, id?: TimerHandle, delay: number | null = 0): TimerHandle | undefined {\n    // If this action is rescheduled with the same delay time, don't clear the interval id.\n    if (delay != null && this.delay === delay && this.pending === false) {\n      return id;\n    }\n    // Otherwise, if the action's delay time is different from the current delay,\n    // or the action has been rescheduled before it's executed, clear the interval id\n    if (id != null) {\n      intervalProvider.clearInterval(id);\n    }\n\n    return undefined;\n  }\n\n  /**\n   * Immediately executes this action and the `work` it contains.\n   */\n  public execute(state: T, delay: number): any {\n    if (this.closed) {\n      return new Error('executing a cancelled action');\n    }\n\n    this.pending = false;\n    const error = this._execute(state, delay);\n    if (error) {\n      return error;\n    } else if (this.pending === false && this.id != null) {\n      // Dequeue if the action didn't reschedule itself. Don't call\n      // unsubscribe(), because the action could reschedule later.\n      // For example:\n      // ```\n      // scheduler.schedule(function doWork(counter) {\n      //   /* ... I'm a busy worker bee ... */\n      //   var originalAction = this;\n      //   /* wait 100ms before rescheduling the action */\n      //   setTimeout(function () {\n      //     originalAction.schedule(counter + 1);\n      //   }, 100);\n      // }, 1000);\n      // ```\n      this.id = this.recycleAsyncId(this.scheduler, this.id, null);\n    }\n  }\n\n  protected _execute(state: T, _delay: number): any {\n    let errored: boolean = false;\n    let errorValue: any;\n    try {\n      this.work(state);\n    } catch (e) {\n      errored = true;\n      // HACK: Since code elsewhere is relying on the \"truthiness\" of the\n      // return here, we can't have it return \"\" or 0 or false.\n      // TODO: Clean this up when we refactor schedulers mid-version-8 or so.\n      errorValue = e ? e : new Error('Scheduled action threw falsy error');\n    }\n    if (errored) {\n      this.unsubscribe();\n      return errorValue;\n    }\n  }\n\n  unsubscribe() {\n    if (!this.closed) {\n      const { id, scheduler } = this;\n      const { actions } = scheduler;\n\n      this.work = this.state = this.scheduler = null!;\n      this.pending = false;\n\n      arrRemove(actions, this);\n      if (id != null) {\n        this.id = this.recycleAsyncId(scheduler, id, null);\n      }\n\n      this.delay = null!;\n      super.unsubscribe();\n    }\n  }\n}\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,UAAU;AAIjC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,SAAS,QAAQ,mBAAmB;AAG7C,IAAAC,WAAA,aAAAC,MAAA;EAAoCC,SAAA,CAAAF,WAAA,EAAAC,MAAA;EAOlC,SAAAD,YAAsBG,SAAyB,EAAYC,IAAmD;IAA9G,IAAAC,KAAA,GACEJ,MAAA,CAAAK,IAAA,OAAMH,SAAS,EAAEC,IAAI,CAAC;IADFC,KAAA,CAAAF,SAAS,GAATA,SAAS;IAA4BE,KAAA,CAAAD,IAAI,GAAJA,IAAI;IAFrDC,KAAA,CAAAE,OAAO,GAAY,KAAK;;EAIlC;EAEOP,WAAA,CAAAQ,SAAA,CAAAC,QAAQ,GAAf,UAAgBC,KAAS,EAAEC,KAAiB;;IAAjB,IAAAA,KAAA;MAAAA,KAAA,IAAiB;IAAA;IAC1C,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,OAAO,IAAI;;IAIb,IAAI,CAACF,KAAK,GAAGA,KAAK;IAElB,IAAMG,EAAE,GAAG,IAAI,CAACA,EAAE;IAClB,IAAMV,SAAS,GAAG,IAAI,CAACA,SAAS;IAuBhC,IAAIU,EAAE,IAAI,IAAI,EAAE;MACd,IAAI,CAACA,EAAE,GAAG,IAAI,CAACC,cAAc,CAACX,SAAS,EAAEU,EAAE,EAAEF,KAAK,CAAC;;IAKrD,IAAI,CAACJ,OAAO,GAAG,IAAI;IAEnB,IAAI,CAACI,KAAK,GAAGA,KAAK;IAElB,IAAI,CAACE,EAAE,GAAG,CAAAE,EAAA,OAAI,CAACF,EAAE,cAAAE,EAAA,cAAAA,EAAA,GAAI,IAAI,CAACC,cAAc,CAACb,SAAS,EAAE,IAAI,CAACU,EAAE,EAAEF,KAAK,CAAC;IAEnE,OAAO,IAAI;EACb,CAAC;EAESX,WAAA,CAAAQ,SAAA,CAAAQ,cAAc,GAAxB,UAAyBb,SAAyB,EAAEc,GAAiB,EAAEN,KAAiB;IAAjB,IAAAA,KAAA;MAAAA,KAAA,IAAiB;IAAA;IACtF,OAAOb,gBAAgB,CAACoB,WAAW,CAACf,SAAS,CAACgB,KAAK,CAACC,IAAI,CAACjB,SAAS,EAAE,IAAI,CAAC,EAAEQ,KAAK,CAAC;EACnF,CAAC;EAESX,WAAA,CAAAQ,SAAA,CAAAM,cAAc,GAAxB,UAAyBO,UAA0B,EAAER,EAAgB,EAAEF,KAAwB;IAAxB,IAAAA,KAAA;MAAAA,KAAA,IAAwB;IAAA;IAE7F,IAAIA,KAAK,IAAI,IAAI,IAAI,IAAI,CAACA,KAAK,KAAKA,KAAK,IAAI,IAAI,CAACJ,OAAO,KAAK,KAAK,EAAE;MACnE,OAAOM,EAAE;;IAIX,IAAIA,EAAE,IAAI,IAAI,EAAE;MACdf,gBAAgB,CAACwB,aAAa,CAACT,EAAE,CAAC;;IAGpC,OAAOU,SAAS;EAClB,CAAC;EAKMvB,WAAA,CAAAQ,SAAA,CAAAgB,OAAO,GAAd,UAAed,KAAQ,EAAEC,KAAa;IACpC,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,OAAO,IAAIa,KAAK,CAAC,8BAA8B,CAAC;;IAGlD,IAAI,CAAClB,OAAO,GAAG,KAAK;IACpB,IAAMmB,KAAK,GAAG,IAAI,CAACC,QAAQ,CAACjB,KAAK,EAAEC,KAAK,CAAC;IACzC,IAAIe,KAAK,EAAE;MACT,OAAOA,KAAK;KACb,MAAM,IAAI,IAAI,CAACnB,OAAO,KAAK,KAAK,IAAI,IAAI,CAACM,EAAE,IAAI,IAAI,EAAE;MAcpD,IAAI,CAACA,EAAE,GAAG,IAAI,CAACC,cAAc,CAAC,IAAI,CAACX,SAAS,EAAE,IAAI,CAACU,EAAE,EAAE,IAAI,CAAC;;EAEhE,CAAC;EAESb,WAAA,CAAAQ,SAAA,CAAAmB,QAAQ,GAAlB,UAAmBjB,KAAQ,EAAEkB,MAAc;IACzC,IAAIC,OAAO,GAAY,KAAK;IAC5B,IAAIC,UAAe;IACnB,IAAI;MACF,IAAI,CAAC1B,IAAI,CAACM,KAAK,CAAC;KACjB,CAAC,OAAOqB,CAAC,EAAE;MACVF,OAAO,GAAG,IAAI;MAIdC,UAAU,GAAGC,CAAC,GAAGA,CAAC,GAAG,IAAIN,KAAK,CAAC,oCAAoC,CAAC;;IAEtE,IAAII,OAAO,EAAE;MACX,IAAI,CAACG,WAAW,EAAE;MAClB,OAAOF,UAAU;;EAErB,CAAC;EAED9B,WAAA,CAAAQ,SAAA,CAAAwB,WAAW,GAAX;IACE,IAAI,CAAC,IAAI,CAACpB,MAAM,EAAE;MACV,IAAAG,EAAA,GAAoB,IAAI;QAAtBF,EAAE,GAAAE,EAAA,CAAAF,EAAA;QAAEV,SAAS,GAAAY,EAAA,CAAAZ,SAAS;MACtB,IAAA8B,OAAO,GAAK9B,SAAS,CAAA8B,OAAd;MAEf,IAAI,CAAC7B,IAAI,GAAG,IAAI,CAACM,KAAK,GAAG,IAAI,CAACP,SAAS,GAAG,IAAK;MAC/C,IAAI,CAACI,OAAO,GAAG,KAAK;MAEpBR,SAAS,CAACkC,OAAO,EAAE,IAAI,CAAC;MACxB,IAAIpB,EAAE,IAAI,IAAI,EAAE;QACd,IAAI,CAACA,EAAE,GAAG,IAAI,CAACC,cAAc,CAACX,SAAS,EAAEU,EAAE,EAAE,IAAI,CAAC;;MAGpD,IAAI,CAACF,KAAK,GAAG,IAAK;MAClBV,MAAA,CAAAO,SAAA,CAAMwB,WAAW,CAAA1B,IAAA,MAAE;;EAEvB,CAAC;EACH,OAAAN,WAAC;AAAD,CAAC,CA7ImCH,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}