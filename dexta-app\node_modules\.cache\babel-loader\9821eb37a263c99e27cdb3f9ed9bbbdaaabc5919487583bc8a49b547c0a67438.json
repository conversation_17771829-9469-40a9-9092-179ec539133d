{"ast": null, "code": "/**\r\n * Creates Unicode flag from a two-letter ISO country code.\r\n * https://stackoverflow.com/questions/24050671/how-to-put-japan-flag-character-in-a-string\r\n * @param  {string} country — A two-letter ISO country code (case-insensitive).\r\n * @return {string}\r\n */\nexport default function getCountryFlag(country) {\n  return getRegionalIndicatorSymbol(country[0]) + getRegionalIndicatorSymbol(country[1]);\n}\n/**\r\n * Converts a letter to a Regional Indicator Symbol.\r\n * @param  {string} letter\r\n * @return {string}\r\n */\n\nfunction getRegionalIndicatorSymbol(letter) {\n  return String.fromCodePoint(0x1F1E6 - 65 + letter.toUpperCase().charCodeAt(0));\n}", "map": {"version": 3, "names": ["getCountryFlag", "country", "getRegionalIndicatorSymbol", "letter", "String", "fromCodePoint", "toUpperCase", "charCodeAt"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\react-phone-number-input\\node_modules\\country-flag-icons\\source\\unicode.js"], "sourcesContent": ["/**\r\n * Creates Unicode flag from a two-letter ISO country code.\r\n * https://stackoverflow.com/questions/24050671/how-to-put-japan-flag-character-in-a-string\r\n * @param  {string} country — A two-letter ISO country code (case-insensitive).\r\n * @return {string}\r\n */\r\nexport default function getCountryFlag(country) {\r\n\treturn getRegionalIndicatorSymbol(country[0]) + getRegionalIndicatorSymbol(country[1])\r\n}\r\n\r\n/**\r\n * Converts a letter to a Regional Indicator Symbol.\r\n * @param  {string} letter\r\n * @return {string}\r\n */\r\nfunction getRegionalIndicatorSymbol(letter) {\r\n\treturn String.fromCodePoint(0x1F1E6 - 65 + letter.toUpperCase().charCodeAt(0))\r\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,cAATA,CAAwBC,OAAxB,EAAiC;EAC/C,OAAOC,0BAA0B,CAACD,OAAO,CAAC,CAAD,CAAR,CAA1B,GAAyCC,0BAA0B,CAACD,OAAO,CAAC,CAAD,CAAR,CAA1E;AACA;AAED;AACA;AACA;AACA;AACA;;AACA,SAASC,0BAATA,CAAoCC,MAApC,EAA4C;EAC3C,OAAOC,MAAM,CAACC,aAAP,CAAqB,UAAU,EAAV,GAAeF,MAAM,CAACG,WAAP,GAAqBC,UAArB,CAAgC,CAAhC,CAApC,CAAP;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}