{"ast": null, "code": "import { operate } from '../util/lift';\nexport function subscribeOn(scheduler, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  return operate(function (source, subscriber) {\n    subscriber.add(scheduler.schedule(function () {\n      return source.subscribe(subscriber);\n    }, delay));\n  });\n}", "map": {"version": 3, "names": ["operate", "subscribeOn", "scheduler", "delay", "source", "subscriber", "add", "schedule", "subscribe"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\subscribeOn.ts"], "sourcesContent": ["import { MonoTypeOperatorFunction, SchedulerLike } from '../types';\nimport { operate } from '../util/lift';\n\n/**\n * Asynchronously subscribes Observers to this Observable on the specified {@link SchedulerLike}.\n *\n * With `subscribeOn` you can decide what type of scheduler a specific Observable will be using when it is subscribed to.\n *\n * Schedulers control the speed and order of emissions to observers from an Observable stream.\n *\n * ![](subscribeOn.png)\n *\n * ## Example\n *\n * Given the following code:\n *\n * ```ts\n * import { of, merge } from 'rxjs';\n *\n * const a = of(1, 2, 3);\n * const b = of(4, 5, 6);\n *\n * merge(a, b).subscribe(console.log);\n *\n * // Outputs\n * // 1\n * // 2\n * // 3\n * // 4\n * // 5\n * // 6\n * ```\n *\n * Both Observable `a` and `b` will emit their values directly and synchronously once they are subscribed to.\n *\n * If we instead use the `subscribeOn` operator declaring that we want to use the {@link asyncScheduler} for values emitted by Observable `a`:\n *\n * ```ts\n * import { of, subscribeOn, asyncScheduler, merge } from 'rxjs';\n *\n * const a = of(1, 2, 3).pipe(subscribeOn(asyncScheduler));\n * const b = of(4, 5, 6);\n *\n * merge(a, b).subscribe(console.log);\n *\n * // Outputs\n * // 4\n * // 5\n * // 6\n * // 1\n * // 2\n * // 3\n * ```\n *\n * The reason for this is that Observable `b` emits its values directly and synchronously like before\n * but the emissions from `a` are scheduled on the event loop because we are now using the {@link asyncScheduler} for that specific Observable.\n *\n * @param scheduler The {@link SchedulerLike} to perform subscription actions on.\n * @param delay A delay to pass to the scheduler to delay subscriptions\n * @return A function that returns an Observable modified so that its\n * subscriptions happen on the specified {@link SchedulerLike}.\n */\nexport function subscribeOn<T>(scheduler: SchedulerLike, delay: number = 0): MonoTypeOperatorFunction<T> {\n  return operate((source, subscriber) => {\n    subscriber.add(scheduler.schedule(() => source.subscribe(subscriber), delay));\n  });\n}\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,cAAc;AA6DtC,OAAM,SAAUC,WAAWA,CAAIC,SAAwB,EAAEC,KAAiB;EAAjB,IAAAA,KAAA;IAAAA,KAAA,IAAiB;EAAA;EACxE,OAAOH,OAAO,CAAC,UAACI,MAAM,EAAEC,UAAU;IAChCA,UAAU,CAACC,GAAG,CAACJ,SAAS,CAACK,QAAQ,CAAC;MAAM,OAAAH,MAAM,CAACI,SAAS,CAACH,UAAU,CAAC;IAA5B,CAA4B,EAAEF,KAAK,CAAC,CAAC;EAC/E,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}