{"ast": null, "code": "export function registerTarget(type, target, manager) {\n  const registry = manager.getRegistry();\n  const targetId = registry.addTarget(type, target);\n  return [targetId, () => registry.removeTarget(targetId)];\n}\nexport function registerSource(type, source, manager) {\n  const registry = manager.getRegistry();\n  const sourceId = registry.addSource(type, source);\n  return [sourceId, () => registry.removeSource(sourceId)];\n}", "map": {"version": 3, "names": ["registerTarget", "type", "target", "manager", "registry", "getRegistry", "targetId", "addTarget", "remove<PERSON>arget", "registerSource", "source", "sourceId", "addSource", "removeSource"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\react-dnd\\src\\internals\\registration.ts"], "sourcesContent": ["import type {\n\tDragDropManager,\n\tDragSource,\n\tDropTarget,\n\tIdentifier,\n\tSourceType,\n\tTargetType,\n\tUnsubscribe,\n} from 'dnd-core'\n\nexport function registerTarget(\n\ttype: TargetType,\n\ttarget: DropTarget,\n\tmanager: DragDropManager,\n): [Identifier, Unsubscribe] {\n\tconst registry = manager.getRegistry()\n\tconst targetId = registry.addTarget(type, target)\n\n\treturn [targetId, () => registry.removeTarget(targetId)]\n}\n\nexport function registerSource(\n\ttype: SourceType,\n\tsource: DragSource,\n\tmanager: DragDropManager,\n): [Identifier, Unsubscribe] {\n\tconst registry = manager.getRegistry()\n\tconst sourceId = registry.addSource(type, source)\n\n\treturn [sourceId, () => registry.removeSource(sourceId)]\n}\n"], "mappings": "AAUA,OAAO,SAASA,cAAcA,CAC7BC,IAAgB,EAChBC,MAAkB,EAClBC,OAAwB,EACI;EAC5B,MAAMC,QAAQ,GAAGD,OAAO,CAACE,WAAW,EAAE;EACtC,MAAMC,QAAQ,GAAGF,QAAQ,CAACG,SAAS,CAACN,IAAI,EAAEC,MAAM,CAAC;EAEjD,OAAO,CAACI,QAAQ,EAAE,MAAMF,QAAQ,CAACI,YAAY,CAACF,QAAQ,CAAC,CAAC;;AAGzD,OAAO,SAASG,cAAcA,CAC7BR,IAAgB,EAChBS,MAAkB,EAClBP,OAAwB,EACI;EAC5B,MAAMC,QAAQ,GAAGD,OAAO,CAACE,WAAW,EAAE;EACtC,MAAMM,QAAQ,GAAGP,QAAQ,CAACQ,SAAS,CAACX,IAAI,EAAES,MAAM,CAAC;EAEjD,OAAO,CAACC,QAAQ,EAAE,MAAMP,QAAQ,CAACS,YAAY,CAACF,QAAQ,CAAC,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}