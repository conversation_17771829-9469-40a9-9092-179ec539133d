{"ast": null, "code": "// The minimum length of the national significant number.\nexport var MIN_LENGTH_FOR_NSN = 2; // The ITU says the maximum length should be 15,\n// but one can find longer numbers in Germany.\n\nexport var MAX_LENGTH_FOR_NSN = 17; // The maximum length of the country calling code.\n\nexport var MAX_LENGTH_COUNTRY_CODE = 3; // Digits accepted in phone numbers\n// (ascii, fullwidth, arabic-indic, and eastern arabic digits).\n\nexport var VALID_DIGITS = \"0-9\\uFF10-\\uFF19\\u0660-\\u0669\\u06F0-\\u06F9\"; // `DASHES` will be right after the opening square bracket of the \"character class\"\n\nvar DASHES = \"-\\u2010-\\u2015\\u2212\\u30FC\\uFF0D\";\nvar SLASHES = \"\\uFF0F/\";\nvar DOTS = \"\\uFF0E.\";\nexport var WHITESPACE = \" \\xA0\\xAD\\u200B\\u2060\\u3000\";\nvar BRACKETS = \"()\\uFF08\\uFF09\\uFF3B\\uFF3D\\\\[\\\\]\"; // export const OPENING_BRACKETS = '(\\uFF08\\uFF3B\\\\\\['\n\nvar TILDES = \"~\\u2053\\u223C\\uFF5E\"; // Regular expression of acceptable punctuation found in phone numbers. This\n// excludes punctuation found as a leading character only. This consists of dash\n// characters, white space characters, full stops, slashes, square brackets,\n// parentheses and tildes. Full-width variants are also present.\n\nexport var VALID_PUNCTUATION = \"\".concat(DASHES).concat(SLASHES).concat(DOTS).concat(WHITESPACE).concat(BRACKETS).concat(TILDES);\nexport var PLUS_CHARS = \"+\\uFF0B\"; // const LEADING_PLUS_CHARS_PATTERN = new RegExp('^[' + PLUS_CHARS + ']+')", "map": {"version": 3, "names": ["MIN_LENGTH_FOR_NSN", "MAX_LENGTH_FOR_NSN", "MAX_LENGTH_COUNTRY_CODE", "VALID_DIGITS", "DASHES", "SLASHES", "DOTS", "WHITESPACE", "BRACKETS", "TILDES", "VALID_PUNCTUATION", "concat", "PLUS_CHARS"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\constants.js"], "sourcesContent": ["// The minimum length of the national significant number.\r\nexport const MIN_LENGTH_FOR_NSN = 2\r\n\r\n// The ITU says the maximum length should be 15,\r\n// but one can find longer numbers in Germany.\r\nexport const MAX_LENGTH_FOR_NSN = 17\r\n\r\n// The maximum length of the country calling code.\r\nexport const MAX_LENGTH_COUNTRY_CODE = 3\r\n\r\n// Digits accepted in phone numbers\r\n// (ascii, fullwidth, arabic-indic, and eastern arabic digits).\r\nexport const VALID_DIGITS = '0-9\\uFF10-\\uFF19\\u0660-\\u0669\\u06F0-\\u06F9'\r\n\r\n// `DASHES` will be right after the opening square bracket of the \"character class\"\r\nconst DASHES = '-\\u2010-\\u2015\\u2212\\u30FC\\uFF0D'\r\nconst SLASHES = '\\uFF0F/'\r\nconst DOTS = '\\uFF0E.'\r\nexport const WHITESPACE = ' \\u00A0\\u00AD\\u200B\\u2060\\u3000'\r\nconst BRACKETS = '()\\uFF08\\uFF09\\uFF3B\\uFF3D\\\\[\\\\]'\r\n// export const OPENING_BRACKETS = '(\\uFF08\\uFF3B\\\\\\['\r\nconst TILDES = '~\\u2053\\u223C\\uFF5E'\r\n\r\n// Regular expression of acceptable punctuation found in phone numbers. This\r\n// excludes punctuation found as a leading character only. This consists of dash\r\n// characters, white space characters, full stops, slashes, square brackets,\r\n// parentheses and tildes. Full-width variants are also present.\r\nexport const VALID_PUNCTUATION = `${DASHES}${SLASHES}${DOTS}${WHITESPACE}${BRACKETS}${TILDES}`\r\n\r\nexport const PLUS_CHARS = '+\\uFF0B'\r\n// const LEADING_PLUS_CHARS_PATTERN = new RegExp('^[' + PLUS_CHARS + ']+')"], "mappings": "AAAA;AACA,OAAO,IAAMA,kBAAkB,GAAG,CAA3B,C,CAEP;AACA;;AACA,OAAO,IAAMC,kBAAkB,GAAG,EAA3B,C,CAEP;;AACA,OAAO,IAAMC,uBAAuB,GAAG,CAAhC,C,CAEP;AACA;;AACA,OAAO,IAAMC,YAAY,GAAG,4CAArB,C,CAEP;;AACA,IAAMC,MAAM,GAAG,kCAAf;AACA,IAAMC,OAAO,GAAG,SAAhB;AACA,IAAMC,IAAI,GAAG,SAAb;AACA,OAAO,IAAMC,UAAU,GAAG,6BAAnB;AACP,IAAMC,QAAQ,GAAG,kCAAjB,C,CACA;;AACA,IAAMC,MAAM,GAAG,qBAAf,C,CAEA;AACA;AACA;AACA;;AACA,OAAO,IAAMC,iBAAiB,MAAAC,MAAA,CAAMP,MAAN,EAAAO,MAAA,CAAeN,OAAf,EAAAM,MAAA,CAAyBL,IAAzB,EAAAK,MAAA,CAAgCJ,UAAhC,EAAAI,MAAA,CAA6CH,QAA7C,EAAAG,MAAA,CAAwDF,MAAxD,CAAvB;AAEP,OAAO,IAAMG,UAAU,GAAG,SAAnB,C,CACP"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}