{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"disabled\", \"component\", \"components\", \"componentsProps\", \"label\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { getMenuItemUnstyledUtilityClass } from './menuItemUnstyledClasses';\nimport useMenuItem from './useMenuItem';\nimport composeClasses from '../composeClasses';\nimport useSlotProps from '../utils/useSlotProps';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction getUtilityClasses(ownerState) {\n  const {\n    disabled,\n    focusVisible\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', focusVisible && 'focusVisible']\n  };\n  return composeClasses(slots, getMenuItemUnstyledUtilityClass, {});\n}\n/**\n *\n * Demos:\n *\n * - [Unstyled menu](https://mui.com/base/react-menu/)\n *\n * API:\n *\n * - [MenuItemUnstyled API](https://mui.com/base/api/menu-item-unstyled/)\n */\n\nconst MenuItemUnstyled = /*#__PURE__*/React.forwardRef(function MenuItemUnstyled(props, ref) {\n  var _ref;\n  const {\n      children,\n      disabled: disabledProp = false,\n      component,\n      components = {},\n      componentsProps = {},\n      label\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    getRootProps,\n    disabled,\n    focusVisible\n  } = useMenuItem({\n    disabled: disabledProp,\n    ref,\n    label\n  });\n  const ownerState = _extends({}, props, {\n    disabled,\n    focusVisible\n  });\n  const classes = getUtilityClasses(ownerState);\n  const Root = (_ref = component != null ? component : components.Root) != null ? _ref : 'li';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    getSlotProps: getRootProps,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    className: classes.root,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? MenuItemUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the MenuItem.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The props used for each slot inside the MenuItem.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * If `true`, the menu item will be disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * A text representation of the menu item's content.\n   * Used for keyboard text navigation matching.\n   */\n  label: PropTypes.string\n} : void 0;\nexport default MenuItemUnstyled;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "getMenuItemUnstyledUtilityClass", "useMenuItem", "composeClasses", "useSlotProps", "jsx", "_jsx", "getUtilityClasses", "ownerState", "disabled", "focusVisible", "slots", "root", "MenuItemUnstyled", "forwardRef", "props", "ref", "_ref", "children", "disabledProp", "component", "components", "componentsProps", "label", "other", "getRootProps", "classes", "Root", "rootProps", "elementType", "getSlotProps", "externalSlotProps", "externalForwardedProps", "className", "process", "env", "NODE_ENV", "propTypes", "node", "shape", "oneOfType", "func", "object", "bool", "string"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/MenuItemUnstyled/MenuItemUnstyled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"disabled\", \"component\", \"components\", \"componentsProps\", \"label\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { getMenuItemUnstyledUtilityClass } from './menuItemUnstyledClasses';\nimport useMenuItem from './useMenuItem';\nimport composeClasses from '../composeClasses';\nimport useSlotProps from '../utils/useSlotProps';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n\nfunction getUtilityClasses(ownerState) {\n  const {\n    disabled,\n    focusVisible\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', focusVisible && 'focusVisible']\n  };\n  return composeClasses(slots, getMenuItemUnstyledUtilityClass, {});\n}\n/**\n *\n * Demos:\n *\n * - [Unstyled menu](https://mui.com/base/react-menu/)\n *\n * API:\n *\n * - [MenuItemUnstyled API](https://mui.com/base/api/menu-item-unstyled/)\n */\n\n\nconst MenuItemUnstyled = /*#__PURE__*/React.forwardRef(function MenuItemUnstyled(props, ref) {\n  var _ref;\n\n  const {\n    children,\n    disabled: disabledProp = false,\n    component,\n    components = {},\n    componentsProps = {},\n    label\n  } = props,\n        other = _objectWithoutPropertiesLoose(props, _excluded);\n\n  const {\n    getRootProps,\n    disabled,\n    focusVisible\n  } = useMenuItem({\n    disabled: disabledProp,\n    ref,\n    label\n  });\n\n  const ownerState = _extends({}, props, {\n    disabled,\n    focusVisible\n  });\n\n  const classes = getUtilityClasses(ownerState);\n  const Root = (_ref = component != null ? component : components.Root) != null ? _ref : 'li';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    getSlotProps: getRootProps,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    className: classes.root,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? MenuItemUnstyled.propTypes\n/* remove-proptypes */\n= {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n\n  /**\n   * The components used for each slot inside the MenuItem.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n\n  /**\n   * The props used for each slot inside the MenuItem.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n\n  /**\n   * If `true`, the menu item will be disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n\n  /**\n   * A text representation of the menu item's content.\n   * Used for keyboard text navigation matching.\n   */\n  label: PropTypes.string\n} : void 0;\nexport default MenuItemUnstyled;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,OAAO,CAAC;AACjG,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,+BAA+B,QAAQ,2BAA2B;AAC3E,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,YAAY,MAAM,uBAAuB;AAChD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAE/C,SAASC,iBAAiBA,CAACC,UAAU,EAAE;EACrC,MAAM;IACJC,QAAQ;IACRC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEH,QAAQ,IAAI,UAAU,EAAEC,YAAY,IAAI,cAAc;EACvE,CAAC;EACD,OAAOP,cAAc,CAACQ,KAAK,EAAEV,+BAA+B,EAAE,CAAC,CAAC,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,MAAMY,gBAAgB,GAAG,aAAad,KAAK,CAACe,UAAU,CAAC,SAASD,gBAAgBA,CAACE,KAAK,EAAEC,GAAG,EAAE;EAC3F,IAAIC,IAAI;EAER,MAAM;MACJC,QAAQ;MACRT,QAAQ,EAAEU,YAAY,GAAG,KAAK;MAC9BC,SAAS;MACTC,UAAU,GAAG,CAAC,CAAC;MACfC,eAAe,GAAG,CAAC,CAAC;MACpBC;IACF,CAAC,GAAGR,KAAK;IACHS,KAAK,GAAG3B,6BAA6B,CAACkB,KAAK,EAAEjB,SAAS,CAAC;EAE7D,MAAM;IACJ2B,YAAY;IACZhB,QAAQ;IACRC;EACF,CAAC,GAAGR,WAAW,CAAC;IACdO,QAAQ,EAAEU,YAAY;IACtBH,GAAG;IACHO;EACF,CAAC,CAAC;EAEF,MAAMf,UAAU,GAAGZ,QAAQ,CAAC,CAAC,CAAC,EAAEmB,KAAK,EAAE;IACrCN,QAAQ;IACRC;EACF,CAAC,CAAC;EAEF,MAAMgB,OAAO,GAAGnB,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMmB,IAAI,GAAG,CAACV,IAAI,GAAGG,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAGC,UAAU,CAACM,IAAI,KAAK,IAAI,GAAGV,IAAI,GAAG,IAAI;EAC3F,MAAMW,SAAS,GAAGxB,YAAY,CAAC;IAC7ByB,WAAW,EAAEF,IAAI;IACjBG,YAAY,EAAEL,YAAY;IAC1BM,iBAAiB,EAAET,eAAe,CAACV,IAAI;IACvCoB,sBAAsB,EAAER,KAAK;IAC7BS,SAAS,EAAEP,OAAO,CAACd,IAAI;IACvBJ;EACF,CAAC,CAAC;EACF,OAAO,aAAaF,IAAI,CAACqB,IAAI,EAAE/B,QAAQ,CAAC,CAAC,CAAC,EAAEgC,SAAS,EAAE;IACrDV,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvB,gBAAgB,CAACwB;AACzD,yBACE;EACA;EACA;EACA;EACA;;EAEA;AACF;AACA;EACEnB,QAAQ,EAAElB,SAAS,CAACsC,IAAI;EAExB;AACF;AACA;AACA;EACElB,SAAS,EAAEpB,SAAS,CAAC6B,WAAW;EAEhC;AACF;AACA;AACA;AACA;EACER,UAAU,EAAErB,SAAS,CAACuC,KAAK,CAAC;IAC1BZ,IAAI,EAAE3B,SAAS,CAAC6B;EAClB,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACEP,eAAe,EAAEtB,SAAS,CAACuC,KAAK,CAAC;IAC/B3B,IAAI,EAAEZ,SAAS,CAACwC,SAAS,CAAC,CAACxC,SAAS,CAACyC,IAAI,EAAEzC,SAAS,CAAC0C,MAAM,CAAC;EAC9D,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACEjC,QAAQ,EAAET,SAAS,CAAC2C,IAAI;EAExB;AACF;AACA;AACA;EACEpB,KAAK,EAAEvB,SAAS,CAAC4C;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,eAAe/B,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}