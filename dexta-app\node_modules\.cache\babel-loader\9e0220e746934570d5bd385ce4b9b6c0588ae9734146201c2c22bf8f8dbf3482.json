{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useIsFocusVisible as useIsFocusVisible, unstable_useEnhancedEffect as useEnhancedEffect, unstable_ownerDocument as ownerDocument, unstable_useEventCallback as useEventCallback, unstable_useForkRef as useForkRef, unstable_useControlled as useControlled, visuallyHidden } from '@mui/utils';\nconst INTENTIONAL_DRAG_COUNT_THRESHOLD = 2;\nfunction asc(a, b) {\n  return a - b;\n}\nfunction clamp(value, min, max) {\n  if (value == null) {\n    return min;\n  }\n  return Math.min(Math.max(min, value), max);\n}\nfunction findClosest(values, currentValue) {\n  var _values$reduce;\n  const {\n    index: closestIndex\n  } = (_values$reduce = values.reduce((acc, value, index) => {\n    const distance = Math.abs(currentValue - value);\n    if (acc === null || distance < acc.distance || distance === acc.distance) {\n      return {\n        distance,\n        index\n      };\n    }\n    return acc;\n  }, null)) != null ? _values$reduce : {};\n  return closestIndex;\n}\nfunction trackFinger(event, touchId) {\n  // The event is TouchEvent\n  if (touchId.current !== undefined && event.changedTouches) {\n    const touchEvent = event;\n    for (let i = 0; i < touchEvent.changedTouches.length; i += 1) {\n      const touch = touchEvent.changedTouches[i];\n      if (touch.identifier === touchId.current) {\n        return {\n          x: touch.clientX,\n          y: touch.clientY\n        };\n      }\n    }\n    return false;\n  } // The event is MouseEvent\n\n  return {\n    x: event.clientX,\n    y: event.clientY\n  };\n}\nexport function valueToPercent(value, min, max) {\n  return (value - min) * 100 / (max - min);\n}\nfunction percentToValue(percent, min, max) {\n  return (max - min) * percent + min;\n}\nfunction getDecimalPrecision(num) {\n  // This handles the case when num is very small (0.00000001), js will turn this into 1e-8.\n  // When num is bigger than 1 or less than -1 it won't get converted to this notation so it's fine.\n  if (Math.abs(num) < 1) {\n    const parts = num.toExponential().split('e-');\n    const matissaDecimalPart = parts[0].split('.')[1];\n    return (matissaDecimalPart ? matissaDecimalPart.length : 0) + parseInt(parts[1], 10);\n  }\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\nfunction roundValueToStep(value, step, min) {\n  const nearest = Math.round((value - min) / step) * step + min;\n  return Number(nearest.toFixed(getDecimalPrecision(step)));\n}\nfunction setValueIndex({\n  values,\n  newValue,\n  index\n}) {\n  const output = values.slice();\n  output[index] = newValue;\n  return output.sort(asc);\n}\nfunction focusThumb({\n  sliderRef,\n  activeIndex,\n  setActive\n}) {\n  var _sliderRef$current, _doc$activeElement;\n  const doc = ownerDocument(sliderRef.current);\n  if (!((_sliderRef$current = sliderRef.current) != null && _sliderRef$current.contains(doc.activeElement)) || Number(doc == null ? void 0 : (_doc$activeElement = doc.activeElement) == null ? void 0 : _doc$activeElement.getAttribute('data-index')) !== activeIndex) {\n    var _sliderRef$current2;\n    (_sliderRef$current2 = sliderRef.current) == null ? void 0 : _sliderRef$current2.querySelector(`[type=\"range\"][data-index=\"${activeIndex}\"]`).focus();\n  }\n  if (setActive) {\n    setActive(activeIndex);\n  }\n}\nconst axisProps = {\n  horizontal: {\n    offset: percent => ({\n      left: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  'horizontal-reverse': {\n    offset: percent => ({\n      right: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  vertical: {\n    offset: percent => ({\n      bottom: `${percent}%`\n    }),\n    leap: percent => ({\n      height: `${percent}%`\n    })\n  }\n};\nexport const Identity = x => x; // TODO: remove support for Safari < 13.\n// https://caniuse.com/#search=touch-action\n//\n// Safari, on iOS, supports touch action since v13.\n// Over 80% of the iOS phones are compatible\n// in August 2020.\n// Utilizing the CSS.supports method to check if touch-action is supported.\n// Since CSS.supports is supported on all but Edge@12 and IE and touch-action\n// is supported on both Edge@12 and IE if CSS.supports is not available that means that\n// touch-action will be supported\n\nlet cachedSupportsTouchActionNone;\nfunction doesSupportTouchActionNone() {\n  if (cachedSupportsTouchActionNone === undefined) {\n    if (typeof CSS !== 'undefined' && typeof CSS.supports === 'function') {\n      cachedSupportsTouchActionNone = CSS.supports('touch-action', 'none');\n    } else {\n      cachedSupportsTouchActionNone = true;\n    }\n  }\n  return cachedSupportsTouchActionNone;\n}\nexport default function useSlider(parameters) {\n  const {\n    'aria-labelledby': ariaLabelledby,\n    defaultValue,\n    disabled = false,\n    disableSwap = false,\n    isRtl = false,\n    marks: marksProp = false,\n    max = 100,\n    min = 0,\n    name,\n    onChange,\n    onChangeCommitted,\n    orientation = 'horizontal',\n    ref,\n    scale = Identity,\n    step = 1,\n    tabIndex,\n    value: valueProp\n  } = parameters;\n  const touchId = React.useRef(); // We can't use the :active browser pseudo-classes.\n  // - The active state isn't triggered when clicking on the rail.\n  // - The active state isn't transferred when inversing a range slider.\n\n  const [active, setActive] = React.useState(-1);\n  const [open, setOpen] = React.useState(-1);\n  const [dragging, setDragging] = React.useState(false);\n  const moveCount = React.useRef(0);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue != null ? defaultValue : min,\n    name: 'Slider'\n  });\n  const handleChange = onChange && ((event, value, thumbIndex) => {\n    // Redefine target to allow name and value to be read.\n    // This allows seamless integration with the most popular form libraries.\n    // https://github.com/mui/material-ui/issues/13485#issuecomment-676048492\n    // Clone the event to not override `target` of the original event.\n    const nativeEvent = event.nativeEvent || event; // @ts-ignore The nativeEvent is function, not object\n\n    const clonedEvent = new nativeEvent.constructor(nativeEvent.type, nativeEvent);\n    Object.defineProperty(clonedEvent, 'target', {\n      writable: true,\n      value: {\n        value,\n        name\n      }\n    });\n    onChange(clonedEvent, value, thumbIndex);\n  });\n  const range = Array.isArray(valueDerived);\n  let values = range ? valueDerived.slice().sort(asc) : [valueDerived];\n  values = values.map(value => clamp(value, min, max));\n  const marks = marksProp === true && step !== null ? [...Array(Math.floor((max - min) / step) + 1)].map((_, index) => ({\n    value: min + step * index\n  })) : marksProp || [];\n  const marksValues = marks.map(mark => mark.value);\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusedThumbIndex, setFocusedThumbIndex] = React.useState(-1);\n  const sliderRef = React.useRef();\n  const handleFocusRef = useForkRef(focusVisibleRef, sliderRef);\n  const handleRef = useForkRef(ref, handleFocusRef);\n  const createHandleHiddenInputFocus = otherHandlers => event => {\n    var _otherHandlers$onFocu;\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      setFocusedThumbIndex(index);\n    }\n    setOpen(index);\n    otherHandlers == null ? void 0 : (_otherHandlers$onFocu = otherHandlers.onFocus) == null ? void 0 : _otherHandlers$onFocu.call(otherHandlers, event);\n  };\n  const createHandleHiddenInputBlur = otherHandlers => event => {\n    var _otherHandlers$onBlur;\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusedThumbIndex(-1);\n    }\n    setOpen(-1);\n    otherHandlers == null ? void 0 : (_otherHandlers$onBlur = otherHandlers.onBlur) == null ? void 0 : _otherHandlers$onBlur.call(otherHandlers, event);\n  };\n  useEnhancedEffect(() => {\n    if (disabled && sliderRef.current.contains(document.activeElement)) {\n      var _document$activeEleme;\n\n      // This is necessary because Firefox and Safari will keep focus\n      // on a disabled element:\n      // https://codesandbox.io/s/mui-pr-22247-forked-h151h?file=/src/App.js\n      // @ts-ignore\n      (_document$activeEleme = document.activeElement) == null ? void 0 : _document$activeEleme.blur();\n    }\n  }, [disabled]);\n  if (disabled && active !== -1) {\n    setActive(-1);\n  }\n  if (disabled && focusedThumbIndex !== -1) {\n    setFocusedThumbIndex(-1);\n  }\n  const createHandleHiddenInputChange = otherHandlers => event => {\n    var _otherHandlers$onChan;\n    (_otherHandlers$onChan = otherHandlers.onChange) == null ? void 0 : _otherHandlers$onChan.call(otherHandlers, event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    const value = values[index];\n    const marksIndex = marksValues.indexOf(value); // @ts-ignore\n\n    let newValue = event.target.valueAsNumber;\n    if (marks && step == null) {\n      newValue = newValue < value ? marksValues[marksIndex - 1] : marksValues[marksIndex + 1];\n    }\n    newValue = clamp(newValue, min, max);\n    if (marks && step == null) {\n      const currentMarkIndex = marksValues.indexOf(values[index]);\n      newValue = newValue < values[index] ? marksValues[currentMarkIndex - 1] : marksValues[currentMarkIndex + 1];\n    }\n    if (range) {\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[index - 1] || -Infinity, values[index + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index\n      });\n      let activeIndex = index; // Potentially swap the index if needed.\n\n      if (!disableSwap) {\n        activeIndex = newValue.indexOf(previousValue);\n      }\n      focusThumb({\n        sliderRef,\n        activeIndex\n      });\n    }\n    setValueState(newValue);\n    setFocusedThumbIndex(index);\n    if (handleChange) {\n      handleChange(event, newValue, index);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(event, newValue);\n    }\n  };\n  const previousIndex = React.useRef();\n  let axis = orientation;\n  if (isRtl && orientation === 'horizontal') {\n    axis += '-reverse';\n  }\n  const getFingerNewValue = ({\n    finger,\n    move = false,\n    values: values2\n  }) => {\n    const {\n      current: slider\n    } = sliderRef;\n    const {\n      width,\n      height,\n      bottom,\n      left\n    } = slider.getBoundingClientRect();\n    let percent;\n    if (axis.indexOf('vertical') === 0) {\n      percent = (bottom - finger.y) / height;\n    } else {\n      percent = (finger.x - left) / width;\n    }\n    if (axis.indexOf('-reverse') !== -1) {\n      percent = 1 - percent;\n    }\n    let newValue;\n    newValue = percentToValue(percent, min, max);\n    if (step) {\n      newValue = roundValueToStep(newValue, step, min);\n    } else {\n      const closestIndex = findClosest(marksValues, newValue);\n      newValue = marksValues[closestIndex];\n    }\n    newValue = clamp(newValue, min, max);\n    let activeIndex = 0;\n    if (range) {\n      if (!move) {\n        activeIndex = findClosest(values2, newValue);\n      } else {\n        activeIndex = previousIndex.current;\n      } // Bound the new value to the thumb's neighbours.\n\n      if (disableSwap) {\n        newValue = clamp(newValue, values2[activeIndex - 1] || -Infinity, values2[activeIndex + 1] || Infinity);\n      }\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values: values2,\n        newValue,\n        index: activeIndex\n      }); // Potentially swap the index if needed.\n\n      if (!(disableSwap && move)) {\n        activeIndex = newValue.indexOf(previousValue);\n        previousIndex.current = activeIndex;\n      }\n    }\n    return {\n      newValue,\n      activeIndex\n    };\n  };\n  const handleTouchMove = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    if (!finger) {\n      return;\n    }\n    moveCount.current += 1; // Cancel move in case some other element consumed a mouseup event and it was not fired.\n    // @ts-ignore buttons doesn't not exists on touch event\n\n    if (nativeEvent.type === 'mousemove' && nativeEvent.buttons === 0) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      handleTouchEnd(nativeEvent);\n      return;\n    }\n    const {\n      newValue,\n      activeIndex\n    } = getFingerNewValue({\n      finger,\n      move: true,\n      values\n    });\n    focusThumb({\n      sliderRef,\n      activeIndex,\n      setActive\n    });\n    setValueState(newValue);\n    if (!dragging && moveCount.current > INTENTIONAL_DRAG_COUNT_THRESHOLD) {\n      setDragging(true);\n    }\n    if (handleChange) {\n      handleChange(nativeEvent, newValue, activeIndex);\n    }\n  });\n  const handleTouchEnd = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    setDragging(false);\n    if (!finger) {\n      return;\n    }\n    const {\n      newValue\n    } = getFingerNewValue({\n      finger,\n      move: true,\n      values\n    });\n    setActive(-1);\n    if (nativeEvent.type === 'touchend') {\n      setOpen(-1);\n    }\n    if (onChangeCommitted) {\n      onChangeCommitted(nativeEvent, newValue);\n    }\n    touchId.current = undefined; // eslint-disable-next-line @typescript-eslint/no-use-before-define\n\n    stopListening();\n  });\n  const handleTouchStart = useEventCallback(nativeEvent => {\n    if (disabled) {\n      return;\n    } // If touch-action: none; is not supported we need to prevent the scroll manually.\n\n    if (!doesSupportTouchActionNone()) {\n      nativeEvent.preventDefault();\n    }\n    const touch = nativeEvent.changedTouches[0];\n    if (touch != null) {\n      // A number that uniquely identifies the current finger in the touch session.\n      touchId.current = touch.identifier;\n    }\n    const finger = trackFinger(nativeEvent, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger,\n        values\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange) {\n        handleChange(nativeEvent, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('touchmove', handleTouchMove);\n    doc.addEventListener('touchend', handleTouchEnd);\n  });\n  const stopListening = React.useCallback(() => {\n    const doc = ownerDocument(sliderRef.current);\n    doc.removeEventListener('mousemove', handleTouchMove);\n    doc.removeEventListener('mouseup', handleTouchEnd);\n    doc.removeEventListener('touchmove', handleTouchMove);\n    doc.removeEventListener('touchend', handleTouchEnd);\n  }, [handleTouchEnd, handleTouchMove]);\n  React.useEffect(() => {\n    const {\n      current: slider\n    } = sliderRef;\n    slider.addEventListener('touchstart', handleTouchStart, {\n      passive: doesSupportTouchActionNone()\n    });\n    return () => {\n      // @ts-ignore\n      slider.removeEventListener('touchstart', handleTouchStart, {\n        passive: doesSupportTouchActionNone()\n      });\n      stopListening();\n    };\n  }, [stopListening, handleTouchStart]);\n  React.useEffect(() => {\n    if (disabled) {\n      stopListening();\n    }\n  }, [disabled, stopListening]);\n  const createHandleMouseDown = otherHandlers => event => {\n    var _otherHandlers$onMous;\n    (_otherHandlers$onMous = otherHandlers.onMouseDown) == null ? void 0 : _otherHandlers$onMous.call(otherHandlers, event);\n    if (disabled) {\n      return;\n    }\n    if (event.defaultPrevented) {\n      return;\n    } // Only handle left clicks\n\n    if (event.button !== 0) {\n      return;\n    } // Avoid text selection\n\n    event.preventDefault();\n    const finger = trackFinger(event, touchId);\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger,\n        values\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n      if (handleChange) {\n        handleChange(event, newValue, activeIndex);\n      }\n    }\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('mousemove', handleTouchMove);\n    doc.addEventListener('mouseup', handleTouchEnd);\n  };\n  const trackOffset = valueToPercent(range ? values[0] : min, min, max);\n  const trackLeap = valueToPercent(values[values.length - 1], min, max) - trackOffset;\n  const getRootProps = (otherHandlers = {}) => {\n    const ownEventHandlers = {\n      onMouseDown: createHandleMouseDown(otherHandlers || {})\n    };\n    const mergedEventHandlers = _extends({}, otherHandlers, ownEventHandlers);\n    return _extends({\n      ref: handleRef\n    }, mergedEventHandlers);\n  };\n  const createHandleMouseOver = otherHandlers => event => {\n    var _otherHandlers$onMous2;\n    (_otherHandlers$onMous2 = otherHandlers.onMouseOver) == null ? void 0 : _otherHandlers$onMous2.call(otherHandlers, event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    setOpen(index);\n  };\n  const createHandleMouseLeave = otherHandlers => event => {\n    var _otherHandlers$onMous3;\n    (_otherHandlers$onMous3 = otherHandlers.onMouseLeave) == null ? void 0 : _otherHandlers$onMous3.call(otherHandlers, event);\n    setOpen(-1);\n  };\n  const getThumbProps = (otherHandlers = {}) => {\n    const ownEventHandlers = {\n      onMouseOver: createHandleMouseOver(otherHandlers || {}),\n      onMouseLeave: createHandleMouseLeave(otherHandlers || {})\n    };\n    return _extends({}, otherHandlers, ownEventHandlers);\n  };\n  const getHiddenInputProps = (otherHandlers = {}) => {\n    var _parameters$step;\n    const ownEventHandlers = {\n      onChange: createHandleHiddenInputChange(otherHandlers || {}),\n      onFocus: createHandleHiddenInputFocus(otherHandlers || {}),\n      onBlur: createHandleHiddenInputBlur(otherHandlers || {})\n    };\n    const mergedEventHandlers = _extends({}, otherHandlers, ownEventHandlers);\n    return _extends({\n      tabIndex,\n      'aria-labelledby': ariaLabelledby,\n      'aria-orientation': orientation,\n      'aria-valuemax': scale(max),\n      'aria-valuemin': scale(min),\n      name,\n      type: 'range',\n      min: parameters.min,\n      max: parameters.max,\n      step: (_parameters$step = parameters.step) != null ? _parameters$step : undefined,\n      disabled\n    }, mergedEventHandlers, {\n      style: _extends({}, visuallyHidden, {\n        direction: isRtl ? 'rtl' : 'ltr',\n        // So that VoiceOver's focus indicator matches the thumb's dimensions\n        width: '100%',\n        height: '100%'\n      })\n    });\n  };\n  return {\n    active,\n    axis: axis,\n    axisProps,\n    dragging,\n    focusedThumbIndex,\n    getHiddenInputProps,\n    getRootProps,\n    getThumbProps,\n    marks: marks,\n    open,\n    range,\n    trackLeap,\n    trackOffset,\n    values\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_useIsFocusVisible", "useIsFocusVisible", "unstable_useEnhancedEffect", "useEnhancedEffect", "unstable_ownerDocument", "ownerDocument", "unstable_useEventCallback", "useEventCallback", "unstable_useForkRef", "useForkRef", "unstable_useControlled", "useControlled", "visuallyHidden", "INTENTIONAL_DRAG_COUNT_THRESHOLD", "asc", "a", "b", "clamp", "value", "min", "max", "Math", "findClosest", "values", "currentValue", "_values$reduce", "index", "closestIndex", "reduce", "acc", "distance", "abs", "trackFinger", "event", "touchId", "current", "undefined", "changedTouches", "touchEvent", "i", "length", "touch", "identifier", "x", "clientX", "y", "clientY", "valueToPercent", "percentToValue", "percent", "getDecimalPrecision", "num", "parts", "toExponential", "split", "mat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parseInt", "decimalPart", "toString", "roundValueToStep", "step", "nearest", "round", "Number", "toFixed", "setValueIndex", "newValue", "output", "slice", "sort", "focusThumb", "sliderRef", "activeIndex", "setActive", "_sliderRef$current", "_doc$activeElement", "doc", "contains", "activeElement", "getAttribute", "_sliderRef$current2", "querySelector", "focus", "axisProps", "horizontal", "offset", "left", "leap", "width", "right", "vertical", "bottom", "height", "Identity", "cachedSupportsTouchActionNone", "doesSupportTouchActionNone", "CSS", "supports", "useSlider", "parameters", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultValue", "disabled", "disableSwap", "isRtl", "marks", "marksProp", "name", "onChange", "onChangeCommitted", "orientation", "ref", "scale", "tabIndex", "valueProp", "useRef", "active", "useState", "open", "<PERSON><PERSON><PERSON>", "dragging", "setDragging", "moveCount", "valueDerived", "setValueState", "controlled", "default", "handleChange", "thumbIndex", "nativeEvent", "clonedEvent", "constructor", "type", "Object", "defineProperty", "writable", "range", "Array", "isArray", "map", "floor", "_", "marksV<PERSON>ues", "mark", "isFocusVisibleRef", "onBlur", "handleBlurVisible", "onFocus", "handleFocusVisible", "focusVisibleRef", "focusedThumbIndex", "setFocusedThumbIndex", "handleFocusRef", "handleRef", "createHandleHiddenInputFocus", "otherHandlers", "_otherHandlers$onFocu", "currentTarget", "call", "createHandleHiddenInputBlur", "_otherHandlers$onBlur", "document", "_document$activeEleme", "blur", "createHandleHiddenInputChange", "_otherHandlers$onChan", "marksIndex", "indexOf", "target", "valueAsNumber", "currentMarkIndex", "Infinity", "previousValue", "previousIndex", "axis", "getFingerNewValue", "finger", "move", "values2", "slider", "getBoundingClientRect", "handleTouchMove", "buttons", "handleTouchEnd", "stopListening", "handleTouchStart", "preventDefault", "addEventListener", "useCallback", "removeEventListener", "useEffect", "passive", "createHandleMouseDown", "_otherHandlers$onMous", "onMouseDown", "defaultPrevented", "button", "trackOffset", "trackLeap", "getRootProps", "ownEventHandlers", "mergedEventHandlers", "createHandleMouseOver", "_otherHandlers$onMous2", "onMouseOver", "createHandleMouseLeave", "_otherHandlers$onMous3", "onMouseLeave", "getThumbProps", "getHiddenInputProps", "_parameters$step", "style", "direction"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/SliderUnstyled/useSlider.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useIsFocusVisible as useIsFocusVisible, unstable_useEnhancedEffect as useEnhancedEffect, unstable_ownerDocument as ownerDocument, unstable_useEventCallback as useEventCallback, unstable_useForkRef as useForkRef, unstable_useControlled as useControlled, visuallyHidden } from '@mui/utils';\nconst INTENTIONAL_DRAG_COUNT_THRESHOLD = 2;\n\nfunction asc(a, b) {\n  return a - b;\n}\n\nfunction clamp(value, min, max) {\n  if (value == null) {\n    return min;\n  }\n\n  return Math.min(Math.max(min, value), max);\n}\n\nfunction findClosest(values, currentValue) {\n  var _values$reduce;\n\n  const {\n    index: closestIndex\n  } = (_values$reduce = values.reduce((acc, value, index) => {\n    const distance = Math.abs(currentValue - value);\n\n    if (acc === null || distance < acc.distance || distance === acc.distance) {\n      return {\n        distance,\n        index\n      };\n    }\n\n    return acc;\n  }, null)) != null ? _values$reduce : {};\n  return closestIndex;\n}\n\nfunction trackFinger(event, touchId) {\n  // The event is TouchEvent\n  if (touchId.current !== undefined && event.changedTouches) {\n    const touchEvent = event;\n\n    for (let i = 0; i < touchEvent.changedTouches.length; i += 1) {\n      const touch = touchEvent.changedTouches[i];\n\n      if (touch.identifier === touchId.current) {\n        return {\n          x: touch.clientX,\n          y: touch.clientY\n        };\n      }\n    }\n\n    return false;\n  } // The event is MouseEvent\n\n\n  return {\n    x: event.clientX,\n    y: event.clientY\n  };\n}\n\nexport function valueToPercent(value, min, max) {\n  return (value - min) * 100 / (max - min);\n}\n\nfunction percentToValue(percent, min, max) {\n  return (max - min) * percent + min;\n}\n\nfunction getDecimalPrecision(num) {\n  // This handles the case when num is very small (0.00000001), js will turn this into 1e-8.\n  // When num is bigger than 1 or less than -1 it won't get converted to this notation so it's fine.\n  if (Math.abs(num) < 1) {\n    const parts = num.toExponential().split('e-');\n    const matissaDecimalPart = parts[0].split('.')[1];\n    return (matissaDecimalPart ? matissaDecimalPart.length : 0) + parseInt(parts[1], 10);\n  }\n\n  const decimalPart = num.toString().split('.')[1];\n  return decimalPart ? decimalPart.length : 0;\n}\n\nfunction roundValueToStep(value, step, min) {\n  const nearest = Math.round((value - min) / step) * step + min;\n  return Number(nearest.toFixed(getDecimalPrecision(step)));\n}\n\nfunction setValueIndex({\n  values,\n  newValue,\n  index\n}) {\n  const output = values.slice();\n  output[index] = newValue;\n  return output.sort(asc);\n}\n\nfunction focusThumb({\n  sliderRef,\n  activeIndex,\n  setActive\n}) {\n  var _sliderRef$current, _doc$activeElement;\n\n  const doc = ownerDocument(sliderRef.current);\n\n  if (!((_sliderRef$current = sliderRef.current) != null && _sliderRef$current.contains(doc.activeElement)) || Number(doc == null ? void 0 : (_doc$activeElement = doc.activeElement) == null ? void 0 : _doc$activeElement.getAttribute('data-index')) !== activeIndex) {\n    var _sliderRef$current2;\n\n    (_sliderRef$current2 = sliderRef.current) == null ? void 0 : _sliderRef$current2.querySelector(`[type=\"range\"][data-index=\"${activeIndex}\"]`).focus();\n  }\n\n  if (setActive) {\n    setActive(activeIndex);\n  }\n}\n\nconst axisProps = {\n  horizontal: {\n    offset: percent => ({\n      left: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  'horizontal-reverse': {\n    offset: percent => ({\n      right: `${percent}%`\n    }),\n    leap: percent => ({\n      width: `${percent}%`\n    })\n  },\n  vertical: {\n    offset: percent => ({\n      bottom: `${percent}%`\n    }),\n    leap: percent => ({\n      height: `${percent}%`\n    })\n  }\n};\nexport const Identity = x => x; // TODO: remove support for Safari < 13.\n// https://caniuse.com/#search=touch-action\n//\n// Safari, on iOS, supports touch action since v13.\n// Over 80% of the iOS phones are compatible\n// in August 2020.\n// Utilizing the CSS.supports method to check if touch-action is supported.\n// Since CSS.supports is supported on all but Edge@12 and IE and touch-action\n// is supported on both Edge@12 and IE if CSS.supports is not available that means that\n// touch-action will be supported\n\nlet cachedSupportsTouchActionNone;\n\nfunction doesSupportTouchActionNone() {\n  if (cachedSupportsTouchActionNone === undefined) {\n    if (typeof CSS !== 'undefined' && typeof CSS.supports === 'function') {\n      cachedSupportsTouchActionNone = CSS.supports('touch-action', 'none');\n    } else {\n      cachedSupportsTouchActionNone = true;\n    }\n  }\n\n  return cachedSupportsTouchActionNone;\n}\n\nexport default function useSlider(parameters) {\n  const {\n    'aria-labelledby': ariaLabelledby,\n    defaultValue,\n    disabled = false,\n    disableSwap = false,\n    isRtl = false,\n    marks: marksProp = false,\n    max = 100,\n    min = 0,\n    name,\n    onChange,\n    onChangeCommitted,\n    orientation = 'horizontal',\n    ref,\n    scale = Identity,\n    step = 1,\n    tabIndex,\n    value: valueProp\n  } = parameters;\n  const touchId = React.useRef(); // We can't use the :active browser pseudo-classes.\n  // - The active state isn't triggered when clicking on the rail.\n  // - The active state isn't transferred when inversing a range slider.\n\n  const [active, setActive] = React.useState(-1);\n  const [open, setOpen] = React.useState(-1);\n  const [dragging, setDragging] = React.useState(false);\n  const moveCount = React.useRef(0);\n  const [valueDerived, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue != null ? defaultValue : min,\n    name: 'Slider'\n  });\n\n  const handleChange = onChange && ((event, value, thumbIndex) => {\n    // Redefine target to allow name and value to be read.\n    // This allows seamless integration with the most popular form libraries.\n    // https://github.com/mui/material-ui/issues/13485#issuecomment-676048492\n    // Clone the event to not override `target` of the original event.\n    const nativeEvent = event.nativeEvent || event; // @ts-ignore The nativeEvent is function, not object\n\n    const clonedEvent = new nativeEvent.constructor(nativeEvent.type, nativeEvent);\n    Object.defineProperty(clonedEvent, 'target', {\n      writable: true,\n      value: {\n        value,\n        name\n      }\n    });\n    onChange(clonedEvent, value, thumbIndex);\n  });\n\n  const range = Array.isArray(valueDerived);\n  let values = range ? valueDerived.slice().sort(asc) : [valueDerived];\n  values = values.map(value => clamp(value, min, max));\n  const marks = marksProp === true && step !== null ? [...Array(Math.floor((max - min) / step) + 1)].map((_, index) => ({\n    value: min + step * index\n  })) : marksProp || [];\n  const marksValues = marks.map(mark => mark.value);\n  const {\n    isFocusVisibleRef,\n    onBlur: handleBlurVisible,\n    onFocus: handleFocusVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusedThumbIndex, setFocusedThumbIndex] = React.useState(-1);\n  const sliderRef = React.useRef();\n  const handleFocusRef = useForkRef(focusVisibleRef, sliderRef);\n  const handleRef = useForkRef(ref, handleFocusRef);\n\n  const createHandleHiddenInputFocus = otherHandlers => event => {\n    var _otherHandlers$onFocu;\n\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    handleFocusVisible(event);\n\n    if (isFocusVisibleRef.current === true) {\n      setFocusedThumbIndex(index);\n    }\n\n    setOpen(index);\n    otherHandlers == null ? void 0 : (_otherHandlers$onFocu = otherHandlers.onFocus) == null ? void 0 : _otherHandlers$onFocu.call(otherHandlers, event);\n  };\n\n  const createHandleHiddenInputBlur = otherHandlers => event => {\n    var _otherHandlers$onBlur;\n\n    handleBlurVisible(event);\n\n    if (isFocusVisibleRef.current === false) {\n      setFocusedThumbIndex(-1);\n    }\n\n    setOpen(-1);\n    otherHandlers == null ? void 0 : (_otherHandlers$onBlur = otherHandlers.onBlur) == null ? void 0 : _otherHandlers$onBlur.call(otherHandlers, event);\n  };\n\n  useEnhancedEffect(() => {\n    if (disabled && sliderRef.current.contains(document.activeElement)) {\n      var _document$activeEleme;\n\n      // This is necessary because Firefox and Safari will keep focus\n      // on a disabled element:\n      // https://codesandbox.io/s/mui-pr-22247-forked-h151h?file=/src/App.js\n      // @ts-ignore\n      (_document$activeEleme = document.activeElement) == null ? void 0 : _document$activeEleme.blur();\n    }\n  }, [disabled]);\n\n  if (disabled && active !== -1) {\n    setActive(-1);\n  }\n\n  if (disabled && focusedThumbIndex !== -1) {\n    setFocusedThumbIndex(-1);\n  }\n\n  const createHandleHiddenInputChange = otherHandlers => event => {\n    var _otherHandlers$onChan;\n\n    (_otherHandlers$onChan = otherHandlers.onChange) == null ? void 0 : _otherHandlers$onChan.call(otherHandlers, event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    const value = values[index];\n    const marksIndex = marksValues.indexOf(value); // @ts-ignore\n\n    let newValue = event.target.valueAsNumber;\n\n    if (marks && step == null) {\n      newValue = newValue < value ? marksValues[marksIndex - 1] : marksValues[marksIndex + 1];\n    }\n\n    newValue = clamp(newValue, min, max);\n\n    if (marks && step == null) {\n      const currentMarkIndex = marksValues.indexOf(values[index]);\n      newValue = newValue < values[index] ? marksValues[currentMarkIndex - 1] : marksValues[currentMarkIndex + 1];\n    }\n\n    if (range) {\n      // Bound the new value to the thumb's neighbours.\n      if (disableSwap) {\n        newValue = clamp(newValue, values[index - 1] || -Infinity, values[index + 1] || Infinity);\n      }\n\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values,\n        newValue,\n        index\n      });\n      let activeIndex = index; // Potentially swap the index if needed.\n\n      if (!disableSwap) {\n        activeIndex = newValue.indexOf(previousValue);\n      }\n\n      focusThumb({\n        sliderRef,\n        activeIndex\n      });\n    }\n\n    setValueState(newValue);\n    setFocusedThumbIndex(index);\n\n    if (handleChange) {\n      handleChange(event, newValue, index);\n    }\n\n    if (onChangeCommitted) {\n      onChangeCommitted(event, newValue);\n    }\n  };\n\n  const previousIndex = React.useRef();\n  let axis = orientation;\n\n  if (isRtl && orientation === 'horizontal') {\n    axis += '-reverse';\n  }\n\n  const getFingerNewValue = ({\n    finger,\n    move = false,\n    values: values2\n  }) => {\n    const {\n      current: slider\n    } = sliderRef;\n    const {\n      width,\n      height,\n      bottom,\n      left\n    } = slider.getBoundingClientRect();\n    let percent;\n\n    if (axis.indexOf('vertical') === 0) {\n      percent = (bottom - finger.y) / height;\n    } else {\n      percent = (finger.x - left) / width;\n    }\n\n    if (axis.indexOf('-reverse') !== -1) {\n      percent = 1 - percent;\n    }\n\n    let newValue;\n    newValue = percentToValue(percent, min, max);\n\n    if (step) {\n      newValue = roundValueToStep(newValue, step, min);\n    } else {\n      const closestIndex = findClosest(marksValues, newValue);\n      newValue = marksValues[closestIndex];\n    }\n\n    newValue = clamp(newValue, min, max);\n    let activeIndex = 0;\n\n    if (range) {\n      if (!move) {\n        activeIndex = findClosest(values2, newValue);\n      } else {\n        activeIndex = previousIndex.current;\n      } // Bound the new value to the thumb's neighbours.\n\n\n      if (disableSwap) {\n        newValue = clamp(newValue, values2[activeIndex - 1] || -Infinity, values2[activeIndex + 1] || Infinity);\n      }\n\n      const previousValue = newValue;\n      newValue = setValueIndex({\n        values: values2,\n        newValue,\n        index: activeIndex\n      }); // Potentially swap the index if needed.\n\n      if (!(disableSwap && move)) {\n        activeIndex = newValue.indexOf(previousValue);\n        previousIndex.current = activeIndex;\n      }\n    }\n\n    return {\n      newValue,\n      activeIndex\n    };\n  };\n\n  const handleTouchMove = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n\n    if (!finger) {\n      return;\n    }\n\n    moveCount.current += 1; // Cancel move in case some other element consumed a mouseup event and it was not fired.\n    // @ts-ignore buttons doesn't not exists on touch event\n\n    if (nativeEvent.type === 'mousemove' && nativeEvent.buttons === 0) {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      handleTouchEnd(nativeEvent);\n      return;\n    }\n\n    const {\n      newValue,\n      activeIndex\n    } = getFingerNewValue({\n      finger,\n      move: true,\n      values\n    });\n    focusThumb({\n      sliderRef,\n      activeIndex,\n      setActive\n    });\n    setValueState(newValue);\n\n    if (!dragging && moveCount.current > INTENTIONAL_DRAG_COUNT_THRESHOLD) {\n      setDragging(true);\n    }\n\n    if (handleChange) {\n      handleChange(nativeEvent, newValue, activeIndex);\n    }\n  });\n  const handleTouchEnd = useEventCallback(nativeEvent => {\n    const finger = trackFinger(nativeEvent, touchId);\n    setDragging(false);\n\n    if (!finger) {\n      return;\n    }\n\n    const {\n      newValue\n    } = getFingerNewValue({\n      finger,\n      move: true,\n      values\n    });\n    setActive(-1);\n\n    if (nativeEvent.type === 'touchend') {\n      setOpen(-1);\n    }\n\n    if (onChangeCommitted) {\n      onChangeCommitted(nativeEvent, newValue);\n    }\n\n    touchId.current = undefined; // eslint-disable-next-line @typescript-eslint/no-use-before-define\n\n    stopListening();\n  });\n  const handleTouchStart = useEventCallback(nativeEvent => {\n    if (disabled) {\n      return;\n    } // If touch-action: none; is not supported we need to prevent the scroll manually.\n\n\n    if (!doesSupportTouchActionNone()) {\n      nativeEvent.preventDefault();\n    }\n\n    const touch = nativeEvent.changedTouches[0];\n\n    if (touch != null) {\n      // A number that uniquely identifies the current finger in the touch session.\n      touchId.current = touch.identifier;\n    }\n\n    const finger = trackFinger(nativeEvent, touchId);\n\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger,\n        values\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n\n      if (handleChange) {\n        handleChange(nativeEvent, newValue, activeIndex);\n      }\n    }\n\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('touchmove', handleTouchMove);\n    doc.addEventListener('touchend', handleTouchEnd);\n  });\n  const stopListening = React.useCallback(() => {\n    const doc = ownerDocument(sliderRef.current);\n    doc.removeEventListener('mousemove', handleTouchMove);\n    doc.removeEventListener('mouseup', handleTouchEnd);\n    doc.removeEventListener('touchmove', handleTouchMove);\n    doc.removeEventListener('touchend', handleTouchEnd);\n  }, [handleTouchEnd, handleTouchMove]);\n  React.useEffect(() => {\n    const {\n      current: slider\n    } = sliderRef;\n    slider.addEventListener('touchstart', handleTouchStart, {\n      passive: doesSupportTouchActionNone()\n    });\n    return () => {\n      // @ts-ignore\n      slider.removeEventListener('touchstart', handleTouchStart, {\n        passive: doesSupportTouchActionNone()\n      });\n      stopListening();\n    };\n  }, [stopListening, handleTouchStart]);\n  React.useEffect(() => {\n    if (disabled) {\n      stopListening();\n    }\n  }, [disabled, stopListening]);\n\n  const createHandleMouseDown = otherHandlers => event => {\n    var _otherHandlers$onMous;\n\n    (_otherHandlers$onMous = otherHandlers.onMouseDown) == null ? void 0 : _otherHandlers$onMous.call(otherHandlers, event);\n\n    if (disabled) {\n      return;\n    }\n\n    if (event.defaultPrevented) {\n      return;\n    } // Only handle left clicks\n\n\n    if (event.button !== 0) {\n      return;\n    } // Avoid text selection\n\n\n    event.preventDefault();\n    const finger = trackFinger(event, touchId);\n\n    if (finger !== false) {\n      const {\n        newValue,\n        activeIndex\n      } = getFingerNewValue({\n        finger,\n        values\n      });\n      focusThumb({\n        sliderRef,\n        activeIndex,\n        setActive\n      });\n      setValueState(newValue);\n\n      if (handleChange) {\n        handleChange(event, newValue, activeIndex);\n      }\n    }\n\n    moveCount.current = 0;\n    const doc = ownerDocument(sliderRef.current);\n    doc.addEventListener('mousemove', handleTouchMove);\n    doc.addEventListener('mouseup', handleTouchEnd);\n  };\n\n  const trackOffset = valueToPercent(range ? values[0] : min, min, max);\n  const trackLeap = valueToPercent(values[values.length - 1], min, max) - trackOffset;\n\n  const getRootProps = (otherHandlers = {}) => {\n    const ownEventHandlers = {\n      onMouseDown: createHandleMouseDown(otherHandlers || {})\n    };\n\n    const mergedEventHandlers = _extends({}, otherHandlers, ownEventHandlers);\n\n    return _extends({\n      ref: handleRef\n    }, mergedEventHandlers);\n  };\n\n  const createHandleMouseOver = otherHandlers => event => {\n    var _otherHandlers$onMous2;\n\n    (_otherHandlers$onMous2 = otherHandlers.onMouseOver) == null ? void 0 : _otherHandlers$onMous2.call(otherHandlers, event);\n    const index = Number(event.currentTarget.getAttribute('data-index'));\n    setOpen(index);\n  };\n\n  const createHandleMouseLeave = otherHandlers => event => {\n    var _otherHandlers$onMous3;\n\n    (_otherHandlers$onMous3 = otherHandlers.onMouseLeave) == null ? void 0 : _otherHandlers$onMous3.call(otherHandlers, event);\n    setOpen(-1);\n  };\n\n  const getThumbProps = (otherHandlers = {}) => {\n    const ownEventHandlers = {\n      onMouseOver: createHandleMouseOver(otherHandlers || {}),\n      onMouseLeave: createHandleMouseLeave(otherHandlers || {})\n    };\n    return _extends({}, otherHandlers, ownEventHandlers);\n  };\n\n  const getHiddenInputProps = (otherHandlers = {}) => {\n    var _parameters$step;\n\n    const ownEventHandlers = {\n      onChange: createHandleHiddenInputChange(otherHandlers || {}),\n      onFocus: createHandleHiddenInputFocus(otherHandlers || {}),\n      onBlur: createHandleHiddenInputBlur(otherHandlers || {})\n    };\n\n    const mergedEventHandlers = _extends({}, otherHandlers, ownEventHandlers);\n\n    return _extends({\n      tabIndex,\n      'aria-labelledby': ariaLabelledby,\n      'aria-orientation': orientation,\n      'aria-valuemax': scale(max),\n      'aria-valuemin': scale(min),\n      name,\n      type: 'range',\n      min: parameters.min,\n      max: parameters.max,\n      step: (_parameters$step = parameters.step) != null ? _parameters$step : undefined,\n      disabled\n    }, mergedEventHandlers, {\n      style: _extends({}, visuallyHidden, {\n        direction: isRtl ? 'rtl' : 'ltr',\n        // So that VoiceOver's focus indicator matches the thumb's dimensions\n        width: '100%',\n        height: '100%'\n      })\n    });\n  };\n\n  return {\n    active,\n    axis: axis,\n    axisProps,\n    dragging,\n    focusedThumbIndex,\n    getHiddenInputProps,\n    getRootProps,\n    getThumbProps,\n    marks: marks,\n    open,\n    range,\n    trackLeap,\n    trackOffset,\n    values\n  };\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,0BAA0B,IAAIC,iBAAiB,EAAEC,0BAA0B,IAAIC,iBAAiB,EAAEC,sBAAsB,IAAIC,aAAa,EAAEC,yBAAyB,IAAIC,gBAAgB,EAAEC,mBAAmB,IAAIC,UAAU,EAAEC,sBAAsB,IAAIC,aAAa,EAAEC,cAAc,QAAQ,YAAY;AACjT,MAAMC,gCAAgC,GAAG,CAAC;AAE1C,SAASC,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACjB,OAAOD,CAAC,GAAGC,CAAC;AACd;AAEA,SAASC,KAAKA,CAACC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC9B,IAAIF,KAAK,IAAI,IAAI,EAAE;IACjB,OAAOC,GAAG;EACZ;EAEA,OAAOE,IAAI,CAACF,GAAG,CAACE,IAAI,CAACD,GAAG,CAACD,GAAG,EAAED,KAAK,CAAC,EAAEE,GAAG,CAAC;AAC5C;AAEA,SAASE,WAAWA,CAACC,MAAM,EAAEC,YAAY,EAAE;EACzC,IAAIC,cAAc;EAElB,MAAM;IACJC,KAAK,EAAEC;EACT,CAAC,GAAG,CAACF,cAAc,GAAGF,MAAM,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEX,KAAK,EAAEQ,KAAK,KAAK;IACzD,MAAMI,QAAQ,GAAGT,IAAI,CAACU,GAAG,CAACP,YAAY,GAAGN,KAAK,CAAC;IAE/C,IAAIW,GAAG,KAAK,IAAI,IAAIC,QAAQ,GAAGD,GAAG,CAACC,QAAQ,IAAIA,QAAQ,KAAKD,GAAG,CAACC,QAAQ,EAAE;MACxE,OAAO;QACLA,QAAQ;QACRJ;MACF,CAAC;IACH;IAEA,OAAOG,GAAG;EACZ,CAAC,EAAE,IAAI,CAAC,KAAK,IAAI,GAAGJ,cAAc,GAAG,CAAC,CAAC;EACvC,OAAOE,YAAY;AACrB;AAEA,SAASK,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACnC;EACA,IAAIA,OAAO,CAACC,OAAO,KAAKC,SAAS,IAAIH,KAAK,CAACI,cAAc,EAAE;IACzD,MAAMC,UAAU,GAAGL,KAAK;IAExB,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,UAAU,CAACD,cAAc,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MAC5D,MAAME,KAAK,GAAGH,UAAU,CAACD,cAAc,CAACE,CAAC,CAAC;MAE1C,IAAIE,KAAK,CAACC,UAAU,KAAKR,OAAO,CAACC,OAAO,EAAE;QACxC,OAAO;UACLQ,CAAC,EAAEF,KAAK,CAACG,OAAO;UAChBC,CAAC,EAAEJ,KAAK,CAACK;QACX,CAAC;MACH;IACF;IAEA,OAAO,KAAK;EACd,CAAC,CAAC;;EAGF,OAAO;IACLH,CAAC,EAAEV,KAAK,CAACW,OAAO;IAChBC,CAAC,EAAEZ,KAAK,CAACa;EACX,CAAC;AACH;AAEA,OAAO,SAASC,cAAcA,CAAC7B,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC9C,OAAO,CAACF,KAAK,GAAGC,GAAG,IAAI,GAAG,IAAIC,GAAG,GAAGD,GAAG,CAAC;AAC1C;AAEA,SAAS6B,cAAcA,CAACC,OAAO,EAAE9B,GAAG,EAAEC,GAAG,EAAE;EACzC,OAAO,CAACA,GAAG,GAAGD,GAAG,IAAI8B,OAAO,GAAG9B,GAAG;AACpC;AAEA,SAAS+B,mBAAmBA,CAACC,GAAG,EAAE;EAChC;EACA;EACA,IAAI9B,IAAI,CAACU,GAAG,CAACoB,GAAG,CAAC,GAAG,CAAC,EAAE;IACrB,MAAMC,KAAK,GAAGD,GAAG,CAACE,aAAa,CAAC,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC;IAC7C,MAAMC,kBAAkB,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACjD,OAAO,CAACC,kBAAkB,GAAGA,kBAAkB,CAACf,MAAM,GAAG,CAAC,IAAIgB,QAAQ,CAACJ,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;EACtF;EAEA,MAAMK,WAAW,GAAGN,GAAG,CAACO,QAAQ,CAAC,CAAC,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAChD,OAAOG,WAAW,GAAGA,WAAW,CAACjB,MAAM,GAAG,CAAC;AAC7C;AAEA,SAASmB,gBAAgBA,CAACzC,KAAK,EAAE0C,IAAI,EAAEzC,GAAG,EAAE;EAC1C,MAAM0C,OAAO,GAAGxC,IAAI,CAACyC,KAAK,CAAC,CAAC5C,KAAK,GAAGC,GAAG,IAAIyC,IAAI,CAAC,GAAGA,IAAI,GAAGzC,GAAG;EAC7D,OAAO4C,MAAM,CAACF,OAAO,CAACG,OAAO,CAACd,mBAAmB,CAACU,IAAI,CAAC,CAAC,CAAC;AAC3D;AAEA,SAASK,aAAaA,CAAC;EACrB1C,MAAM;EACN2C,QAAQ;EACRxC;AACF,CAAC,EAAE;EACD,MAAMyC,MAAM,GAAG5C,MAAM,CAAC6C,KAAK,CAAC,CAAC;EAC7BD,MAAM,CAACzC,KAAK,CAAC,GAAGwC,QAAQ;EACxB,OAAOC,MAAM,CAACE,IAAI,CAACvD,GAAG,CAAC;AACzB;AAEA,SAASwD,UAAUA,CAAC;EAClBC,SAAS;EACTC,WAAW;EACXC;AACF,CAAC,EAAE;EACD,IAAIC,kBAAkB,EAAEC,kBAAkB;EAE1C,MAAMC,GAAG,GAAGvE,aAAa,CAACkE,SAAS,CAACpC,OAAO,CAAC;EAE5C,IAAI,EAAE,CAACuC,kBAAkB,GAAGH,SAAS,CAACpC,OAAO,KAAK,IAAI,IAAIuC,kBAAkB,CAACG,QAAQ,CAACD,GAAG,CAACE,aAAa,CAAC,CAAC,IAAIf,MAAM,CAACa,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAACD,kBAAkB,GAAGC,GAAG,CAACE,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,kBAAkB,CAACI,YAAY,CAAC,YAAY,CAAC,CAAC,KAAKP,WAAW,EAAE;IACrQ,IAAIQ,mBAAmB;IAEvB,CAACA,mBAAmB,GAAGT,SAAS,CAACpC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG6C,mBAAmB,CAACC,aAAa,CAAE,8BAA6BT,WAAY,IAAG,CAAC,CAACU,KAAK,CAAC,CAAC;EACvJ;EAEA,IAAIT,SAAS,EAAE;IACbA,SAAS,CAACD,WAAW,CAAC;EACxB;AACF;AAEA,MAAMW,SAAS,GAAG;EAChBC,UAAU,EAAE;IACVC,MAAM,EAAEpC,OAAO,KAAK;MAClBqC,IAAI,EAAG,GAAErC,OAAQ;IACnB,CAAC,CAAC;IACFsC,IAAI,EAAEtC,OAAO,KAAK;MAChBuC,KAAK,EAAG,GAAEvC,OAAQ;IACpB,CAAC;EACH,CAAC;EACD,oBAAoB,EAAE;IACpBoC,MAAM,EAAEpC,OAAO,KAAK;MAClBwC,KAAK,EAAG,GAAExC,OAAQ;IACpB,CAAC,CAAC;IACFsC,IAAI,EAAEtC,OAAO,KAAK;MAChBuC,KAAK,EAAG,GAAEvC,OAAQ;IACpB,CAAC;EACH,CAAC;EACDyC,QAAQ,EAAE;IACRL,MAAM,EAAEpC,OAAO,KAAK;MAClB0C,MAAM,EAAG,GAAE1C,OAAQ;IACrB,CAAC,CAAC;IACFsC,IAAI,EAAEtC,OAAO,KAAK;MAChB2C,MAAM,EAAG,GAAE3C,OAAQ;IACrB,CAAC;EACH;AACF,CAAC;AACD,OAAO,MAAM4C,QAAQ,GAAGlD,CAAC,IAAIA,CAAC,CAAC,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAImD,6BAA6B;AAEjC,SAASC,0BAA0BA,CAAA,EAAG;EACpC,IAAID,6BAA6B,KAAK1D,SAAS,EAAE;IAC/C,IAAI,OAAO4D,GAAG,KAAK,WAAW,IAAI,OAAOA,GAAG,CAACC,QAAQ,KAAK,UAAU,EAAE;MACpEH,6BAA6B,GAAGE,GAAG,CAACC,QAAQ,CAAC,cAAc,EAAE,MAAM,CAAC;IACtE,CAAC,MAAM;MACLH,6BAA6B,GAAG,IAAI;IACtC;EACF;EAEA,OAAOA,6BAA6B;AACtC;AAEA,eAAe,SAASI,SAASA,CAACC,UAAU,EAAE;EAC5C,MAAM;IACJ,iBAAiB,EAAEC,cAAc;IACjCC,YAAY;IACZC,QAAQ,GAAG,KAAK;IAChBC,WAAW,GAAG,KAAK;IACnBC,KAAK,GAAG,KAAK;IACbC,KAAK,EAAEC,SAAS,GAAG,KAAK;IACxBtF,GAAG,GAAG,GAAG;IACTD,GAAG,GAAG,CAAC;IACPwF,IAAI;IACJC,QAAQ;IACRC,iBAAiB;IACjBC,WAAW,GAAG,YAAY;IAC1BC,GAAG;IACHC,KAAK,GAAGnB,QAAQ;IAChBjC,IAAI,GAAG,CAAC;IACRqD,QAAQ;IACR/F,KAAK,EAAEgG;EACT,CAAC,GAAGf,UAAU;EACd,MAAMjE,OAAO,GAAGnC,KAAK,CAACoH,MAAM,CAAC,CAAC,CAAC,CAAC;EAChC;EACA;;EAEA,MAAM,CAACC,MAAM,EAAE3C,SAAS,CAAC,GAAG1E,KAAK,CAACsH,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGxH,KAAK,CAACsH,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACG,QAAQ,EAAEC,WAAW,CAAC,GAAG1H,KAAK,CAACsH,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAMK,SAAS,GAAG3H,KAAK,CAACoH,MAAM,CAAC,CAAC,CAAC;EACjC,MAAM,CAACQ,YAAY,EAAEC,aAAa,CAAC,GAAGjH,aAAa,CAAC;IAClDkH,UAAU,EAAEX,SAAS;IACrBY,OAAO,EAAEzB,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAGlF,GAAG;IAClDwF,IAAI,EAAE;EACR,CAAC,CAAC;EAEF,MAAMoB,YAAY,GAAGnB,QAAQ,KAAK,CAAC3E,KAAK,EAAEf,KAAK,EAAE8G,UAAU,KAAK;IAC9D;IACA;IACA;IACA;IACA,MAAMC,WAAW,GAAGhG,KAAK,CAACgG,WAAW,IAAIhG,KAAK,CAAC,CAAC;;IAEhD,MAAMiG,WAAW,GAAG,IAAID,WAAW,CAACE,WAAW,CAACF,WAAW,CAACG,IAAI,EAAEH,WAAW,CAAC;IAC9EI,MAAM,CAACC,cAAc,CAACJ,WAAW,EAAE,QAAQ,EAAE;MAC3CK,QAAQ,EAAE,IAAI;MACdrH,KAAK,EAAE;QACLA,KAAK;QACLyF;MACF;IACF,CAAC,CAAC;IACFC,QAAQ,CAACsB,WAAW,EAAEhH,KAAK,EAAE8G,UAAU,CAAC;EAC1C,CAAC,CAAC;EAEF,MAAMQ,KAAK,GAAGC,KAAK,CAACC,OAAO,CAACf,YAAY,CAAC;EACzC,IAAIpG,MAAM,GAAGiH,KAAK,GAAGb,YAAY,CAACvD,KAAK,CAAC,CAAC,CAACC,IAAI,CAACvD,GAAG,CAAC,GAAG,CAAC6G,YAAY,CAAC;EACpEpG,MAAM,GAAGA,MAAM,CAACoH,GAAG,CAACzH,KAAK,IAAID,KAAK,CAACC,KAAK,EAAEC,GAAG,EAAEC,GAAG,CAAC,CAAC;EACpD,MAAMqF,KAAK,GAAGC,SAAS,KAAK,IAAI,IAAI9C,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG6E,KAAK,CAACpH,IAAI,CAACuH,KAAK,CAAC,CAACxH,GAAG,GAAGD,GAAG,IAAIyC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC+E,GAAG,CAAC,CAACE,CAAC,EAAEnH,KAAK,MAAM;IACpHR,KAAK,EAAEC,GAAG,GAAGyC,IAAI,GAAGlC;EACtB,CAAC,CAAC,CAAC,GAAGgF,SAAS,IAAI,EAAE;EACrB,MAAMoC,WAAW,GAAGrC,KAAK,CAACkC,GAAG,CAACI,IAAI,IAAIA,IAAI,CAAC7H,KAAK,CAAC;EACjD,MAAM;IACJ8H,iBAAiB;IACjBC,MAAM,EAAEC,iBAAiB;IACzBC,OAAO,EAAEC,kBAAkB;IAC3BrC,GAAG,EAAEsC;EACP,CAAC,GAAGpJ,iBAAiB,CAAC,CAAC;EACvB,MAAM,CAACqJ,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxJ,KAAK,CAACsH,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpE,MAAM9C,SAAS,GAAGxE,KAAK,CAACoH,MAAM,CAAC,CAAC;EAChC,MAAMqC,cAAc,GAAG/I,UAAU,CAAC4I,eAAe,EAAE9E,SAAS,CAAC;EAC7D,MAAMkF,SAAS,GAAGhJ,UAAU,CAACsG,GAAG,EAAEyC,cAAc,CAAC;EAEjD,MAAME,4BAA4B,GAAGC,aAAa,IAAI1H,KAAK,IAAI;IAC7D,IAAI2H,qBAAqB;IAEzB,MAAMlI,KAAK,GAAGqC,MAAM,CAAC9B,KAAK,CAAC4H,aAAa,CAAC9E,YAAY,CAAC,YAAY,CAAC,CAAC;IACpEqE,kBAAkB,CAACnH,KAAK,CAAC;IAEzB,IAAI+G,iBAAiB,CAAC7G,OAAO,KAAK,IAAI,EAAE;MACtCoH,oBAAoB,CAAC7H,KAAK,CAAC;IAC7B;IAEA6F,OAAO,CAAC7F,KAAK,CAAC;IACdiI,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,aAAa,CAACR,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGS,qBAAqB,CAACE,IAAI,CAACH,aAAa,EAAE1H,KAAK,CAAC;EACtJ,CAAC;EAED,MAAM8H,2BAA2B,GAAGJ,aAAa,IAAI1H,KAAK,IAAI;IAC5D,IAAI+H,qBAAqB;IAEzBd,iBAAiB,CAACjH,KAAK,CAAC;IAExB,IAAI+G,iBAAiB,CAAC7G,OAAO,KAAK,KAAK,EAAE;MACvCoH,oBAAoB,CAAC,CAAC,CAAC,CAAC;IAC1B;IAEAhC,OAAO,CAAC,CAAC,CAAC,CAAC;IACXoC,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAACK,qBAAqB,GAAGL,aAAa,CAACV,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGe,qBAAqB,CAACF,IAAI,CAACH,aAAa,EAAE1H,KAAK,CAAC;EACrJ,CAAC;EAED9B,iBAAiB,CAAC,MAAM;IACtB,IAAImG,QAAQ,IAAI/B,SAAS,CAACpC,OAAO,CAAC0C,QAAQ,CAACoF,QAAQ,CAACnF,aAAa,CAAC,EAAE;MAClE,IAAIoF,qBAAqB;;MAEzB;MACA;MACA;MACA;MACA,CAACA,qBAAqB,GAAGD,QAAQ,CAACnF,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoF,qBAAqB,CAACC,IAAI,CAAC,CAAC;IAClG;EACF,CAAC,EAAE,CAAC7D,QAAQ,CAAC,CAAC;EAEd,IAAIA,QAAQ,IAAIc,MAAM,KAAK,CAAC,CAAC,EAAE;IAC7B3C,SAAS,CAAC,CAAC,CAAC,CAAC;EACf;EAEA,IAAI6B,QAAQ,IAAIgD,iBAAiB,KAAK,CAAC,CAAC,EAAE;IACxCC,oBAAoB,CAAC,CAAC,CAAC,CAAC;EAC1B;EAEA,MAAMa,6BAA6B,GAAGT,aAAa,IAAI1H,KAAK,IAAI;IAC9D,IAAIoI,qBAAqB;IAEzB,CAACA,qBAAqB,GAAGV,aAAa,CAAC/C,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGyD,qBAAqB,CAACP,IAAI,CAACH,aAAa,EAAE1H,KAAK,CAAC;IACpH,MAAMP,KAAK,GAAGqC,MAAM,CAAC9B,KAAK,CAAC4H,aAAa,CAAC9E,YAAY,CAAC,YAAY,CAAC,CAAC;IACpE,MAAM7D,KAAK,GAAGK,MAAM,CAACG,KAAK,CAAC;IAC3B,MAAM4I,UAAU,GAAGxB,WAAW,CAACyB,OAAO,CAACrJ,KAAK,CAAC,CAAC,CAAC;;IAE/C,IAAIgD,QAAQ,GAAGjC,KAAK,CAACuI,MAAM,CAACC,aAAa;IAEzC,IAAIhE,KAAK,IAAI7C,IAAI,IAAI,IAAI,EAAE;MACzBM,QAAQ,GAAGA,QAAQ,GAAGhD,KAAK,GAAG4H,WAAW,CAACwB,UAAU,GAAG,CAAC,CAAC,GAAGxB,WAAW,CAACwB,UAAU,GAAG,CAAC,CAAC;IACzF;IAEApG,QAAQ,GAAGjD,KAAK,CAACiD,QAAQ,EAAE/C,GAAG,EAAEC,GAAG,CAAC;IAEpC,IAAIqF,KAAK,IAAI7C,IAAI,IAAI,IAAI,EAAE;MACzB,MAAM8G,gBAAgB,GAAG5B,WAAW,CAACyB,OAAO,CAAChJ,MAAM,CAACG,KAAK,CAAC,CAAC;MAC3DwC,QAAQ,GAAGA,QAAQ,GAAG3C,MAAM,CAACG,KAAK,CAAC,GAAGoH,WAAW,CAAC4B,gBAAgB,GAAG,CAAC,CAAC,GAAG5B,WAAW,CAAC4B,gBAAgB,GAAG,CAAC,CAAC;IAC7G;IAEA,IAAIlC,KAAK,EAAE;MACT;MACA,IAAIjC,WAAW,EAAE;QACfrC,QAAQ,GAAGjD,KAAK,CAACiD,QAAQ,EAAE3C,MAAM,CAACG,KAAK,GAAG,CAAC,CAAC,IAAI,CAACiJ,QAAQ,EAAEpJ,MAAM,CAACG,KAAK,GAAG,CAAC,CAAC,IAAIiJ,QAAQ,CAAC;MAC3F;MAEA,MAAMC,aAAa,GAAG1G,QAAQ;MAC9BA,QAAQ,GAAGD,aAAa,CAAC;QACvB1C,MAAM;QACN2C,QAAQ;QACRxC;MACF,CAAC,CAAC;MACF,IAAI8C,WAAW,GAAG9C,KAAK,CAAC,CAAC;;MAEzB,IAAI,CAAC6E,WAAW,EAAE;QAChB/B,WAAW,GAAGN,QAAQ,CAACqG,OAAO,CAACK,aAAa,CAAC;MAC/C;MAEAtG,UAAU,CAAC;QACTC,SAAS;QACTC;MACF,CAAC,CAAC;IACJ;IAEAoD,aAAa,CAAC1D,QAAQ,CAAC;IACvBqF,oBAAoB,CAAC7H,KAAK,CAAC;IAE3B,IAAIqG,YAAY,EAAE;MAChBA,YAAY,CAAC9F,KAAK,EAAEiC,QAAQ,EAAExC,KAAK,CAAC;IACtC;IAEA,IAAImF,iBAAiB,EAAE;MACrBA,iBAAiB,CAAC5E,KAAK,EAAEiC,QAAQ,CAAC;IACpC;EACF,CAAC;EAED,MAAM2G,aAAa,GAAG9K,KAAK,CAACoH,MAAM,CAAC,CAAC;EACpC,IAAI2D,IAAI,GAAGhE,WAAW;EAEtB,IAAIN,KAAK,IAAIM,WAAW,KAAK,YAAY,EAAE;IACzCgE,IAAI,IAAI,UAAU;EACpB;EAEA,MAAMC,iBAAiB,GAAGA,CAAC;IACzBC,MAAM;IACNC,IAAI,GAAG,KAAK;IACZ1J,MAAM,EAAE2J;EACV,CAAC,KAAK;IACJ,MAAM;MACJ/I,OAAO,EAAEgJ;IACX,CAAC,GAAG5G,SAAS;IACb,MAAM;MACJiB,KAAK;MACLI,MAAM;MACND,MAAM;MACNL;IACF,CAAC,GAAG6F,MAAM,CAACC,qBAAqB,CAAC,CAAC;IAClC,IAAInI,OAAO;IAEX,IAAI6H,IAAI,CAACP,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;MAClCtH,OAAO,GAAG,CAAC0C,MAAM,GAAGqF,MAAM,CAACnI,CAAC,IAAI+C,MAAM;IACxC,CAAC,MAAM;MACL3C,OAAO,GAAG,CAAC+H,MAAM,CAACrI,CAAC,GAAG2C,IAAI,IAAIE,KAAK;IACrC;IAEA,IAAIsF,IAAI,CAACP,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE;MACnCtH,OAAO,GAAG,CAAC,GAAGA,OAAO;IACvB;IAEA,IAAIiB,QAAQ;IACZA,QAAQ,GAAGlB,cAAc,CAACC,OAAO,EAAE9B,GAAG,EAAEC,GAAG,CAAC;IAE5C,IAAIwC,IAAI,EAAE;MACRM,QAAQ,GAAGP,gBAAgB,CAACO,QAAQ,EAAEN,IAAI,EAAEzC,GAAG,CAAC;IAClD,CAAC,MAAM;MACL,MAAMQ,YAAY,GAAGL,WAAW,CAACwH,WAAW,EAAE5E,QAAQ,CAAC;MACvDA,QAAQ,GAAG4E,WAAW,CAACnH,YAAY,CAAC;IACtC;IAEAuC,QAAQ,GAAGjD,KAAK,CAACiD,QAAQ,EAAE/C,GAAG,EAAEC,GAAG,CAAC;IACpC,IAAIoD,WAAW,GAAG,CAAC;IAEnB,IAAIgE,KAAK,EAAE;MACT,IAAI,CAACyC,IAAI,EAAE;QACTzG,WAAW,GAAGlD,WAAW,CAAC4J,OAAO,EAAEhH,QAAQ,CAAC;MAC9C,CAAC,MAAM;QACLM,WAAW,GAAGqG,aAAa,CAAC1I,OAAO;MACrC,CAAC,CAAC;;MAGF,IAAIoE,WAAW,EAAE;QACfrC,QAAQ,GAAGjD,KAAK,CAACiD,QAAQ,EAAEgH,OAAO,CAAC1G,WAAW,GAAG,CAAC,CAAC,IAAI,CAACmG,QAAQ,EAAEO,OAAO,CAAC1G,WAAW,GAAG,CAAC,CAAC,IAAImG,QAAQ,CAAC;MACzG;MAEA,MAAMC,aAAa,GAAG1G,QAAQ;MAC9BA,QAAQ,GAAGD,aAAa,CAAC;QACvB1C,MAAM,EAAE2J,OAAO;QACfhH,QAAQ;QACRxC,KAAK,EAAE8C;MACT,CAAC,CAAC,CAAC,CAAC;;MAEJ,IAAI,EAAE+B,WAAW,IAAI0E,IAAI,CAAC,EAAE;QAC1BzG,WAAW,GAAGN,QAAQ,CAACqG,OAAO,CAACK,aAAa,CAAC;QAC7CC,aAAa,CAAC1I,OAAO,GAAGqC,WAAW;MACrC;IACF;IAEA,OAAO;MACLN,QAAQ;MACRM;IACF,CAAC;EACH,CAAC;EAED,MAAM6G,eAAe,GAAG9K,gBAAgB,CAAC0H,WAAW,IAAI;IACtD,MAAM+C,MAAM,GAAGhJ,WAAW,CAACiG,WAAW,EAAE/F,OAAO,CAAC;IAEhD,IAAI,CAAC8I,MAAM,EAAE;MACX;IACF;IAEAtD,SAAS,CAACvF,OAAO,IAAI,CAAC,CAAC,CAAC;IACxB;;IAEA,IAAI8F,WAAW,CAACG,IAAI,KAAK,WAAW,IAAIH,WAAW,CAACqD,OAAO,KAAK,CAAC,EAAE;MACjE;MACAC,cAAc,CAACtD,WAAW,CAAC;MAC3B;IACF;IAEA,MAAM;MACJ/D,QAAQ;MACRM;IACF,CAAC,GAAGuG,iBAAiB,CAAC;MACpBC,MAAM;MACNC,IAAI,EAAE,IAAI;MACV1J;IACF,CAAC,CAAC;IACF+C,UAAU,CAAC;MACTC,SAAS;MACTC,WAAW;MACXC;IACF,CAAC,CAAC;IACFmD,aAAa,CAAC1D,QAAQ,CAAC;IAEvB,IAAI,CAACsD,QAAQ,IAAIE,SAAS,CAACvF,OAAO,GAAGtB,gCAAgC,EAAE;MACrE4G,WAAW,CAAC,IAAI,CAAC;IACnB;IAEA,IAAIM,YAAY,EAAE;MAChBA,YAAY,CAACE,WAAW,EAAE/D,QAAQ,EAAEM,WAAW,CAAC;IAClD;EACF,CAAC,CAAC;EACF,MAAM+G,cAAc,GAAGhL,gBAAgB,CAAC0H,WAAW,IAAI;IACrD,MAAM+C,MAAM,GAAGhJ,WAAW,CAACiG,WAAW,EAAE/F,OAAO,CAAC;IAChDuF,WAAW,CAAC,KAAK,CAAC;IAElB,IAAI,CAACuD,MAAM,EAAE;MACX;IACF;IAEA,MAAM;MACJ9G;IACF,CAAC,GAAG6G,iBAAiB,CAAC;MACpBC,MAAM;MACNC,IAAI,EAAE,IAAI;MACV1J;IACF,CAAC,CAAC;IACFkD,SAAS,CAAC,CAAC,CAAC,CAAC;IAEb,IAAIwD,WAAW,CAACG,IAAI,KAAK,UAAU,EAAE;MACnCb,OAAO,CAAC,CAAC,CAAC,CAAC;IACb;IAEA,IAAIV,iBAAiB,EAAE;MACrBA,iBAAiB,CAACoB,WAAW,EAAE/D,QAAQ,CAAC;IAC1C;IAEAhC,OAAO,CAACC,OAAO,GAAGC,SAAS,CAAC,CAAC;;IAE7BoJ,aAAa,CAAC,CAAC;EACjB,CAAC,CAAC;EACF,MAAMC,gBAAgB,GAAGlL,gBAAgB,CAAC0H,WAAW,IAAI;IACvD,IAAI3B,QAAQ,EAAE;MACZ;IACF,CAAC,CAAC;;IAGF,IAAI,CAACP,0BAA0B,CAAC,CAAC,EAAE;MACjCkC,WAAW,CAACyD,cAAc,CAAC,CAAC;IAC9B;IAEA,MAAMjJ,KAAK,GAAGwF,WAAW,CAAC5F,cAAc,CAAC,CAAC,CAAC;IAE3C,IAAII,KAAK,IAAI,IAAI,EAAE;MACjB;MACAP,OAAO,CAACC,OAAO,GAAGM,KAAK,CAACC,UAAU;IACpC;IAEA,MAAMsI,MAAM,GAAGhJ,WAAW,CAACiG,WAAW,EAAE/F,OAAO,CAAC;IAEhD,IAAI8I,MAAM,KAAK,KAAK,EAAE;MACpB,MAAM;QACJ9G,QAAQ;QACRM;MACF,CAAC,GAAGuG,iBAAiB,CAAC;QACpBC,MAAM;QACNzJ;MACF,CAAC,CAAC;MACF+C,UAAU,CAAC;QACTC,SAAS;QACTC,WAAW;QACXC;MACF,CAAC,CAAC;MACFmD,aAAa,CAAC1D,QAAQ,CAAC;MAEvB,IAAI6D,YAAY,EAAE;QAChBA,YAAY,CAACE,WAAW,EAAE/D,QAAQ,EAAEM,WAAW,CAAC;MAClD;IACF;IAEAkD,SAAS,CAACvF,OAAO,GAAG,CAAC;IACrB,MAAMyC,GAAG,GAAGvE,aAAa,CAACkE,SAAS,CAACpC,OAAO,CAAC;IAC5CyC,GAAG,CAAC+G,gBAAgB,CAAC,WAAW,EAAEN,eAAe,CAAC;IAClDzG,GAAG,CAAC+G,gBAAgB,CAAC,UAAU,EAAEJ,cAAc,CAAC;EAClD,CAAC,CAAC;EACF,MAAMC,aAAa,GAAGzL,KAAK,CAAC6L,WAAW,CAAC,MAAM;IAC5C,MAAMhH,GAAG,GAAGvE,aAAa,CAACkE,SAAS,CAACpC,OAAO,CAAC;IAC5CyC,GAAG,CAACiH,mBAAmB,CAAC,WAAW,EAAER,eAAe,CAAC;IACrDzG,GAAG,CAACiH,mBAAmB,CAAC,SAAS,EAAEN,cAAc,CAAC;IAClD3G,GAAG,CAACiH,mBAAmB,CAAC,WAAW,EAAER,eAAe,CAAC;IACrDzG,GAAG,CAACiH,mBAAmB,CAAC,UAAU,EAAEN,cAAc,CAAC;EACrD,CAAC,EAAE,CAACA,cAAc,EAAEF,eAAe,CAAC,CAAC;EACrCtL,KAAK,CAAC+L,SAAS,CAAC,MAAM;IACpB,MAAM;MACJ3J,OAAO,EAAEgJ;IACX,CAAC,GAAG5G,SAAS;IACb4G,MAAM,CAACQ,gBAAgB,CAAC,YAAY,EAAEF,gBAAgB,EAAE;MACtDM,OAAO,EAAEhG,0BAA0B,CAAC;IACtC,CAAC,CAAC;IACF,OAAO,MAAM;MACX;MACAoF,MAAM,CAACU,mBAAmB,CAAC,YAAY,EAAEJ,gBAAgB,EAAE;QACzDM,OAAO,EAAEhG,0BAA0B,CAAC;MACtC,CAAC,CAAC;MACFyF,aAAa,CAAC,CAAC;IACjB,CAAC;EACH,CAAC,EAAE,CAACA,aAAa,EAAEC,gBAAgB,CAAC,CAAC;EACrC1L,KAAK,CAAC+L,SAAS,CAAC,MAAM;IACpB,IAAIxF,QAAQ,EAAE;MACZkF,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAAClF,QAAQ,EAAEkF,aAAa,CAAC,CAAC;EAE7B,MAAMQ,qBAAqB,GAAGrC,aAAa,IAAI1H,KAAK,IAAI;IACtD,IAAIgK,qBAAqB;IAEzB,CAACA,qBAAqB,GAAGtC,aAAa,CAACuC,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,qBAAqB,CAACnC,IAAI,CAACH,aAAa,EAAE1H,KAAK,CAAC;IAEvH,IAAIqE,QAAQ,EAAE;MACZ;IACF;IAEA,IAAIrE,KAAK,CAACkK,gBAAgB,EAAE;MAC1B;IACF,CAAC,CAAC;;IAGF,IAAIlK,KAAK,CAACmK,MAAM,KAAK,CAAC,EAAE;MACtB;IACF,CAAC,CAAC;;IAGFnK,KAAK,CAACyJ,cAAc,CAAC,CAAC;IACtB,MAAMV,MAAM,GAAGhJ,WAAW,CAACC,KAAK,EAAEC,OAAO,CAAC;IAE1C,IAAI8I,MAAM,KAAK,KAAK,EAAE;MACpB,MAAM;QACJ9G,QAAQ;QACRM;MACF,CAAC,GAAGuG,iBAAiB,CAAC;QACpBC,MAAM;QACNzJ;MACF,CAAC,CAAC;MACF+C,UAAU,CAAC;QACTC,SAAS;QACTC,WAAW;QACXC;MACF,CAAC,CAAC;MACFmD,aAAa,CAAC1D,QAAQ,CAAC;MAEvB,IAAI6D,YAAY,EAAE;QAChBA,YAAY,CAAC9F,KAAK,EAAEiC,QAAQ,EAAEM,WAAW,CAAC;MAC5C;IACF;IAEAkD,SAAS,CAACvF,OAAO,GAAG,CAAC;IACrB,MAAMyC,GAAG,GAAGvE,aAAa,CAACkE,SAAS,CAACpC,OAAO,CAAC;IAC5CyC,GAAG,CAAC+G,gBAAgB,CAAC,WAAW,EAAEN,eAAe,CAAC;IAClDzG,GAAG,CAAC+G,gBAAgB,CAAC,SAAS,EAAEJ,cAAc,CAAC;EACjD,CAAC;EAED,MAAMc,WAAW,GAAGtJ,cAAc,CAACyF,KAAK,GAAGjH,MAAM,CAAC,CAAC,CAAC,GAAGJ,GAAG,EAAEA,GAAG,EAAEC,GAAG,CAAC;EACrE,MAAMkL,SAAS,GAAGvJ,cAAc,CAACxB,MAAM,CAACA,MAAM,CAACiB,MAAM,GAAG,CAAC,CAAC,EAAErB,GAAG,EAAEC,GAAG,CAAC,GAAGiL,WAAW;EAEnF,MAAME,YAAY,GAAGA,CAAC5C,aAAa,GAAG,CAAC,CAAC,KAAK;IAC3C,MAAM6C,gBAAgB,GAAG;MACvBN,WAAW,EAAEF,qBAAqB,CAACrC,aAAa,IAAI,CAAC,CAAC;IACxD,CAAC;IAED,MAAM8C,mBAAmB,GAAG3M,QAAQ,CAAC,CAAC,CAAC,EAAE6J,aAAa,EAAE6C,gBAAgB,CAAC;IAEzE,OAAO1M,QAAQ,CAAC;MACdiH,GAAG,EAAE0C;IACP,CAAC,EAAEgD,mBAAmB,CAAC;EACzB,CAAC;EAED,MAAMC,qBAAqB,GAAG/C,aAAa,IAAI1H,KAAK,IAAI;IACtD,IAAI0K,sBAAsB;IAE1B,CAACA,sBAAsB,GAAGhD,aAAa,CAACiD,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,sBAAsB,CAAC7C,IAAI,CAACH,aAAa,EAAE1H,KAAK,CAAC;IACzH,MAAMP,KAAK,GAAGqC,MAAM,CAAC9B,KAAK,CAAC4H,aAAa,CAAC9E,YAAY,CAAC,YAAY,CAAC,CAAC;IACpEwC,OAAO,CAAC7F,KAAK,CAAC;EAChB,CAAC;EAED,MAAMmL,sBAAsB,GAAGlD,aAAa,IAAI1H,KAAK,IAAI;IACvD,IAAI6K,sBAAsB;IAE1B,CAACA,sBAAsB,GAAGnD,aAAa,CAACoD,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,sBAAsB,CAAChD,IAAI,CAACH,aAAa,EAAE1H,KAAK,CAAC;IAC1HsF,OAAO,CAAC,CAAC,CAAC,CAAC;EACb,CAAC;EAED,MAAMyF,aAAa,GAAGA,CAACrD,aAAa,GAAG,CAAC,CAAC,KAAK;IAC5C,MAAM6C,gBAAgB,GAAG;MACvBI,WAAW,EAAEF,qBAAqB,CAAC/C,aAAa,IAAI,CAAC,CAAC,CAAC;MACvDoD,YAAY,EAAEF,sBAAsB,CAAClD,aAAa,IAAI,CAAC,CAAC;IAC1D,CAAC;IACD,OAAO7J,QAAQ,CAAC,CAAC,CAAC,EAAE6J,aAAa,EAAE6C,gBAAgB,CAAC;EACtD,CAAC;EAED,MAAMS,mBAAmB,GAAGA,CAACtD,aAAa,GAAG,CAAC,CAAC,KAAK;IAClD,IAAIuD,gBAAgB;IAEpB,MAAMV,gBAAgB,GAAG;MACvB5F,QAAQ,EAAEwD,6BAA6B,CAACT,aAAa,IAAI,CAAC,CAAC,CAAC;MAC5DR,OAAO,EAAEO,4BAA4B,CAACC,aAAa,IAAI,CAAC,CAAC,CAAC;MAC1DV,MAAM,EAAEc,2BAA2B,CAACJ,aAAa,IAAI,CAAC,CAAC;IACzD,CAAC;IAED,MAAM8C,mBAAmB,GAAG3M,QAAQ,CAAC,CAAC,CAAC,EAAE6J,aAAa,EAAE6C,gBAAgB,CAAC;IAEzE,OAAO1M,QAAQ,CAAC;MACdmH,QAAQ;MACR,iBAAiB,EAAEb,cAAc;MACjC,kBAAkB,EAAEU,WAAW;MAC/B,eAAe,EAAEE,KAAK,CAAC5F,GAAG,CAAC;MAC3B,eAAe,EAAE4F,KAAK,CAAC7F,GAAG,CAAC;MAC3BwF,IAAI;MACJyB,IAAI,EAAE,OAAO;MACbjH,GAAG,EAAEgF,UAAU,CAAChF,GAAG;MACnBC,GAAG,EAAE+E,UAAU,CAAC/E,GAAG;MACnBwC,IAAI,EAAE,CAACsJ,gBAAgB,GAAG/G,UAAU,CAACvC,IAAI,KAAK,IAAI,GAAGsJ,gBAAgB,GAAG9K,SAAS;MACjFkE;IACF,CAAC,EAAEmG,mBAAmB,EAAE;MACtBU,KAAK,EAAErN,QAAQ,CAAC,CAAC,CAAC,EAAEc,cAAc,EAAE;QAClCwM,SAAS,EAAE5G,KAAK,GAAG,KAAK,GAAG,KAAK;QAChC;QACAhB,KAAK,EAAE,MAAM;QACbI,MAAM,EAAE;MACV,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EAED,OAAO;IACLwB,MAAM;IACN0D,IAAI,EAAEA,IAAI;IACV3F,SAAS;IACTqC,QAAQ;IACR8B,iBAAiB;IACjB2D,mBAAmB;IACnBV,YAAY;IACZS,aAAa;IACbvG,KAAK,EAAEA,KAAK;IACZa,IAAI;IACJkB,KAAK;IACL8D,SAAS;IACTD,WAAW;IACX9K;EACF,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}