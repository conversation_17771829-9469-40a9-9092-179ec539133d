{"ast": null, "code": "import { __extends } from \"tslib\";\nimport { AsyncAction } from './AsyncAction';\nvar QueueAction = function (_super) {\n  __extends(QueueAction, _super);\n  function QueueAction(scheduler, work) {\n    var _this = _super.call(this, scheduler, work) || this;\n    _this.scheduler = scheduler;\n    _this.work = work;\n    return _this;\n  }\n  QueueAction.prototype.schedule = function (state, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (delay > 0) {\n      return _super.prototype.schedule.call(this, state, delay);\n    }\n    this.delay = delay;\n    this.state = state;\n    this.scheduler.flush(this);\n    return this;\n  };\n  QueueAction.prototype.execute = function (state, delay) {\n    return delay > 0 || this.closed ? _super.prototype.execute.call(this, state, delay) : this._execute(state, delay);\n  };\n  QueueAction.prototype.requestAsyncId = function (scheduler, id, delay) {\n    if (delay === void 0) {\n      delay = 0;\n    }\n    if (delay != null && delay > 0 || delay == null && this.delay > 0) {\n      return _super.prototype.requestAsyncId.call(this, scheduler, id, delay);\n    }\n    scheduler.flush(this);\n    return 0;\n  };\n  return QueueAction;\n}(AsyncAction);\nexport { QueueAction };", "map": {"version": 3, "names": ["AsyncAction", "QueueAction", "_super", "__extends", "scheduler", "work", "_this", "call", "prototype", "schedule", "state", "delay", "flush", "execute", "closed", "_execute", "requestAsyncId", "id"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\scheduler\\QueueAction.ts"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nimport { Subscription } from '../Subscription';\nimport { QueueScheduler } from './QueueScheduler';\nimport { SchedulerAction } from '../types';\nimport { TimerHandle } from './timerHandle';\n\nexport class QueueAction<T> extends AsyncAction<T> {\n  constructor(protected scheduler: QueueScheduler, protected work: (this: SchedulerAction<T>, state?: T) => void) {\n    super(scheduler, work);\n  }\n\n  public schedule(state?: T, delay: number = 0): Subscription {\n    if (delay > 0) {\n      return super.schedule(state, delay);\n    }\n    this.delay = delay;\n    this.state = state;\n    this.scheduler.flush(this);\n    return this;\n  }\n\n  public execute(state: T, delay: number): any {\n    return delay > 0 || this.closed ? super.execute(state, delay) : this._execute(state, delay);\n  }\n\n  protected requestAsyncId(scheduler: QueueScheduler, id?: TimerHandle, delay: number = 0): TimerHandle {\n    // If delay exists and is greater than 0, or if the delay is null (the\n    // action wasn't rescheduled) but was originally scheduled as an async\n    // action, then recycle as an async action.\n\n    if ((delay != null && delay > 0) || (delay == null && this.delay > 0)) {\n      return super.requestAsyncId(scheduler, id, delay);\n    }\n\n    // Otherwise flush the scheduler starting with this action.\n    scheduler.flush(this);\n\n    // HACK: In the past, this was returning `void`. However, `void` isn't a valid\n    // `TimerHandle`, and generally the return value here isn't really used. So the\n    // compromise is to return `0` which is both \"falsy\" and a valid `TimerHandle`,\n    // as opposed to refactoring every other instanceo of `requestAsyncId`.\n    return 0;\n  }\n}\n"], "mappings": ";AAAA,SAASA,WAAW,QAAQ,eAAe;AAM3C,IAAAC,WAAA,aAAAC,MAAA;EAAoCC,SAAA,CAAAF,WAAA,EAAAC,MAAA;EAClC,SAAAD,YAAsBG,SAAyB,EAAYC,IAAmD;IAA9G,IAAAC,KAAA,GACEJ,MAAA,CAAAK,IAAA,OAAMH,SAAS,EAAEC,IAAI,CAAC;IADFC,KAAA,CAAAF,SAAS,GAATA,SAAS;IAA4BE,KAAA,CAAAD,IAAI,GAAJA,IAAI;;EAE/D;EAEOJ,WAAA,CAAAO,SAAA,CAAAC,QAAQ,GAAf,UAAgBC,KAAS,EAAEC,KAAiB;IAAjB,IAAAA,KAAA;MAAAA,KAAA,IAAiB;IAAA;IAC1C,IAAIA,KAAK,GAAG,CAAC,EAAE;MACb,OAAOT,MAAA,CAAAM,SAAA,CAAMC,QAAQ,CAAAF,IAAA,OAACG,KAAK,EAAEC,KAAK,CAAC;;IAErC,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACN,SAAS,CAACQ,KAAK,CAAC,IAAI,CAAC;IAC1B,OAAO,IAAI;EACb,CAAC;EAEMX,WAAA,CAAAO,SAAA,CAAAK,OAAO,GAAd,UAAeH,KAAQ,EAAEC,KAAa;IACpC,OAAOA,KAAK,GAAG,CAAC,IAAI,IAAI,CAACG,MAAM,GAAGZ,MAAA,CAAAM,SAAA,CAAMK,OAAO,CAAAN,IAAA,OAACG,KAAK,EAAEC,KAAK,CAAC,GAAG,IAAI,CAACI,QAAQ,CAACL,KAAK,EAAEC,KAAK,CAAC;EAC7F,CAAC;EAESV,WAAA,CAAAO,SAAA,CAAAQ,cAAc,GAAxB,UAAyBZ,SAAyB,EAAEa,EAAgB,EAAEN,KAAiB;IAAjB,IAAAA,KAAA;MAAAA,KAAA,IAAiB;IAAA;IAKrF,IAAKA,KAAK,IAAI,IAAI,IAAIA,KAAK,GAAG,CAAC,IAAMA,KAAK,IAAI,IAAI,IAAI,IAAI,CAACA,KAAK,GAAG,CAAE,EAAE;MACrE,OAAOT,MAAA,CAAAM,SAAA,CAAMQ,cAAc,CAAAT,IAAA,OAACH,SAAS,EAAEa,EAAE,EAAEN,KAAK,CAAC;;IAInDP,SAAS,CAACQ,KAAK,CAAC,IAAI,CAAC;IAMrB,OAAO,CAAC;EACV,CAAC;EACH,OAAAX,WAAC;AAAD,CAAC,CArCmCD,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}