{"ast": null, "code": "export var performanceTimestampProvider = {\n  now: function () {\n    return (performanceTimestampProvider.delegate || performance).now();\n  },\n  delegate: undefined\n};", "map": {"version": 3, "names": ["performanceTimestampProvider", "now", "delegate", "performance", "undefined"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\scheduler\\performanceTimestampProvider.ts"], "sourcesContent": ["import { TimestampProvider } from '../types';\n\ninterface PerformanceTimestampProvider extends TimestampProvider {\n  delegate: TimestampProvider | undefined;\n}\n\nexport const performanceTimestampProvider: PerformanceTimestampProvider = {\n  now() {\n    // Use the variable rather than `this` so that the function can be called\n    // without being bound to the provider.\n    return (performanceTimestampProvider.delegate || performance).now();\n  },\n  delegate: undefined,\n};\n"], "mappings": "AAMA,OAAO,IAAMA,4BAA4B,GAAiC;EACxEC,GAAG,WAAAA,CAAA;IAGD,OAAO,CAACD,4BAA4B,CAACE,QAAQ,IAAIC,WAAW,EAAEF,GAAG,EAAE;EACrE,CAAC;EACDC,QAAQ,EAAEE;CACX"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}