{"ast": null, "code": "var Symbol = require('./_Symbol');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */\nfunction getRawTag(value) {\n  var isOwn = hasOwnProperty.call(value, symToStringTag),\n    tag = value[symToStringTag];\n  try {\n    value[symToStringTag] = undefined;\n    var unmasked = true;\n  } catch (e) {}\n  var result = nativeObjectToString.call(value);\n  if (unmasked) {\n    if (isOwn) {\n      value[symToStringTag] = tag;\n    } else {\n      delete value[symToStringTag];\n    }\n  }\n  return result;\n}\nmodule.exports = getRawTag;", "map": {"version": 3, "names": ["Symbol", "require", "objectProto", "Object", "prototype", "hasOwnProperty", "nativeObjectToString", "toString", "symToStringTag", "toStringTag", "undefined", "getRawTag", "value", "isOwn", "call", "tag", "unmasked", "e", "result", "module", "exports"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/lodash/_getRawTag.js"], "sourcesContent": ["var Symbol = require('./_Symbol');\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */\nfunction getRawTag(value) {\n  var isOwn = hasOwnProperty.call(value, symToStringTag),\n      tag = value[symToStringTag];\n\n  try {\n    value[symToStringTag] = undefined;\n    var unmasked = true;\n  } catch (e) {}\n\n  var result = nativeObjectToString.call(value);\n  if (unmasked) {\n    if (isOwn) {\n      value[symToStringTag] = tag;\n    } else {\n      delete value[symToStringTag];\n    }\n  }\n  return result;\n}\n\nmodule.exports = getRawTag;\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,WAAW,CAAC;;AAEjC;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA,IAAIC,oBAAoB,GAAGJ,WAAW,CAACK,QAAQ;;AAE/C;AACA,IAAIC,cAAc,GAAGR,MAAM,GAAGA,MAAM,CAACS,WAAW,GAAGC,SAAS;;AAE5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,IAAIC,KAAK,GAAGR,cAAc,CAACS,IAAI,CAACF,KAAK,EAAEJ,cAAc,CAAC;IAClDO,GAAG,GAAGH,KAAK,CAACJ,cAAc,CAAC;EAE/B,IAAI;IACFI,KAAK,CAACJ,cAAc,CAAC,GAAGE,SAAS;IACjC,IAAIM,QAAQ,GAAG,IAAI;EACrB,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAC;EAEb,IAAIC,MAAM,GAAGZ,oBAAoB,CAACQ,IAAI,CAACF,KAAK,CAAC;EAC7C,IAAII,QAAQ,EAAE;IACZ,IAAIH,KAAK,EAAE;MACTD,KAAK,CAACJ,cAAc,CAAC,GAAGO,GAAG;IAC7B,CAAC,MAAM;MACL,OAAOH,KAAK,CAACJ,cAAc,CAAC;IAC9B;EACF;EACA,OAAOU,MAAM;AACf;AAEAC,MAAM,CAACC,OAAO,GAAGT,SAAS"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}