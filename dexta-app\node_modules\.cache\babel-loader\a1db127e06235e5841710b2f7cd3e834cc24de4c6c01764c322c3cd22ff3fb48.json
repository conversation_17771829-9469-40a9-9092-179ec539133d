{"ast": null, "code": "// Edits text `value` (if `operation` is passed) and repositions the `caret` if needed.\n//\n// Example:\n//\n// value - '88005553535'\n// caret - 2 // starting from 0; is positioned before the first zero\n// operation - 'Backspace'\n//\n// Returns\n// {\n// \tvalue: '8005553535'\n// \tcaret: 1\n// }\n//\n// Currently supports just 'Delete' and 'Backspace' operations\n//\nexport default function edit(value, caret, operation) {\n  switch (operation) {\n    case 'Backspace':\n      // If there exists the previous character,\n      // then erase it and reposition the caret.\n      if (caret > 0) {\n        // Remove the previous character\n        value = value.slice(0, caret - 1) + value.slice(caret); // Position the caret where the previous (erased) character was\n\n        caret--;\n      }\n      break;\n    case 'Delete':\n      // Remove current digit (if any)\n      value = value.slice(0, caret) + value.slice(caret + 1);\n      break;\n  }\n  return {\n    value: value,\n    caret: caret\n  };\n}", "map": {"version": 3, "names": ["edit", "value", "caret", "operation", "slice"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\input-format\\source\\edit.js"], "sourcesContent": ["// Edits text `value` (if `operation` is passed) and repositions the `caret` if needed.\r\n//\r\n// Example:\r\n//\r\n// value - '88005553535'\r\n// caret - 2 // starting from 0; is positioned before the first zero\r\n// operation - 'Backspace'\r\n//\r\n// Returns\r\n// {\r\n// \tvalue: '8005553535'\r\n// \tcaret: 1\r\n// }\r\n//\r\n// Currently supports just 'Delete' and 'Backspace' operations\r\n//\r\nexport default function edit(value, caret, operation)\r\n{\r\n\tswitch (operation)\r\n\t{\r\n\t\tcase 'Backspace':\r\n\t\t\t// If there exists the previous character,\r\n\t\t\t// then erase it and reposition the caret.\r\n\t\t\tif (caret > 0)\r\n\t\t\t{\r\n\t\t\t\t// Remove the previous character\r\n\t\t\t\tvalue = value.slice(0, caret - 1) + value.slice(caret)\r\n\t\t\t\t// Position the caret where the previous (erased) character was\r\n\t\t\t\tcaret--\r\n\t\t\t}\r\n\t\t\tbreak\r\n\r\n\t\tcase 'Delete':\r\n\t\t\t// Remove current digit (if any)\r\n\t\t\tvalue = value.slice(0, caret) + value.slice(caret + 1)\r\n\t\t\tbreak\r\n\t}\r\n\r\n\treturn { value, caret }\r\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,IAATA,CAAcC,KAAd,EAAqBC,KAArB,EAA4BC,SAA5B,EACf;EACC,QAAQA,SAAR;IAEC,KAAK,WAAL;MACC;MACA;MACA,IAAID,KAAK,GAAG,CAAZ,EACA;QACC;QACAD,KAAK,GAAGA,KAAK,CAACG,KAAN,CAAY,CAAZ,EAAeF,KAAK,GAAG,CAAvB,IAA4BD,KAAK,CAACG,KAAN,CAAYF,KAAZ,CAApC,CAFD,CAGC;;QACAA,KAAK;MACL;MACD;IAED,KAAK,QAAL;MACC;MACAD,KAAK,GAAGA,KAAK,CAACG,KAAN,CAAY,CAAZ,EAAeF,KAAf,IAAwBD,KAAK,CAACG,KAAN,CAAYF,KAAK,GAAG,CAApB,CAAhC;MACA;EAjBF;EAoBA,OAAO;IAAED,KAAK,EAALA,KAAF;IAASC,KAAK,EAALA;EAAT,CAAP;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}