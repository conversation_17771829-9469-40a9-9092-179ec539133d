{"ast": null, "code": "// This is a port of Google Android `libphonenumber`'s\n// `phonenumberutil.js` of December 31th, 2018.\n//\n// https://github.com/googlei18n/libphonenumber/commits/master/javascript/i18n/phonenumbers/phonenumberutil.js\nimport { VALID_DIGITS, PLUS_CHARS, MIN_LENGTH_FOR_NSN, MAX_LENGTH_FOR_NSN } from './constants.js';\nimport ParseError from './ParseError.js';\nimport Metadata from './metadata.js';\nimport isViablePhoneNumber, { isViablePhoneNumberStart } from './helpers/isViablePhoneNumber.js';\nimport extractExtension from './helpers/extension/extractExtension.js';\nimport parseIncompletePhoneNumber from './parseIncompletePhoneNumber.js';\nimport getCountryCallingCode from './getCountryCallingCode.js';\nimport { isPossibleNumber } from './isPossible.js'; // import { parseRFC3966 } from './helpers/RFC3966.js'\n\nimport PhoneNumber from './PhoneNumber.js';\nimport matchesEntirely from './helpers/matchesEntirely.js';\nimport extractCountryCallingCode from './helpers/extractCountryCallingCode.js';\nimport extractNationalNumber from './helpers/extractNationalNumber.js';\nimport stripIddPrefix from './helpers/stripIddPrefix.js';\nimport getCountryByCallingCode from './helpers/getCountryByCallingCode.js';\nimport extractFormattedPhoneNumberFromPossibleRfc3966NumberUri from './helpers/extractFormattedPhoneNumberFromPossibleRfc3966NumberUri.js'; // We don't allow input strings for parsing to be longer than 250 chars.\n// This prevents malicious input from consuming CPU.\n\nvar MAX_INPUT_STRING_LENGTH = 250; // This consists of the plus symbol, digits, and arabic-indic digits.\n\nvar PHONE_NUMBER_START_PATTERN = new RegExp('[' + PLUS_CHARS + VALID_DIGITS + ']'); // Regular expression of trailing characters that we want to remove.\n// A trailing `#` is sometimes used when writing phone numbers with extensions in US.\n// Example: \"+****************-910#\" number has extension \"910\".\n\nvar AFTER_PHONE_NUMBER_END_PATTERN = new RegExp('[^' + VALID_DIGITS + '#' + ']+$');\nvar USE_NON_GEOGRAPHIC_COUNTRY_CODE = false; // Examples:\n//\n// ```js\n// parse('8 (800) 555-35-35', 'RU')\n// parse('8 (800) 555-35-35', 'RU', metadata)\n// parse('8 (800) 555-35-35', { country: { default: 'RU' } })\n// parse('8 (800) 555-35-35', { country: { default: 'RU' } }, metadata)\n// parse('****** 555 35 35')\n// parse('****** 555 35 35', metadata)\n// ```\n//\n\n/**\r\n * Parses a phone number.\r\n *\r\n * parse('123456789', { defaultCountry: 'RU', v2: true }, metadata)\r\n * parse('123456789', { defaultCountry: 'RU' }, metadata)\r\n * parse('123456789', undefined, metadata)\r\n *\r\n * @param  {string} input\r\n * @param  {object} [options]\r\n * @param  {object} metadata\r\n * @return {object|PhoneNumber?} If `options.v2: true` flag is passed, it returns a `PhoneNumber?` instance. Otherwise, returns an object of shape `{ phone: '...', country: '...' }` (or just `{}` if no phone number was parsed).\r\n */\n\nexport default function parse(text, options, metadata) {\n  // If assigning the `{}` default value is moved to the arguments above,\n  // code coverage would decrease for some weird reason.\n  options = options || {};\n  metadata = new Metadata(metadata); // Validate `defaultCountry`.\n\n  if (options.defaultCountry && !metadata.hasCountry(options.defaultCountry)) {\n    if (options.v2) {\n      throw new ParseError('INVALID_COUNTRY');\n    }\n    throw new Error(\"Unknown country: \".concat(options.defaultCountry));\n  } // Parse the phone number.\n\n  var _parseInput = parseInput(text, options.v2, options.extract),\n    formattedPhoneNumber = _parseInput.number,\n    ext = _parseInput.ext,\n    error = _parseInput.error; // If the phone number is not viable then return nothing.\n\n  if (!formattedPhoneNumber) {\n    if (options.v2) {\n      if (error === 'TOO_SHORT') {\n        throw new ParseError('TOO_SHORT');\n      }\n      throw new ParseError('NOT_A_NUMBER');\n    }\n    return {};\n  }\n  var _parsePhoneNumber = parsePhoneNumber(formattedPhoneNumber, options.defaultCountry, options.defaultCallingCode, metadata),\n    country = _parsePhoneNumber.country,\n    nationalNumber = _parsePhoneNumber.nationalNumber,\n    countryCallingCode = _parsePhoneNumber.countryCallingCode,\n    countryCallingCodeSource = _parsePhoneNumber.countryCallingCodeSource,\n    carrierCode = _parsePhoneNumber.carrierCode;\n  if (!metadata.hasSelectedNumberingPlan()) {\n    if (options.v2) {\n      throw new ParseError('INVALID_COUNTRY');\n    }\n    return {};\n  } // Validate national (significant) number length.\n\n  if (!nationalNumber || nationalNumber.length < MIN_LENGTH_FOR_NSN) {\n    // Won't throw here because the regexp already demands length > 1.\n\n    /* istanbul ignore if */\n    if (options.v2) {\n      throw new ParseError('TOO_SHORT');\n    } // Google's demo just throws an error in this case.\n\n    return {};\n  } // Validate national (significant) number length.\n  //\n  // A sidenote:\n  //\n  // They say that sometimes national (significant) numbers\n  // can be longer than `MAX_LENGTH_FOR_NSN` (e.g. in Germany).\n  // https://github.com/googlei18n/libphonenumber/blob/7e1748645552da39c4e1ba731e47969d97bdb539/resources/phonenumber.proto#L36\n  // Such numbers will just be discarded.\n  //\n\n  if (nationalNumber.length > MAX_LENGTH_FOR_NSN) {\n    if (options.v2) {\n      throw new ParseError('TOO_LONG');\n    } // Google's demo just throws an error in this case.\n\n    return {};\n  }\n  if (options.v2) {\n    var phoneNumber = new PhoneNumber(countryCallingCode, nationalNumber, metadata.metadata);\n    if (country) {\n      phoneNumber.country = country;\n    }\n    if (carrierCode) {\n      phoneNumber.carrierCode = carrierCode;\n    }\n    if (ext) {\n      phoneNumber.ext = ext;\n    }\n    phoneNumber.__countryCallingCodeSource = countryCallingCodeSource;\n    return phoneNumber;\n  } // Check if national phone number pattern matches the number.\n  // National number pattern is different for each country,\n  // even for those ones which are part of the \"NANPA\" group.\n\n  var valid = (options.extended ? metadata.hasSelectedNumberingPlan() : country) ? matchesEntirely(nationalNumber, metadata.nationalNumberPattern()) : false;\n  if (!options.extended) {\n    return valid ? result(country, nationalNumber, ext) : {};\n  } // isInternational: countryCallingCode !== undefined\n\n  return {\n    country: country,\n    countryCallingCode: countryCallingCode,\n    carrierCode: carrierCode,\n    valid: valid,\n    possible: valid ? true : options.extended === true && metadata.possibleLengths() && isPossibleNumber(nationalNumber, metadata) ? true : false,\n    phone: nationalNumber,\n    ext: ext\n  };\n}\n/**\r\n * Extracts a formatted phone number from text.\r\n * Doesn't guarantee that the extracted phone number\r\n * is a valid phone number (for example, doesn't validate its length).\r\n * @param  {string} text\r\n * @param  {boolean} [extract] — If `false`, then will parse the entire `text` as a phone number.\r\n * @param  {boolean} [throwOnError] — By default, it won't throw if the text is too long.\r\n * @return {string}\r\n * @example\r\n * // Returns \"(*************\".\r\n * extractFormattedPhoneNumber(\"Call (************* for assistance.\")\r\n */\n\nfunction _extractFormattedPhoneNumber(text, extract, throwOnError) {\n  if (!text) {\n    return;\n  }\n  if (text.length > MAX_INPUT_STRING_LENGTH) {\n    if (throwOnError) {\n      throw new ParseError('TOO_LONG');\n    }\n    return;\n  }\n  if (extract === false) {\n    return text;\n  } // Attempt to extract a possible number from the string passed in\n\n  var startsAt = text.search(PHONE_NUMBER_START_PATTERN);\n  if (startsAt < 0) {\n    return;\n  }\n  return text // Trim everything to the left of the phone number\n  .slice(startsAt) // Remove trailing non-numerical characters\n  .replace(AFTER_PHONE_NUMBER_END_PATTERN, '');\n}\n/**\r\n * @param  {string} text - Input.\r\n * @param  {boolean} v2 - Legacy API functions don't pass `v2: true` flag.\r\n * @param  {boolean} [extract] - Whether to extract a phone number from `text`, or attempt to parse the entire text as a phone number.\r\n * @return {object} `{ ?number, ?ext }`.\r\n */\n\nfunction parseInput(text, v2, extract) {\n  // // Parse RFC 3966 phone number URI.\n  // if (text && text.indexOf('tel:') === 0) {\n  // \treturn parseRFC3966(text)\n  // }\n  // let number = extractFormattedPhoneNumber(text, extract, v2)\n  var number = extractFormattedPhoneNumberFromPossibleRfc3966NumberUri(text, {\n    extractFormattedPhoneNumber: function extractFormattedPhoneNumber(text) {\n      return _extractFormattedPhoneNumber(text, extract, v2);\n    }\n  }); // If the phone number is not viable, then abort.\n\n  if (!number) {\n    return {};\n  }\n  if (!isViablePhoneNumber(number)) {\n    if (isViablePhoneNumberStart(number)) {\n      return {\n        error: 'TOO_SHORT'\n      };\n    }\n    return {};\n  } // Attempt to parse extension first, since it doesn't require region-specific\n  // data and we want to have the non-normalised number here.\n\n  var withExtensionStripped = extractExtension(number);\n  if (withExtensionStripped.ext) {\n    return withExtensionStripped;\n  }\n  return {\n    number: number\n  };\n}\n/**\r\n * Creates `parse()` result object.\r\n */\n\nfunction result(country, nationalNumber, ext) {\n  var result = {\n    country: country,\n    phone: nationalNumber\n  };\n  if (ext) {\n    result.ext = ext;\n  }\n  return result;\n}\n/**\r\n * Parses a viable phone number.\r\n * @param {string} formattedPhoneNumber — Example: \"(*************\".\r\n * @param {string} [defaultCountry]\r\n * @param {string} [defaultCallingCode]\r\n * @param {Metadata} metadata\r\n * @return {object} Returns `{ country: string?, countryCallingCode: string?, nationalNumber: string? }`.\r\n */\n\nfunction parsePhoneNumber(formattedPhoneNumber, defaultCountry, defaultCallingCode, metadata) {\n  // Extract calling code from phone number.\n  var _extractCountryCallin = extractCountryCallingCode(parseIncompletePhoneNumber(formattedPhoneNumber), defaultCountry, defaultCallingCode, metadata.metadata),\n    countryCallingCodeSource = _extractCountryCallin.countryCallingCodeSource,\n    countryCallingCode = _extractCountryCallin.countryCallingCode,\n    number = _extractCountryCallin.number; // Choose a country by `countryCallingCode`.\n\n  var country;\n  if (countryCallingCode) {\n    metadata.selectNumberingPlan(countryCallingCode);\n  } // If `formattedPhoneNumber` is passed in \"national\" format\n  // then `number` is defined and `countryCallingCode` is `undefined`.\n  else if (number && (defaultCountry || defaultCallingCode)) {\n    metadata.selectNumberingPlan(defaultCountry, defaultCallingCode);\n    if (defaultCountry) {\n      country = defaultCountry;\n    } else {\n      /* istanbul ignore if */\n      if (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\n        if (metadata.isNonGeographicCallingCode(defaultCallingCode)) {\n          country = '001';\n        }\n      }\n    }\n    countryCallingCode = defaultCallingCode || getCountryCallingCode(defaultCountry, metadata.metadata);\n  } else return {};\n  if (!number) {\n    return {\n      countryCallingCodeSource: countryCallingCodeSource,\n      countryCallingCode: countryCallingCode\n    };\n  }\n  var _extractNationalNumbe = extractNationalNumber(parseIncompletePhoneNumber(number), metadata),\n    nationalNumber = _extractNationalNumbe.nationalNumber,\n    carrierCode = _extractNationalNumbe.carrierCode; // Sometimes there are several countries\n  // corresponding to the same country phone code\n  // (e.g. NANPA countries all having `1` country phone code).\n  // Therefore, to reliably determine the exact country,\n  // national (significant) number should have been parsed first.\n  //\n  // When `metadata.json` is generated, all \"ambiguous\" country phone codes\n  // get their countries populated with the full set of\n  // \"phone number type\" regular expressions.\n  //\n\n  var exactCountry = getCountryByCallingCode(countryCallingCode, {\n    nationalNumber: nationalNumber,\n    defaultCountry: defaultCountry,\n    metadata: metadata\n  });\n  if (exactCountry) {\n    country = exactCountry;\n    /* istanbul ignore if */\n\n    if (exactCountry === '001') {// Can't happen with `USE_NON_GEOGRAPHIC_COUNTRY_CODE` being `false`.\n      // If `USE_NON_GEOGRAPHIC_COUNTRY_CODE` is set to `true` for some reason,\n      // then remove the \"istanbul ignore if\".\n    } else {\n      metadata.country(country);\n    }\n  }\n  return {\n    country: country,\n    countryCallingCode: countryCallingCode,\n    countryCallingCodeSource: countryCallingCodeSource,\n    nationalNumber: nationalNumber,\n    carrierCode: carrierCode\n  };\n}", "map": {"version": 3, "names": ["VALID_DIGITS", "PLUS_CHARS", "MIN_LENGTH_FOR_NSN", "MAX_LENGTH_FOR_NSN", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "isViablePhoneNumber", "isViablePhoneNumberStart", "extractExtension", "parseIncompletePhoneNumber", "getCountryCallingCode", "isPossibleNumber", "PhoneNumber", "matchesEntirely", "extractCountryCallingCode", "extractNationalNumber", "stripIddPrefix", "getCountryByCallingCode", "extractFormattedPhoneNumberFromPossibleRfc3966NumberUri", "MAX_INPUT_STRING_LENGTH", "PHONE_NUMBER_START_PATTERN", "RegExp", "AFTER_PHONE_NUMBER_END_PATTERN", "USE_NON_GEOGRAPHIC_COUNTRY_CODE", "parse", "text", "options", "metadata", "defaultCountry", "hasCountry", "v2", "Error", "concat", "_parseInput", "parseInput", "extract", "formattedPhoneNumber", "number", "ext", "error", "_parsePhoneNumber", "parsePhoneNumber", "defaultCallingCode", "country", "nationalNumber", "countryCallingCode", "countryCallingCodeSource", "carrierCode", "hasSelectedNumberingPlan", "length", "phoneNumber", "__countryCallingCodeSource", "valid", "extended", "nationalNumberPattern", "result", "possible", "possibleLengths", "phone", "_extractFormattedPhoneNumber", "extractFormattedPhoneNumber", "throwOnError", "startsAt", "search", "slice", "replace", "withExtensionStripped", "_extractCountryCallin", "selectNumberingPlan", "isNonGeographicCallingCode", "_extractNationalNumbe", "exactCountry"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\parse.js"], "sourcesContent": ["// This is a port of Google Android `libphonenumber`'s\r\n// `phonenumberutil.js` of December 31th, 2018.\r\n//\r\n// https://github.com/googlei18n/libphonenumber/commits/master/javascript/i18n/phonenumbers/phonenumberutil.js\r\n\r\nimport {\r\n\tVALID_DIGITS,\r\n\tPLUS_CHARS,\r\n\tMIN_LENGTH_FOR_NSN,\r\n\tMAX_LENGTH_FOR_NSN\r\n} from './constants.js'\r\n\r\nimport ParseError from './ParseError.js'\r\nimport Metadata from './metadata.js'\r\nimport isViablePhoneNumber, { isViablePhoneNumberStart } from './helpers/isViablePhoneNumber.js'\r\nimport extractExtension from './helpers/extension/extractExtension.js'\r\nimport parseIncompletePhoneNumber from './parseIncompletePhoneNumber.js'\r\nimport getCountryCallingCode from './getCountryCallingCode.js'\r\nimport { isPossibleNumber } from './isPossible.js'\r\n// import { parseRFC3966 } from './helpers/RFC3966.js'\r\nimport PhoneNumber from './PhoneNumber.js'\r\nimport matchesEntirely from './helpers/matchesEntirely.js'\r\nimport extractCountryCallingCode from './helpers/extractCountryCallingCode.js'\r\nimport extractNationalNumber from './helpers/extractNationalNumber.js'\r\nimport stripIddPrefix from './helpers/stripIddPrefix.js'\r\nimport getCountryByCallingCode from './helpers/getCountryByCallingCode.js'\r\nimport extractFormattedPhoneNumberFromPossibleRfc3966NumberUri from './helpers/extractFormattedPhoneNumberFromPossibleRfc3966NumberUri.js'\r\n\r\n// We don't allow input strings for parsing to be longer than 250 chars.\r\n// This prevents malicious input from consuming CPU.\r\nconst MAX_INPUT_STRING_LENGTH = 250\r\n\r\n// This consists of the plus symbol, digits, and arabic-indic digits.\r\nconst PHONE_NUMBER_START_PATTERN = new RegExp('[' + PLUS_CHARS + VALID_DIGITS + ']')\r\n\r\n// Regular expression of trailing characters that we want to remove.\r\n// A trailing `#` is sometimes used when writing phone numbers with extensions in US.\r\n// Example: \"+****************-910#\" number has extension \"910\".\r\nconst AFTER_PHONE_NUMBER_END_PATTERN = new RegExp('[^' + VALID_DIGITS + '#' + ']+$')\r\n\r\nconst USE_NON_GEOGRAPHIC_COUNTRY_CODE = false\r\n\r\n// Examples:\r\n//\r\n// ```js\r\n// parse('8 (800) 555-35-35', 'RU')\r\n// parse('8 (800) 555-35-35', 'RU', metadata)\r\n// parse('8 (800) 555-35-35', { country: { default: 'RU' } })\r\n// parse('8 (800) 555-35-35', { country: { default: 'RU' } }, metadata)\r\n// parse('****** 555 35 35')\r\n// parse('****** 555 35 35', metadata)\r\n// ```\r\n//\r\n\r\n/**\r\n * Parses a phone number.\r\n *\r\n * parse('123456789', { defaultCountry: 'RU', v2: true }, metadata)\r\n * parse('123456789', { defaultCountry: 'RU' }, metadata)\r\n * parse('123456789', undefined, metadata)\r\n *\r\n * @param  {string} input\r\n * @param  {object} [options]\r\n * @param  {object} metadata\r\n * @return {object|PhoneNumber?} If `options.v2: true` flag is passed, it returns a `PhoneNumber?` instance. Otherwise, returns an object of shape `{ phone: '...', country: '...' }` (or just `{}` if no phone number was parsed).\r\n */\r\nexport default function parse(text, options, metadata) {\r\n\t// If assigning the `{}` default value is moved to the arguments above,\r\n\t// code coverage would decrease for some weird reason.\r\n\toptions = options || {}\r\n\r\n\tmetadata = new Metadata(metadata)\r\n\r\n\t// Validate `defaultCountry`.\r\n\tif (options.defaultCountry && !metadata.hasCountry(options.defaultCountry)) {\r\n\t\tif (options.v2) {\r\n\t\t\tthrow new ParseError('INVALID_COUNTRY')\r\n\t\t}\r\n\t\tthrow new Error(`Unknown country: ${options.defaultCountry}`)\r\n\t}\r\n\r\n\t// Parse the phone number.\r\n\tconst { number: formattedPhoneNumber, ext, error } = parseInput(text, options.v2, options.extract)\r\n\r\n\t// If the phone number is not viable then return nothing.\r\n\tif (!formattedPhoneNumber) {\r\n\t\tif (options.v2) {\r\n\t\t\tif (error === 'TOO_SHORT') {\r\n\t\t\t\tthrow new ParseError('TOO_SHORT')\r\n\t\t\t}\r\n\t\t\tthrow new ParseError('NOT_A_NUMBER')\r\n\t\t}\r\n\t\treturn {}\r\n\t}\r\n\r\n\tconst {\r\n\t\tcountry,\r\n\t\tnationalNumber,\r\n\t\tcountryCallingCode,\r\n\t\tcountryCallingCodeSource,\r\n\t\tcarrierCode\r\n\t} = parsePhoneNumber(\r\n\t\tformattedPhoneNumber,\r\n\t\toptions.defaultCountry,\r\n\t\toptions.defaultCallingCode,\r\n\t\tmetadata\r\n\t)\r\n\r\n\tif (!metadata.hasSelectedNumberingPlan()) {\r\n\t\tif (options.v2) {\r\n\t\t\tthrow new ParseError('INVALID_COUNTRY')\r\n\t\t}\r\n\t\treturn {}\r\n\t}\r\n\r\n\t// Validate national (significant) number length.\r\n\tif (!nationalNumber || nationalNumber.length < MIN_LENGTH_FOR_NSN) {\r\n\t\t// Won't throw here because the regexp already demands length > 1.\r\n\t\t/* istanbul ignore if */\r\n\t\tif (options.v2) {\r\n\t\t\tthrow new ParseError('TOO_SHORT')\r\n\t\t}\r\n\t\t// Google's demo just throws an error in this case.\r\n\t\treturn {}\r\n\t}\r\n\r\n\t// Validate national (significant) number length.\r\n\t//\r\n\t// A sidenote:\r\n\t//\r\n\t// They say that sometimes national (significant) numbers\r\n\t// can be longer than `MAX_LENGTH_FOR_NSN` (e.g. in Germany).\r\n\t// https://github.com/googlei18n/libphonenumber/blob/7e1748645552da39c4e1ba731e47969d97bdb539/resources/phonenumber.proto#L36\r\n\t// Such numbers will just be discarded.\r\n\t//\r\n\tif (nationalNumber.length > MAX_LENGTH_FOR_NSN) {\r\n\t\tif (options.v2) {\r\n\t\t\tthrow new ParseError('TOO_LONG')\r\n\t\t}\r\n\t\t// Google's demo just throws an error in this case.\r\n\t\treturn {}\r\n\t}\r\n\r\n\tif (options.v2) {\r\n\t\tconst phoneNumber = new PhoneNumber(\r\n\t\t\tcountryCallingCode,\r\n\t\t\tnationalNumber,\r\n\t\t\tmetadata.metadata\r\n\t\t)\r\n\t\tif (country) {\r\n\t\t\tphoneNumber.country = country\r\n\t\t}\r\n\t\tif (carrierCode) {\r\n\t\t\tphoneNumber.carrierCode = carrierCode\r\n\t\t}\r\n\t\tif (ext) {\r\n\t\t\tphoneNumber.ext = ext\r\n\t\t}\r\n\t\tphoneNumber.__countryCallingCodeSource = countryCallingCodeSource\r\n\t\treturn phoneNumber\r\n\t}\r\n\r\n\t// Check if national phone number pattern matches the number.\r\n\t// National number pattern is different for each country,\r\n\t// even for those ones which are part of the \"NANPA\" group.\r\n\tconst valid = (options.extended ? metadata.hasSelectedNumberingPlan() : country) ?\r\n\t\tmatchesEntirely(nationalNumber, metadata.nationalNumberPattern()) :\r\n\t\tfalse\r\n\r\n\tif (!options.extended) {\r\n\t\treturn valid ? result(country, nationalNumber, ext) : {}\r\n\t}\r\n\r\n\t// isInternational: countryCallingCode !== undefined\r\n\r\n\treturn {\r\n\t\tcountry,\r\n\t\tcountryCallingCode,\r\n\t\tcarrierCode,\r\n\t\tvalid,\r\n\t\tpossible: valid ? true : (\r\n\t\t\toptions.extended === true &&\r\n\t\t\tmetadata.possibleLengths() &&\r\n\t\t\tisPossibleNumber(nationalNumber, metadata) ? true : false\r\n\t\t),\r\n\t\tphone: nationalNumber,\r\n\t\text\r\n\t}\r\n}\r\n\r\n/**\r\n * Extracts a formatted phone number from text.\r\n * Doesn't guarantee that the extracted phone number\r\n * is a valid phone number (for example, doesn't validate its length).\r\n * @param  {string} text\r\n * @param  {boolean} [extract] — If `false`, then will parse the entire `text` as a phone number.\r\n * @param  {boolean} [throwOnError] — By default, it won't throw if the text is too long.\r\n * @return {string}\r\n * @example\r\n * // Returns \"(*************\".\r\n * extractFormattedPhoneNumber(\"Call (************* for assistance.\")\r\n */\r\nfunction extractFormattedPhoneNumber(text, extract, throwOnError) {\r\n\tif (!text) {\r\n\t\treturn\r\n\t}\r\n\tif (text.length > MAX_INPUT_STRING_LENGTH) {\r\n\t\tif (throwOnError) {\r\n\t\t\tthrow new ParseError('TOO_LONG')\r\n\t\t}\r\n\t\treturn\r\n\t}\r\n\tif (extract === false) {\r\n\t\treturn text\r\n\t}\r\n\t// Attempt to extract a possible number from the string passed in\r\n\tconst startsAt = text.search(PHONE_NUMBER_START_PATTERN)\r\n\tif (startsAt < 0) {\r\n\t\treturn\r\n\t}\r\n\treturn text\r\n\t\t// Trim everything to the left of the phone number\r\n\t\t.slice(startsAt)\r\n\t\t// Remove trailing non-numerical characters\r\n\t\t.replace(AFTER_PHONE_NUMBER_END_PATTERN, '')\r\n}\r\n\r\n/**\r\n * @param  {string} text - Input.\r\n * @param  {boolean} v2 - Legacy API functions don't pass `v2: true` flag.\r\n * @param  {boolean} [extract] - Whether to extract a phone number from `text`, or attempt to parse the entire text as a phone number.\r\n * @return {object} `{ ?number, ?ext }`.\r\n */\r\nfunction parseInput(text, v2, extract) {\r\n\t// // Parse RFC 3966 phone number URI.\r\n\t// if (text && text.indexOf('tel:') === 0) {\r\n\t// \treturn parseRFC3966(text)\r\n\t// }\r\n\t// let number = extractFormattedPhoneNumber(text, extract, v2)\r\n\tlet number = extractFormattedPhoneNumberFromPossibleRfc3966NumberUri(text, {\r\n\t\textractFormattedPhoneNumber: (text) => extractFormattedPhoneNumber(text, extract, v2)\r\n\t})\r\n\t// If the phone number is not viable, then abort.\r\n\tif (!number) {\r\n\t\treturn {}\r\n\t}\r\n\tif (!isViablePhoneNumber(number)) {\r\n\t\tif (isViablePhoneNumberStart(number)) {\r\n\t\t\treturn { error: 'TOO_SHORT' }\r\n\t\t}\r\n\t\treturn {}\r\n\t}\r\n\t// Attempt to parse extension first, since it doesn't require region-specific\r\n\t// data and we want to have the non-normalised number here.\r\n\tconst withExtensionStripped = extractExtension(number)\r\n\tif (withExtensionStripped.ext) {\r\n\t\treturn withExtensionStripped\r\n\t}\r\n\treturn { number }\r\n}\r\n\r\n/**\r\n * Creates `parse()` result object.\r\n */\r\nfunction result(country, nationalNumber, ext) {\r\n\tconst result = {\r\n\t\tcountry,\r\n\t\tphone: nationalNumber\r\n\t}\r\n\tif (ext) {\r\n\t\tresult.ext = ext\r\n\t}\r\n\treturn result\r\n}\r\n\r\n/**\r\n * Parses a viable phone number.\r\n * @param {string} formattedPhoneNumber — Example: \"(*************\".\r\n * @param {string} [defaultCountry]\r\n * @param {string} [defaultCallingCode]\r\n * @param {Metadata} metadata\r\n * @return {object} Returns `{ country: string?, countryCallingCode: string?, nationalNumber: string? }`.\r\n */\r\nfunction parsePhoneNumber(\r\n\tformattedPhoneNumber,\r\n\tdefaultCountry,\r\n\tdefaultCallingCode,\r\n\tmetadata\r\n) {\r\n\t// Extract calling code from phone number.\r\n\tlet { countryCallingCodeSource, countryCallingCode, number } = extractCountryCallingCode(\r\n\t\tparseIncompletePhoneNumber(formattedPhoneNumber),\r\n\t\tdefaultCountry,\r\n\t\tdefaultCallingCode,\r\n\t\tmetadata.metadata\r\n\t)\r\n\r\n\t// Choose a country by `countryCallingCode`.\r\n\tlet country\r\n\tif (countryCallingCode) {\r\n\t\tmetadata.selectNumberingPlan(countryCallingCode)\r\n\t}\r\n\t// If `formattedPhoneNumber` is passed in \"national\" format\r\n\t// then `number` is defined and `countryCallingCode` is `undefined`.\r\n\telse if (number && (defaultCountry || defaultCallingCode)) {\r\n\t\tmetadata.selectNumberingPlan(defaultCountry, defaultCallingCode)\r\n\t\tif (defaultCountry) {\r\n\t\t\tcountry = defaultCountry\r\n\t\t} else {\r\n\t\t\t/* istanbul ignore if */\r\n\t\t\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\t\t\tif (metadata.isNonGeographicCallingCode(defaultCallingCode)) {\r\n\t\t\t\t\tcountry = '001'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tcountryCallingCode = defaultCallingCode || getCountryCallingCode(defaultCountry, metadata.metadata)\r\n\t}\r\n\telse return {}\r\n\r\n\tif (!number) {\r\n\t\treturn {\r\n\t\t\tcountryCallingCodeSource,\r\n\t\t\tcountryCallingCode\r\n\t\t}\r\n\t}\r\n\r\n\tconst {\r\n\t\tnationalNumber,\r\n\t\tcarrierCode\r\n\t} = extractNationalNumber(\r\n\t\tparseIncompletePhoneNumber(number),\r\n\t\tmetadata\r\n\t)\r\n\r\n\t// Sometimes there are several countries\r\n\t// corresponding to the same country phone code\r\n\t// (e.g. NANPA countries all having `1` country phone code).\r\n\t// Therefore, to reliably determine the exact country,\r\n\t// national (significant) number should have been parsed first.\r\n\t//\r\n\t// When `metadata.json` is generated, all \"ambiguous\" country phone codes\r\n\t// get their countries populated with the full set of\r\n\t// \"phone number type\" regular expressions.\r\n\t//\r\n\tconst exactCountry = getCountryByCallingCode(countryCallingCode, {\r\n\t\tnationalNumber,\r\n\t\tdefaultCountry,\r\n\t\tmetadata\r\n\t})\r\n\tif (exactCountry) {\r\n\t\tcountry = exactCountry\r\n\t\t/* istanbul ignore if */\r\n\t\tif (exactCountry === '001') {\r\n\t\t\t// Can't happen with `USE_NON_GEOGRAPHIC_COUNTRY_CODE` being `false`.\r\n\t\t\t// If `USE_NON_GEOGRAPHIC_COUNTRY_CODE` is set to `true` for some reason,\r\n\t\t\t// then remove the \"istanbul ignore if\".\r\n\t\t} else {\r\n\t\t\tmetadata.country(country)\r\n\t\t}\r\n\t}\r\n\r\n\treturn {\r\n\t\tcountry,\r\n\t\tcountryCallingCode,\r\n\t\tcountryCallingCodeSource,\r\n\t\tnationalNumber,\r\n\t\tcarrierCode\r\n\t}\r\n}"], "mappings": "AAAA;AACA;AACA;AACA;AAEA,SACCA,YADD,EAECC,UAFD,EAGCC,kBAHD,EAICC,kBAJD,QAKO,gBALP;AAOA,OAAOC,UAAP,MAAuB,iBAAvB;AACA,OAAOC,QAAP,MAAqB,eAArB;AACA,OAAOC,mBAAP,IAA8BC,wBAA9B,QAA8D,kCAA9D;AACA,OAAOC,gBAAP,MAA6B,yCAA7B;AACA,OAAOC,0BAAP,MAAuC,iCAAvC;AACA,OAAOC,qBAAP,MAAkC,4BAAlC;AACA,SAASC,gBAAT,QAAiC,iBAAjC,C,CACA;;AACA,OAAOC,WAAP,MAAwB,kBAAxB;AACA,OAAOC,eAAP,MAA4B,8BAA5B;AACA,OAAOC,yBAAP,MAAsC,wCAAtC;AACA,OAAOC,qBAAP,MAAkC,oCAAlC;AACA,OAAOC,cAAP,MAA2B,6BAA3B;AACA,OAAOC,uBAAP,MAAoC,sCAApC;AACA,OAAOC,uDAAP,MAAoE,sEAApE,C,CAEA;AACA;;AACA,IAAMC,uBAAuB,GAAG,GAAhC,C,CAEA;;AACA,IAAMC,0BAA0B,GAAG,IAAIC,MAAJ,CAAW,MAAMpB,UAAN,GAAmBD,YAAnB,GAAkC,GAA7C,CAAnC,C,CAEA;AACA;AACA;;AACA,IAAMsB,8BAA8B,GAAG,IAAID,MAAJ,CAAW,OAAOrB,YAAP,GAAsB,GAAtB,GAA4B,KAAvC,CAAvC;AAEA,IAAMuB,+BAA+B,GAAG,KAAxC,C,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASC,KAATA,CAAeC,IAAf,EAAqBC,OAArB,EAA8BC,QAA9B,EAAwC;EACtD;EACA;EACAD,OAAO,GAAGA,OAAO,IAAI,EAArB;EAEAC,QAAQ,GAAG,IAAItB,QAAJ,CAAasB,QAAb,CAAX,CALsD,CAOtD;;EACA,IAAID,OAAO,CAACE,cAAR,IAA0B,CAACD,QAAQ,CAACE,UAAT,CAAoBH,OAAO,CAACE,cAA5B,CAA/B,EAA4E;IAC3E,IAAIF,OAAO,CAACI,EAAZ,EAAgB;MACf,MAAM,IAAI1B,UAAJ,CAAe,iBAAf,CAAN;IACA;IACD,MAAM,IAAI2B,KAAJ,qBAAAC,MAAA,CAA8BN,OAAO,CAACE,cAAtC,EAAN;EACA,CAbqD,CAetD;;EACA,IAAAK,WAAA,GAAqDC,UAAU,CAACT,IAAD,EAAOC,OAAO,CAACI,EAAf,EAAmBJ,OAAO,CAACS,OAA3B,CAA/D;IAAgBC,oBAAhB,GAAAH,WAAA,CAAQI,MAAR;IAAsCC,GAAtC,GAAAL,WAAA,CAAsCK,GAAtC;IAA2CC,KAA3C,GAAAN,WAAA,CAA2CM,KAA3C,CAhBsD,CAkBtD;;EACA,IAAI,CAACH,oBAAL,EAA2B;IAC1B,IAAIV,OAAO,CAACI,EAAZ,EAAgB;MACf,IAAIS,KAAK,KAAK,WAAd,EAA2B;QAC1B,MAAM,IAAInC,UAAJ,CAAe,WAAf,CAAN;MACA;MACD,MAAM,IAAIA,UAAJ,CAAe,cAAf,CAAN;IACA;IACD,OAAO,EAAP;EACA;EAED,IAAAoC,iBAAA,GAMIC,gBAAgB,CACnBL,oBADmB,EAEnBV,OAAO,CAACE,cAFW,EAGnBF,OAAO,CAACgB,kBAHW,EAInBf,QAJmB,CANpB;IACCgB,OADD,GAAAH,iBAAA,CACCG,OADD;IAECC,cAFD,GAAAJ,iBAAA,CAECI,cAFD;IAGCC,kBAHD,GAAAL,iBAAA,CAGCK,kBAHD;IAICC,wBAJD,GAAAN,iBAAA,CAICM,wBAJD;IAKCC,WALD,GAAAP,iBAAA,CAKCO,WALD;EAaA,IAAI,CAACpB,QAAQ,CAACqB,wBAAT,EAAL,EAA0C;IACzC,IAAItB,OAAO,CAACI,EAAZ,EAAgB;MACf,MAAM,IAAI1B,UAAJ,CAAe,iBAAf,CAAN;IACA;IACD,OAAO,EAAP;EACA,CA/CqD,CAiDtD;;EACA,IAAI,CAACwC,cAAD,IAAmBA,cAAc,CAACK,MAAf,GAAwB/C,kBAA/C,EAAmE;IAClE;;IACA;IACA,IAAIwB,OAAO,CAACI,EAAZ,EAAgB;MACf,MAAM,IAAI1B,UAAJ,CAAe,WAAf,CAAN;IACA,CALiE,CAMlE;;IACA,OAAO,EAAP;EACA,CA1DqD,CA4DtD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,IAAIwC,cAAc,CAACK,MAAf,GAAwB9C,kBAA5B,EAAgD;IAC/C,IAAIuB,OAAO,CAACI,EAAZ,EAAgB;MACf,MAAM,IAAI1B,UAAJ,CAAe,UAAf,CAAN;IACA,CAH8C,CAI/C;;IACA,OAAO,EAAP;EACA;EAED,IAAIsB,OAAO,CAACI,EAAZ,EAAgB;IACf,IAAMoB,WAAW,GAAG,IAAItC,WAAJ,CACnBiC,kBADmB,EAEnBD,cAFmB,EAGnBjB,QAAQ,CAACA,QAHU,CAApB;IAKA,IAAIgB,OAAJ,EAAa;MACZO,WAAW,CAACP,OAAZ,GAAsBA,OAAtB;IACA;IACD,IAAII,WAAJ,EAAiB;MAChBG,WAAW,CAACH,WAAZ,GAA0BA,WAA1B;IACA;IACD,IAAIT,GAAJ,EAAS;MACRY,WAAW,CAACZ,GAAZ,GAAkBA,GAAlB;IACA;IACDY,WAAW,CAACC,0BAAZ,GAAyCL,wBAAzC;IACA,OAAOI,WAAP;EACA,CA9FqD,CAgGtD;EACA;EACA;;EACA,IAAME,KAAK,GAAG,CAAC1B,OAAO,CAAC2B,QAAR,GAAmB1B,QAAQ,CAACqB,wBAAT,EAAnB,GAAyDL,OAA1D,IACb9B,eAAe,CAAC+B,cAAD,EAAiBjB,QAAQ,CAAC2B,qBAAT,EAAjB,CADF,GAEb,KAFD;EAIA,IAAI,CAAC5B,OAAO,CAAC2B,QAAb,EAAuB;IACtB,OAAOD,KAAK,GAAGG,MAAM,CAACZ,OAAD,EAAUC,cAAV,EAA0BN,GAA1B,CAAT,GAA0C,EAAtD;EACA,CAzGqD,CA2GtD;;EAEA,OAAO;IACNK,OAAO,EAAPA,OADM;IAENE,kBAAkB,EAAlBA,kBAFM;IAGNE,WAAW,EAAXA,WAHM;IAINK,KAAK,EAALA,KAJM;IAKNI,QAAQ,EAAEJ,KAAK,GAAG,IAAH,GACd1B,OAAO,CAAC2B,QAAR,KAAqB,IAArB,IACA1B,QAAQ,CAAC8B,eAAT,EADA,IAEA9C,gBAAgB,CAACiC,cAAD,EAAiBjB,QAAjB,CAFhB,GAE6C,IAF7C,GAEoD,KAR/C;IAUN+B,KAAK,EAAEd,cAVD;IAWNN,GAAG,EAAHA;EAXM,CAAP;AAaA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASqB,4BAATC,CAAqCnC,IAArC,EAA2CU,OAA3C,EAAoD0B,YAApD,EAAkE;EACjE,IAAI,CAACpC,IAAL,EAAW;IACV;EACA;EACD,IAAIA,IAAI,CAACwB,MAAL,GAAc9B,uBAAlB,EAA2C;IAC1C,IAAI0C,YAAJ,EAAkB;MACjB,MAAM,IAAIzD,UAAJ,CAAe,UAAf,CAAN;IACA;IACD;EACA;EACD,IAAI+B,OAAO,KAAK,KAAhB,EAAuB;IACtB,OAAOV,IAAP;EACA,CAZgE,CAajE;;EACA,IAAMqC,QAAQ,GAAGrC,IAAI,CAACsC,MAAL,CAAY3C,0BAAZ,CAAjB;EACA,IAAI0C,QAAQ,GAAG,CAAf,EAAkB;IACjB;EACA;EACD,OAAOrC,IAAI,CACV;EAAA,CACCuC,KAFK,CAECF,QAFD,EAGN;EAAA,CACCG,OAJK,CAIG3C,8BAJH,EAImC,EAJnC,CAAP;AAKA;AAED;AACA;AACA;AACA;AACA;AACA;;AACA,SAASY,UAATA,CAAoBT,IAApB,EAA0BK,EAA1B,EAA8BK,OAA9B,EAAuC;EACtC;EACA;EACA;EACA;EACA;EACA,IAAIE,MAAM,GAAGnB,uDAAuD,CAACO,IAAD,EAAO;IAC1EmC,2BAA2B,EAAE,SAAAA,4BAACnC,IAAD;MAAA,OAAUkC,4BAA2B,CAAClC,IAAD,EAAOU,OAAP,EAAgBL,EAAhB,CAArC;IAAA;EAD6C,CAAP,CAApE,CANsC,CAStC;;EACA,IAAI,CAACO,MAAL,EAAa;IACZ,OAAO,EAAP;EACA;EACD,IAAI,CAAC/B,mBAAmB,CAAC+B,MAAD,CAAxB,EAAkC;IACjC,IAAI9B,wBAAwB,CAAC8B,MAAD,CAA5B,EAAsC;MACrC,OAAO;QAAEE,KAAK,EAAE;MAAT,CAAP;IACA;IACD,OAAO,EAAP;EACA,CAlBqC,CAmBtC;EACA;;EACA,IAAM2B,qBAAqB,GAAG1D,gBAAgB,CAAC6B,MAAD,CAA9C;EACA,IAAI6B,qBAAqB,CAAC5B,GAA1B,EAA+B;IAC9B,OAAO4B,qBAAP;EACA;EACD,OAAO;IAAE7B,MAAM,EAANA;EAAF,CAAP;AACA;AAED;AACA;AACA;;AACA,SAASkB,MAATA,CAAgBZ,OAAhB,EAAyBC,cAAzB,EAAyCN,GAAzC,EAA8C;EAC7C,IAAMiB,MAAM,GAAG;IACdZ,OAAO,EAAPA,OADc;IAEde,KAAK,EAAEd;EAFO,CAAf;EAIA,IAAIN,GAAJ,EAAS;IACRiB,MAAM,CAACjB,GAAP,GAAaA,GAAb;EACA;EACD,OAAOiB,MAAP;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASd,gBAATA,CACCL,oBADD,EAECR,cAFD,EAGCc,kBAHD,EAICf,QAJD,EAKE;EACD;EACA,IAAAwC,qBAAA,GAA+DrD,yBAAyB,CACvFL,0BAA0B,CAAC2B,oBAAD,CAD6D,EAEvFR,cAFuF,EAGvFc,kBAHuF,EAIvFf,QAAQ,CAACA,QAJ8E,CAAxF;IAAMmB,wBAAN,GAAAqB,qBAAA,CAAMrB,wBAAN;IAAgCD,kBAAhC,GAAAsB,qBAAA,CAAgCtB,kBAAhC;IAAoDR,MAApD,GAAA8B,qBAAA,CAAoD9B,MAApD,CAFC,CASD;;EACA,IAAIM,OAAJ;EACA,IAAIE,kBAAJ,EAAwB;IACvBlB,QAAQ,CAACyC,mBAAT,CAA6BvB,kBAA7B;EACA,CAFD,CAGA;EACA;EAAA,KACK,IAAIR,MAAM,KAAKT,cAAc,IAAIc,kBAAvB,CAAV,EAAsD;IAC1Df,QAAQ,CAACyC,mBAAT,CAA6BxC,cAA7B,EAA6Cc,kBAA7C;IACA,IAAId,cAAJ,EAAoB;MACnBe,OAAO,GAAGf,cAAV;IACA,CAFD,MAEO;MACN;MACA,IAAIL,+BAAJ,EAAqC;QACpC,IAAII,QAAQ,CAAC0C,0BAAT,CAAoC3B,kBAApC,CAAJ,EAA6D;UAC5DC,OAAO,GAAG,KAAV;QACA;MACD;IACD;IACDE,kBAAkB,GAAGH,kBAAkB,IAAIhC,qBAAqB,CAACkB,cAAD,EAAiBD,QAAQ,CAACA,QAA1B,CAAhE;EACA,CAbI,MAcA,OAAO,EAAP;EAEL,IAAI,CAACU,MAAL,EAAa;IACZ,OAAO;MACNS,wBAAwB,EAAxBA,wBADM;MAEND,kBAAkB,EAAlBA;IAFM,CAAP;EAIA;EAED,IAAAyB,qBAAA,GAGIvD,qBAAqB,CACxBN,0BAA0B,CAAC4B,MAAD,CADF,EAExBV,QAFwB,CAHzB;IACCiB,cADD,GAAA0B,qBAAA,CACC1B,cADD;IAECG,WAFD,GAAAuB,qBAAA,CAECvB,WAFD,CAvCC,CA+CD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,IAAMwB,YAAY,GAAGtD,uBAAuB,CAAC4B,kBAAD,EAAqB;IAChED,cAAc,EAAdA,cADgE;IAEhEhB,cAAc,EAAdA,cAFgE;IAGhED,QAAQ,EAARA;EAHgE,CAArB,CAA5C;EAKA,IAAI4C,YAAJ,EAAkB;IACjB5B,OAAO,GAAG4B,YAAV;IACA;;IACA,IAAIA,YAAY,KAAK,KAArB,EAA4B,CAC3B;MACA;MACA;IAAA,CAHD,MAIO;MACN5C,QAAQ,CAACgB,OAAT,CAAiBA,OAAjB;IACA;EACD;EAED,OAAO;IACNA,OAAO,EAAPA,OADM;IAENE,kBAAkB,EAAlBA,kBAFM;IAGNC,wBAAwB,EAAxBA,wBAHM;IAINF,cAAc,EAAdA,cAJM;IAKNG,WAAW,EAAXA;EALM,CAAP;AAOA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}