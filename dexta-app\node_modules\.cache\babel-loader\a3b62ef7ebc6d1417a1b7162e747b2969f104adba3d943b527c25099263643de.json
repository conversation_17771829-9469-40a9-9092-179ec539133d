{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"defaultValue\", \"children\", \"component\", \"components\", \"componentsProps\", \"disabled\", \"error\", \"onChange\", \"required\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_useControlled as useControlled } from '@mui/utils';\nimport FormControlUnstyledContext from './FormControlUnstyledContext';\nimport { getFormControlUnstyledUtilityClass } from './formControlUnstyledClasses';\nimport { useSlotProps } from '../utils';\nimport composeClasses from '../composeClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction hasValue(value) {\n  return value != null && !(Array.isArray(value) && value.length === 0) && value !== '';\n}\nfunction useUtilityClasses(ownerState) {\n  const {\n    disabled,\n    error,\n    filled,\n    focused,\n    required\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', focused && 'focused', error && 'error', filled && 'filled', required && 'required']\n  };\n  return composeClasses(slots, getFormControlUnstyledUtilityClass, {});\n}\n/**\n * Provides context such as filled/focused/error/required for form inputs.\n * Relying on the context provides high flexibility and ensures that the state always stays\n * consistent across the children of the `FormControl`.\n * This context is used by the following components:\n *\n * *   FormLabel\n * *   FormHelperText\n * *   Input\n * *   InputLabel\n *\n * You can find one composition example below and more going to [the demos](https://mui.com/material-ui/react-text-field/#components).\n *\n * ```jsx\n * <FormControl>\n *   <InputLabel htmlFor=\"my-input\">Email address</InputLabel>\n *   <Input id=\"my-input\" aria-describedby=\"my-helper-text\" />\n *   <FormHelperText id=\"my-helper-text\">We'll never share your email.</FormHelperText>\n * </FormControl>\n * ```\n *\n * ⚠️ Only one `Input` can be used within a FormControl because it create visual inconsistencies.\n * For instance, only one input can be focused at the same time, the state shouldn't be shared.\n *\n * Demos:\n *\n * - [Unstyled form control](https://mui.com/base/react-form-control/)\n *\n * API:\n *\n * - [FormControlUnstyled API](https://mui.com/base/api/form-control-unstyled/)\n */\n\nconst FormControlUnstyled = /*#__PURE__*/React.forwardRef(function FormControlUnstyled(props, ref) {\n  var _ref;\n  const {\n      defaultValue,\n      children,\n      component,\n      components = {},\n      componentsProps = {},\n      disabled = false,\n      error = false,\n      onChange,\n      required = false,\n      value: incomingValue\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const [value, setValue] = useControlled({\n    controlled: incomingValue,\n    default: defaultValue,\n    name: 'FormControl',\n    state: 'value'\n  });\n  const filled = hasValue(value);\n  const [focused, setFocused] = React.useState(false);\n  if (disabled && focused) {\n    setFocused(false);\n  }\n  const ownerState = _extends({}, props, {\n    disabled,\n    error,\n    filled,\n    focused,\n    required\n  });\n  const handleChange = event => {\n    setValue(event.target.value);\n    onChange == null ? void 0 : onChange(event);\n  };\n  const childContext = {\n    disabled,\n    error,\n    filled,\n    focused,\n    onBlur: () => {\n      setFocused(false);\n    },\n    onChange: handleChange,\n    onFocus: () => {\n      setFocused(true);\n    },\n    required,\n    value: value != null ? value : ''\n  };\n  const classes = useUtilityClasses(ownerState);\n  const renderChildren = () => {\n    if (typeof children === 'function') {\n      return children(childContext);\n    }\n    return children;\n  };\n  const Root = (_ref = component != null ? component : components.Root) != null ? _ref : 'div';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref,\n      children: renderChildren()\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(FormControlUnstyledContext.Provider, {\n    value: childContext,\n    children: /*#__PURE__*/_jsx(Root, _extends({}, rootProps))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? FormControlUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * The content of the component.\n   */\n  children: PropTypes\n  /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.node, PropTypes.func]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the FormControl.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * @ignore\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * @ignore\n   */\n  defaultValue: PropTypes.any,\n  /**\n   * If `true`, the label, input and helper text should be displayed in a disabled state.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the label is displayed in an error state.\n   * @default false\n   */\n  error: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, the label will indicate that the `input` is required.\n   * @default false\n   */\n  required: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  value: PropTypes.any\n} : void 0;\nexport default FormControlUnstyled;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "unstable_useControlled", "useControlled", "FormControlUnstyledContext", "getFormControlUnstyledUtilityClass", "useSlotProps", "composeClasses", "jsx", "_jsx", "hasValue", "value", "Array", "isArray", "length", "useUtilityClasses", "ownerState", "disabled", "error", "filled", "focused", "required", "slots", "root", "FormControlUnstyled", "forwardRef", "props", "ref", "_ref", "defaultValue", "children", "component", "components", "componentsProps", "onChange", "incomingValue", "other", "setValue", "controlled", "default", "name", "state", "setFocused", "useState", "handleChange", "event", "target", "childContext", "onBlur", "onFocus", "classes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Root", "rootProps", "elementType", "externalSlotProps", "externalForwardedProps", "additionalProps", "className", "Provider", "process", "env", "NODE_ENV", "propTypes", "oneOfType", "node", "func", "shape", "object", "any", "bool"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/FormControlUnstyled/FormControlUnstyled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"defaultValue\", \"children\", \"component\", \"components\", \"componentsProps\", \"disabled\", \"error\", \"onChange\", \"required\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_useControlled as useControlled } from '@mui/utils';\nimport FormControlUnstyledContext from './FormControlUnstyledContext';\nimport { getFormControlUnstyledUtilityClass } from './formControlUnstyledClasses';\nimport { useSlotProps } from '../utils';\nimport composeClasses from '../composeClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n\nfunction hasValue(value) {\n  return value != null && !(Array.isArray(value) && value.length === 0) && value !== '';\n}\n\nfunction useUtilityClasses(ownerState) {\n  const {\n    disabled,\n    error,\n    filled,\n    focused,\n    required\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', focused && 'focused', error && 'error', filled && 'filled', required && 'required']\n  };\n  return composeClasses(slots, getFormControlUnstyledUtilityClass, {});\n}\n/**\n * Provides context such as filled/focused/error/required for form inputs.\n * Relying on the context provides high flexibility and ensures that the state always stays\n * consistent across the children of the `FormControl`.\n * This context is used by the following components:\n *\n * *   FormLabel\n * *   FormHelperText\n * *   Input\n * *   InputLabel\n *\n * You can find one composition example below and more going to [the demos](https://mui.com/material-ui/react-text-field/#components).\n *\n * ```jsx\n * <FormControl>\n *   <InputLabel htmlFor=\"my-input\">Email address</InputLabel>\n *   <Input id=\"my-input\" aria-describedby=\"my-helper-text\" />\n *   <FormHelperText id=\"my-helper-text\">We'll never share your email.</FormHelperText>\n * </FormControl>\n * ```\n *\n * ⚠️ Only one `Input` can be used within a FormControl because it create visual inconsistencies.\n * For instance, only one input can be focused at the same time, the state shouldn't be shared.\n *\n * Demos:\n *\n * - [Unstyled form control](https://mui.com/base/react-form-control/)\n *\n * API:\n *\n * - [FormControlUnstyled API](https://mui.com/base/api/form-control-unstyled/)\n */\n\n\nconst FormControlUnstyled = /*#__PURE__*/React.forwardRef(function FormControlUnstyled(props, ref) {\n  var _ref;\n\n  const {\n    defaultValue,\n    children,\n    component,\n    components = {},\n    componentsProps = {},\n    disabled = false,\n    error = false,\n    onChange,\n    required = false,\n    value: incomingValue\n  } = props,\n        other = _objectWithoutPropertiesLoose(props, _excluded);\n\n  const [value, setValue] = useControlled({\n    controlled: incomingValue,\n    default: defaultValue,\n    name: 'FormControl',\n    state: 'value'\n  });\n  const filled = hasValue(value);\n  const [focused, setFocused] = React.useState(false);\n\n  if (disabled && focused) {\n    setFocused(false);\n  }\n\n  const ownerState = _extends({}, props, {\n    disabled,\n    error,\n    filled,\n    focused,\n    required\n  });\n\n  const handleChange = event => {\n    setValue(event.target.value);\n    onChange == null ? void 0 : onChange(event);\n  };\n\n  const childContext = {\n    disabled,\n    error,\n    filled,\n    focused,\n    onBlur: () => {\n      setFocused(false);\n    },\n    onChange: handleChange,\n    onFocus: () => {\n      setFocused(true);\n    },\n    required,\n    value: value != null ? value : ''\n  };\n  const classes = useUtilityClasses(ownerState);\n\n  const renderChildren = () => {\n    if (typeof children === 'function') {\n      return children(childContext);\n    }\n\n    return children;\n  };\n\n  const Root = (_ref = component != null ? component : components.Root) != null ? _ref : 'div';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref,\n      children: renderChildren()\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(FormControlUnstyledContext.Provider, {\n    value: childContext,\n    children: /*#__PURE__*/_jsx(Root, _extends({}, rootProps))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? FormControlUnstyled.propTypes\n/* remove-proptypes */\n= {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * The content of the component.\n   */\n  children: PropTypes\n  /* @typescript-to-proptypes-ignore */\n  .oneOfType([PropTypes.node, PropTypes.func]),\n\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n\n  /**\n   * The components used for each slot inside the FormControl.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n\n  /**\n   * @ignore\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n\n  /**\n   * @ignore\n   */\n  defaultValue: PropTypes.any,\n\n  /**\n   * If `true`, the label, input and helper text should be displayed in a disabled state.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n\n  /**\n   * If `true`, the label is displayed in an error state.\n   * @default false\n   */\n  error: PropTypes.bool,\n\n  /**\n   * @ignore\n   */\n  onChange: PropTypes.func,\n\n  /**\n   * If `true`, the label will indicate that the `input` is required.\n   * @default false\n   */\n  required: PropTypes.bool,\n\n  /**\n   * @ignore\n   */\n  value: PropTypes.any\n} : void 0;\nexport default FormControlUnstyled;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,cAAc,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC;AAClJ,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,sBAAsB,IAAIC,aAAa,QAAQ,YAAY;AACpE,OAAOC,0BAA0B,MAAM,8BAA8B;AACrE,SAASC,kCAAkC,QAAQ,8BAA8B;AACjF,SAASC,YAAY,QAAQ,UAAU;AACvC,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAE/C,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,OAAOA,KAAK,IAAI,IAAI,IAAI,EAAEC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAAIA,KAAK,CAACG,MAAM,KAAK,CAAC,CAAC,IAAIH,KAAK,KAAK,EAAE;AACvF;AAEA,SAASI,iBAAiBA,CAACC,UAAU,EAAE;EACrC,MAAM;IACJC,QAAQ;IACRC,KAAK;IACLC,MAAM;IACNC,OAAO;IACPC;EACF,CAAC,GAAGL,UAAU;EACd,MAAMM,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEN,QAAQ,IAAI,UAAU,EAAEG,OAAO,IAAI,SAAS,EAAEF,KAAK,IAAI,OAAO,EAAEC,MAAM,IAAI,QAAQ,EAAEE,QAAQ,IAAI,UAAU;EAC3H,CAAC;EACD,OAAOd,cAAc,CAACe,KAAK,EAAEjB,kCAAkC,EAAE,CAAC,CAAC,CAAC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,MAAMmB,mBAAmB,GAAG,aAAaxB,KAAK,CAACyB,UAAU,CAAC,SAASD,mBAAmBA,CAACE,KAAK,EAAEC,GAAG,EAAE;EACjG,IAAIC,IAAI;EAER,MAAM;MACJC,YAAY;MACZC,QAAQ;MACRC,SAAS;MACTC,UAAU,GAAG,CAAC,CAAC;MACfC,eAAe,GAAG,CAAC,CAAC;MACpBhB,QAAQ,GAAG,KAAK;MAChBC,KAAK,GAAG,KAAK;MACbgB,QAAQ;MACRb,QAAQ,GAAG,KAAK;MAChBV,KAAK,EAAEwB;IACT,CAAC,GAAGT,KAAK;IACHU,KAAK,GAAGtC,6BAA6B,CAAC4B,KAAK,EAAE3B,SAAS,CAAC;EAE7D,MAAM,CAACY,KAAK,EAAE0B,QAAQ,CAAC,GAAGlC,aAAa,CAAC;IACtCmC,UAAU,EAAEH,aAAa;IACzBI,OAAO,EAAEV,YAAY;IACrBW,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAMtB,MAAM,GAAGT,QAAQ,CAACC,KAAK,CAAC;EAC9B,MAAM,CAACS,OAAO,EAAEsB,UAAU,CAAC,GAAG1C,KAAK,CAAC2C,QAAQ,CAAC,KAAK,CAAC;EAEnD,IAAI1B,QAAQ,IAAIG,OAAO,EAAE;IACvBsB,UAAU,CAAC,KAAK,CAAC;EACnB;EAEA,MAAM1B,UAAU,GAAGnB,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,EAAE;IACrCT,QAAQ;IACRC,KAAK;IACLC,MAAM;IACNC,OAAO;IACPC;EACF,CAAC,CAAC;EAEF,MAAMuB,YAAY,GAAGC,KAAK,IAAI;IAC5BR,QAAQ,CAACQ,KAAK,CAACC,MAAM,CAACnC,KAAK,CAAC;IAC5BuB,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACW,KAAK,CAAC;EAC7C,CAAC;EAED,MAAME,YAAY,GAAG;IACnB9B,QAAQ;IACRC,KAAK;IACLC,MAAM;IACNC,OAAO;IACP4B,MAAM,EAAEA,CAAA,KAAM;MACZN,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IACDR,QAAQ,EAAEU,YAAY;IACtBK,OAAO,EAAEA,CAAA,KAAM;MACbP,UAAU,CAAC,IAAI,CAAC;IAClB,CAAC;IACDrB,QAAQ;IACRV,KAAK,EAAEA,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG;EACjC,CAAC;EACD,MAAMuC,OAAO,GAAGnC,iBAAiB,CAACC,UAAU,CAAC;EAE7C,MAAMmC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,OAAOrB,QAAQ,KAAK,UAAU,EAAE;MAClC,OAAOA,QAAQ,CAACiB,YAAY,CAAC;IAC/B;IAEA,OAAOjB,QAAQ;EACjB,CAAC;EAED,MAAMsB,IAAI,GAAG,CAACxB,IAAI,GAAGG,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAGC,UAAU,CAACoB,IAAI,KAAK,IAAI,GAAGxB,IAAI,GAAG,KAAK;EAC5F,MAAMyB,SAAS,GAAG/C,YAAY,CAAC;IAC7BgD,WAAW,EAAEF,IAAI;IACjBG,iBAAiB,EAAEtB,eAAe,CAACV,IAAI;IACvCiC,sBAAsB,EAAEpB,KAAK;IAC7BqB,eAAe,EAAE;MACf9B,GAAG;MACHG,QAAQ,EAAEqB,cAAc,CAAC;IAC3B,CAAC;IACDnC,UAAU;IACV0C,SAAS,EAAER,OAAO,CAAC3B;EACrB,CAAC,CAAC;EACF,OAAO,aAAad,IAAI,CAACL,0BAA0B,CAACuD,QAAQ,EAAE;IAC5DhD,KAAK,EAAEoC,YAAY;IACnBjB,QAAQ,EAAE,aAAarB,IAAI,CAAC2C,IAAI,EAAEvD,QAAQ,CAAC,CAAC,CAAC,EAAEwD,SAAS,CAAC;EAC3D,CAAC,CAAC;AACJ,CAAC,CAAC;AACFO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGtC,mBAAmB,CAACuC;AAC5D,yBACE;EACA;EACA;EACA;EACA;;EAEA;AACF;AACA;EACEjC,QAAQ,EAAE7B;EACV,sCACC+D,SAAS,CAAC,CAAC/D,SAAS,CAACgE,IAAI,EAAEhE,SAAS,CAACiE,IAAI,CAAC,CAAC;EAE5C;AACF;AACA;AACA;EACEnC,SAAS,EAAE9B,SAAS,CAACqD,WAAW;EAEhC;AACF;AACA;AACA;AACA;EACEtB,UAAU,EAAE/B,SAAS,CAACkE,KAAK,CAAC;IAC1Bf,IAAI,EAAEnD,SAAS,CAACqD;EAClB,CAAC,CAAC;EAEF;AACF;AACA;EACErB,eAAe,EAAEhC,SAAS,CAACkE,KAAK,CAAC;IAC/B5C,IAAI,EAAEtB,SAAS,CAAC+D,SAAS,CAAC,CAAC/D,SAAS,CAACiE,IAAI,EAAEjE,SAAS,CAACmE,MAAM,CAAC;EAC9D,CAAC,CAAC;EAEF;AACF;AACA;EACEvC,YAAY,EAAE5B,SAAS,CAACoE,GAAG;EAE3B;AACF;AACA;AACA;EACEpD,QAAQ,EAAEhB,SAAS,CAACqE,IAAI;EAExB;AACF;AACA;AACA;EACEpD,KAAK,EAAEjB,SAAS,CAACqE,IAAI;EAErB;AACF;AACA;EACEpC,QAAQ,EAAEjC,SAAS,CAACiE,IAAI;EAExB;AACF;AACA;AACA;EACE7C,QAAQ,EAAEpB,SAAS,CAACqE,IAAI;EAExB;AACF;AACA;EACE3D,KAAK,EAAEV,SAAS,CAACoE;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,eAAe7C,mBAAmB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}