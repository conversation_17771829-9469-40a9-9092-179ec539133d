{"ast": null, "code": "import { config } from '../config';\nvar context = null;\nexport function errorContext(cb) {\n  if (config.useDeprecatedSynchronousErrorHandling) {\n    var isRoot = !context;\n    if (isRoot) {\n      context = {\n        errorThrown: false,\n        error: null\n      };\n    }\n    cb();\n    if (isRoot) {\n      var _a = context,\n        errorThrown = _a.errorThrown,\n        error = _a.error;\n      context = null;\n      if (errorThrown) {\n        throw error;\n      }\n    }\n  } else {\n    cb();\n  }\n}\nexport function captureError(err) {\n  if (config.useDeprecatedSynchronousErrorHandling && context) {\n    context.errorThrown = true;\n    context.error = err;\n  }\n}", "map": {"version": 3, "names": ["config", "context", "errorContext", "cb", "useDeprecatedSynchronousErrorHandling", "isRoot", "errorThrown", "error", "_a", "captureError", "err"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\util\\errorContext.ts"], "sourcesContent": ["import { config } from '../config';\n\nlet context: { errorThrown: boolean; error: any } | null = null;\n\n/**\n * <PERSON>les dealing with errors for super-gross mode. Creates a context, in which\n * any synchronously thrown errors will be passed to {@link captureError}. Which\n * will record the error such that it will be rethrown after the call back is complete.\n * TODO: Remove in v8\n * @param cb An immediately executed function.\n */\nexport function errorContext(cb: () => void) {\n  if (config.useDeprecatedSynchronousErrorHandling) {\n    const isRoot = !context;\n    if (isRoot) {\n      context = { errorThrown: false, error: null };\n    }\n    cb();\n    if (isRoot) {\n      const { errorThrown, error } = context!;\n      context = null;\n      if (errorThrown) {\n        throw error;\n      }\n    }\n  } else {\n    // This is the general non-deprecated path for everyone that\n    // isn't crazy enough to use super-gross mode (useDeprecatedSynchronousErrorHandling)\n    cb();\n  }\n}\n\n/**\n * Captures errors only in super-gross mode.\n * @param err the error to capture\n */\nexport function captureError(err: any) {\n  if (config.useDeprecatedSynchronousErrorHandling && context) {\n    context.errorThrown = true;\n    context.error = err;\n  }\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,WAAW;AAElC,IAAIC,OAAO,GAAgD,IAAI;AAS/D,OAAM,SAAUC,YAAYA,CAACC,EAAc;EACzC,IAAIH,MAAM,CAACI,qCAAqC,EAAE;IAChD,IAAMC,MAAM,GAAG,CAACJ,OAAO;IACvB,IAAII,MAAM,EAAE;MACVJ,OAAO,GAAG;QAAEK,WAAW,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAI,CAAE;;IAE/CJ,EAAE,EAAE;IACJ,IAAIE,MAAM,EAAE;MACJ,IAAAG,EAAA,GAAyBP,OAAQ;QAA/BK,WAAW,GAAAE,EAAA,CAAAF,WAAA;QAAEC,KAAK,GAAAC,EAAA,CAAAD,KAAa;MACvCN,OAAO,GAAG,IAAI;MACd,IAAIK,WAAW,EAAE;QACf,MAAMC,KAAK;;;GAGhB,MAAM;IAGLJ,EAAE,EAAE;;AAER;AAMA,OAAM,SAAUM,YAAYA,CAACC,GAAQ;EACnC,IAAIV,MAAM,CAACI,qCAAqC,IAAIH,OAAO,EAAE;IAC3DA,OAAO,CAACK,WAAW,GAAG,IAAI;IAC1BL,OAAO,CAACM,KAAK,GAAGG,GAAG;;AAEvB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}