{"ast": null, "code": "import * as React from 'react';\nimport FormControlUnstyledContext from './FormControlUnstyledContext';\nexport default function useFormControlUnstyledContext() {\n  return React.useContext(FormControlUnstyledContext);\n}", "map": {"version": 3, "names": ["React", "FormControlUnstyledContext", "useFormControlUnstyledContext", "useContext"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/FormControlUnstyled/useFormControlUnstyledContext.js"], "sourcesContent": ["import * as React from 'react';\nimport FormControlUnstyledContext from './FormControlUnstyledContext';\nexport default function useFormControlUnstyledContext() {\n  return React.useContext(FormControlUnstyledContext);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,0BAA0B,MAAM,8BAA8B;AACrE,eAAe,SAASC,6BAA6BA,CAAA,EAAG;EACtD,OAAOF,KAAK,CAACG,UAAU,CAACF,0BAA0B,CAAC;AACrD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}