{"ast": null, "code": "import { Subject } from '../Subject';\nimport { asyncScheduler } from '../scheduler/async';\nimport { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nimport { popScheduler } from '../util/args';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function windowTime(windowTimeSpan) {\n  var _a, _b;\n  var otherArgs = [];\n  for (var _i = 1; _i < arguments.length; _i++) {\n    otherArgs[_i - 1] = arguments[_i];\n  }\n  var scheduler = (_a = popScheduler(otherArgs)) !== null && _a !== void 0 ? _a : asyncScheduler;\n  var windowCreationInterval = (_b = otherArgs[0]) !== null && _b !== void 0 ? _b : null;\n  var maxWindowSize = otherArgs[1] || Infinity;\n  return operate(function (source, subscriber) {\n    var windowRecords = [];\n    var restartOnClose = false;\n    var closeWindow = function (record) {\n      var window = record.window,\n        subs = record.subs;\n      window.complete();\n      subs.unsubscribe();\n      arrRemove(windowRecords, record);\n      restartOnClose && startWindow();\n    };\n    var startWindow = function () {\n      if (windowRecords) {\n        var subs = new Subscription();\n        subscriber.add(subs);\n        var window_1 = new Subject();\n        var record_1 = {\n          window: window_1,\n          subs: subs,\n          seen: 0\n        };\n        windowRecords.push(record_1);\n        subscriber.next(window_1.asObservable());\n        executeSchedule(subs, scheduler, function () {\n          return closeWindow(record_1);\n        }, windowTimeSpan);\n      }\n    };\n    if (windowCreationInterval !== null && windowCreationInterval >= 0) {\n      executeSchedule(subscriber, scheduler, startWindow, windowCreationInterval, true);\n    } else {\n      restartOnClose = true;\n    }\n    startWindow();\n    var loop = function (cb) {\n      return windowRecords.slice().forEach(cb);\n    };\n    var terminate = function (cb) {\n      loop(function (_a) {\n        var window = _a.window;\n        return cb(window);\n      });\n      cb(subscriber);\n      subscriber.unsubscribe();\n    };\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      loop(function (record) {\n        record.window.next(value);\n        maxWindowSize <= ++record.seen && closeWindow(record);\n      });\n    }, function () {\n      return terminate(function (consumer) {\n        return consumer.complete();\n      });\n    }, function (err) {\n      return terminate(function (consumer) {\n        return consumer.error(err);\n      });\n    }));\n    return function () {\n      windowRecords = null;\n    };\n  });\n}", "map": {"version": 3, "names": ["Subject", "asyncScheduler", "Subscription", "operate", "createOperatorSubscriber", "arr<PERSON><PERSON><PERSON>", "popScheduler", "executeSchedule", "windowTime", "windowTimeSpan", "otherArgs", "_i", "arguments", "length", "scheduler", "_a", "windowCreationInterval", "_b", "maxWindowSize", "Infinity", "source", "subscriber", "windowRecords", "restartOnClose", "closeWindow", "record", "window", "subs", "complete", "unsubscribe", "startWindow", "add", "window_1", "record_1", "seen", "push", "next", "asObservable", "loop", "cb", "slice", "for<PERSON>ach", "terminate", "subscribe", "value", "consumer", "err", "error"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\windowTime.ts"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { asyncScheduler } from '../scheduler/async';\nimport { Observable } from '../Observable';\nimport { Subscription } from '../Subscription';\nimport { Observer, OperatorFunction, SchedulerLike } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { arrRemove } from '../util/arrRemove';\nimport { popScheduler } from '../util/args';\nimport { executeSchedule } from '../util/executeSchedule';\n\nexport function windowTime<T>(windowTimeSpan: number, scheduler?: SchedulerLike): OperatorFunction<T, Observable<T>>;\nexport function windowTime<T>(\n  windowTimeSpan: number,\n  windowCreationInterval: number,\n  scheduler?: SchedulerLike\n): OperatorFunction<T, Observable<T>>;\nexport function windowTime<T>(\n  windowTimeSpan: number,\n  windowCreationInterval: number | null | void,\n  maxWindowSize: number,\n  scheduler?: SchedulerLike\n): OperatorFunction<T, Observable<T>>;\n\n/**\n * Branch out the source Observable values as a nested Observable periodically\n * in time.\n *\n * <span class=\"informal\">It's like {@link bufferTime}, but emits a nested\n * Observable instead of an array.</span>\n *\n * ![](windowTime.png)\n *\n * Returns an Observable that emits windows of items it collects from the source\n * Observable. The output Observable starts a new window periodically, as\n * determined by the `windowCreationInterval` argument. It emits each window\n * after a fixed timespan, specified by the `windowTimeSpan` argument. When the\n * source Observable completes or encounters an error, the output Observable\n * emits the current window and propagates the notification from the source\n * Observable. If `windowCreationInterval` is not provided, the output\n * Observable starts a new window when the previous window of duration\n * `windowTimeSpan` completes. If `maxWindowCount` is provided, each window\n * will emit at most fixed number of values. Window will complete immediately\n * after emitting last value and next one still will open as specified by\n * `windowTimeSpan` and `windowCreationInterval` arguments.\n *\n * ## Examples\n *\n * In every window of 1 second each, emit at most 2 click events\n *\n * ```ts\n * import { fromEvent, windowTime, map, take, mergeAll } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(\n *   windowTime(1000),\n *   map(win => win.pipe(take(2))), // take at most 2 emissions from each window\n *   mergeAll()                     // flatten the Observable-of-Observables\n * );\n * result.subscribe(x => console.log(x));\n * ```\n *\n * Every 5 seconds start a window 1 second long, and emit at most 2 click events per window\n *\n * ```ts\n * import { fromEvent, windowTime, map, take, mergeAll } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(\n *   windowTime(1000, 5000),\n *   map(win => win.pipe(take(2))), // take at most 2 emissions from each window\n *   mergeAll()                     // flatten the Observable-of-Observables\n * );\n * result.subscribe(x => console.log(x));\n * ```\n *\n * Same as example above but with `maxWindowCount` instead of `take`\n *\n * ```ts\n * import { fromEvent, windowTime, mergeAll } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(\n *   windowTime(1000, 5000, 2), // take at most 2 emissions from each window\n *   mergeAll()                 // flatten the Observable-of-Observables\n * );\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link window}\n * @see {@link windowCount}\n * @see {@link windowToggle}\n * @see {@link windowWhen}\n * @see {@link bufferTime}\n *\n * @param windowTimeSpan The amount of time, in milliseconds, to fill each window.\n * @param windowCreationInterval The interval at which to start new\n * windows.\n * @param maxWindowSize Max number of\n * values each window can emit before completion.\n * @param scheduler The scheduler on which to schedule the\n * intervals that determine window boundaries.\n * @return A function that returns an Observable of windows, which in turn are\n * Observables.\n */\nexport function windowTime<T>(windowTimeSpan: number, ...otherArgs: any[]): OperatorFunction<T, Observable<T>> {\n  const scheduler = popScheduler(otherArgs) ?? asyncScheduler;\n  const windowCreationInterval = (otherArgs[0] as number) ?? null;\n  const maxWindowSize = (otherArgs[1] as number) || Infinity;\n\n  return operate((source, subscriber) => {\n    // The active windows, their related subscriptions, and removal functions.\n    let windowRecords: WindowRecord<T>[] | null = [];\n    // If true, it means that every time we close a window, we want to start a new window.\n    // This is only really used for when *just* the time span is passed.\n    let restartOnClose = false;\n\n    const closeWindow = (record: { window: Subject<T>; subs: Subscription }) => {\n      const { window, subs } = record;\n      window.complete();\n      subs.unsubscribe();\n      arrRemove(windowRecords, record);\n      restartOnClose && startWindow();\n    };\n\n    /**\n     * Called every time we start a new window. This also does\n     * the work of scheduling the job to close the window.\n     */\n    const startWindow = () => {\n      if (windowRecords) {\n        const subs = new Subscription();\n        subscriber.add(subs);\n        const window = new Subject<T>();\n        const record = {\n          window,\n          subs,\n          seen: 0,\n        };\n        windowRecords.push(record);\n        subscriber.next(window.asObservable());\n        executeSchedule(subs, scheduler, () => closeWindow(record), windowTimeSpan);\n      }\n    };\n\n    if (windowCreationInterval !== null && windowCreationInterval >= 0) {\n      // The user passed both a windowTimeSpan (required), and a creation interval\n      // That means we need to start new window on the interval, and those windows need\n      // to wait the required time span before completing.\n      executeSchedule(subscriber, scheduler, startWindow, windowCreationInterval, true);\n    } else {\n      restartOnClose = true;\n    }\n\n    startWindow();\n\n    /**\n     * We need to loop over a copy of the window records several times in this operator.\n     * This is to save bytes over the wire more than anything.\n     * The reason we copy the array is that reentrant code could mutate the array while\n     * we are iterating over it.\n     */\n    const loop = (cb: (record: WindowRecord<T>) => void) => windowRecords!.slice().forEach(cb);\n\n    /**\n     * Used to notify all of the windows and the subscriber in the same way\n     * in the error and complete handlers.\n     */\n    const terminate = (cb: (consumer: Observer<any>) => void) => {\n      loop(({ window }) => cb(window));\n      cb(subscriber);\n      subscriber.unsubscribe();\n    };\n\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (value: T) => {\n          // Notify all windows of the value.\n          loop((record) => {\n            record.window.next(value);\n            // If the window is over the max size, we need to close it.\n            maxWindowSize <= ++record.seen && closeWindow(record);\n          });\n        },\n        // Complete the windows and the downstream subscriber and clean up.\n        () => terminate((consumer) => consumer.complete()),\n        // Notify the windows and the downstream subscriber of the error and clean up.\n        (err) => terminate((consumer) => consumer.error(err))\n      )\n    );\n\n    // Additional finalization. This will be called when the\n    // destination tears down. Other finalizations are registered implicitly\n    // above via subscription.\n    return () => {\n      // Ensure that the buffer is released.\n      windowRecords = null!;\n    };\n  });\n}\n\ninterface WindowRecord<T> {\n  seen: number;\n  window: Subject<T>;\n  subs: Subscription;\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,cAAc,QAAQ,oBAAoB;AAEnD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,YAAY,QAAQ,cAAc;AAC3C,SAASC,eAAe,QAAQ,yBAAyB;AAgGzD,OAAM,SAAUC,UAAUA,CAAIC,cAAsB;;EAAE,IAAAC,SAAA;OAAA,IAAAC,EAAA,IAAmB,EAAnBA,EAAA,GAAAC,SAAA,CAAAC,MAAmB,EAAnBF,EAAA,EAAmB;IAAnBD,SAAA,CAAAC,EAAA,QAAAC,SAAA,CAAAD,EAAA;;EACpD,IAAMG,SAAS,GAAG,CAAAC,EAAA,GAAAT,YAAY,CAACI,SAAS,CAAC,cAAAK,EAAA,cAAAA,EAAA,GAAId,cAAc;EAC3D,IAAMe,sBAAsB,GAAG,CAAAC,EAAA,GAACP,SAAS,CAAC,CAAC,CAAY,cAAAO,EAAA,cAAAA,EAAA,GAAI,IAAI;EAC/D,IAAMC,aAAa,GAAIR,SAAS,CAAC,CAAC,CAAY,IAAIS,QAAQ;EAE1D,OAAOhB,OAAO,CAAC,UAACiB,MAAM,EAAEC,UAAU;IAEhC,IAAIC,aAAa,GAA6B,EAAE;IAGhD,IAAIC,cAAc,GAAG,KAAK;IAE1B,IAAMC,WAAW,GAAG,SAAAA,CAACC,MAAkD;MAC7D,IAAAC,MAAM,GAAWD,MAAM,CAAAC,MAAjB;QAAEC,IAAI,GAAKF,MAAM,CAAAE,IAAX;MACpBD,MAAM,CAACE,QAAQ,EAAE;MACjBD,IAAI,CAACE,WAAW,EAAE;MAClBxB,SAAS,CAACiB,aAAa,EAAEG,MAAM,CAAC;MAChCF,cAAc,IAAIO,WAAW,EAAE;IACjC,CAAC;IAMD,IAAMA,WAAW,GAAG,SAAAA,CAAA;MAClB,IAAIR,aAAa,EAAE;QACjB,IAAMK,IAAI,GAAG,IAAIzB,YAAY,EAAE;QAC/BmB,UAAU,CAACU,GAAG,CAACJ,IAAI,CAAC;QACpB,IAAMK,QAAM,GAAG,IAAIhC,OAAO,EAAK;QAC/B,IAAMiC,QAAM,GAAG;UACbP,MAAM,EAAAM,QAAA;UACNL,IAAI,EAAAA,IAAA;UACJO,IAAI,EAAE;SACP;QACDZ,aAAa,CAACa,IAAI,CAACF,QAAM,CAAC;QAC1BZ,UAAU,CAACe,IAAI,CAACJ,QAAM,CAACK,YAAY,EAAE,CAAC;QACtC9B,eAAe,CAACoB,IAAI,EAAEb,SAAS,EAAE;UAAM,OAAAU,WAAW,CAACS,QAAM,CAAC;QAAnB,CAAmB,EAAExB,cAAc,CAAC;;IAE/E,CAAC;IAED,IAAIO,sBAAsB,KAAK,IAAI,IAAIA,sBAAsB,IAAI,CAAC,EAAE;MAIlET,eAAe,CAACc,UAAU,EAAEP,SAAS,EAAEgB,WAAW,EAAEd,sBAAsB,EAAE,IAAI,CAAC;KAClF,MAAM;MACLO,cAAc,GAAG,IAAI;;IAGvBO,WAAW,EAAE;IAQb,IAAMQ,IAAI,GAAG,SAAAA,CAACC,EAAqC;MAAK,OAAAjB,aAAc,CAACkB,KAAK,EAAE,CAACC,OAAO,CAACF,EAAE,CAAC;IAAlC,CAAkC;IAM1F,IAAMG,SAAS,GAAG,SAAAA,CAACH,EAAqC;MACtDD,IAAI,CAAC,UAACvB,EAAU;YAARW,MAAM,GAAAX,EAAA,CAAAW,MAAA;QAAO,OAAAa,EAAE,CAACb,MAAM,CAAC;MAAV,CAAU,CAAC;MAChCa,EAAE,CAAClB,UAAU,CAAC;MACdA,UAAU,CAACQ,WAAW,EAAE;IAC1B,CAAC;IAEDT,MAAM,CAACuB,SAAS,CACdvC,wBAAwB,CACtBiB,UAAU,EACV,UAACuB,KAAQ;MAEPN,IAAI,CAAC,UAACb,MAAM;QACVA,MAAM,CAACC,MAAM,CAACU,IAAI,CAACQ,KAAK,CAAC;QAEzB1B,aAAa,IAAI,EAAEO,MAAM,CAACS,IAAI,IAAIV,WAAW,CAACC,MAAM,CAAC;MACvD,CAAC,CAAC;IACJ,CAAC,EAED;MAAM,OAAAiB,SAAS,CAAC,UAACG,QAAQ;QAAK,OAAAA,QAAQ,CAACjB,QAAQ,EAAE;MAAnB,CAAmB,CAAC;IAA5C,CAA4C,EAElD,UAACkB,GAAG;MAAK,OAAAJ,SAAS,CAAC,UAACG,QAAQ;QAAK,OAAAA,QAAQ,CAACE,KAAK,CAACD,GAAG,CAAC;MAAnB,CAAmB,CAAC;IAA5C,CAA4C,CACtD,CACF;IAKD,OAAO;MAELxB,aAAa,GAAG,IAAK;IACvB,CAAC;EACH,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}