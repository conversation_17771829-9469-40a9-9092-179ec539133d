{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nimport Metadata from '../metadata.js';\nimport getNumberType from './getNumberType.js';\nexport default function getCountryByNationalNumber(nationalPhoneNumber, _ref) {\n  var countries = _ref.countries,\n    defaultCountry = _ref.defaultCountry,\n    metadata = _ref.metadata;\n  // Re-create `metadata` because it will be selecting a `country`.\n  metadata = new Metadata(metadata);\n  var matchingCountries = [];\n  for (var _iterator = _createForOfIteratorHelperLoose(countries), _step; !(_step = _iterator()).done;) {\n    var country = _step.value;\n    metadata.country(country); // \"Leading digits\" patterns are only defined for about 20% of all countries.\n    // By definition, matching \"leading digits\" is a sufficient but not a necessary\n    // condition for a phone number to belong to a country.\n    // The point of \"leading digits\" check is that it's the fastest one to get a match.\n    // https://gitlab.com/catamphetamine/libphonenumber-js/blob/master/METADATA.md#leading_digits\n    // I'd suppose that \"leading digits\" patterns are mutually exclusive for different countries\n    // because of the intended use of that feature.\n\n    if (metadata.leadingDigits()) {\n      if (nationalPhoneNumber && nationalPhoneNumber.search(metadata.leadingDigits()) === 0) {\n        return country;\n      }\n    } // Else perform full validation with all of those\n    // fixed-line/mobile/etc regular expressions.\n    else if (getNumberType({\n      phone: nationalPhoneNumber,\n      country: country\n    }, undefined, metadata.metadata)) {\n      // If the `defaultCountry` is among the `matchingCountries` then return it.\n      if (defaultCountry) {\n        if (country === defaultCountry) {\n          return country;\n        }\n        matchingCountries.push(country);\n      } else {\n        return country;\n      }\n    }\n  } // Return the first (\"main\") one of the `matchingCountries`.\n\n  if (matchingCountries.length > 0) {\n    return matchingCountries[0];\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "getNumberType", "getCountryByNationalNumber", "nationalPhoneNumber", "_ref", "countries", "defaultCountry", "metadata", "matchingCountries", "_iterator", "_createForOfIteratorHelperLoose", "_step", "done", "country", "value", "leadingDigits", "search", "phone", "undefined", "push", "length"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\helpers\\getCountryByNationalNumber.js"], "sourcesContent": ["import Metadata from '../metadata.js'\r\nimport getNumberType from './getNumberType.js'\r\n\r\nexport default function getCountryByNationalNumber(nationalPhoneNumber, {\r\n\tcountries,\r\n\tdefaultCountry,\r\n\tmetadata\r\n}) {\r\n\t// Re-create `metadata` because it will be selecting a `country`.\r\n\tmetadata = new Metadata(metadata)\r\n\r\n\tconst matchingCountries = []\r\n\r\n\tfor (const country of countries) {\r\n\t\tmetadata.country(country)\r\n\t\t// \"Leading digits\" patterns are only defined for about 20% of all countries.\r\n\t\t// By definition, matching \"leading digits\" is a sufficient but not a necessary\r\n\t\t// condition for a phone number to belong to a country.\r\n\t\t// The point of \"leading digits\" check is that it's the fastest one to get a match.\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/blob/master/METADATA.md#leading_digits\r\n\t\t// I'd suppose that \"leading digits\" patterns are mutually exclusive for different countries\r\n\t\t// because of the intended use of that feature.\r\n\t\tif (metadata.leadingDigits()) {\r\n\t\t\tif (nationalPhoneNumber &&\r\n\t\t\t\tnationalPhoneNumber.search(metadata.leadingDigits()) === 0) {\r\n\t\t\t\treturn country\r\n\t\t\t}\r\n\t\t}\r\n\t\t// Else perform full validation with all of those\r\n\t\t// fixed-line/mobile/etc regular expressions.\r\n\t\telse if (getNumberType({ phone: nationalPhoneNumber, country }, undefined, metadata.metadata)) {\r\n\t\t\t// If the `defaultCountry` is among the `matchingCountries` then return it.\r\n\t\t\tif (defaultCountry) {\r\n\t\t\t\tif (country === defaultCountry) {\r\n\t\t\t\t\treturn country\r\n\t\t\t\t}\r\n\t\t\t\tmatchingCountries.push(country)\r\n\t\t\t} else {\r\n\t\t\t\treturn country\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// Return the first (\"main\") one of the `matchingCountries`.\r\n\tif (matchingCountries.length > 0) {\r\n\t\treturn matchingCountries[0]\r\n\t}\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,QAAP,MAAqB,gBAArB;AACA,OAAOC,aAAP,MAA0B,oBAA1B;AAEA,eAAe,SAASC,0BAATA,CAAoCC,mBAApC,EAAAC,IAAA,EAIZ;EAAA,IAHFC,SAGE,GAAAD,IAAA,CAHFC,SAGE;IAFFC,cAEE,GAAAF,IAAA,CAFFE,cAEE;IADFC,QACE,GAAAH,IAAA,CADFG,QACE;EACF;EACAA,QAAQ,GAAG,IAAIP,QAAJ,CAAaO,QAAb,CAAX;EAEA,IAAMC,iBAAiB,GAAG,EAA1B;EAEA,SAAAC,SAAA,GAAAC,+BAAA,CAAsBL,SAAtB,GAAAM,KAAA,IAAAA,KAAA,GAAAF,SAAA,IAAAG,IAAA,GAAiC;IAAA,IAAtBC,OAAsB,GAAAF,KAAA,CAAAG,KAAA;IAChCP,QAAQ,CAACM,OAAT,CAAiBA,OAAjB,EADgC,CAEhC;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,IAAIN,QAAQ,CAACQ,aAAT,EAAJ,EAA8B;MAC7B,IAAIZ,mBAAmB,IACtBA,mBAAmB,CAACa,MAApB,CAA2BT,QAAQ,CAACQ,aAAT,EAA3B,MAAyD,CAD1D,EAC6D;QAC5D,OAAOF,OAAP;MACA;IACD,CALD,CAMA;IACA;IAAA,KACK,IAAIZ,aAAa,CAAC;MAAEgB,KAAK,EAAEd,mBAAT;MAA8BU,OAAO,EAAPA;IAA9B,CAAD,EAA0CK,SAA1C,EAAqDX,QAAQ,CAACA,QAA9D,CAAjB,EAA0F;MAC9F;MACA,IAAID,cAAJ,EAAoB;QACnB,IAAIO,OAAO,KAAKP,cAAhB,EAAgC;UAC/B,OAAOO,OAAP;QACA;QACDL,iBAAiB,CAACW,IAAlB,CAAuBN,OAAvB;MACA,CALD,MAKO;QACN,OAAOA,OAAP;MACA;IACD;EACD,CAlCC,CAoCF;;EACA,IAAIL,iBAAiB,CAACY,MAAlB,GAA2B,CAA/B,EAAkC;IACjC,OAAOZ,iBAAiB,CAAC,CAAD,CAAxB;EACA;AACD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}