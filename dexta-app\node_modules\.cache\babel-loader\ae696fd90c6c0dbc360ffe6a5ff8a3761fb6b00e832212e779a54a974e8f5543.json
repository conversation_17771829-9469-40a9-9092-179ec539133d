{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { concat } from '../observable/concat';\nimport { of } from '../observable/of';\nexport function endWith() {\n  var values = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    values[_i] = arguments[_i];\n  }\n  return function (source) {\n    return concat(source, of.apply(void 0, __spreadArray([], __read(values))));\n  };\n}", "map": {"version": 3, "names": ["concat", "of", "endWith", "values", "_i", "arguments", "length", "source", "apply", "__spread<PERSON><PERSON>y", "__read"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\endWith.ts"], "sourcesContent": ["/** prettier */\nimport { Observable } from '../Observable';\nimport { concat } from '../observable/concat';\nimport { of } from '../observable/of';\nimport { MonoTypeOperatorFunction, SchedulerLike, OperatorFunction, ValueFromArray } from '../types';\n\n/** @deprecated The `scheduler` parameter will be removed in v8. Use `scheduled` and `concatAll`. Details: https://rxjs.dev/deprecations/scheduler-argument */\nexport function endWith<T>(scheduler: SchedulerLike): MonoTypeOperatorFunction<T>;\n/** @deprecated The `scheduler` parameter will be removed in v8. Use `scheduled` and `concatAll`. Details: https://rxjs.dev/deprecations/scheduler-argument */\nexport function endWith<T, A extends unknown[] = T[]>(\n  ...valuesAndScheduler: [...A, SchedulerLike]\n): OperatorFunction<T, T | ValueFromArray<A>>;\n\nexport function endWith<T, A extends unknown[] = T[]>(...values: A): OperatorFunction<T, T | ValueFromArray<A>>;\n\n/**\n * Returns an observable that will emit all values from the source, then synchronously emit\n * the provided value(s) immediately after the source completes.\n *\n * NOTE: Passing a last argument of a Scheduler is _deprecated_, and may result in incorrect\n * types in TypeScript.\n *\n * This is useful for knowing when an observable ends. Particularly when paired with an\n * operator like {@link takeUntil}\n *\n * ![](endWith.png)\n *\n * ## Example\n *\n * Emit values to know when an interval starts and stops. The interval will\n * stop when a user clicks anywhere on the document.\n *\n * ```ts\n * import { interval, map, fromEvent, startWith, takeUntil, endWith } from 'rxjs';\n *\n * const ticker$ = interval(5000).pipe(\n *   map(() => 'tick')\n * );\n *\n * const documentClicks$ = fromEvent(document, 'click');\n *\n * ticker$.pipe(\n *   startWith('interval started'),\n *   takeUntil(documentClicks$),\n *   endWith('interval ended by click')\n * )\n * .subscribe(x => console.log(x));\n *\n * // Result (assuming a user clicks after 15 seconds)\n * // 'interval started'\n * // 'tick'\n * // 'tick'\n * // 'tick'\n * // 'interval ended by click'\n * ```\n *\n * @see {@link startWith}\n * @see {@link concat}\n * @see {@link takeUntil}\n *\n * @param values Items you want the modified Observable to emit last.\n * @return A function that returns an Observable that emits all values from the\n * source, then synchronously emits the provided value(s) immediately after the\n * source completes.\n */\nexport function endWith<T>(...values: Array<T | SchedulerLike>): MonoTypeOperatorFunction<T> {\n  return (source: Observable<T>) => concat(source, of(...values)) as Observable<T>;\n}\n"], "mappings": ";AAEA,SAASA,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,EAAE,QAAQ,kBAAkB;AA8DrC,OAAM,SAAUC,OAAOA,CAAA;EAAI,IAAAC,MAAA;OAAA,IAAAC,EAAA,IAAmC,EAAnCA,EAAA,GAAAC,SAAA,CAAAC,MAAmC,EAAnCF,EAAA,EAAmC;IAAnCD,MAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EACzB,OAAO,UAACG,MAAqB;IAAK,OAAAP,MAAM,CAACO,MAAM,EAAEN,EAAE,CAAAO,KAAA,SAAAC,aAAA,KAAAC,MAAA,CAAIP,MAAM,IAAmB;EAA9C,CAA8C;AAClF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}