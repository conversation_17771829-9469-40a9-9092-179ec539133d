{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '@mui/base';\nexport function getSwitchBaseUtilityClass(slot) {\n  return generateUtilityClass('PrivateSwitchBase', slot);\n}\nconst switchBaseClasses = generateUtilityClasses('PrivateSwitchBase', ['root', 'checked', 'disabled', 'input', 'edgeStart', 'edgeEnd']);\nexport default switchBaseClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getSwitchBaseUtilityClass", "slot", "switchBaseClasses"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/material/esm/internal/switchBaseClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '@mui/base';\nexport function getSwitchBaseUtilityClass(slot) {\n  return generateUtilityClass('PrivateSwitchBase', slot);\n}\nconst switchBaseClasses = generateUtilityClasses('PrivateSwitchBase', ['root', 'checked', 'disabled', 'input', 'edgeStart', 'edgeEnd']);\nexport default switchBaseClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,WAAW;AACxE,OAAO,SAASC,yBAAyBA,CAACC,IAAI,EAAE;EAC9C,OAAOH,oBAAoB,CAAC,mBAAmB,EAAEG,IAAI,CAAC;AACxD;AACA,MAAMC,iBAAiB,GAAGH,sBAAsB,CAAC,mBAAmB,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;AACvI,eAAeG,iBAAiB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}