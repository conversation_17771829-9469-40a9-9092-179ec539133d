{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n\n/* eslint-disable no-constant-condition */\nimport * as React from 'react';\nimport { unstable_setRef as setRef, unstable_useEventCallback as useEventCallback, unstable_useControlled as useControlled, unstable_useId as useId } from '@mui/utils'; // https://stackoverflow.com/questions/990904/remove-accents-diacritics-in-a-string-in-javascript\n// Give up on IE11 support for this feature\n\nfunction stripDiacritics(string) {\n  return typeof string.normalize !== 'undefined' ? string.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '') : string;\n}\nexport function createFilterOptions(config = {}) {\n  const {\n    ignoreAccents = true,\n    ignoreCase = true,\n    limit,\n    matchFrom = 'any',\n    stringify,\n    trim = false\n  } = config;\n  return (options, {\n    inputValue,\n    getOptionLabel\n  }) => {\n    let input = trim ? inputValue.trim() : inputValue;\n    if (ignoreCase) {\n      input = input.toLowerCase();\n    }\n    if (ignoreAccents) {\n      input = stripDiacritics(input);\n    }\n    const filteredOptions = options.filter(option => {\n      let candidate = (stringify || getOptionLabel)(option);\n      if (ignoreCase) {\n        candidate = candidate.toLowerCase();\n      }\n      if (ignoreAccents) {\n        candidate = stripDiacritics(candidate);\n      }\n      return matchFrom === 'start' ? candidate.indexOf(input) === 0 : candidate.indexOf(input) > -1;\n    });\n    return typeof limit === 'number' ? filteredOptions.slice(0, limit) : filteredOptions;\n  };\n} // To replace with .findIndex() once we stop IE11 support.\n\nfunction findIndex(array, comp) {\n  for (let i = 0; i < array.length; i += 1) {\n    if (comp(array[i])) {\n      return i;\n    }\n  }\n  return -1;\n}\nconst defaultFilterOptions = createFilterOptions(); // Number of options to jump in list box when pageup and pagedown keys are used.\n\nconst pageSize = 5;\nexport default function useAutocomplete(props) {\n  const {\n    autoComplete = false,\n    autoHighlight = false,\n    autoSelect = false,\n    blurOnSelect = false,\n    clearOnBlur = !props.freeSolo,\n    clearOnEscape = false,\n    componentName = 'useAutocomplete',\n    defaultValue = props.multiple ? [] : null,\n    disableClearable = false,\n    disableCloseOnSelect = false,\n    disabled: disabledProp,\n    disabledItemsFocusable = false,\n    disableListWrap = false,\n    filterOptions = defaultFilterOptions,\n    filterSelectedOptions = false,\n    freeSolo = false,\n    getOptionDisabled,\n    getOptionLabel: getOptionLabelProp = option => {\n      var _option$label;\n      return (_option$label = option.label) != null ? _option$label : option;\n    },\n    groupBy,\n    handleHomeEndKeys = !props.freeSolo,\n    id: idProp,\n    includeInputInList = false,\n    inputValue: inputValueProp,\n    isOptionEqualToValue = (option, value) => option === value,\n    multiple = false,\n    onChange,\n    onClose,\n    onHighlightChange,\n    onInputChange,\n    onOpen,\n    open: openProp,\n    openOnFocus = false,\n    options,\n    readOnly = false,\n    selectOnFocus = !props.freeSolo,\n    value: valueProp\n  } = props;\n  const id = useId(idProp);\n  let getOptionLabel = getOptionLabelProp;\n  getOptionLabel = option => {\n    const optionLabel = getOptionLabelProp(option);\n    if (typeof optionLabel !== 'string') {\n      if (process.env.NODE_ENV !== 'production') {\n        const erroneousReturn = optionLabel === undefined ? 'undefined' : `${typeof optionLabel} (${optionLabel})`;\n        console.error(`MUI: The \\`getOptionLabel\\` method of ${componentName} returned ${erroneousReturn} instead of a string for ${JSON.stringify(option)}.`);\n      }\n      return String(optionLabel);\n    }\n    return optionLabel;\n  };\n  const ignoreFocus = React.useRef(false);\n  const firstFocus = React.useRef(true);\n  const inputRef = React.useRef(null);\n  const listboxRef = React.useRef(null);\n  const [anchorEl, setAnchorEl] = React.useState(null);\n  const [focusedTag, setFocusedTag] = React.useState(-1);\n  const defaultHighlighted = autoHighlight ? 0 : -1;\n  const highlightedIndexRef = React.useRef(defaultHighlighted);\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: componentName\n  });\n  const [inputValue, setInputValueState] = useControlled({\n    controlled: inputValueProp,\n    default: '',\n    name: componentName,\n    state: 'inputValue'\n  });\n  const [focused, setFocused] = React.useState(false);\n  const resetInputValue = React.useCallback((event, newValue) => {\n    // retain current `inputValue` if new option isn't selected and `clearOnBlur` is false\n    // When `multiple` is enabled, `newValue` is an array of all selected items including the newly selected item\n    const isOptionSelected = multiple ? value.length < newValue.length : newValue !== null;\n    if (!isOptionSelected && !clearOnBlur) {\n      return;\n    }\n    let newInputValue;\n    if (multiple) {\n      newInputValue = '';\n    } else if (newValue == null) {\n      newInputValue = '';\n    } else {\n      const optionLabel = getOptionLabel(newValue);\n      newInputValue = typeof optionLabel === 'string' ? optionLabel : '';\n    }\n    if (inputValue === newInputValue) {\n      return;\n    }\n    setInputValueState(newInputValue);\n    if (onInputChange) {\n      onInputChange(event, newInputValue, 'reset');\n    }\n  }, [getOptionLabel, inputValue, multiple, onInputChange, setInputValueState, clearOnBlur, value]);\n  const prevValue = React.useRef();\n  React.useEffect(() => {\n    const valueChange = value !== prevValue.current;\n    prevValue.current = value;\n    if (focused && !valueChange) {\n      return;\n    } // Only reset the input's value when freeSolo if the component's value changes.\n\n    if (freeSolo && !valueChange) {\n      return;\n    }\n    resetInputValue(null, value);\n  }, [value, resetInputValue, focused, prevValue, freeSolo]);\n  const [open, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: componentName,\n    state: 'open'\n  });\n  const [inputPristine, setInputPristine] = React.useState(true);\n  const inputValueIsSelectedValue = !multiple && value != null && inputValue === getOptionLabel(value);\n  const popupOpen = open && !readOnly;\n  const filteredOptions = popupOpen ? filterOptions(options.filter(option => {\n    if (filterSelectedOptions && (multiple ? value : [value]).some(value2 => value2 !== null && isOptionEqualToValue(option, value2))) {\n      return false;\n    }\n    return true;\n  }),\n  // we use the empty string to manipulate `filterOptions` to not filter any options\n  // i.e. the filter predicate always returns true\n  {\n    inputValue: inputValueIsSelectedValue && inputPristine ? '' : inputValue,\n    getOptionLabel\n  }) : [];\n  const listboxAvailable = open && filteredOptions.length > 0 && !readOnly;\n  if (process.env.NODE_ENV !== 'production') {\n    if (value !== null && !freeSolo && options.length > 0) {\n      const missingValue = (multiple ? value : [value]).filter(value2 => !options.some(option => isOptionEqualToValue(option, value2)));\n      if (missingValue.length > 0) {\n        console.warn([`MUI: The value provided to ${componentName} is invalid.`, `None of the options match with \\`${missingValue.length > 1 ? JSON.stringify(missingValue) : JSON.stringify(missingValue[0])}\\`.`, 'You can use the `isOptionEqualToValue` prop to customize the equality test.'].join('\\n'));\n      }\n    }\n  }\n  const focusTag = useEventCallback(tagToFocus => {\n    if (tagToFocus === -1) {\n      inputRef.current.focus();\n    } else {\n      anchorEl.querySelector(`[data-tag-index=\"${tagToFocus}\"]`).focus();\n    }\n  }); // Ensure the focusedTag is never inconsistent\n\n  React.useEffect(() => {\n    if (multiple && focusedTag > value.length - 1) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    }\n  }, [value, multiple, focusedTag, focusTag]);\n  function validOptionIndex(index, direction) {\n    if (!listboxRef.current || index === -1) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      // Out of range\n      if (direction === 'next' && nextFocus === filteredOptions.length || direction === 'previous' && nextFocus === -1) {\n        return -1;\n      }\n      const option = listboxRef.current.querySelector(`[data-option-index=\"${nextFocus}\"]`); // Same logic as MenuList.js\n\n      const nextFocusDisabled = disabledItemsFocusable ? false : !option || option.disabled || option.getAttribute('aria-disabled') === 'true';\n      if (option && !option.hasAttribute('tabindex') || nextFocusDisabled) {\n        // Move to the next element.\n        nextFocus += direction === 'next' ? 1 : -1;\n      } else {\n        return nextFocus;\n      }\n    }\n  }\n  const setHighlightedIndex = useEventCallback(({\n    event,\n    index,\n    reason = 'auto'\n  }) => {\n    highlightedIndexRef.current = index; // does the index exist?\n\n    if (index === -1) {\n      inputRef.current.removeAttribute('aria-activedescendant');\n    } else {\n      inputRef.current.setAttribute('aria-activedescendant', `${id}-option-${index}`);\n    }\n    if (onHighlightChange) {\n      onHighlightChange(event, index === -1 ? null : filteredOptions[index], reason);\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n    const prev = listboxRef.current.querySelector('[role=\"option\"].Mui-focused');\n    if (prev) {\n      prev.classList.remove('Mui-focused');\n      prev.classList.remove('Mui-focusVisible');\n    }\n    const listboxNode = listboxRef.current.parentElement.querySelector('[role=\"listbox\"]'); // \"No results\"\n\n    if (!listboxNode) {\n      return;\n    }\n    if (index === -1) {\n      listboxNode.scrollTop = 0;\n      return;\n    }\n    const option = listboxRef.current.querySelector(`[data-option-index=\"${index}\"]`);\n    if (!option) {\n      return;\n    }\n    option.classList.add('Mui-focused');\n    if (reason === 'keyboard') {\n      option.classList.add('Mui-focusVisible');\n    } // Scroll active descendant into view.\n    // Logic copied from https://www.w3.org/WAI/ARIA/apg/example-index/combobox/js/select-only.js\n    //\n    // Consider this API instead once it has a better browser support:\n    // .scrollIntoView({ scrollMode: 'if-needed', block: 'nearest' });\n\n    if (listboxNode.scrollHeight > listboxNode.clientHeight && reason !== 'mouse') {\n      const element = option;\n      const scrollBottom = listboxNode.clientHeight + listboxNode.scrollTop;\n      const elementBottom = element.offsetTop + element.offsetHeight;\n      if (elementBottom > scrollBottom) {\n        listboxNode.scrollTop = elementBottom - listboxNode.clientHeight;\n      } else if (element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0) < listboxNode.scrollTop) {\n        listboxNode.scrollTop = element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0);\n      }\n    }\n  });\n  const changeHighlightedIndex = useEventCallback(({\n    event,\n    diff,\n    direction = 'next',\n    reason = 'auto'\n  }) => {\n    if (!popupOpen) {\n      return;\n    }\n    const getNextIndex = () => {\n      const maxIndex = filteredOptions.length - 1;\n      if (diff === 'reset') {\n        return defaultHighlighted;\n      }\n      if (diff === 'start') {\n        return 0;\n      }\n      if (diff === 'end') {\n        return maxIndex;\n      }\n      const newIndex = highlightedIndexRef.current + diff;\n      if (newIndex < 0) {\n        if (newIndex === -1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap && highlightedIndexRef.current !== -1 || Math.abs(diff) > 1) {\n          return 0;\n        }\n        return maxIndex;\n      }\n      if (newIndex > maxIndex) {\n        if (newIndex === maxIndex + 1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap || Math.abs(diff) > 1) {\n          return maxIndex;\n        }\n        return 0;\n      }\n      return newIndex;\n    };\n    const nextIndex = validOptionIndex(getNextIndex(), direction);\n    setHighlightedIndex({\n      index: nextIndex,\n      reason,\n      event\n    }); // Sync the content of the input with the highlighted option.\n\n    if (autoComplete && diff !== 'reset') {\n      if (nextIndex === -1) {\n        inputRef.current.value = inputValue;\n      } else {\n        const option = getOptionLabel(filteredOptions[nextIndex]);\n        inputRef.current.value = option; // The portion of the selected suggestion that has not been typed by the user,\n        // a completion string, appears inline after the input cursor in the textbox.\n\n        const index = option.toLowerCase().indexOf(inputValue.toLowerCase());\n        if (index === 0 && inputValue.length > 0) {\n          inputRef.current.setSelectionRange(inputValue.length, option.length);\n        }\n      }\n    }\n  });\n  const syncHighlightedIndex = React.useCallback(() => {\n    if (!popupOpen) {\n      return;\n    }\n    const valueItem = multiple ? value[0] : value; // The popup is empty, reset\n\n    if (filteredOptions.length === 0 || valueItem == null) {\n      changeHighlightedIndex({\n        diff: 'reset'\n      });\n      return;\n    }\n    if (!listboxRef.current) {\n      return;\n    } // Synchronize the value with the highlighted index\n\n    if (valueItem != null) {\n      const currentOption = filteredOptions[highlightedIndexRef.current]; // Keep the current highlighted index if possible\n\n      if (multiple && currentOption && findIndex(value, val => isOptionEqualToValue(currentOption, val)) !== -1) {\n        return;\n      }\n      const itemIndex = findIndex(filteredOptions, optionItem => isOptionEqualToValue(optionItem, valueItem));\n      if (itemIndex === -1) {\n        changeHighlightedIndex({\n          diff: 'reset'\n        });\n      } else {\n        setHighlightedIndex({\n          index: itemIndex\n        });\n      }\n      return;\n    } // Prevent the highlighted index to leak outside the boundaries.\n\n    if (highlightedIndexRef.current >= filteredOptions.length - 1) {\n      setHighlightedIndex({\n        index: filteredOptions.length - 1\n      });\n      return;\n    } // Restore the focus to the previous index.\n\n    setHighlightedIndex({\n      index: highlightedIndexRef.current\n    }); // Ignore filteredOptions (and options, isOptionEqualToValue, getOptionLabel) not to break the scroll position\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n  // Only sync the highlighted index when the option switch between empty and not\n  filteredOptions.length,\n  // Don't sync the highlighted index with the value when multiple\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  multiple ? false : value, filterSelectedOptions, changeHighlightedIndex, setHighlightedIndex, popupOpen, inputValue, multiple]);\n  const handleListboxRef = useEventCallback(node => {\n    setRef(listboxRef, node);\n    if (!node) {\n      return;\n    }\n    syncHighlightedIndex();\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (!inputRef.current || inputRef.current.nodeName !== 'INPUT') {\n        if (inputRef.current && inputRef.current.nodeName === 'TEXTAREA') {\n          console.warn([`A textarea element was provided to ${componentName} where input was expected.`, `This is not a supported scenario but it may work under certain conditions.`, `A textarea keyboard navigation may conflict with Autocomplete controls (e.g. enter and arrow keys).`, `Make sure to test keyboard navigation and add custom event handlers if necessary.`].join('\\n'));\n        } else {\n          console.error([`MUI: Unable to find the input element. It was resolved to ${inputRef.current} while an HTMLInputElement was expected.`, `Instead, ${componentName} expects an input element.`, '', componentName === 'useAutocomplete' ? 'Make sure you have binded getInputProps correctly and that the normal ref/effect resolutions order is guaranteed.' : 'Make sure you have customized the input component correctly.'].join('\\n'));\n        }\n      }\n    }, [componentName]);\n  }\n  React.useEffect(() => {\n    syncHighlightedIndex();\n  }, [syncHighlightedIndex]);\n  const handleOpen = event => {\n    if (open) {\n      return;\n    }\n    setOpenState(true);\n    setInputPristine(true);\n    if (onOpen) {\n      onOpen(event);\n    }\n  };\n  const handleClose = (event, reason) => {\n    if (!open) {\n      return;\n    }\n    setOpenState(false);\n    if (onClose) {\n      onClose(event, reason);\n    }\n  };\n  const handleValue = (event, newValue, reason, details) => {\n    if (multiple) {\n      if (value.length === newValue.length && value.every((val, i) => val === newValue[i])) {\n        return;\n      }\n    } else if (value === newValue) {\n      return;\n    }\n    if (onChange) {\n      onChange(event, newValue, reason, details);\n    }\n    setValueState(newValue);\n  };\n  const isTouch = React.useRef(false);\n  const selectNewValue = (event, option, reasonProp = 'selectOption', origin = 'options') => {\n    let reason = reasonProp;\n    let newValue = option;\n    if (multiple) {\n      newValue = Array.isArray(value) ? value.slice() : [];\n      if (process.env.NODE_ENV !== 'production') {\n        const matches = newValue.filter(val => isOptionEqualToValue(option, val));\n        if (matches.length > 1) {\n          console.error([`MUI: The \\`isOptionEqualToValue\\` method of ${componentName} do not handle the arguments correctly.`, `The component expects a single value to match a given option but found ${matches.length} matches.`].join('\\n'));\n        }\n      }\n      const itemIndex = findIndex(newValue, valueItem => isOptionEqualToValue(option, valueItem));\n      if (itemIndex === -1) {\n        newValue.push(option);\n      } else if (origin !== 'freeSolo') {\n        newValue.splice(itemIndex, 1);\n        reason = 'removeOption';\n      }\n    }\n    resetInputValue(event, newValue);\n    handleValue(event, newValue, reason, {\n      option\n    });\n    if (!disableCloseOnSelect && (!event || !event.ctrlKey && !event.metaKey)) {\n      handleClose(event, reason);\n    }\n    if (blurOnSelect === true || blurOnSelect === 'touch' && isTouch.current || blurOnSelect === 'mouse' && !isTouch.current) {\n      inputRef.current.blur();\n    }\n  };\n  function validTagIndex(index, direction) {\n    if (index === -1) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      // Out of range\n      if (direction === 'next' && nextFocus === value.length || direction === 'previous' && nextFocus === -1) {\n        return -1;\n      }\n      const option = anchorEl.querySelector(`[data-tag-index=\"${nextFocus}\"]`); // Same logic as MenuList.js\n\n      if (!option || !option.hasAttribute('tabindex') || option.disabled || option.getAttribute('aria-disabled') === 'true') {\n        nextFocus += direction === 'next' ? 1 : -1;\n      } else {\n        return nextFocus;\n      }\n    }\n  }\n  const handleFocusTag = (event, direction) => {\n    if (!multiple) {\n      return;\n    }\n    if (inputValue === '') {\n      handleClose(event, 'toggleInput');\n    }\n    let nextTag = focusedTag;\n    if (focusedTag === -1) {\n      if (inputValue === '' && direction === 'previous') {\n        nextTag = value.length - 1;\n      }\n    } else {\n      nextTag += direction === 'next' ? 1 : -1;\n      if (nextTag < 0) {\n        nextTag = 0;\n      }\n      if (nextTag === value.length) {\n        nextTag = -1;\n      }\n    }\n    nextTag = validTagIndex(nextTag, direction);\n    setFocusedTag(nextTag);\n    focusTag(nextTag);\n  };\n  const handleClear = event => {\n    ignoreFocus.current = true;\n    setInputValueState('');\n    if (onInputChange) {\n      onInputChange(event, '', 'clear');\n    }\n    handleValue(event, multiple ? [] : null, 'clear');\n  };\n  const handleKeyDown = other => event => {\n    if (other.onKeyDown) {\n      other.onKeyDown(event);\n    }\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (focusedTag !== -1 && ['ArrowLeft', 'ArrowRight'].indexOf(event.key) === -1) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    } // Wait until IME is settled.\n\n    if (event.which !== 229) {\n      switch (event.key) {\n        case 'Home':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'start',\n              direction: 'next',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'End':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'end',\n              direction: 'previous',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'PageUp':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -pageSize,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'PageDown':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: pageSize,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowDown':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: 1,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowUp':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -1,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowLeft':\n          handleFocusTag(event, 'previous');\n          break;\n        case 'ArrowRight':\n          handleFocusTag(event, 'next');\n          break;\n        case 'Enter':\n          if (highlightedIndexRef.current !== -1 && popupOpen) {\n            const option = filteredOptions[highlightedIndexRef.current];\n            const disabled = getOptionDisabled ? getOptionDisabled(option) : false; // Avoid early form validation, let the end-users continue filling the form.\n\n            event.preventDefault();\n            if (disabled) {\n              return;\n            }\n            selectNewValue(event, option, 'selectOption'); // Move the selection to the end.\n\n            if (autoComplete) {\n              inputRef.current.setSelectionRange(inputRef.current.value.length, inputRef.current.value.length);\n            }\n          } else if (freeSolo && inputValue !== '' && inputValueIsSelectedValue === false) {\n            if (multiple) {\n              // Allow people to add new values before they submit the form.\n              event.preventDefault();\n            }\n            selectNewValue(event, inputValue, 'createOption', 'freeSolo');\n          }\n          break;\n        case 'Escape':\n          if (popupOpen) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault(); // Avoid the Modal to handle the event.\n\n            event.stopPropagation();\n            handleClose(event, 'escape');\n          } else if (clearOnEscape && (inputValue !== '' || multiple && value.length > 0)) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault(); // Avoid the Modal to handle the event.\n\n            event.stopPropagation();\n            handleClear(event);\n          }\n          break;\n        case 'Backspace':\n          if (multiple && !readOnly && inputValue === '' && value.length > 0) {\n            const index = focusedTag === -1 ? value.length - 1 : focusedTag;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          break;\n        default:\n      }\n    }\n  };\n  const handleFocus = event => {\n    setFocused(true);\n    if (openOnFocus && !ignoreFocus.current) {\n      handleOpen(event);\n    }\n  };\n  const handleBlur = event => {\n    // Ignore the event when using the scrollbar with IE11\n    if (listboxRef.current !== null && listboxRef.current.parentElement.contains(document.activeElement)) {\n      inputRef.current.focus();\n      return;\n    }\n    setFocused(false);\n    firstFocus.current = true;\n    ignoreFocus.current = false;\n    if (autoSelect && highlightedIndexRef.current !== -1 && popupOpen) {\n      selectNewValue(event, filteredOptions[highlightedIndexRef.current], 'blur');\n    } else if (autoSelect && freeSolo && inputValue !== '') {\n      selectNewValue(event, inputValue, 'blur', 'freeSolo');\n    } else if (clearOnBlur) {\n      resetInputValue(event, value);\n    }\n    handleClose(event, 'blur');\n  };\n  const handleInputChange = event => {\n    const newValue = event.target.value;\n    if (inputValue !== newValue) {\n      setInputValueState(newValue);\n      setInputPristine(false);\n      if (onInputChange) {\n        onInputChange(event, newValue, 'input');\n      }\n    }\n    if (newValue === '') {\n      if (!disableClearable && !multiple) {\n        handleValue(event, null, 'clear');\n      }\n    } else {\n      handleOpen(event);\n    }\n  };\n  const handleOptionMouseOver = event => {\n    setHighlightedIndex({\n      event,\n      index: Number(event.currentTarget.getAttribute('data-option-index')),\n      reason: 'mouse'\n    });\n  };\n  const handleOptionTouchStart = () => {\n    isTouch.current = true;\n  };\n  const handleOptionClick = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    selectNewValue(event, filteredOptions[index], 'selectOption');\n    isTouch.current = false;\n  };\n  const handleTagDelete = index => event => {\n    const newValue = value.slice();\n    newValue.splice(index, 1);\n    handleValue(event, newValue, 'removeOption', {\n      option: value[index]\n    });\n  };\n  const handlePopupIndicator = event => {\n    if (open) {\n      handleClose(event, 'toggleInput');\n    } else {\n      handleOpen(event);\n    }\n  }; // Prevent input blur when interacting with the combobox\n\n  const handleMouseDown = event => {\n    if (event.target.getAttribute('id') !== id) {\n      event.preventDefault();\n    }\n  }; // Focus the input when interacting with the combobox\n\n  const handleClick = () => {\n    inputRef.current.focus();\n    if (selectOnFocus && firstFocus.current && inputRef.current.selectionEnd - inputRef.current.selectionStart === 0) {\n      inputRef.current.select();\n    }\n    firstFocus.current = false;\n  };\n  const handleInputMouseDown = event => {\n    if (inputValue === '' || !open) {\n      handlePopupIndicator(event);\n    }\n  };\n  let dirty = freeSolo && inputValue.length > 0;\n  dirty = dirty || (multiple ? value.length > 0 : value !== null);\n  let groupedOptions = filteredOptions;\n  if (groupBy) {\n    // used to keep track of key and indexes in the result array\n    const indexBy = new Map();\n    let warn = false;\n    groupedOptions = filteredOptions.reduce((acc, option, index) => {\n      const group = groupBy(option);\n      if (acc.length > 0 && acc[acc.length - 1].group === group) {\n        acc[acc.length - 1].options.push(option);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          if (indexBy.get(group) && !warn) {\n            console.warn(`MUI: The options provided combined with the \\`groupBy\\` method of ${componentName} returns duplicated headers.`, 'You can solve the issue by sorting the options with the output of `groupBy`.');\n            warn = true;\n          }\n          indexBy.set(group, true);\n        }\n        acc.push({\n          key: index,\n          index,\n          group,\n          options: [option]\n        });\n      }\n      return acc;\n    }, []);\n  }\n  if (disabledProp && focused) {\n    handleBlur();\n  }\n  return {\n    getRootProps: (other = {}) => _extends({\n      'aria-owns': listboxAvailable ? `${id}-listbox` : null\n    }, other, {\n      onKeyDown: handleKeyDown(other),\n      onMouseDown: handleMouseDown,\n      onClick: handleClick\n    }),\n    getInputLabelProps: () => ({\n      id: `${id}-label`,\n      htmlFor: id\n    }),\n    getInputProps: () => ({\n      id,\n      value: inputValue,\n      onBlur: handleBlur,\n      onFocus: handleFocus,\n      onChange: handleInputChange,\n      onMouseDown: handleInputMouseDown,\n      // if open then this is handled imperativeley so don't let react override\n      // only have an opinion about this when closed\n      'aria-activedescendant': popupOpen ? '' : null,\n      'aria-autocomplete': autoComplete ? 'both' : 'list',\n      'aria-controls': listboxAvailable ? `${id}-listbox` : undefined,\n      'aria-expanded': listboxAvailable,\n      // Disable browser's suggestion that might overlap with the popup.\n      // Handle autocomplete but not autofill.\n      autoComplete: 'off',\n      ref: inputRef,\n      autoCapitalize: 'none',\n      spellCheck: 'false',\n      role: 'combobox'\n    }),\n    getClearProps: () => ({\n      tabIndex: -1,\n      onClick: handleClear\n    }),\n    getPopupIndicatorProps: () => ({\n      tabIndex: -1,\n      onClick: handlePopupIndicator\n    }),\n    getTagProps: ({\n      index\n    }) => _extends({\n      key: index,\n      'data-tag-index': index,\n      tabIndex: -1\n    }, !readOnly && {\n      onDelete: handleTagDelete(index)\n    }),\n    getListboxProps: () => ({\n      role: 'listbox',\n      id: `${id}-listbox`,\n      'aria-labelledby': `${id}-label`,\n      ref: handleListboxRef,\n      onMouseDown: event => {\n        // Prevent blur\n        event.preventDefault();\n      }\n    }),\n    getOptionProps: ({\n      index,\n      option\n    }) => {\n      const selected = (multiple ? value : [value]).some(value2 => value2 != null && isOptionEqualToValue(option, value2));\n      const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n      return {\n        key: getOptionLabel(option),\n        tabIndex: -1,\n        role: 'option',\n        id: `${id}-option-${index}`,\n        onMouseOver: handleOptionMouseOver,\n        onClick: handleOptionClick,\n        onTouchStart: handleOptionTouchStart,\n        'data-option-index': index,\n        'aria-disabled': disabled,\n        'aria-selected': selected\n      };\n    },\n    id,\n    inputValue,\n    value,\n    dirty,\n    popupOpen,\n    focused: focused || focusedTag !== -1,\n    anchorEl,\n    setAnchorEl,\n    focusedTag,\n    groupedOptions\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_setRef", "setRef", "unstable_useEventCallback", "useEventCallback", "unstable_useControlled", "useControlled", "unstable_useId", "useId", "stripDiacritics", "string", "normalize", "replace", "createFilterOptions", "config", "ignoreAccents", "ignoreCase", "limit", "matchFrom", "stringify", "trim", "options", "inputValue", "getOptionLabel", "input", "toLowerCase", "filteredOptions", "filter", "option", "candidate", "indexOf", "slice", "findIndex", "array", "comp", "i", "length", "defaultFilterOptions", "pageSize", "useAutocomplete", "props", "autoComplete", "autoHighlight", "autoSelect", "blurOnSelect", "clearOnBlur", "freeSolo", "clearOnEscape", "componentName", "defaultValue", "multiple", "disableClearable", "disableCloseOnSelect", "disabled", "disabledProp", "disabledItemsFocusable", "disableListWrap", "filterOptions", "filterSelectedOptions", "getOptionDisabled", "getOptionLabelProp", "_option$label", "label", "groupBy", "handleHomeEndKeys", "id", "idProp", "includeInputInList", "inputValueProp", "isOptionEqualToValue", "value", "onChange", "onClose", "onHighlightChange", "onInputChange", "onOpen", "open", "openProp", "openOnFocus", "readOnly", "selectOnFocus", "valueProp", "optionLabel", "process", "env", "NODE_ENV", "erroneousReturn", "undefined", "console", "error", "JSON", "String", "ignoreFocus", "useRef", "firstFocus", "inputRef", "listboxRef", "anchorEl", "setAnchorEl", "useState", "focusedTag", "setFocusedTag", "defaultHighlighted", "highlightedIndexRef", "setValueState", "controlled", "default", "name", "setInputValueState", "state", "focused", "setFocused", "resetInputValue", "useCallback", "event", "newValue", "isOptionSelected", "newInputValue", "prevValue", "useEffect", "valueChange", "current", "setOpenState", "inputPristine", "setInputPristine", "inputValueIsSelectedValue", "popupOpen", "some", "value2", "listboxAvailable", "missing<PERSON><PERSON><PERSON>", "warn", "join", "focusTag", "tagToFocus", "focus", "querySelector", "validOptionIndex", "index", "direction", "nextFocus", "nextFocusDisabled", "getAttribute", "hasAttribute", "setHighlightedIndex", "reason", "removeAttribute", "setAttribute", "prev", "classList", "remove", "listboxNode", "parentElement", "scrollTop", "add", "scrollHeight", "clientHeight", "element", "scrollBottom", "elementBottom", "offsetTop", "offsetHeight", "changeHighlightedIndex", "diff", "getNextIndex", "maxIndex", "newIndex", "Math", "abs", "nextIndex", "setSelectionRange", "syncHighlightedIndex", "valueItem", "currentOption", "val", "itemIndex", "optionItem", "handleListboxRef", "node", "nodeName", "handleOpen", "handleClose", "handleValue", "details", "every", "is<PERSON><PERSON>ch", "selectNewValue", "reasonProp", "origin", "Array", "isArray", "matches", "push", "splice", "ctrl<PERSON>ey", "metaKey", "blur", "validTagIndex", "handleFocusTag", "nextTag", "handleClear", "handleKeyDown", "other", "onKeyDown", "defaultMuiPrevented", "key", "which", "preventDefault", "stopPropagation", "handleFocus", "handleBlur", "contains", "document", "activeElement", "handleInputChange", "target", "handleOptionMouseOver", "Number", "currentTarget", "handleOptionTouchStart", "handleOptionClick", "handleTagDelete", "handlePopupIndicator", "handleMouseDown", "handleClick", "selectionEnd", "selectionStart", "select", "handleInputMouseDown", "dirty", "groupedOptions", "indexBy", "Map", "reduce", "acc", "group", "get", "set", "getRootProps", "onMouseDown", "onClick", "getInputLabelProps", "htmlFor", "getInputProps", "onBlur", "onFocus", "ref", "autoCapitalize", "spell<PERSON>heck", "role", "getClearProps", "tabIndex", "getPopupIndicatorProps", "getTagProps", "onDelete", "getListboxProps", "getOptionProps", "selected", "onMouseOver", "onTouchStart"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/AutocompleteUnstyled/useAutocomplete.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n\n/* eslint-disable no-constant-condition */\nimport * as React from 'react';\nimport { unstable_setRef as setRef, unstable_useEventCallback as useEventCallback, unstable_useControlled as useControlled, unstable_useId as useId } from '@mui/utils'; // https://stackoverflow.com/questions/990904/remove-accents-diacritics-in-a-string-in-javascript\n// Give up on IE11 support for this feature\n\nfunction stripDiacritics(string) {\n  return typeof string.normalize !== 'undefined' ? string.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '') : string;\n}\n\nexport function createFilterOptions(config = {}) {\n  const {\n    ignoreAccents = true,\n    ignoreCase = true,\n    limit,\n    matchFrom = 'any',\n    stringify,\n    trim = false\n  } = config;\n  return (options, {\n    inputValue,\n    getOptionLabel\n  }) => {\n    let input = trim ? inputValue.trim() : inputValue;\n\n    if (ignoreCase) {\n      input = input.toLowerCase();\n    }\n\n    if (ignoreAccents) {\n      input = stripDiacritics(input);\n    }\n\n    const filteredOptions = options.filter(option => {\n      let candidate = (stringify || getOptionLabel)(option);\n\n      if (ignoreCase) {\n        candidate = candidate.toLowerCase();\n      }\n\n      if (ignoreAccents) {\n        candidate = stripDiacritics(candidate);\n      }\n\n      return matchFrom === 'start' ? candidate.indexOf(input) === 0 : candidate.indexOf(input) > -1;\n    });\n    return typeof limit === 'number' ? filteredOptions.slice(0, limit) : filteredOptions;\n  };\n} // To replace with .findIndex() once we stop IE11 support.\n\nfunction findIndex(array, comp) {\n  for (let i = 0; i < array.length; i += 1) {\n    if (comp(array[i])) {\n      return i;\n    }\n  }\n\n  return -1;\n}\n\nconst defaultFilterOptions = createFilterOptions(); // Number of options to jump in list box when pageup and pagedown keys are used.\n\nconst pageSize = 5;\nexport default function useAutocomplete(props) {\n  const {\n    autoComplete = false,\n    autoHighlight = false,\n    autoSelect = false,\n    blurOnSelect = false,\n    clearOnBlur = !props.freeSolo,\n    clearOnEscape = false,\n    componentName = 'useAutocomplete',\n    defaultValue = props.multiple ? [] : null,\n    disableClearable = false,\n    disableCloseOnSelect = false,\n    disabled: disabledProp,\n    disabledItemsFocusable = false,\n    disableListWrap = false,\n    filterOptions = defaultFilterOptions,\n    filterSelectedOptions = false,\n    freeSolo = false,\n    getOptionDisabled,\n    getOptionLabel: getOptionLabelProp = option => {\n      var _option$label;\n\n      return (_option$label = option.label) != null ? _option$label : option;\n    },\n    groupBy,\n    handleHomeEndKeys = !props.freeSolo,\n    id: idProp,\n    includeInputInList = false,\n    inputValue: inputValueProp,\n    isOptionEqualToValue = (option, value) => option === value,\n    multiple = false,\n    onChange,\n    onClose,\n    onHighlightChange,\n    onInputChange,\n    onOpen,\n    open: openProp,\n    openOnFocus = false,\n    options,\n    readOnly = false,\n    selectOnFocus = !props.freeSolo,\n    value: valueProp\n  } = props;\n  const id = useId(idProp);\n  let getOptionLabel = getOptionLabelProp;\n\n  getOptionLabel = option => {\n    const optionLabel = getOptionLabelProp(option);\n\n    if (typeof optionLabel !== 'string') {\n      if (process.env.NODE_ENV !== 'production') {\n        const erroneousReturn = optionLabel === undefined ? 'undefined' : `${typeof optionLabel} (${optionLabel})`;\n        console.error(`MUI: The \\`getOptionLabel\\` method of ${componentName} returned ${erroneousReturn} instead of a string for ${JSON.stringify(option)}.`);\n      }\n\n      return String(optionLabel);\n    }\n\n    return optionLabel;\n  };\n\n  const ignoreFocus = React.useRef(false);\n  const firstFocus = React.useRef(true);\n  const inputRef = React.useRef(null);\n  const listboxRef = React.useRef(null);\n  const [anchorEl, setAnchorEl] = React.useState(null);\n  const [focusedTag, setFocusedTag] = React.useState(-1);\n  const defaultHighlighted = autoHighlight ? 0 : -1;\n  const highlightedIndexRef = React.useRef(defaultHighlighted);\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: componentName\n  });\n  const [inputValue, setInputValueState] = useControlled({\n    controlled: inputValueProp,\n    default: '',\n    name: componentName,\n    state: 'inputValue'\n  });\n  const [focused, setFocused] = React.useState(false);\n  const resetInputValue = React.useCallback((event, newValue) => {\n    // retain current `inputValue` if new option isn't selected and `clearOnBlur` is false\n    // When `multiple` is enabled, `newValue` is an array of all selected items including the newly selected item\n    const isOptionSelected = multiple ? value.length < newValue.length : newValue !== null;\n\n    if (!isOptionSelected && !clearOnBlur) {\n      return;\n    }\n\n    let newInputValue;\n\n    if (multiple) {\n      newInputValue = '';\n    } else if (newValue == null) {\n      newInputValue = '';\n    } else {\n      const optionLabel = getOptionLabel(newValue);\n      newInputValue = typeof optionLabel === 'string' ? optionLabel : '';\n    }\n\n    if (inputValue === newInputValue) {\n      return;\n    }\n\n    setInputValueState(newInputValue);\n\n    if (onInputChange) {\n      onInputChange(event, newInputValue, 'reset');\n    }\n  }, [getOptionLabel, inputValue, multiple, onInputChange, setInputValueState, clearOnBlur, value]);\n  const prevValue = React.useRef();\n  React.useEffect(() => {\n    const valueChange = value !== prevValue.current;\n    prevValue.current = value;\n\n    if (focused && !valueChange) {\n      return;\n    } // Only reset the input's value when freeSolo if the component's value changes.\n\n\n    if (freeSolo && !valueChange) {\n      return;\n    }\n\n    resetInputValue(null, value);\n  }, [value, resetInputValue, focused, prevValue, freeSolo]);\n  const [open, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: componentName,\n    state: 'open'\n  });\n  const [inputPristine, setInputPristine] = React.useState(true);\n  const inputValueIsSelectedValue = !multiple && value != null && inputValue === getOptionLabel(value);\n  const popupOpen = open && !readOnly;\n  const filteredOptions = popupOpen ? filterOptions(options.filter(option => {\n    if (filterSelectedOptions && (multiple ? value : [value]).some(value2 => value2 !== null && isOptionEqualToValue(option, value2))) {\n      return false;\n    }\n\n    return true;\n  }), // we use the empty string to manipulate `filterOptions` to not filter any options\n  // i.e. the filter predicate always returns true\n  {\n    inputValue: inputValueIsSelectedValue && inputPristine ? '' : inputValue,\n    getOptionLabel\n  }) : [];\n  const listboxAvailable = open && filteredOptions.length > 0 && !readOnly;\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (value !== null && !freeSolo && options.length > 0) {\n      const missingValue = (multiple ? value : [value]).filter(value2 => !options.some(option => isOptionEqualToValue(option, value2)));\n\n      if (missingValue.length > 0) {\n        console.warn([`MUI: The value provided to ${componentName} is invalid.`, `None of the options match with \\`${missingValue.length > 1 ? JSON.stringify(missingValue) : JSON.stringify(missingValue[0])}\\`.`, 'You can use the `isOptionEqualToValue` prop to customize the equality test.'].join('\\n'));\n      }\n    }\n  }\n\n  const focusTag = useEventCallback(tagToFocus => {\n    if (tagToFocus === -1) {\n      inputRef.current.focus();\n    } else {\n      anchorEl.querySelector(`[data-tag-index=\"${tagToFocus}\"]`).focus();\n    }\n  }); // Ensure the focusedTag is never inconsistent\n\n  React.useEffect(() => {\n    if (multiple && focusedTag > value.length - 1) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    }\n  }, [value, multiple, focusedTag, focusTag]);\n\n  function validOptionIndex(index, direction) {\n    if (!listboxRef.current || index === -1) {\n      return -1;\n    }\n\n    let nextFocus = index;\n\n    while (true) {\n      // Out of range\n      if (direction === 'next' && nextFocus === filteredOptions.length || direction === 'previous' && nextFocus === -1) {\n        return -1;\n      }\n\n      const option = listboxRef.current.querySelector(`[data-option-index=\"${nextFocus}\"]`); // Same logic as MenuList.js\n\n      const nextFocusDisabled = disabledItemsFocusable ? false : !option || option.disabled || option.getAttribute('aria-disabled') === 'true';\n\n      if (option && !option.hasAttribute('tabindex') || nextFocusDisabled) {\n        // Move to the next element.\n        nextFocus += direction === 'next' ? 1 : -1;\n      } else {\n        return nextFocus;\n      }\n    }\n  }\n\n  const setHighlightedIndex = useEventCallback(({\n    event,\n    index,\n    reason = 'auto'\n  }) => {\n    highlightedIndexRef.current = index; // does the index exist?\n\n    if (index === -1) {\n      inputRef.current.removeAttribute('aria-activedescendant');\n    } else {\n      inputRef.current.setAttribute('aria-activedescendant', `${id}-option-${index}`);\n    }\n\n    if (onHighlightChange) {\n      onHighlightChange(event, index === -1 ? null : filteredOptions[index], reason);\n    }\n\n    if (!listboxRef.current) {\n      return;\n    }\n\n    const prev = listboxRef.current.querySelector('[role=\"option\"].Mui-focused');\n\n    if (prev) {\n      prev.classList.remove('Mui-focused');\n      prev.classList.remove('Mui-focusVisible');\n    }\n\n    const listboxNode = listboxRef.current.parentElement.querySelector('[role=\"listbox\"]'); // \"No results\"\n\n    if (!listboxNode) {\n      return;\n    }\n\n    if (index === -1) {\n      listboxNode.scrollTop = 0;\n      return;\n    }\n\n    const option = listboxRef.current.querySelector(`[data-option-index=\"${index}\"]`);\n\n    if (!option) {\n      return;\n    }\n\n    option.classList.add('Mui-focused');\n\n    if (reason === 'keyboard') {\n      option.classList.add('Mui-focusVisible');\n    } // Scroll active descendant into view.\n    // Logic copied from https://www.w3.org/WAI/ARIA/apg/example-index/combobox/js/select-only.js\n    //\n    // Consider this API instead once it has a better browser support:\n    // .scrollIntoView({ scrollMode: 'if-needed', block: 'nearest' });\n\n\n    if (listboxNode.scrollHeight > listboxNode.clientHeight && reason !== 'mouse') {\n      const element = option;\n      const scrollBottom = listboxNode.clientHeight + listboxNode.scrollTop;\n      const elementBottom = element.offsetTop + element.offsetHeight;\n\n      if (elementBottom > scrollBottom) {\n        listboxNode.scrollTop = elementBottom - listboxNode.clientHeight;\n      } else if (element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0) < listboxNode.scrollTop) {\n        listboxNode.scrollTop = element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0);\n      }\n    }\n  });\n  const changeHighlightedIndex = useEventCallback(({\n    event,\n    diff,\n    direction = 'next',\n    reason = 'auto'\n  }) => {\n    if (!popupOpen) {\n      return;\n    }\n\n    const getNextIndex = () => {\n      const maxIndex = filteredOptions.length - 1;\n\n      if (diff === 'reset') {\n        return defaultHighlighted;\n      }\n\n      if (diff === 'start') {\n        return 0;\n      }\n\n      if (diff === 'end') {\n        return maxIndex;\n      }\n\n      const newIndex = highlightedIndexRef.current + diff;\n\n      if (newIndex < 0) {\n        if (newIndex === -1 && includeInputInList) {\n          return -1;\n        }\n\n        if (disableListWrap && highlightedIndexRef.current !== -1 || Math.abs(diff) > 1) {\n          return 0;\n        }\n\n        return maxIndex;\n      }\n\n      if (newIndex > maxIndex) {\n        if (newIndex === maxIndex + 1 && includeInputInList) {\n          return -1;\n        }\n\n        if (disableListWrap || Math.abs(diff) > 1) {\n          return maxIndex;\n        }\n\n        return 0;\n      }\n\n      return newIndex;\n    };\n\n    const nextIndex = validOptionIndex(getNextIndex(), direction);\n    setHighlightedIndex({\n      index: nextIndex,\n      reason,\n      event\n    }); // Sync the content of the input with the highlighted option.\n\n    if (autoComplete && diff !== 'reset') {\n      if (nextIndex === -1) {\n        inputRef.current.value = inputValue;\n      } else {\n        const option = getOptionLabel(filteredOptions[nextIndex]);\n        inputRef.current.value = option; // The portion of the selected suggestion that has not been typed by the user,\n        // a completion string, appears inline after the input cursor in the textbox.\n\n        const index = option.toLowerCase().indexOf(inputValue.toLowerCase());\n\n        if (index === 0 && inputValue.length > 0) {\n          inputRef.current.setSelectionRange(inputValue.length, option.length);\n        }\n      }\n    }\n  });\n  const syncHighlightedIndex = React.useCallback(() => {\n    if (!popupOpen) {\n      return;\n    }\n\n    const valueItem = multiple ? value[0] : value; // The popup is empty, reset\n\n    if (filteredOptions.length === 0 || valueItem == null) {\n      changeHighlightedIndex({\n        diff: 'reset'\n      });\n      return;\n    }\n\n    if (!listboxRef.current) {\n      return;\n    } // Synchronize the value with the highlighted index\n\n\n    if (valueItem != null) {\n      const currentOption = filteredOptions[highlightedIndexRef.current]; // Keep the current highlighted index if possible\n\n      if (multiple && currentOption && findIndex(value, val => isOptionEqualToValue(currentOption, val)) !== -1) {\n        return;\n      }\n\n      const itemIndex = findIndex(filteredOptions, optionItem => isOptionEqualToValue(optionItem, valueItem));\n\n      if (itemIndex === -1) {\n        changeHighlightedIndex({\n          diff: 'reset'\n        });\n      } else {\n        setHighlightedIndex({\n          index: itemIndex\n        });\n      }\n\n      return;\n    } // Prevent the highlighted index to leak outside the boundaries.\n\n\n    if (highlightedIndexRef.current >= filteredOptions.length - 1) {\n      setHighlightedIndex({\n        index: filteredOptions.length - 1\n      });\n      return;\n    } // Restore the focus to the previous index.\n\n\n    setHighlightedIndex({\n      index: highlightedIndexRef.current\n    }); // Ignore filteredOptions (and options, isOptionEqualToValue, getOptionLabel) not to break the scroll position\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [// Only sync the highlighted index when the option switch between empty and not\n  filteredOptions.length, // Don't sync the highlighted index with the value when multiple\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  multiple ? false : value, filterSelectedOptions, changeHighlightedIndex, setHighlightedIndex, popupOpen, inputValue, multiple]);\n  const handleListboxRef = useEventCallback(node => {\n    setRef(listboxRef, node);\n\n    if (!node) {\n      return;\n    }\n\n    syncHighlightedIndex();\n  });\n\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (!inputRef.current || inputRef.current.nodeName !== 'INPUT') {\n        if (inputRef.current && inputRef.current.nodeName === 'TEXTAREA') {\n          console.warn([`A textarea element was provided to ${componentName} where input was expected.`, `This is not a supported scenario but it may work under certain conditions.`, `A textarea keyboard navigation may conflict with Autocomplete controls (e.g. enter and arrow keys).`, `Make sure to test keyboard navigation and add custom event handlers if necessary.`].join('\\n'));\n        } else {\n          console.error([`MUI: Unable to find the input element. It was resolved to ${inputRef.current} while an HTMLInputElement was expected.`, `Instead, ${componentName} expects an input element.`, '', componentName === 'useAutocomplete' ? 'Make sure you have binded getInputProps correctly and that the normal ref/effect resolutions order is guaranteed.' : 'Make sure you have customized the input component correctly.'].join('\\n'));\n        }\n      }\n    }, [componentName]);\n  }\n\n  React.useEffect(() => {\n    syncHighlightedIndex();\n  }, [syncHighlightedIndex]);\n\n  const handleOpen = event => {\n    if (open) {\n      return;\n    }\n\n    setOpenState(true);\n    setInputPristine(true);\n\n    if (onOpen) {\n      onOpen(event);\n    }\n  };\n\n  const handleClose = (event, reason) => {\n    if (!open) {\n      return;\n    }\n\n    setOpenState(false);\n\n    if (onClose) {\n      onClose(event, reason);\n    }\n  };\n\n  const handleValue = (event, newValue, reason, details) => {\n    if (multiple) {\n      if (value.length === newValue.length && value.every((val, i) => val === newValue[i])) {\n        return;\n      }\n    } else if (value === newValue) {\n      return;\n    }\n\n    if (onChange) {\n      onChange(event, newValue, reason, details);\n    }\n\n    setValueState(newValue);\n  };\n\n  const isTouch = React.useRef(false);\n\n  const selectNewValue = (event, option, reasonProp = 'selectOption', origin = 'options') => {\n    let reason = reasonProp;\n    let newValue = option;\n\n    if (multiple) {\n      newValue = Array.isArray(value) ? value.slice() : [];\n\n      if (process.env.NODE_ENV !== 'production') {\n        const matches = newValue.filter(val => isOptionEqualToValue(option, val));\n\n        if (matches.length > 1) {\n          console.error([`MUI: The \\`isOptionEqualToValue\\` method of ${componentName} do not handle the arguments correctly.`, `The component expects a single value to match a given option but found ${matches.length} matches.`].join('\\n'));\n        }\n      }\n\n      const itemIndex = findIndex(newValue, valueItem => isOptionEqualToValue(option, valueItem));\n\n      if (itemIndex === -1) {\n        newValue.push(option);\n      } else if (origin !== 'freeSolo') {\n        newValue.splice(itemIndex, 1);\n        reason = 'removeOption';\n      }\n    }\n\n    resetInputValue(event, newValue);\n    handleValue(event, newValue, reason, {\n      option\n    });\n\n    if (!disableCloseOnSelect && (!event || !event.ctrlKey && !event.metaKey)) {\n      handleClose(event, reason);\n    }\n\n    if (blurOnSelect === true || blurOnSelect === 'touch' && isTouch.current || blurOnSelect === 'mouse' && !isTouch.current) {\n      inputRef.current.blur();\n    }\n  };\n\n  function validTagIndex(index, direction) {\n    if (index === -1) {\n      return -1;\n    }\n\n    let nextFocus = index;\n\n    while (true) {\n      // Out of range\n      if (direction === 'next' && nextFocus === value.length || direction === 'previous' && nextFocus === -1) {\n        return -1;\n      }\n\n      const option = anchorEl.querySelector(`[data-tag-index=\"${nextFocus}\"]`); // Same logic as MenuList.js\n\n      if (!option || !option.hasAttribute('tabindex') || option.disabled || option.getAttribute('aria-disabled') === 'true') {\n        nextFocus += direction === 'next' ? 1 : -1;\n      } else {\n        return nextFocus;\n      }\n    }\n  }\n\n  const handleFocusTag = (event, direction) => {\n    if (!multiple) {\n      return;\n    }\n\n    if (inputValue === '') {\n      handleClose(event, 'toggleInput');\n    }\n\n    let nextTag = focusedTag;\n\n    if (focusedTag === -1) {\n      if (inputValue === '' && direction === 'previous') {\n        nextTag = value.length - 1;\n      }\n    } else {\n      nextTag += direction === 'next' ? 1 : -1;\n\n      if (nextTag < 0) {\n        nextTag = 0;\n      }\n\n      if (nextTag === value.length) {\n        nextTag = -1;\n      }\n    }\n\n    nextTag = validTagIndex(nextTag, direction);\n    setFocusedTag(nextTag);\n    focusTag(nextTag);\n  };\n\n  const handleClear = event => {\n    ignoreFocus.current = true;\n    setInputValueState('');\n\n    if (onInputChange) {\n      onInputChange(event, '', 'clear');\n    }\n\n    handleValue(event, multiple ? [] : null, 'clear');\n  };\n\n  const handleKeyDown = other => event => {\n    if (other.onKeyDown) {\n      other.onKeyDown(event);\n    }\n\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n\n    if (focusedTag !== -1 && ['ArrowLeft', 'ArrowRight'].indexOf(event.key) === -1) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    } // Wait until IME is settled.\n\n\n    if (event.which !== 229) {\n      switch (event.key) {\n        case 'Home':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'start',\n              direction: 'next',\n              reason: 'keyboard',\n              event\n            });\n          }\n\n          break;\n\n        case 'End':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'end',\n              direction: 'previous',\n              reason: 'keyboard',\n              event\n            });\n          }\n\n          break;\n\n        case 'PageUp':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -pageSize,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n\n        case 'PageDown':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: pageSize,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n\n        case 'ArrowDown':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: 1,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n\n        case 'ArrowUp':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -1,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n\n        case 'ArrowLeft':\n          handleFocusTag(event, 'previous');\n          break;\n\n        case 'ArrowRight':\n          handleFocusTag(event, 'next');\n          break;\n\n        case 'Enter':\n          if (highlightedIndexRef.current !== -1 && popupOpen) {\n            const option = filteredOptions[highlightedIndexRef.current];\n            const disabled = getOptionDisabled ? getOptionDisabled(option) : false; // Avoid early form validation, let the end-users continue filling the form.\n\n            event.preventDefault();\n\n            if (disabled) {\n              return;\n            }\n\n            selectNewValue(event, option, 'selectOption'); // Move the selection to the end.\n\n            if (autoComplete) {\n              inputRef.current.setSelectionRange(inputRef.current.value.length, inputRef.current.value.length);\n            }\n          } else if (freeSolo && inputValue !== '' && inputValueIsSelectedValue === false) {\n            if (multiple) {\n              // Allow people to add new values before they submit the form.\n              event.preventDefault();\n            }\n\n            selectNewValue(event, inputValue, 'createOption', 'freeSolo');\n          }\n\n          break;\n\n        case 'Escape':\n          if (popupOpen) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault(); // Avoid the Modal to handle the event.\n\n            event.stopPropagation();\n            handleClose(event, 'escape');\n          } else if (clearOnEscape && (inputValue !== '' || multiple && value.length > 0)) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault(); // Avoid the Modal to handle the event.\n\n            event.stopPropagation();\n            handleClear(event);\n          }\n\n          break;\n\n        case 'Backspace':\n          if (multiple && !readOnly && inputValue === '' && value.length > 0) {\n            const index = focusedTag === -1 ? value.length - 1 : focusedTag;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n\n          break;\n\n        default:\n      }\n    }\n  };\n\n  const handleFocus = event => {\n    setFocused(true);\n\n    if (openOnFocus && !ignoreFocus.current) {\n      handleOpen(event);\n    }\n  };\n\n  const handleBlur = event => {\n    // Ignore the event when using the scrollbar with IE11\n    if (listboxRef.current !== null && listboxRef.current.parentElement.contains(document.activeElement)) {\n      inputRef.current.focus();\n      return;\n    }\n\n    setFocused(false);\n    firstFocus.current = true;\n    ignoreFocus.current = false;\n\n    if (autoSelect && highlightedIndexRef.current !== -1 && popupOpen) {\n      selectNewValue(event, filteredOptions[highlightedIndexRef.current], 'blur');\n    } else if (autoSelect && freeSolo && inputValue !== '') {\n      selectNewValue(event, inputValue, 'blur', 'freeSolo');\n    } else if (clearOnBlur) {\n      resetInputValue(event, value);\n    }\n\n    handleClose(event, 'blur');\n  };\n\n  const handleInputChange = event => {\n    const newValue = event.target.value;\n\n    if (inputValue !== newValue) {\n      setInputValueState(newValue);\n      setInputPristine(false);\n\n      if (onInputChange) {\n        onInputChange(event, newValue, 'input');\n      }\n    }\n\n    if (newValue === '') {\n      if (!disableClearable && !multiple) {\n        handleValue(event, null, 'clear');\n      }\n    } else {\n      handleOpen(event);\n    }\n  };\n\n  const handleOptionMouseOver = event => {\n    setHighlightedIndex({\n      event,\n      index: Number(event.currentTarget.getAttribute('data-option-index')),\n      reason: 'mouse'\n    });\n  };\n\n  const handleOptionTouchStart = () => {\n    isTouch.current = true;\n  };\n\n  const handleOptionClick = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    selectNewValue(event, filteredOptions[index], 'selectOption');\n    isTouch.current = false;\n  };\n\n  const handleTagDelete = index => event => {\n    const newValue = value.slice();\n    newValue.splice(index, 1);\n    handleValue(event, newValue, 'removeOption', {\n      option: value[index]\n    });\n  };\n\n  const handlePopupIndicator = event => {\n    if (open) {\n      handleClose(event, 'toggleInput');\n    } else {\n      handleOpen(event);\n    }\n  }; // Prevent input blur when interacting with the combobox\n\n\n  const handleMouseDown = event => {\n    if (event.target.getAttribute('id') !== id) {\n      event.preventDefault();\n    }\n  }; // Focus the input when interacting with the combobox\n\n\n  const handleClick = () => {\n    inputRef.current.focus();\n\n    if (selectOnFocus && firstFocus.current && inputRef.current.selectionEnd - inputRef.current.selectionStart === 0) {\n      inputRef.current.select();\n    }\n\n    firstFocus.current = false;\n  };\n\n  const handleInputMouseDown = event => {\n    if (inputValue === '' || !open) {\n      handlePopupIndicator(event);\n    }\n  };\n\n  let dirty = freeSolo && inputValue.length > 0;\n  dirty = dirty || (multiple ? value.length > 0 : value !== null);\n  let groupedOptions = filteredOptions;\n\n  if (groupBy) {\n    // used to keep track of key and indexes in the result array\n    const indexBy = new Map();\n    let warn = false;\n    groupedOptions = filteredOptions.reduce((acc, option, index) => {\n      const group = groupBy(option);\n\n      if (acc.length > 0 && acc[acc.length - 1].group === group) {\n        acc[acc.length - 1].options.push(option);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          if (indexBy.get(group) && !warn) {\n            console.warn(`MUI: The options provided combined with the \\`groupBy\\` method of ${componentName} returns duplicated headers.`, 'You can solve the issue by sorting the options with the output of `groupBy`.');\n            warn = true;\n          }\n\n          indexBy.set(group, true);\n        }\n\n        acc.push({\n          key: index,\n          index,\n          group,\n          options: [option]\n        });\n      }\n\n      return acc;\n    }, []);\n  }\n\n  if (disabledProp && focused) {\n    handleBlur();\n  }\n\n  return {\n    getRootProps: (other = {}) => _extends({\n      'aria-owns': listboxAvailable ? `${id}-listbox` : null\n    }, other, {\n      onKeyDown: handleKeyDown(other),\n      onMouseDown: handleMouseDown,\n      onClick: handleClick\n    }),\n    getInputLabelProps: () => ({\n      id: `${id}-label`,\n      htmlFor: id\n    }),\n    getInputProps: () => ({\n      id,\n      value: inputValue,\n      onBlur: handleBlur,\n      onFocus: handleFocus,\n      onChange: handleInputChange,\n      onMouseDown: handleInputMouseDown,\n      // if open then this is handled imperativeley so don't let react override\n      // only have an opinion about this when closed\n      'aria-activedescendant': popupOpen ? '' : null,\n      'aria-autocomplete': autoComplete ? 'both' : 'list',\n      'aria-controls': listboxAvailable ? `${id}-listbox` : undefined,\n      'aria-expanded': listboxAvailable,\n      // Disable browser's suggestion that might overlap with the popup.\n      // Handle autocomplete but not autofill.\n      autoComplete: 'off',\n      ref: inputRef,\n      autoCapitalize: 'none',\n      spellCheck: 'false',\n      role: 'combobox'\n    }),\n    getClearProps: () => ({\n      tabIndex: -1,\n      onClick: handleClear\n    }),\n    getPopupIndicatorProps: () => ({\n      tabIndex: -1,\n      onClick: handlePopupIndicator\n    }),\n    getTagProps: ({\n      index\n    }) => _extends({\n      key: index,\n      'data-tag-index': index,\n      tabIndex: -1\n    }, !readOnly && {\n      onDelete: handleTagDelete(index)\n    }),\n    getListboxProps: () => ({\n      role: 'listbox',\n      id: `${id}-listbox`,\n      'aria-labelledby': `${id}-label`,\n      ref: handleListboxRef,\n      onMouseDown: event => {\n        // Prevent blur\n        event.preventDefault();\n      }\n    }),\n    getOptionProps: ({\n      index,\n      option\n    }) => {\n      const selected = (multiple ? value : [value]).some(value2 => value2 != null && isOptionEqualToValue(option, value2));\n      const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n      return {\n        key: getOptionLabel(option),\n        tabIndex: -1,\n        role: 'option',\n        id: `${id}-option-${index}`,\n        onMouseOver: handleOptionMouseOver,\n        onClick: handleOptionClick,\n        onTouchStart: handleOptionTouchStart,\n        'data-option-index': index,\n        'aria-disabled': disabled,\n        'aria-selected': selected\n      };\n    },\n    id,\n    inputValue,\n    value,\n    dirty,\n    popupOpen,\n    focused: focused || focusedTag !== -1,\n    anchorEl,\n    setAnchorEl,\n    focusedTag,\n    groupedOptions\n  };\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;;AAEzD;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,IAAIC,MAAM,EAAEC,yBAAyB,IAAIC,gBAAgB,EAAEC,sBAAsB,IAAIC,aAAa,EAAEC,cAAc,IAAIC,KAAK,QAAQ,YAAY,CAAC,CAAC;AACzK;;AAEA,SAASC,eAAeA,CAACC,MAAM,EAAE;EAC/B,OAAO,OAAOA,MAAM,CAACC,SAAS,KAAK,WAAW,GAAGD,MAAM,CAACC,SAAS,CAAC,KAAK,CAAC,CAACC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,GAAGF,MAAM;AACnH;AAEA,OAAO,SAASG,mBAAmBA,CAACC,MAAM,GAAG,CAAC,CAAC,EAAE;EAC/C,MAAM;IACJC,aAAa,GAAG,IAAI;IACpBC,UAAU,GAAG,IAAI;IACjBC,KAAK;IACLC,SAAS,GAAG,KAAK;IACjBC,SAAS;IACTC,IAAI,GAAG;EACT,CAAC,GAAGN,MAAM;EACV,OAAO,CAACO,OAAO,EAAE;IACfC,UAAU;IACVC;EACF,CAAC,KAAK;IACJ,IAAIC,KAAK,GAAGJ,IAAI,GAAGE,UAAU,CAACF,IAAI,CAAC,CAAC,GAAGE,UAAU;IAEjD,IAAIN,UAAU,EAAE;MACdQ,KAAK,GAAGA,KAAK,CAACC,WAAW,CAAC,CAAC;IAC7B;IAEA,IAAIV,aAAa,EAAE;MACjBS,KAAK,GAAGf,eAAe,CAACe,KAAK,CAAC;IAChC;IAEA,MAAME,eAAe,GAAGL,OAAO,CAACM,MAAM,CAACC,MAAM,IAAI;MAC/C,IAAIC,SAAS,GAAG,CAACV,SAAS,IAAII,cAAc,EAAEK,MAAM,CAAC;MAErD,IAAIZ,UAAU,EAAE;QACda,SAAS,GAAGA,SAAS,CAACJ,WAAW,CAAC,CAAC;MACrC;MAEA,IAAIV,aAAa,EAAE;QACjBc,SAAS,GAAGpB,eAAe,CAACoB,SAAS,CAAC;MACxC;MAEA,OAAOX,SAAS,KAAK,OAAO,GAAGW,SAAS,CAACC,OAAO,CAACN,KAAK,CAAC,KAAK,CAAC,GAAGK,SAAS,CAACC,OAAO,CAACN,KAAK,CAAC,GAAG,CAAC,CAAC;IAC/F,CAAC,CAAC;IACF,OAAO,OAAOP,KAAK,KAAK,QAAQ,GAAGS,eAAe,CAACK,KAAK,CAAC,CAAC,EAAEd,KAAK,CAAC,GAAGS,eAAe;EACtF,CAAC;AACH,CAAC,CAAC;;AAEF,SAASM,SAASA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACxC,IAAID,IAAI,CAACD,KAAK,CAACE,CAAC,CAAC,CAAC,EAAE;MAClB,OAAOA,CAAC;IACV;EACF;EAEA,OAAO,CAAC,CAAC;AACX;AAEA,MAAME,oBAAoB,GAAGxB,mBAAmB,CAAC,CAAC,CAAC,CAAC;;AAEpD,MAAMyB,QAAQ,GAAG,CAAC;AAClB,eAAe,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC7C,MAAM;IACJC,YAAY,GAAG,KAAK;IACpBC,aAAa,GAAG,KAAK;IACrBC,UAAU,GAAG,KAAK;IAClBC,YAAY,GAAG,KAAK;IACpBC,WAAW,GAAG,CAACL,KAAK,CAACM,QAAQ;IAC7BC,aAAa,GAAG,KAAK;IACrBC,aAAa,GAAG,iBAAiB;IACjCC,YAAY,GAAGT,KAAK,CAACU,QAAQ,GAAG,EAAE,GAAG,IAAI;IACzCC,gBAAgB,GAAG,KAAK;IACxBC,oBAAoB,GAAG,KAAK;IAC5BC,QAAQ,EAAEC,YAAY;IACtBC,sBAAsB,GAAG,KAAK;IAC9BC,eAAe,GAAG,KAAK;IACvBC,aAAa,GAAGpB,oBAAoB;IACpCqB,qBAAqB,GAAG,KAAK;IAC7BZ,QAAQ,GAAG,KAAK;IAChBa,iBAAiB;IACjBpC,cAAc,EAAEqC,kBAAkB,GAAGhC,MAAM,IAAI;MAC7C,IAAIiC,aAAa;MAEjB,OAAO,CAACA,aAAa,GAAGjC,MAAM,CAACkC,KAAK,KAAK,IAAI,GAAGD,aAAa,GAAGjC,MAAM;IACxE,CAAC;IACDmC,OAAO;IACPC,iBAAiB,GAAG,CAACxB,KAAK,CAACM,QAAQ;IACnCmB,EAAE,EAAEC,MAAM;IACVC,kBAAkB,GAAG,KAAK;IAC1B7C,UAAU,EAAE8C,cAAc;IAC1BC,oBAAoB,GAAGA,CAACzC,MAAM,EAAE0C,KAAK,KAAK1C,MAAM,KAAK0C,KAAK;IAC1DpB,QAAQ,GAAG,KAAK;IAChBqB,QAAQ;IACRC,OAAO;IACPC,iBAAiB;IACjBC,aAAa;IACbC,MAAM;IACNC,IAAI,EAAEC,QAAQ;IACdC,WAAW,GAAG,KAAK;IACnBzD,OAAO;IACP0D,QAAQ,GAAG,KAAK;IAChBC,aAAa,GAAG,CAACxC,KAAK,CAACM,QAAQ;IAC/BwB,KAAK,EAAEW;EACT,CAAC,GAAGzC,KAAK;EACT,MAAMyB,EAAE,GAAGzD,KAAK,CAAC0D,MAAM,CAAC;EACxB,IAAI3C,cAAc,GAAGqC,kBAAkB;EAEvCrC,cAAc,GAAGK,MAAM,IAAI;IACzB,MAAMsD,WAAW,GAAGtB,kBAAkB,CAAChC,MAAM,CAAC;IAE9C,IAAI,OAAOsD,WAAW,KAAK,QAAQ,EAAE;MACnC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,MAAMC,eAAe,GAAGJ,WAAW,KAAKK,SAAS,GAAG,WAAW,GAAI,GAAE,OAAOL,WAAY,KAAIA,WAAY,GAAE;QAC1GM,OAAO,CAACC,KAAK,CAAE,yCAAwCzC,aAAc,aAAYsC,eAAgB,4BAA2BI,IAAI,CAACvE,SAAS,CAACS,MAAM,CAAE,GAAE,CAAC;MACxJ;MAEA,OAAO+D,MAAM,CAACT,WAAW,CAAC;IAC5B;IAEA,OAAOA,WAAW;EACpB,CAAC;EAED,MAAMU,WAAW,GAAG5F,KAAK,CAAC6F,MAAM,CAAC,KAAK,CAAC;EACvC,MAAMC,UAAU,GAAG9F,KAAK,CAAC6F,MAAM,CAAC,IAAI,CAAC;EACrC,MAAME,QAAQ,GAAG/F,KAAK,CAAC6F,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMG,UAAU,GAAGhG,KAAK,CAAC6F,MAAM,CAAC,IAAI,CAAC;EACrC,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAGlG,KAAK,CAACmG,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGrG,KAAK,CAACmG,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtD,MAAMG,kBAAkB,GAAG5D,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;EACjD,MAAM6D,mBAAmB,GAAGvG,KAAK,CAAC6F,MAAM,CAACS,kBAAkB,CAAC;EAC5D,MAAM,CAAChC,KAAK,EAAEkC,aAAa,CAAC,GAAGlG,aAAa,CAAC;IAC3CmG,UAAU,EAAExB,SAAS;IACrByB,OAAO,EAAEzD,YAAY;IACrB0D,IAAI,EAAE3D;EACR,CAAC,CAAC;EACF,MAAM,CAAC1B,UAAU,EAAEsF,kBAAkB,CAAC,GAAGtG,aAAa,CAAC;IACrDmG,UAAU,EAAErC,cAAc;IAC1BsC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE3D,aAAa;IACnB6D,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG/G,KAAK,CAACmG,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMa,eAAe,GAAGhH,KAAK,CAACiH,WAAW,CAAC,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC7D;IACA;IACA,MAAMC,gBAAgB,GAAGlE,QAAQ,GAAGoB,KAAK,CAAClC,MAAM,GAAG+E,QAAQ,CAAC/E,MAAM,GAAG+E,QAAQ,KAAK,IAAI;IAEtF,IAAI,CAACC,gBAAgB,IAAI,CAACvE,WAAW,EAAE;MACrC;IACF;IAEA,IAAIwE,aAAa;IAEjB,IAAInE,QAAQ,EAAE;MACZmE,aAAa,GAAG,EAAE;IACpB,CAAC,MAAM,IAAIF,QAAQ,IAAI,IAAI,EAAE;MAC3BE,aAAa,GAAG,EAAE;IACpB,CAAC,MAAM;MACL,MAAMnC,WAAW,GAAG3D,cAAc,CAAC4F,QAAQ,CAAC;MAC5CE,aAAa,GAAG,OAAOnC,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAG,EAAE;IACpE;IAEA,IAAI5D,UAAU,KAAK+F,aAAa,EAAE;MAChC;IACF;IAEAT,kBAAkB,CAACS,aAAa,CAAC;IAEjC,IAAI3C,aAAa,EAAE;MACjBA,aAAa,CAACwC,KAAK,EAAEG,aAAa,EAAE,OAAO,CAAC;IAC9C;EACF,CAAC,EAAE,CAAC9F,cAAc,EAAED,UAAU,EAAE4B,QAAQ,EAAEwB,aAAa,EAAEkC,kBAAkB,EAAE/D,WAAW,EAAEyB,KAAK,CAAC,CAAC;EACjG,MAAMgD,SAAS,GAAGtH,KAAK,CAAC6F,MAAM,CAAC,CAAC;EAChC7F,KAAK,CAACuH,SAAS,CAAC,MAAM;IACpB,MAAMC,WAAW,GAAGlD,KAAK,KAAKgD,SAAS,CAACG,OAAO;IAC/CH,SAAS,CAACG,OAAO,GAAGnD,KAAK;IAEzB,IAAIwC,OAAO,IAAI,CAACU,WAAW,EAAE;MAC3B;IACF,CAAC,CAAC;;IAGF,IAAI1E,QAAQ,IAAI,CAAC0E,WAAW,EAAE;MAC5B;IACF;IAEAR,eAAe,CAAC,IAAI,EAAE1C,KAAK,CAAC;EAC9B,CAAC,EAAE,CAACA,KAAK,EAAE0C,eAAe,EAAEF,OAAO,EAAEQ,SAAS,EAAExE,QAAQ,CAAC,CAAC;EAC1D,MAAM,CAAC8B,IAAI,EAAE8C,YAAY,CAAC,GAAGpH,aAAa,CAAC;IACzCmG,UAAU,EAAE5B,QAAQ;IACpB6B,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE3D,aAAa;IACnB6D,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACc,aAAa,EAAEC,gBAAgB,CAAC,GAAG5H,KAAK,CAACmG,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM0B,yBAAyB,GAAG,CAAC3E,QAAQ,IAAIoB,KAAK,IAAI,IAAI,IAAIhD,UAAU,KAAKC,cAAc,CAAC+C,KAAK,CAAC;EACpG,MAAMwD,SAAS,GAAGlD,IAAI,IAAI,CAACG,QAAQ;EACnC,MAAMrD,eAAe,GAAGoG,SAAS,GAAGrE,aAAa,CAACpC,OAAO,CAACM,MAAM,CAACC,MAAM,IAAI;IACzE,IAAI8B,qBAAqB,IAAI,CAACR,QAAQ,GAAGoB,KAAK,GAAG,CAACA,KAAK,CAAC,EAAEyD,IAAI,CAACC,MAAM,IAAIA,MAAM,KAAK,IAAI,IAAI3D,oBAAoB,CAACzC,MAAM,EAAEoG,MAAM,CAAC,CAAC,EAAE;MACjI,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;EAAE;EACJ;EACA;IACE1G,UAAU,EAAEuG,yBAAyB,IAAIF,aAAa,GAAG,EAAE,GAAGrG,UAAU;IACxEC;EACF,CAAC,CAAC,GAAG,EAAE;EACP,MAAM0G,gBAAgB,GAAGrD,IAAI,IAAIlD,eAAe,CAACU,MAAM,GAAG,CAAC,IAAI,CAAC2C,QAAQ;EAExE,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIf,KAAK,KAAK,IAAI,IAAI,CAACxB,QAAQ,IAAIzB,OAAO,CAACe,MAAM,GAAG,CAAC,EAAE;MACrD,MAAM8F,YAAY,GAAG,CAAChF,QAAQ,GAAGoB,KAAK,GAAG,CAACA,KAAK,CAAC,EAAE3C,MAAM,CAACqG,MAAM,IAAI,CAAC3G,OAAO,CAAC0G,IAAI,CAACnG,MAAM,IAAIyC,oBAAoB,CAACzC,MAAM,EAAEoG,MAAM,CAAC,CAAC,CAAC;MAEjI,IAAIE,YAAY,CAAC9F,MAAM,GAAG,CAAC,EAAE;QAC3BoD,OAAO,CAAC2C,IAAI,CAAC,CAAE,8BAA6BnF,aAAc,cAAa,EAAG,oCAAmCkF,YAAY,CAAC9F,MAAM,GAAG,CAAC,GAAGsD,IAAI,CAACvE,SAAS,CAAC+G,YAAY,CAAC,GAAGxC,IAAI,CAACvE,SAAS,CAAC+G,YAAY,CAAC,CAAC,CAAC,CAAE,KAAI,EAAE,6EAA6E,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,CAAC;MACxS;IACF;EACF;EAEA,MAAMC,QAAQ,GAAGjI,gBAAgB,CAACkI,UAAU,IAAI;IAC9C,IAAIA,UAAU,KAAK,CAAC,CAAC,EAAE;MACrBvC,QAAQ,CAAC0B,OAAO,CAACc,KAAK,CAAC,CAAC;IAC1B,CAAC,MAAM;MACLtC,QAAQ,CAACuC,aAAa,CAAE,oBAAmBF,UAAW,IAAG,CAAC,CAACC,KAAK,CAAC,CAAC;IACpE;EACF,CAAC,CAAC,CAAC,CAAC;;EAEJvI,KAAK,CAACuH,SAAS,CAAC,MAAM;IACpB,IAAIrE,QAAQ,IAAIkD,UAAU,GAAG9B,KAAK,CAAClC,MAAM,GAAG,CAAC,EAAE;MAC7CiE,aAAa,CAAC,CAAC,CAAC,CAAC;MACjBgC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAAC/D,KAAK,EAAEpB,QAAQ,EAAEkD,UAAU,EAAEiC,QAAQ,CAAC,CAAC;EAE3C,SAASI,gBAAgBA,CAACC,KAAK,EAAEC,SAAS,EAAE;IAC1C,IAAI,CAAC3C,UAAU,CAACyB,OAAO,IAAIiB,KAAK,KAAK,CAAC,CAAC,EAAE;MACvC,OAAO,CAAC,CAAC;IACX;IAEA,IAAIE,SAAS,GAAGF,KAAK;IAErB,OAAO,IAAI,EAAE;MACX;MACA,IAAIC,SAAS,KAAK,MAAM,IAAIC,SAAS,KAAKlH,eAAe,CAACU,MAAM,IAAIuG,SAAS,KAAK,UAAU,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;QAChH,OAAO,CAAC,CAAC;MACX;MAEA,MAAMhH,MAAM,GAAGoE,UAAU,CAACyB,OAAO,CAACe,aAAa,CAAE,uBAAsBI,SAAU,IAAG,CAAC,CAAC,CAAC;;MAEvF,MAAMC,iBAAiB,GAAGtF,sBAAsB,GAAG,KAAK,GAAG,CAAC3B,MAAM,IAAIA,MAAM,CAACyB,QAAQ,IAAIzB,MAAM,CAACkH,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM;MAExI,IAAIlH,MAAM,IAAI,CAACA,MAAM,CAACmH,YAAY,CAAC,UAAU,CAAC,IAAIF,iBAAiB,EAAE;QACnE;QACAD,SAAS,IAAID,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MAC5C,CAAC,MAAM;QACL,OAAOC,SAAS;MAClB;IACF;EACF;EAEA,MAAMI,mBAAmB,GAAG5I,gBAAgB,CAAC,CAAC;IAC5C8G,KAAK;IACLwB,KAAK;IACLO,MAAM,GAAG;EACX,CAAC,KAAK;IACJ1C,mBAAmB,CAACkB,OAAO,GAAGiB,KAAK,CAAC,CAAC;;IAErC,IAAIA,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB3C,QAAQ,CAAC0B,OAAO,CAACyB,eAAe,CAAC,uBAAuB,CAAC;IAC3D,CAAC,MAAM;MACLnD,QAAQ,CAAC0B,OAAO,CAAC0B,YAAY,CAAC,uBAAuB,EAAG,GAAElF,EAAG,WAAUyE,KAAM,EAAC,CAAC;IACjF;IAEA,IAAIjE,iBAAiB,EAAE;MACrBA,iBAAiB,CAACyC,KAAK,EAAEwB,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,GAAGhH,eAAe,CAACgH,KAAK,CAAC,EAAEO,MAAM,CAAC;IAChF;IAEA,IAAI,CAACjD,UAAU,CAACyB,OAAO,EAAE;MACvB;IACF;IAEA,MAAM2B,IAAI,GAAGpD,UAAU,CAACyB,OAAO,CAACe,aAAa,CAAC,6BAA6B,CAAC;IAE5E,IAAIY,IAAI,EAAE;MACRA,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,aAAa,CAAC;MACpCF,IAAI,CAACC,SAAS,CAACC,MAAM,CAAC,kBAAkB,CAAC;IAC3C;IAEA,MAAMC,WAAW,GAAGvD,UAAU,CAACyB,OAAO,CAAC+B,aAAa,CAAChB,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAAC;;IAExF,IAAI,CAACe,WAAW,EAAE;MAChB;IACF;IAEA,IAAIb,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBa,WAAW,CAACE,SAAS,GAAG,CAAC;MACzB;IACF;IAEA,MAAM7H,MAAM,GAAGoE,UAAU,CAACyB,OAAO,CAACe,aAAa,CAAE,uBAAsBE,KAAM,IAAG,CAAC;IAEjF,IAAI,CAAC9G,MAAM,EAAE;MACX;IACF;IAEAA,MAAM,CAACyH,SAAS,CAACK,GAAG,CAAC,aAAa,CAAC;IAEnC,IAAIT,MAAM,KAAK,UAAU,EAAE;MACzBrH,MAAM,CAACyH,SAAS,CAACK,GAAG,CAAC,kBAAkB,CAAC;IAC1C,CAAC,CAAC;IACF;IACA;IACA;IACA;;IAGA,IAAIH,WAAW,CAACI,YAAY,GAAGJ,WAAW,CAACK,YAAY,IAAIX,MAAM,KAAK,OAAO,EAAE;MAC7E,MAAMY,OAAO,GAAGjI,MAAM;MACtB,MAAMkI,YAAY,GAAGP,WAAW,CAACK,YAAY,GAAGL,WAAW,CAACE,SAAS;MACrE,MAAMM,aAAa,GAAGF,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACI,YAAY;MAE9D,IAAIF,aAAa,GAAGD,YAAY,EAAE;QAChCP,WAAW,CAACE,SAAS,GAAGM,aAAa,GAAGR,WAAW,CAACK,YAAY;MAClE,CAAC,MAAM,IAAIC,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACI,YAAY,IAAIlG,OAAO,GAAG,GAAG,GAAG,CAAC,CAAC,GAAGwF,WAAW,CAACE,SAAS,EAAE;QACjGF,WAAW,CAACE,SAAS,GAAGI,OAAO,CAACG,SAAS,GAAGH,OAAO,CAACI,YAAY,IAAIlG,OAAO,GAAG,GAAG,GAAG,CAAC,CAAC;MACxF;IACF;EACF,CAAC,CAAC;EACF,MAAMmG,sBAAsB,GAAG9J,gBAAgB,CAAC,CAAC;IAC/C8G,KAAK;IACLiD,IAAI;IACJxB,SAAS,GAAG,MAAM;IAClBM,MAAM,GAAG;EACX,CAAC,KAAK;IACJ,IAAI,CAACnB,SAAS,EAAE;MACd;IACF;IAEA,MAAMsC,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,QAAQ,GAAG3I,eAAe,CAACU,MAAM,GAAG,CAAC;MAE3C,IAAI+H,IAAI,KAAK,OAAO,EAAE;QACpB,OAAO7D,kBAAkB;MAC3B;MAEA,IAAI6D,IAAI,KAAK,OAAO,EAAE;QACpB,OAAO,CAAC;MACV;MAEA,IAAIA,IAAI,KAAK,KAAK,EAAE;QAClB,OAAOE,QAAQ;MACjB;MAEA,MAAMC,QAAQ,GAAG/D,mBAAmB,CAACkB,OAAO,GAAG0C,IAAI;MAEnD,IAAIG,QAAQ,GAAG,CAAC,EAAE;QAChB,IAAIA,QAAQ,KAAK,CAAC,CAAC,IAAInG,kBAAkB,EAAE;UACzC,OAAO,CAAC,CAAC;QACX;QAEA,IAAIX,eAAe,IAAI+C,mBAAmB,CAACkB,OAAO,KAAK,CAAC,CAAC,IAAI8C,IAAI,CAACC,GAAG,CAACL,IAAI,CAAC,GAAG,CAAC,EAAE;UAC/E,OAAO,CAAC;QACV;QAEA,OAAOE,QAAQ;MACjB;MAEA,IAAIC,QAAQ,GAAGD,QAAQ,EAAE;QACvB,IAAIC,QAAQ,KAAKD,QAAQ,GAAG,CAAC,IAAIlG,kBAAkB,EAAE;UACnD,OAAO,CAAC,CAAC;QACX;QAEA,IAAIX,eAAe,IAAI+G,IAAI,CAACC,GAAG,CAACL,IAAI,CAAC,GAAG,CAAC,EAAE;UACzC,OAAOE,QAAQ;QACjB;QAEA,OAAO,CAAC;MACV;MAEA,OAAOC,QAAQ;IACjB,CAAC;IAED,MAAMG,SAAS,GAAGhC,gBAAgB,CAAC2B,YAAY,CAAC,CAAC,EAAEzB,SAAS,CAAC;IAC7DK,mBAAmB,CAAC;MAClBN,KAAK,EAAE+B,SAAS;MAChBxB,MAAM;MACN/B;IACF,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAIzE,YAAY,IAAI0H,IAAI,KAAK,OAAO,EAAE;MACpC,IAAIM,SAAS,KAAK,CAAC,CAAC,EAAE;QACpB1E,QAAQ,CAAC0B,OAAO,CAACnD,KAAK,GAAGhD,UAAU;MACrC,CAAC,MAAM;QACL,MAAMM,MAAM,GAAGL,cAAc,CAACG,eAAe,CAAC+I,SAAS,CAAC,CAAC;QACzD1E,QAAQ,CAAC0B,OAAO,CAACnD,KAAK,GAAG1C,MAAM,CAAC,CAAC;QACjC;;QAEA,MAAM8G,KAAK,GAAG9G,MAAM,CAACH,WAAW,CAAC,CAAC,CAACK,OAAO,CAACR,UAAU,CAACG,WAAW,CAAC,CAAC,CAAC;QAEpE,IAAIiH,KAAK,KAAK,CAAC,IAAIpH,UAAU,CAACc,MAAM,GAAG,CAAC,EAAE;UACxC2D,QAAQ,CAAC0B,OAAO,CAACiD,iBAAiB,CAACpJ,UAAU,CAACc,MAAM,EAAER,MAAM,CAACQ,MAAM,CAAC;QACtE;MACF;IACF;EACF,CAAC,CAAC;EACF,MAAMuI,oBAAoB,GAAG3K,KAAK,CAACiH,WAAW,CAAC,MAAM;IACnD,IAAI,CAACa,SAAS,EAAE;MACd;IACF;IAEA,MAAM8C,SAAS,GAAG1H,QAAQ,GAAGoB,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC;;IAE/C,IAAI5C,eAAe,CAACU,MAAM,KAAK,CAAC,IAAIwI,SAAS,IAAI,IAAI,EAAE;MACrDV,sBAAsB,CAAC;QACrBC,IAAI,EAAE;MACR,CAAC,CAAC;MACF;IACF;IAEA,IAAI,CAACnE,UAAU,CAACyB,OAAO,EAAE;MACvB;IACF,CAAC,CAAC;;IAGF,IAAImD,SAAS,IAAI,IAAI,EAAE;MACrB,MAAMC,aAAa,GAAGnJ,eAAe,CAAC6E,mBAAmB,CAACkB,OAAO,CAAC,CAAC,CAAC;;MAEpE,IAAIvE,QAAQ,IAAI2H,aAAa,IAAI7I,SAAS,CAACsC,KAAK,EAAEwG,GAAG,IAAIzG,oBAAoB,CAACwG,aAAa,EAAEC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QACzG;MACF;MAEA,MAAMC,SAAS,GAAG/I,SAAS,CAACN,eAAe,EAAEsJ,UAAU,IAAI3G,oBAAoB,CAAC2G,UAAU,EAAEJ,SAAS,CAAC,CAAC;MAEvG,IAAIG,SAAS,KAAK,CAAC,CAAC,EAAE;QACpBb,sBAAsB,CAAC;UACrBC,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,MAAM;QACLnB,mBAAmB,CAAC;UAClBN,KAAK,EAAEqC;QACT,CAAC,CAAC;MACJ;MAEA;IACF,CAAC,CAAC;;IAGF,IAAIxE,mBAAmB,CAACkB,OAAO,IAAI/F,eAAe,CAACU,MAAM,GAAG,CAAC,EAAE;MAC7D4G,mBAAmB,CAAC;QAClBN,KAAK,EAAEhH,eAAe,CAACU,MAAM,GAAG;MAClC,CAAC,CAAC;MACF;IACF,CAAC,CAAC;;IAGF4G,mBAAmB,CAAC;MAClBN,KAAK,EAAEnC,mBAAmB,CAACkB;IAC7B,CAAC,CAAC,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;EAAC;EACJ/F,eAAe,CAACU,MAAM;EAAE;EACxB;EACAc,QAAQ,GAAG,KAAK,GAAGoB,KAAK,EAAEZ,qBAAqB,EAAEwG,sBAAsB,EAAElB,mBAAmB,EAAElB,SAAS,EAAExG,UAAU,EAAE4B,QAAQ,CAAC,CAAC;EAC/H,MAAM+H,gBAAgB,GAAG7K,gBAAgB,CAAC8K,IAAI,IAAI;IAChDhL,MAAM,CAAC8F,UAAU,EAAEkF,IAAI,CAAC;IAExB,IAAI,CAACA,IAAI,EAAE;MACT;IACF;IAEAP,oBAAoB,CAAC,CAAC;EACxB,CAAC,CAAC;EAEF,IAAIxF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACArF,KAAK,CAACuH,SAAS,CAAC,MAAM;MACpB,IAAI,CAACxB,QAAQ,CAAC0B,OAAO,IAAI1B,QAAQ,CAAC0B,OAAO,CAAC0D,QAAQ,KAAK,OAAO,EAAE;QAC9D,IAAIpF,QAAQ,CAAC0B,OAAO,IAAI1B,QAAQ,CAAC0B,OAAO,CAAC0D,QAAQ,KAAK,UAAU,EAAE;UAChE3F,OAAO,CAAC2C,IAAI,CAAC,CAAE,sCAAqCnF,aAAc,4BAA2B,EAAG,4EAA2E,EAAG,qGAAoG,EAAG,mFAAkF,CAAC,CAACoF,IAAI,CAAC,IAAI,CAAC,CAAC;QACtX,CAAC,MAAM;UACL5C,OAAO,CAACC,KAAK,CAAC,CAAE,6DAA4DM,QAAQ,CAAC0B,OAAQ,0CAAyC,EAAG,YAAWzE,aAAc,4BAA2B,EAAE,EAAE,EAAEA,aAAa,KAAK,iBAAiB,GAAG,mHAAmH,GAAG,8DAA8D,CAAC,CAACoF,IAAI,CAAC,IAAI,CAAC,CAAC;QAC5a;MACF;IACF,CAAC,EAAE,CAACpF,aAAa,CAAC,CAAC;EACrB;EAEAhD,KAAK,CAACuH,SAAS,CAAC,MAAM;IACpBoD,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,CAACA,oBAAoB,CAAC,CAAC;EAE1B,MAAMS,UAAU,GAAGlE,KAAK,IAAI;IAC1B,IAAItC,IAAI,EAAE;MACR;IACF;IAEA8C,YAAY,CAAC,IAAI,CAAC;IAClBE,gBAAgB,CAAC,IAAI,CAAC;IAEtB,IAAIjD,MAAM,EAAE;MACVA,MAAM,CAACuC,KAAK,CAAC;IACf;EACF,CAAC;EAED,MAAMmE,WAAW,GAAGA,CAACnE,KAAK,EAAE+B,MAAM,KAAK;IACrC,IAAI,CAACrE,IAAI,EAAE;MACT;IACF;IAEA8C,YAAY,CAAC,KAAK,CAAC;IAEnB,IAAIlD,OAAO,EAAE;MACXA,OAAO,CAAC0C,KAAK,EAAE+B,MAAM,CAAC;IACxB;EACF,CAAC;EAED,MAAMqC,WAAW,GAAGA,CAACpE,KAAK,EAAEC,QAAQ,EAAE8B,MAAM,EAAEsC,OAAO,KAAK;IACxD,IAAIrI,QAAQ,EAAE;MACZ,IAAIoB,KAAK,CAAClC,MAAM,KAAK+E,QAAQ,CAAC/E,MAAM,IAAIkC,KAAK,CAACkH,KAAK,CAAC,CAACV,GAAG,EAAE3I,CAAC,KAAK2I,GAAG,KAAK3D,QAAQ,CAAChF,CAAC,CAAC,CAAC,EAAE;QACpF;MACF;IACF,CAAC,MAAM,IAAImC,KAAK,KAAK6C,QAAQ,EAAE;MAC7B;IACF;IAEA,IAAI5C,QAAQ,EAAE;MACZA,QAAQ,CAAC2C,KAAK,EAAEC,QAAQ,EAAE8B,MAAM,EAAEsC,OAAO,CAAC;IAC5C;IAEA/E,aAAa,CAACW,QAAQ,CAAC;EACzB,CAAC;EAED,MAAMsE,OAAO,GAAGzL,KAAK,CAAC6F,MAAM,CAAC,KAAK,CAAC;EAEnC,MAAM6F,cAAc,GAAGA,CAACxE,KAAK,EAAEtF,MAAM,EAAE+J,UAAU,GAAG,cAAc,EAAEC,MAAM,GAAG,SAAS,KAAK;IACzF,IAAI3C,MAAM,GAAG0C,UAAU;IACvB,IAAIxE,QAAQ,GAAGvF,MAAM;IAErB,IAAIsB,QAAQ,EAAE;MACZiE,QAAQ,GAAG0E,KAAK,CAACC,OAAO,CAACxH,KAAK,CAAC,GAAGA,KAAK,CAACvC,KAAK,CAAC,CAAC,GAAG,EAAE;MAEpD,IAAIoD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,MAAM0G,OAAO,GAAG5E,QAAQ,CAACxF,MAAM,CAACmJ,GAAG,IAAIzG,oBAAoB,CAACzC,MAAM,EAAEkJ,GAAG,CAAC,CAAC;QAEzE,IAAIiB,OAAO,CAAC3J,MAAM,GAAG,CAAC,EAAE;UACtBoD,OAAO,CAACC,KAAK,CAAC,CAAE,+CAA8CzC,aAAc,yCAAwC,EAAG,0EAAyE+I,OAAO,CAAC3J,MAAO,WAAU,CAAC,CAACgG,IAAI,CAAC,IAAI,CAAC,CAAC;QACxO;MACF;MAEA,MAAM2C,SAAS,GAAG/I,SAAS,CAACmF,QAAQ,EAAEyD,SAAS,IAAIvG,oBAAoB,CAACzC,MAAM,EAAEgJ,SAAS,CAAC,CAAC;MAE3F,IAAIG,SAAS,KAAK,CAAC,CAAC,EAAE;QACpB5D,QAAQ,CAAC6E,IAAI,CAACpK,MAAM,CAAC;MACvB,CAAC,MAAM,IAAIgK,MAAM,KAAK,UAAU,EAAE;QAChCzE,QAAQ,CAAC8E,MAAM,CAAClB,SAAS,EAAE,CAAC,CAAC;QAC7B9B,MAAM,GAAG,cAAc;MACzB;IACF;IAEAjC,eAAe,CAACE,KAAK,EAAEC,QAAQ,CAAC;IAChCmE,WAAW,CAACpE,KAAK,EAAEC,QAAQ,EAAE8B,MAAM,EAAE;MACnCrH;IACF,CAAC,CAAC;IAEF,IAAI,CAACwB,oBAAoB,KAAK,CAAC8D,KAAK,IAAI,CAACA,KAAK,CAACgF,OAAO,IAAI,CAAChF,KAAK,CAACiF,OAAO,CAAC,EAAE;MACzEd,WAAW,CAACnE,KAAK,EAAE+B,MAAM,CAAC;IAC5B;IAEA,IAAIrG,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,OAAO,IAAI6I,OAAO,CAAChE,OAAO,IAAI7E,YAAY,KAAK,OAAO,IAAI,CAAC6I,OAAO,CAAChE,OAAO,EAAE;MACxH1B,QAAQ,CAAC0B,OAAO,CAAC2E,IAAI,CAAC,CAAC;IACzB;EACF,CAAC;EAED,SAASC,aAAaA,CAAC3D,KAAK,EAAEC,SAAS,EAAE;IACvC,IAAID,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,OAAO,CAAC,CAAC;IACX;IAEA,IAAIE,SAAS,GAAGF,KAAK;IAErB,OAAO,IAAI,EAAE;MACX;MACA,IAAIC,SAAS,KAAK,MAAM,IAAIC,SAAS,KAAKtE,KAAK,CAAClC,MAAM,IAAIuG,SAAS,KAAK,UAAU,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;QACtG,OAAO,CAAC,CAAC;MACX;MAEA,MAAMhH,MAAM,GAAGqE,QAAQ,CAACuC,aAAa,CAAE,oBAAmBI,SAAU,IAAG,CAAC,CAAC,CAAC;;MAE1E,IAAI,CAAChH,MAAM,IAAI,CAACA,MAAM,CAACmH,YAAY,CAAC,UAAU,CAAC,IAAInH,MAAM,CAACyB,QAAQ,IAAIzB,MAAM,CAACkH,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM,EAAE;QACrHF,SAAS,IAAID,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MAC5C,CAAC,MAAM;QACL,OAAOC,SAAS;MAClB;IACF;EACF;EAEA,MAAM0D,cAAc,GAAGA,CAACpF,KAAK,EAAEyB,SAAS,KAAK;IAC3C,IAAI,CAACzF,QAAQ,EAAE;MACb;IACF;IAEA,IAAI5B,UAAU,KAAK,EAAE,EAAE;MACrB+J,WAAW,CAACnE,KAAK,EAAE,aAAa,CAAC;IACnC;IAEA,IAAIqF,OAAO,GAAGnG,UAAU;IAExB,IAAIA,UAAU,KAAK,CAAC,CAAC,EAAE;MACrB,IAAI9E,UAAU,KAAK,EAAE,IAAIqH,SAAS,KAAK,UAAU,EAAE;QACjD4D,OAAO,GAAGjI,KAAK,CAAClC,MAAM,GAAG,CAAC;MAC5B;IACF,CAAC,MAAM;MACLmK,OAAO,IAAI5D,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;MAExC,IAAI4D,OAAO,GAAG,CAAC,EAAE;QACfA,OAAO,GAAG,CAAC;MACb;MAEA,IAAIA,OAAO,KAAKjI,KAAK,CAAClC,MAAM,EAAE;QAC5BmK,OAAO,GAAG,CAAC,CAAC;MACd;IACF;IAEAA,OAAO,GAAGF,aAAa,CAACE,OAAO,EAAE5D,SAAS,CAAC;IAC3CtC,aAAa,CAACkG,OAAO,CAAC;IACtBlE,QAAQ,CAACkE,OAAO,CAAC;EACnB,CAAC;EAED,MAAMC,WAAW,GAAGtF,KAAK,IAAI;IAC3BtB,WAAW,CAAC6B,OAAO,GAAG,IAAI;IAC1Bb,kBAAkB,CAAC,EAAE,CAAC;IAEtB,IAAIlC,aAAa,EAAE;MACjBA,aAAa,CAACwC,KAAK,EAAE,EAAE,EAAE,OAAO,CAAC;IACnC;IAEAoE,WAAW,CAACpE,KAAK,EAAEhE,QAAQ,GAAG,EAAE,GAAG,IAAI,EAAE,OAAO,CAAC;EACnD,CAAC;EAED,MAAMuJ,aAAa,GAAGC,KAAK,IAAIxF,KAAK,IAAI;IACtC,IAAIwF,KAAK,CAACC,SAAS,EAAE;MACnBD,KAAK,CAACC,SAAS,CAACzF,KAAK,CAAC;IACxB;IAEA,IAAIA,KAAK,CAAC0F,mBAAmB,EAAE;MAC7B;IACF;IAEA,IAAIxG,UAAU,KAAK,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAACtE,OAAO,CAACoF,KAAK,CAAC2F,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MAC9ExG,aAAa,CAAC,CAAC,CAAC,CAAC;MACjBgC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC;;IAGF,IAAInB,KAAK,CAAC4F,KAAK,KAAK,GAAG,EAAE;MACvB,QAAQ5F,KAAK,CAAC2F,GAAG;QACf,KAAK,MAAM;UACT,IAAI/E,SAAS,IAAI9D,iBAAiB,EAAE;YAClC;YACAkD,KAAK,CAAC6F,cAAc,CAAC,CAAC;YACtB7C,sBAAsB,CAAC;cACrBC,IAAI,EAAE,OAAO;cACbxB,SAAS,EAAE,MAAM;cACjBM,MAAM,EAAE,UAAU;cAClB/B;YACF,CAAC,CAAC;UACJ;UAEA;QAEF,KAAK,KAAK;UACR,IAAIY,SAAS,IAAI9D,iBAAiB,EAAE;YAClC;YACAkD,KAAK,CAAC6F,cAAc,CAAC,CAAC;YACtB7C,sBAAsB,CAAC;cACrBC,IAAI,EAAE,KAAK;cACXxB,SAAS,EAAE,UAAU;cACrBM,MAAM,EAAE,UAAU;cAClB/B;YACF,CAAC,CAAC;UACJ;UAEA;QAEF,KAAK,QAAQ;UACX;UACAA,KAAK,CAAC6F,cAAc,CAAC,CAAC;UACtB7C,sBAAsB,CAAC;YACrBC,IAAI,EAAE,CAAC7H,QAAQ;YACfqG,SAAS,EAAE,UAAU;YACrBM,MAAM,EAAE,UAAU;YAClB/B;UACF,CAAC,CAAC;UACFkE,UAAU,CAAClE,KAAK,CAAC;UACjB;QAEF,KAAK,UAAU;UACb;UACAA,KAAK,CAAC6F,cAAc,CAAC,CAAC;UACtB7C,sBAAsB,CAAC;YACrBC,IAAI,EAAE7H,QAAQ;YACdqG,SAAS,EAAE,MAAM;YACjBM,MAAM,EAAE,UAAU;YAClB/B;UACF,CAAC,CAAC;UACFkE,UAAU,CAAClE,KAAK,CAAC;UACjB;QAEF,KAAK,WAAW;UACd;UACAA,KAAK,CAAC6F,cAAc,CAAC,CAAC;UACtB7C,sBAAsB,CAAC;YACrBC,IAAI,EAAE,CAAC;YACPxB,SAAS,EAAE,MAAM;YACjBM,MAAM,EAAE,UAAU;YAClB/B;UACF,CAAC,CAAC;UACFkE,UAAU,CAAClE,KAAK,CAAC;UACjB;QAEF,KAAK,SAAS;UACZ;UACAA,KAAK,CAAC6F,cAAc,CAAC,CAAC;UACtB7C,sBAAsB,CAAC;YACrBC,IAAI,EAAE,CAAC,CAAC;YACRxB,SAAS,EAAE,UAAU;YACrBM,MAAM,EAAE,UAAU;YAClB/B;UACF,CAAC,CAAC;UACFkE,UAAU,CAAClE,KAAK,CAAC;UACjB;QAEF,KAAK,WAAW;UACdoF,cAAc,CAACpF,KAAK,EAAE,UAAU,CAAC;UACjC;QAEF,KAAK,YAAY;UACfoF,cAAc,CAACpF,KAAK,EAAE,MAAM,CAAC;UAC7B;QAEF,KAAK,OAAO;UACV,IAAIX,mBAAmB,CAACkB,OAAO,KAAK,CAAC,CAAC,IAAIK,SAAS,EAAE;YACnD,MAAMlG,MAAM,GAAGF,eAAe,CAAC6E,mBAAmB,CAACkB,OAAO,CAAC;YAC3D,MAAMpE,QAAQ,GAAGM,iBAAiB,GAAGA,iBAAiB,CAAC/B,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC;;YAExEsF,KAAK,CAAC6F,cAAc,CAAC,CAAC;YAEtB,IAAI1J,QAAQ,EAAE;cACZ;YACF;YAEAqI,cAAc,CAACxE,KAAK,EAAEtF,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC;;YAE/C,IAAIa,YAAY,EAAE;cAChBsD,QAAQ,CAAC0B,OAAO,CAACiD,iBAAiB,CAAC3E,QAAQ,CAAC0B,OAAO,CAACnD,KAAK,CAAClC,MAAM,EAAE2D,QAAQ,CAAC0B,OAAO,CAACnD,KAAK,CAAClC,MAAM,CAAC;YAClG;UACF,CAAC,MAAM,IAAIU,QAAQ,IAAIxB,UAAU,KAAK,EAAE,IAAIuG,yBAAyB,KAAK,KAAK,EAAE;YAC/E,IAAI3E,QAAQ,EAAE;cACZ;cACAgE,KAAK,CAAC6F,cAAc,CAAC,CAAC;YACxB;YAEArB,cAAc,CAACxE,KAAK,EAAE5F,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC;UAC/D;UAEA;QAEF,KAAK,QAAQ;UACX,IAAIwG,SAAS,EAAE;YACb;YACAZ,KAAK,CAAC6F,cAAc,CAAC,CAAC,CAAC,CAAC;;YAExB7F,KAAK,CAAC8F,eAAe,CAAC,CAAC;YACvB3B,WAAW,CAACnE,KAAK,EAAE,QAAQ,CAAC;UAC9B,CAAC,MAAM,IAAInE,aAAa,KAAKzB,UAAU,KAAK,EAAE,IAAI4B,QAAQ,IAAIoB,KAAK,CAAClC,MAAM,GAAG,CAAC,CAAC,EAAE;YAC/E;YACA8E,KAAK,CAAC6F,cAAc,CAAC,CAAC,CAAC,CAAC;;YAExB7F,KAAK,CAAC8F,eAAe,CAAC,CAAC;YACvBR,WAAW,CAACtF,KAAK,CAAC;UACpB;UAEA;QAEF,KAAK,WAAW;UACd,IAAIhE,QAAQ,IAAI,CAAC6B,QAAQ,IAAIzD,UAAU,KAAK,EAAE,IAAIgD,KAAK,CAAClC,MAAM,GAAG,CAAC,EAAE;YAClE,MAAMsG,KAAK,GAAGtC,UAAU,KAAK,CAAC,CAAC,GAAG9B,KAAK,CAAClC,MAAM,GAAG,CAAC,GAAGgE,UAAU;YAC/D,MAAMe,QAAQ,GAAG7C,KAAK,CAACvC,KAAK,CAAC,CAAC;YAC9BoF,QAAQ,CAAC8E,MAAM,CAACvD,KAAK,EAAE,CAAC,CAAC;YACzB4C,WAAW,CAACpE,KAAK,EAAEC,QAAQ,EAAE,cAAc,EAAE;cAC3CvF,MAAM,EAAE0C,KAAK,CAACoE,KAAK;YACrB,CAAC,CAAC;UACJ;UAEA;QAEF;MACF;IACF;EACF,CAAC;EAED,MAAMuE,WAAW,GAAG/F,KAAK,IAAI;IAC3BH,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAIjC,WAAW,IAAI,CAACc,WAAW,CAAC6B,OAAO,EAAE;MACvC2D,UAAU,CAAClE,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgG,UAAU,GAAGhG,KAAK,IAAI;IAC1B;IACA,IAAIlB,UAAU,CAACyB,OAAO,KAAK,IAAI,IAAIzB,UAAU,CAACyB,OAAO,CAAC+B,aAAa,CAAC2D,QAAQ,CAACC,QAAQ,CAACC,aAAa,CAAC,EAAE;MACpGtH,QAAQ,CAAC0B,OAAO,CAACc,KAAK,CAAC,CAAC;MACxB;IACF;IAEAxB,UAAU,CAAC,KAAK,CAAC;IACjBjB,UAAU,CAAC2B,OAAO,GAAG,IAAI;IACzB7B,WAAW,CAAC6B,OAAO,GAAG,KAAK;IAE3B,IAAI9E,UAAU,IAAI4D,mBAAmB,CAACkB,OAAO,KAAK,CAAC,CAAC,IAAIK,SAAS,EAAE;MACjE4D,cAAc,CAACxE,KAAK,EAAExF,eAAe,CAAC6E,mBAAmB,CAACkB,OAAO,CAAC,EAAE,MAAM,CAAC;IAC7E,CAAC,MAAM,IAAI9E,UAAU,IAAIG,QAAQ,IAAIxB,UAAU,KAAK,EAAE,EAAE;MACtDoK,cAAc,CAACxE,KAAK,EAAE5F,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC;IACvD,CAAC,MAAM,IAAIuB,WAAW,EAAE;MACtBmE,eAAe,CAACE,KAAK,EAAE5C,KAAK,CAAC;IAC/B;IAEA+G,WAAW,CAACnE,KAAK,EAAE,MAAM,CAAC;EAC5B,CAAC;EAED,MAAMoG,iBAAiB,GAAGpG,KAAK,IAAI;IACjC,MAAMC,QAAQ,GAAGD,KAAK,CAACqG,MAAM,CAACjJ,KAAK;IAEnC,IAAIhD,UAAU,KAAK6F,QAAQ,EAAE;MAC3BP,kBAAkB,CAACO,QAAQ,CAAC;MAC5BS,gBAAgB,CAAC,KAAK,CAAC;MAEvB,IAAIlD,aAAa,EAAE;QACjBA,aAAa,CAACwC,KAAK,EAAEC,QAAQ,EAAE,OAAO,CAAC;MACzC;IACF;IAEA,IAAIA,QAAQ,KAAK,EAAE,EAAE;MACnB,IAAI,CAAChE,gBAAgB,IAAI,CAACD,QAAQ,EAAE;QAClCoI,WAAW,CAACpE,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC;MACnC;IACF,CAAC,MAAM;MACLkE,UAAU,CAAClE,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsG,qBAAqB,GAAGtG,KAAK,IAAI;IACrC8B,mBAAmB,CAAC;MAClB9B,KAAK;MACLwB,KAAK,EAAE+E,MAAM,CAACvG,KAAK,CAACwG,aAAa,CAAC5E,YAAY,CAAC,mBAAmB,CAAC,CAAC;MACpEG,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAM0E,sBAAsB,GAAGA,CAAA,KAAM;IACnClC,OAAO,CAAChE,OAAO,GAAG,IAAI;EACxB,CAAC;EAED,MAAMmG,iBAAiB,GAAG1G,KAAK,IAAI;IACjC,MAAMwB,KAAK,GAAG+E,MAAM,CAACvG,KAAK,CAACwG,aAAa,CAAC5E,YAAY,CAAC,mBAAmB,CAAC,CAAC;IAC3E4C,cAAc,CAACxE,KAAK,EAAExF,eAAe,CAACgH,KAAK,CAAC,EAAE,cAAc,CAAC;IAC7D+C,OAAO,CAAChE,OAAO,GAAG,KAAK;EACzB,CAAC;EAED,MAAMoG,eAAe,GAAGnF,KAAK,IAAIxB,KAAK,IAAI;IACxC,MAAMC,QAAQ,GAAG7C,KAAK,CAACvC,KAAK,CAAC,CAAC;IAC9BoF,QAAQ,CAAC8E,MAAM,CAACvD,KAAK,EAAE,CAAC,CAAC;IACzB4C,WAAW,CAACpE,KAAK,EAAEC,QAAQ,EAAE,cAAc,EAAE;MAC3CvF,MAAM,EAAE0C,KAAK,CAACoE,KAAK;IACrB,CAAC,CAAC;EACJ,CAAC;EAED,MAAMoF,oBAAoB,GAAG5G,KAAK,IAAI;IACpC,IAAItC,IAAI,EAAE;MACRyG,WAAW,CAACnE,KAAK,EAAE,aAAa,CAAC;IACnC,CAAC,MAAM;MACLkE,UAAU,CAAClE,KAAK,CAAC;IACnB;EACF,CAAC,CAAC,CAAC;;EAGH,MAAM6G,eAAe,GAAG7G,KAAK,IAAI;IAC/B,IAAIA,KAAK,CAACqG,MAAM,CAACzE,YAAY,CAAC,IAAI,CAAC,KAAK7E,EAAE,EAAE;MAC1CiD,KAAK,CAAC6F,cAAc,CAAC,CAAC;IACxB;EACF,CAAC,CAAC,CAAC;;EAGH,MAAMiB,WAAW,GAAGA,CAAA,KAAM;IACxBjI,QAAQ,CAAC0B,OAAO,CAACc,KAAK,CAAC,CAAC;IAExB,IAAIvD,aAAa,IAAIc,UAAU,CAAC2B,OAAO,IAAI1B,QAAQ,CAAC0B,OAAO,CAACwG,YAAY,GAAGlI,QAAQ,CAAC0B,OAAO,CAACyG,cAAc,KAAK,CAAC,EAAE;MAChHnI,QAAQ,CAAC0B,OAAO,CAAC0G,MAAM,CAAC,CAAC;IAC3B;IAEArI,UAAU,CAAC2B,OAAO,GAAG,KAAK;EAC5B,CAAC;EAED,MAAM2G,oBAAoB,GAAGlH,KAAK,IAAI;IACpC,IAAI5F,UAAU,KAAK,EAAE,IAAI,CAACsD,IAAI,EAAE;MAC9BkJ,oBAAoB,CAAC5G,KAAK,CAAC;IAC7B;EACF,CAAC;EAED,IAAImH,KAAK,GAAGvL,QAAQ,IAAIxB,UAAU,CAACc,MAAM,GAAG,CAAC;EAC7CiM,KAAK,GAAGA,KAAK,KAAKnL,QAAQ,GAAGoB,KAAK,CAAClC,MAAM,GAAG,CAAC,GAAGkC,KAAK,KAAK,IAAI,CAAC;EAC/D,IAAIgK,cAAc,GAAG5M,eAAe;EAEpC,IAAIqC,OAAO,EAAE;IACX;IACA,MAAMwK,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC;IACzB,IAAIrG,IAAI,GAAG,KAAK;IAChBmG,cAAc,GAAG5M,eAAe,CAAC+M,MAAM,CAAC,CAACC,GAAG,EAAE9M,MAAM,EAAE8G,KAAK,KAAK;MAC9D,MAAMiG,KAAK,GAAG5K,OAAO,CAACnC,MAAM,CAAC;MAE7B,IAAI8M,GAAG,CAACtM,MAAM,GAAG,CAAC,IAAIsM,GAAG,CAACA,GAAG,CAACtM,MAAM,GAAG,CAAC,CAAC,CAACuM,KAAK,KAAKA,KAAK,EAAE;QACzDD,GAAG,CAACA,GAAG,CAACtM,MAAM,GAAG,CAAC,CAAC,CAACf,OAAO,CAAC2K,IAAI,CAACpK,MAAM,CAAC;MAC1C,CAAC,MAAM;QACL,IAAIuD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC,IAAIkJ,OAAO,CAACK,GAAG,CAACD,KAAK,CAAC,IAAI,CAACxG,IAAI,EAAE;YAC/B3C,OAAO,CAAC2C,IAAI,CAAE,qEAAoEnF,aAAc,8BAA6B,EAAE,8EAA8E,CAAC;YAC9MmF,IAAI,GAAG,IAAI;UACb;UAEAoG,OAAO,CAACM,GAAG,CAACF,KAAK,EAAE,IAAI,CAAC;QAC1B;QAEAD,GAAG,CAAC1C,IAAI,CAAC;UACPa,GAAG,EAAEnE,KAAK;UACVA,KAAK;UACLiG,KAAK;UACLtN,OAAO,EAAE,CAACO,MAAM;QAClB,CAAC,CAAC;MACJ;MAEA,OAAO8M,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;EACR;EAEA,IAAIpL,YAAY,IAAIwD,OAAO,EAAE;IAC3BoG,UAAU,CAAC,CAAC;EACd;EAEA,OAAO;IACL4B,YAAY,EAAEA,CAACpC,KAAK,GAAG,CAAC,CAAC,KAAK3M,QAAQ,CAAC;MACrC,WAAW,EAAEkI,gBAAgB,GAAI,GAAEhE,EAAG,UAAS,GAAG;IACpD,CAAC,EAAEyI,KAAK,EAAE;MACRC,SAAS,EAAEF,aAAa,CAACC,KAAK,CAAC;MAC/BqC,WAAW,EAAEhB,eAAe;MAC5BiB,OAAO,EAAEhB;IACX,CAAC,CAAC;IACFiB,kBAAkB,EAAEA,CAAA,MAAO;MACzBhL,EAAE,EAAG,GAAEA,EAAG,QAAO;MACjBiL,OAAO,EAAEjL;IACX,CAAC,CAAC;IACFkL,aAAa,EAAEA,CAAA,MAAO;MACpBlL,EAAE;MACFK,KAAK,EAAEhD,UAAU;MACjB8N,MAAM,EAAElC,UAAU;MAClBmC,OAAO,EAAEpC,WAAW;MACpB1I,QAAQ,EAAE+I,iBAAiB;MAC3ByB,WAAW,EAAEX,oBAAoB;MACjC;MACA;MACA,uBAAuB,EAAEtG,SAAS,GAAG,EAAE,GAAG,IAAI;MAC9C,mBAAmB,EAAErF,YAAY,GAAG,MAAM,GAAG,MAAM;MACnD,eAAe,EAAEwF,gBAAgB,GAAI,GAAEhE,EAAG,UAAS,GAAGsB,SAAS;MAC/D,eAAe,EAAE0C,gBAAgB;MACjC;MACA;MACAxF,YAAY,EAAE,KAAK;MACnB6M,GAAG,EAAEvJ,QAAQ;MACbwJ,cAAc,EAAE,MAAM;MACtBC,UAAU,EAAE,OAAO;MACnBC,IAAI,EAAE;IACR,CAAC,CAAC;IACFC,aAAa,EAAEA,CAAA,MAAO;MACpBC,QAAQ,EAAE,CAAC,CAAC;MACZX,OAAO,EAAExC;IACX,CAAC,CAAC;IACFoD,sBAAsB,EAAEA,CAAA,MAAO;MAC7BD,QAAQ,EAAE,CAAC,CAAC;MACZX,OAAO,EAAElB;IACX,CAAC,CAAC;IACF+B,WAAW,EAAEA,CAAC;MACZnH;IACF,CAAC,KAAK3I,QAAQ,CAAC;MACb8M,GAAG,EAAEnE,KAAK;MACV,gBAAgB,EAAEA,KAAK;MACvBiH,QAAQ,EAAE,CAAC;IACb,CAAC,EAAE,CAAC5K,QAAQ,IAAI;MACd+K,QAAQ,EAAEjC,eAAe,CAACnF,KAAK;IACjC,CAAC,CAAC;IACFqH,eAAe,EAAEA,CAAA,MAAO;MACtBN,IAAI,EAAE,SAAS;MACfxL,EAAE,EAAG,GAAEA,EAAG,UAAS;MACnB,iBAAiB,EAAG,GAAEA,EAAG,QAAO;MAChCqL,GAAG,EAAErE,gBAAgB;MACrB8D,WAAW,EAAE7H,KAAK,IAAI;QACpB;QACAA,KAAK,CAAC6F,cAAc,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;IACFiD,cAAc,EAAEA,CAAC;MACftH,KAAK;MACL9G;IACF,CAAC,KAAK;MACJ,MAAMqO,QAAQ,GAAG,CAAC/M,QAAQ,GAAGoB,KAAK,GAAG,CAACA,KAAK,CAAC,EAAEyD,IAAI,CAACC,MAAM,IAAIA,MAAM,IAAI,IAAI,IAAI3D,oBAAoB,CAACzC,MAAM,EAAEoG,MAAM,CAAC,CAAC;MACpH,MAAM3E,QAAQ,GAAGM,iBAAiB,GAAGA,iBAAiB,CAAC/B,MAAM,CAAC,GAAG,KAAK;MACtE,OAAO;QACLiL,GAAG,EAAEtL,cAAc,CAACK,MAAM,CAAC;QAC3B+N,QAAQ,EAAE,CAAC,CAAC;QACZF,IAAI,EAAE,QAAQ;QACdxL,EAAE,EAAG,GAAEA,EAAG,WAAUyE,KAAM,EAAC;QAC3BwH,WAAW,EAAE1C,qBAAqB;QAClCwB,OAAO,EAAEpB,iBAAiB;QAC1BuC,YAAY,EAAExC,sBAAsB;QACpC,mBAAmB,EAAEjF,KAAK;QAC1B,eAAe,EAAErF,QAAQ;QACzB,eAAe,EAAE4M;MACnB,CAAC;IACH,CAAC;IACDhM,EAAE;IACF3C,UAAU;IACVgD,KAAK;IACL+J,KAAK;IACLvG,SAAS;IACThB,OAAO,EAAEA,OAAO,IAAIV,UAAU,KAAK,CAAC,CAAC;IACrCH,QAAQ;IACRC,WAAW;IACXE,UAAU;IACVkI;EACF,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}