{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.decodeHTML = exports.decodeHTMLStrict = exports.decodeXML = void 0;\nvar entities_json_1 = __importDefault(require(\"./maps/entities.json\"));\nvar legacy_json_1 = __importDefault(require(\"./maps/legacy.json\"));\nvar xml_json_1 = __importDefault(require(\"./maps/xml.json\"));\nvar decode_codepoint_1 = __importDefault(require(\"./decode_codepoint\"));\nvar strictEntityRe = /&(?:[a-zA-Z0-9]+|#[xX][\\da-fA-F]+|#\\d+);/g;\nexports.decodeXML = getStrictDecoder(xml_json_1.default);\nexports.decodeHTMLStrict = getStrictDecoder(entities_json_1.default);\nfunction getStrictDecoder(map) {\n  var replace = getReplacer(map);\n  return function (str) {\n    return String(str).replace(strictEntityRe, replace);\n  };\n}\nvar sorter = function (a, b) {\n  return a < b ? 1 : -1;\n};\nexports.decodeHTML = function () {\n  var legacy = Object.keys(legacy_json_1.default).sort(sorter);\n  var keys = Object.keys(entities_json_1.default).sort(sorter);\n  for (var i = 0, j = 0; i < keys.length; i++) {\n    if (legacy[j] === keys[i]) {\n      keys[i] += \";?\";\n      j++;\n    } else {\n      keys[i] += \";\";\n    }\n  }\n  var re = new RegExp(\"&(?:\" + keys.join(\"|\") + \"|#[xX][\\\\da-fA-F]+;?|#\\\\d+;?)\", \"g\");\n  var replace = getReplacer(entities_json_1.default);\n  function replacer(str) {\n    if (str.substr(-1) !== \";\") str += \";\";\n    return replace(str);\n  }\n  // TODO consider creating a merged map\n  return function (str) {\n    return String(str).replace(re, replacer);\n  };\n}();\nfunction getReplacer(map) {\n  return function replace(str) {\n    if (str.charAt(1) === \"#\") {\n      var secondChar = str.charAt(2);\n      if (secondChar === \"X\" || secondChar === \"x\") {\n        return decode_codepoint_1.default(parseInt(str.substr(3), 16));\n      }\n      return decode_codepoint_1.default(parseInt(str.substr(2), 10));\n    }\n    // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n    return map[str.slice(1, -1)] || str;\n  };\n}", "map": {"version": 3, "names": ["__importDefault", "mod", "__esModule", "Object", "defineProperty", "exports", "value", "decodeHTML", "decodeHTMLStrict", "decodeXML", "entities_json_1", "require", "legacy_json_1", "xml_json_1", "decode_codepoint_1", "strictEntityRe", "getStrictDecoder", "default", "map", "replace", "getReplacer", "str", "String", "sorter", "a", "b", "legacy", "keys", "sort", "i", "j", "length", "re", "RegExp", "join", "replacer", "substr", "char<PERSON>t", "secondChar", "parseInt", "slice"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/entities/lib/decode.js"], "sourcesContent": ["\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.decodeHTML = exports.decodeHTMLStrict = exports.decodeXML = void 0;\nvar entities_json_1 = __importDefault(require(\"./maps/entities.json\"));\nvar legacy_json_1 = __importDefault(require(\"./maps/legacy.json\"));\nvar xml_json_1 = __importDefault(require(\"./maps/xml.json\"));\nvar decode_codepoint_1 = __importDefault(require(\"./decode_codepoint\"));\nvar strictEntityRe = /&(?:[a-zA-Z0-9]+|#[xX][\\da-fA-F]+|#\\d+);/g;\nexports.decodeXML = getStrictDecoder(xml_json_1.default);\nexports.decodeHTMLStrict = getStrictDecoder(entities_json_1.default);\nfunction getStrictDecoder(map) {\n    var replace = getReplacer(map);\n    return function (str) { return String(str).replace(strictEntityRe, replace); };\n}\nvar sorter = function (a, b) { return (a < b ? 1 : -1); };\nexports.decodeHTML = (function () {\n    var legacy = Object.keys(legacy_json_1.default).sort(sorter);\n    var keys = Object.keys(entities_json_1.default).sort(sorter);\n    for (var i = 0, j = 0; i < keys.length; i++) {\n        if (legacy[j] === keys[i]) {\n            keys[i] += \";?\";\n            j++;\n        }\n        else {\n            keys[i] += \";\";\n        }\n    }\n    var re = new RegExp(\"&(?:\" + keys.join(\"|\") + \"|#[xX][\\\\da-fA-F]+;?|#\\\\d+;?)\", \"g\");\n    var replace = getReplacer(entities_json_1.default);\n    function replacer(str) {\n        if (str.substr(-1) !== \";\")\n            str += \";\";\n        return replace(str);\n    }\n    // TODO consider creating a merged map\n    return function (str) { return String(str).replace(re, replacer); };\n})();\nfunction getReplacer(map) {\n    return function replace(str) {\n        if (str.charAt(1) === \"#\") {\n            var secondChar = str.charAt(2);\n            if (secondChar === \"X\" || secondChar === \"x\") {\n                return decode_codepoint_1.default(parseInt(str.substr(3), 16));\n            }\n            return decode_codepoint_1.default(parseInt(str.substr(2), 10));\n        }\n        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing\n        return map[str.slice(1, -1)] || str;\n    };\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAI,IAAI,IAAI,IAAI,CAACA,eAAe,IAAK,UAAUC,GAAG,EAAE;EACnE,OAAQA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAID,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAC7D,CAAC;AACDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,UAAU,GAAGF,OAAO,CAACG,gBAAgB,GAAGH,OAAO,CAACI,SAAS,GAAG,KAAK,CAAC;AAC1E,IAAIC,eAAe,GAAGV,eAAe,CAACW,OAAO,CAAC,sBAAsB,CAAC,CAAC;AACtE,IAAIC,aAAa,GAAGZ,eAAe,CAACW,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAClE,IAAIE,UAAU,GAAGb,eAAe,CAACW,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAC5D,IAAIG,kBAAkB,GAAGd,eAAe,CAACW,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACvE,IAAII,cAAc,GAAG,2CAA2C;AAChEV,OAAO,CAACI,SAAS,GAAGO,gBAAgB,CAACH,UAAU,CAACI,OAAO,CAAC;AACxDZ,OAAO,CAACG,gBAAgB,GAAGQ,gBAAgB,CAACN,eAAe,CAACO,OAAO,CAAC;AACpE,SAASD,gBAAgBA,CAACE,GAAG,EAAE;EAC3B,IAAIC,OAAO,GAAGC,WAAW,CAACF,GAAG,CAAC;EAC9B,OAAO,UAAUG,GAAG,EAAE;IAAE,OAAOC,MAAM,CAACD,GAAG,CAAC,CAACF,OAAO,CAACJ,cAAc,EAAEI,OAAO,CAAC;EAAE,CAAC;AAClF;AACA,IAAII,MAAM,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAQD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAAG,CAAC;AACzDpB,OAAO,CAACE,UAAU,GAAI,YAAY;EAC9B,IAAImB,MAAM,GAAGvB,MAAM,CAACwB,IAAI,CAACf,aAAa,CAACK,OAAO,CAAC,CAACW,IAAI,CAACL,MAAM,CAAC;EAC5D,IAAII,IAAI,GAAGxB,MAAM,CAACwB,IAAI,CAACjB,eAAe,CAACO,OAAO,CAAC,CAACW,IAAI,CAACL,MAAM,CAAC;EAC5D,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAED,CAAC,GAAGF,IAAI,CAACI,MAAM,EAAEF,CAAC,EAAE,EAAE;IACzC,IAAIH,MAAM,CAACI,CAAC,CAAC,KAAKH,IAAI,CAACE,CAAC,CAAC,EAAE;MACvBF,IAAI,CAACE,CAAC,CAAC,IAAI,IAAI;MACfC,CAAC,EAAE;IACP,CAAC,MACI;MACDH,IAAI,CAACE,CAAC,CAAC,IAAI,GAAG;IAClB;EACJ;EACA,IAAIG,EAAE,GAAG,IAAIC,MAAM,CAAC,MAAM,GAAGN,IAAI,CAACO,IAAI,CAAC,GAAG,CAAC,GAAG,+BAA+B,EAAE,GAAG,CAAC;EACnF,IAAIf,OAAO,GAAGC,WAAW,CAACV,eAAe,CAACO,OAAO,CAAC;EAClD,SAASkB,QAAQA,CAACd,GAAG,EAAE;IACnB,IAAIA,GAAG,CAACe,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EACtBf,GAAG,IAAI,GAAG;IACd,OAAOF,OAAO,CAACE,GAAG,CAAC;EACvB;EACA;EACA,OAAO,UAAUA,GAAG,EAAE;IAAE,OAAOC,MAAM,CAACD,GAAG,CAAC,CAACF,OAAO,CAACa,EAAE,EAAEG,QAAQ,CAAC;EAAE,CAAC;AACvE,CAAC,CAAE,CAAC;AACJ,SAASf,WAAWA,CAACF,GAAG,EAAE;EACtB,OAAO,SAASC,OAAOA,CAACE,GAAG,EAAE;IACzB,IAAIA,GAAG,CAACgB,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACvB,IAAIC,UAAU,GAAGjB,GAAG,CAACgB,MAAM,CAAC,CAAC,CAAC;MAC9B,IAAIC,UAAU,KAAK,GAAG,IAAIA,UAAU,KAAK,GAAG,EAAE;QAC1C,OAAOxB,kBAAkB,CAACG,OAAO,CAACsB,QAAQ,CAAClB,GAAG,CAACe,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;MAClE;MACA,OAAOtB,kBAAkB,CAACG,OAAO,CAACsB,QAAQ,CAAClB,GAAG,CAACe,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAClE;IACA;IACA,OAAOlB,GAAG,CAACG,GAAG,CAACmB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAInB,GAAG;EACvC,CAAC;AACL"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}