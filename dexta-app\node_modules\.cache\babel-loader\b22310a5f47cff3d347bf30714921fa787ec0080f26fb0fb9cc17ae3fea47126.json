{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { delayWhen } from './delayWhen';\nimport { timer } from '../observable/timer';\nexport function delay(due, scheduler) {\n  if (scheduler === void 0) {\n    scheduler = asyncScheduler;\n  }\n  var duration = timer(due, scheduler);\n  return delayWhen(function () {\n    return duration;\n  });\n}", "map": {"version": 3, "names": ["asyncScheduler", "<PERSON><PERSON>hen", "timer", "delay", "due", "scheduler", "duration"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\delay.ts"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { MonoTypeOperatorFunction, SchedulerLike } from '../types';\nimport { delayWhen } from './delayWhen';\nimport { timer } from '../observable/timer';\n\n/**\n * Delays the emission of items from the source Observable by a given timeout or\n * until a given Date.\n *\n * <span class=\"informal\">Time shifts each item by some specified amount of\n * milliseconds.</span>\n *\n * ![](delay.svg)\n *\n * If the delay argument is a Number, this operator time shifts the source\n * Observable by that amount of time expressed in milliseconds. The relative\n * time intervals between the values are preserved.\n *\n * If the delay argument is a Date, this operator time shifts the start of the\n * Observable execution until the given date occurs.\n *\n * ## Examples\n *\n * Delay each click by one second\n *\n * ```ts\n * import { fromEvent, delay } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const delayedClicks = clicks.pipe(delay(1000)); // each click emitted after 1 second\n * delayedClicks.subscribe(x => console.log(x));\n * ```\n *\n * Delay all clicks until a future date happens\n *\n * ```ts\n * import { fromEvent, delay } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const date = new Date('March 15, 2050 12:00:00'); // in the future\n * const delayedClicks = clicks.pipe(delay(date)); // click emitted only after that date\n * delayedClicks.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link delayWhen}\n * @see {@link throttle}\n * @see {@link throttleTime}\n * @see {@link debounce}\n * @see {@link debounceTime}\n * @see {@link sample}\n * @see {@link sampleTime}\n * @see {@link audit}\n * @see {@link auditTime}\n *\n * @param due The delay duration in milliseconds (a `number`) or a `Date` until\n * which the emission of the source items is delayed.\n * @param scheduler The {@link SchedulerLike} to use for managing the timers\n * that handle the time-shift for each item.\n * @return A function that returns an Observable that delays the emissions of\n * the source Observable by the specified timeout or Date.\n */\nexport function delay<T>(due: number | Date, scheduler: SchedulerLike = asyncScheduler): MonoTypeOperatorFunction<T> {\n  const duration = timer(due, scheduler);\n  return delayWhen(() => duration);\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,oBAAoB;AAEnD,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,KAAK,QAAQ,qBAAqB;AA0D3C,OAAM,SAAUC,KAAKA,CAAIC,GAAkB,EAAEC,SAAyC;EAAzC,IAAAA,SAAA;IAAAA,SAAA,GAAAL,cAAyC;EAAA;EACpF,IAAMM,QAAQ,GAAGJ,KAAK,CAACE,GAAG,EAAEC,SAAS,CAAC;EACtC,OAAOJ,SAAS,CAAC;IAAM,OAAAK,QAAQ;EAAR,CAAQ,CAAC;AAClC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}