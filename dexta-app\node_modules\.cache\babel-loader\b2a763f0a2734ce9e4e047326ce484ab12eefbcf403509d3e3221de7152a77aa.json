{"ast": null, "code": "/* The following list is defined in React's core */\nvar IS_UNITLESS = {\n  animationIterationCount: true,\n  boxFlex: true,\n  boxFlexGroup: true,\n  boxOrdinalGroup: true,\n  columnCount: true,\n  flex: true,\n  flexGrow: true,\n  flexPositive: true,\n  flexShrink: true,\n  flexNegative: true,\n  flexOrder: true,\n  gridRow: true,\n  gridColumn: true,\n  fontWeight: true,\n  lineClamp: true,\n  lineHeight: true,\n  opacity: true,\n  order: true,\n  orphans: true,\n  tabSize: true,\n  widows: true,\n  zIndex: true,\n  zoom: true,\n  // SVG-related properties\n  fillOpacity: true,\n  stopOpacity: true,\n  strokeDashoffset: true,\n  strokeOpacity: true,\n  strokeWidth: true\n};\nmodule.exports = function (name, value) {\n  if (typeof value === 'number' && !IS_UNITLESS[name]) {\n    return value + 'px';\n  } else {\n    return value;\n  }\n};", "map": {"version": 3, "names": ["IS_UNITLESS", "animationIterationCount", "boxFlex", "boxFlexGroup", "boxOrdinalGroup", "columnCount", "flex", "flexGrow", "flexPositive", "flexShrink", "flexNegative", "flexOrder", "gridRow", "gridColumn", "fontWeight", "lineClamp", "lineHeight", "opacity", "order", "orphans", "tabSize", "widows", "zIndex", "zoom", "fillOpacity", "stopOpacity", "strokeDashoffset", "strokeOpacity", "strokeWidth", "module", "exports", "name", "value"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/add-px-to-style/index.js"], "sourcesContent": ["/* The following list is defined in React's core */\nvar IS_UNITLESS = {\n  animationIterationCount: true,\n  boxFlex: true,\n  boxFlexGroup: true,\n  boxOrdinalGroup: true,\n  columnCount: true,\n  flex: true,\n  flexGrow: true,\n  flexPositive: true,\n  flexShrink: true,\n  flexNegative: true,\n  flexOrder: true,\n  gridRow: true,\n  gridColumn: true,\n  fontWeight: true,\n  lineClamp: true,\n  lineHeight: true,\n  opacity: true,\n  order: true,\n  orphans: true,\n  tabSize: true,\n  widows: true,\n  zIndex: true,\n  zoom: true,\n\n  // SVG-related properties\n  fillOpacity: true,\n  stopOpacity: true,\n  strokeDashoffset: true,\n  strokeOpacity: true,\n  strokeWidth: true\n};\n\nmodule.exports = function(name, value) {\n  if(typeof value === 'number' && !IS_UNITLESS[ name ]) {\n    return value + 'px';\n  } else {\n    return value;\n  }\n};"], "mappings": "AAAA;AACA,IAAIA,WAAW,GAAG;EAChBC,uBAAuB,EAAE,IAAI;EAC7BC,OAAO,EAAE,IAAI;EACbC,YAAY,EAAE,IAAI;EAClBC,eAAe,EAAE,IAAI;EACrBC,WAAW,EAAE,IAAI;EACjBC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,IAAI;EACdC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE,IAAI;EAChBC,YAAY,EAAE,IAAI;EAClBC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,IAAI;EACbC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,IAAI;EACbC,KAAK,EAAE,IAAI;EACXC,OAAO,EAAE,IAAI;EACbC,OAAO,EAAE,IAAI;EACbC,MAAM,EAAE,IAAI;EACZC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,IAAI;EAEV;EACAC,WAAW,EAAE,IAAI;EACjBC,WAAW,EAAE,IAAI;EACjBC,gBAAgB,EAAE,IAAI;EACtBC,aAAa,EAAE,IAAI;EACnBC,WAAW,EAAE;AACf,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG,UAASC,IAAI,EAAEC,KAAK,EAAE;EACrC,IAAG,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAAChC,WAAW,CAAE+B,IAAI,CAAE,EAAE;IACpD,OAAOC,KAAK,GAAG,IAAI;EACrB,CAAC,MAAM;IACL,OAAOA,KAAK;EACd;AACF,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}