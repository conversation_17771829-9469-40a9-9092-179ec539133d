{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n/**\n * Mapping of standard HTML attributes to their React counterparts\n * List taken and reversed from react/src/renderers/dom/shared/HTMLDOMPropertyConfig.js\n * https://github.com/facebook/react/blob/c9c3c339b757682f1154f1c915eb55e6a8766933/src/renderers/dom/shared/HTMLDOMPropertyConfig.js\n * @type {Object}\n */\nexports.default = {\n  /**\n   * Standard Properties\n   */\n  accept: 'accept',\n  'accept-charset': 'acceptCharset',\n  accesskey: 'accessKey',\n  action: 'action',\n  allowfullscreen: 'allowFullScreen',\n  allowtransparency: 'allowTransparency',\n  alt: 'alt',\n  as: 'as',\n  async: 'async',\n  autocomplete: 'autoComplete',\n  autoplay: 'autoPlay',\n  capture: 'capture',\n  cellpadding: 'cellPadding',\n  cellspacing: 'cellSpacing',\n  charset: 'charSet',\n  challenge: 'challenge',\n  checked: 'checked',\n  cite: 'cite',\n  classid: 'classID',\n  class: 'className',\n  cols: 'cols',\n  colspan: 'colSpan',\n  content: 'content',\n  contenteditable: 'contentEditable',\n  contextmenu: 'contextMenu',\n  controls: 'controls',\n  controlsList: 'controlsList',\n  coords: 'coords',\n  crossorigin: 'crossOrigin',\n  data: 'data',\n  datetime: 'dateTime',\n  default: 'default',\n  defer: 'defer',\n  dir: 'dir',\n  disabled: 'disabled',\n  download: 'download',\n  draggable: 'draggable',\n  enctype: 'encType',\n  form: 'form',\n  formaction: 'formAction',\n  formenctype: 'formEncType',\n  formmethod: 'formMethod',\n  formnovalidate: 'formNoValidate',\n  formtarget: 'formTarget',\n  frameborder: 'frameBorder',\n  headers: 'headers',\n  height: 'height',\n  hidden: 'hidden',\n  high: 'high',\n  href: 'href',\n  hreflang: 'hrefLang',\n  for: 'htmlFor',\n  'http-equiv': 'httpEquiv',\n  icon: 'icon',\n  id: 'id',\n  inputmode: 'inputMode',\n  integrity: 'integrity',\n  is: 'is',\n  keyparams: 'keyParams',\n  keytype: 'keyType',\n  kind: 'kind',\n  label: 'label',\n  lang: 'lang',\n  list: 'list',\n  loop: 'loop',\n  low: 'low',\n  manifest: 'manifest',\n  marginheight: 'marginHeight',\n  marginwidth: 'marginWidth',\n  max: 'max',\n  maxlength: 'maxLength',\n  media: 'media',\n  mediagroup: 'mediaGroup',\n  method: 'method',\n  min: 'min',\n  minlength: 'minLength',\n  multiple: 'multiple',\n  muted: 'muted',\n  name: 'name',\n  nonce: 'nonce',\n  novalidate: 'noValidate',\n  open: 'open',\n  optimum: 'optimum',\n  pattern: 'pattern',\n  placeholder: 'placeholder',\n  playsinline: 'playsInline',\n  poster: 'poster',\n  preload: 'preload',\n  profile: 'profile',\n  radiogroup: 'radioGroup',\n  readonly: 'readOnly',\n  referrerpolicy: 'referrerPolicy',\n  rel: 'rel',\n  required: 'required',\n  reversed: 'reversed',\n  role: 'role',\n  rows: 'rows',\n  rowspan: 'rowSpan',\n  sandbox: 'sandbox',\n  scope: 'scope',\n  scoped: 'scoped',\n  scrolling: 'scrolling',\n  seamless: 'seamless',\n  selected: 'selected',\n  shape: 'shape',\n  size: 'size',\n  sizes: 'sizes',\n  slot: 'slot',\n  span: 'span',\n  spellcheck: 'spellCheck',\n  src: 'src',\n  srcdoc: 'srcDoc',\n  srclang: 'srcLang',\n  srcset: 'srcSet',\n  start: 'start',\n  step: 'step',\n  style: 'style',\n  summary: 'summary',\n  tabindex: 'tabIndex',\n  target: 'target',\n  title: 'title',\n  type: 'type',\n  usemap: 'useMap',\n  value: 'value',\n  width: 'width',\n  wmode: 'wmode',\n  wrap: 'wrap',\n  /**\n   * RDFa Properties\n   */\n  about: 'about',\n  datatype: 'datatype',\n  inlist: 'inlist',\n  prefix: 'prefix',\n  property: 'property',\n  resource: 'resource',\n  typeof: 'typeof',\n  vocab: 'vocab',\n  /**\n   * Non-standard Properties\n   */\n  autocapitalize: 'autoCapitalize',\n  autocorrect: 'autoCorrect',\n  autosave: 'autoSave',\n  color: 'color',\n  itemprop: 'itemProp',\n  itemscope: 'itemScope',\n  itemtype: 'itemType',\n  itemid: 'itemID',\n  itemref: 'itemRef',\n  results: 'results',\n  security: 'security',\n  unselectable: 'unselectable'\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "accept", "accesskey", "action", "allowfullscreen", "allowtransparency", "alt", "as", "async", "autocomplete", "autoplay", "capture", "cellpadding", "cellspacing", "charset", "challenge", "checked", "cite", "classid", "class", "cols", "colspan", "content", "contenteditable", "contextmenu", "controls", "controlsList", "coords", "crossorigin", "data", "datetime", "defer", "dir", "disabled", "download", "draggable", "enctype", "form", "formaction", "formenctype", "formmethod", "formnovalidate", "formtarget", "frameborder", "headers", "height", "hidden", "high", "href", "hreflang", "for", "icon", "id", "inputmode", "integrity", "is", "keyparams", "keytype", "kind", "label", "lang", "list", "loop", "low", "manifest", "marginheight", "marginwidth", "max", "maxlength", "media", "mediagroup", "method", "min", "minlength", "multiple", "muted", "name", "nonce", "novalidate", "open", "optimum", "pattern", "placeholder", "playsinline", "poster", "preload", "profile", "radiogroup", "readonly", "referrerpolicy", "rel", "required", "reversed", "role", "rows", "rowspan", "sandbox", "scope", "scoped", "scrolling", "seamless", "selected", "shape", "size", "sizes", "slot", "span", "spellcheck", "src", "srcdoc", "srclang", "srcset", "start", "step", "style", "summary", "tabindex", "target", "title", "type", "usemap", "width", "wmode", "wrap", "about", "datatype", "inlist", "prefix", "property", "resource", "typeof", "vocab", "autocapitalize", "autocorrect", "autosave", "color", "itemprop", "itemscope", "itemtype", "itemid", "itemref", "results", "security", "unselectable"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/react-html-parser/lib/dom/attributes/ReactAttributes.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n/**\n * Mapping of standard HTML attributes to their React counterparts\n * List taken and reversed from react/src/renderers/dom/shared/HTMLDOMPropertyConfig.js\n * https://github.com/facebook/react/blob/c9c3c339b757682f1154f1c915eb55e6a8766933/src/renderers/dom/shared/HTMLDOMPropertyConfig.js\n * @type {Object}\n */\nexports.default = {\n  /**\n   * Standard Properties\n   */\n  accept: 'accept',\n  'accept-charset': 'acceptCharset',\n  accesskey: 'accessKey',\n  action: 'action',\n  allowfullscreen: 'allowFullScreen',\n  allowtransparency: 'allowTransparency',\n  alt: 'alt',\n  as: 'as',\n  async: 'async',\n  autocomplete: 'autoComplete',\n  autoplay: 'autoPlay',\n  capture: 'capture',\n  cellpadding: 'cellPadding',\n  cellspacing: 'cellSpacing',\n  charset: 'charSet',\n  challenge: 'challenge',\n  checked: 'checked',\n  cite: 'cite',\n  classid: 'classID',\n  class: 'className',\n  cols: 'cols',\n  colspan: 'colSpan',\n  content: 'content',\n  contenteditable: 'contentEditable',\n  contextmenu: 'contextMenu',\n  controls: 'controls',\n  controlsList: 'controlsList',\n  coords: 'coords',\n  crossorigin: 'crossOrigin',\n  data: 'data',\n  datetime: 'dateTime',\n  default: 'default',\n  defer: 'defer',\n  dir: 'dir',\n  disabled: 'disabled',\n  download: 'download',\n  draggable: 'draggable',\n  enctype: 'encType',\n  form: 'form',\n  formaction: 'formAction',\n  formenctype: 'formEncType',\n  formmethod: 'formMethod',\n  formnovalidate: 'formNoValidate',\n  formtarget: 'formTarget',\n  frameborder: 'frameBorder',\n  headers: 'headers',\n  height: 'height',\n  hidden: 'hidden',\n  high: 'high',\n  href: 'href',\n  hreflang: 'hrefLang',\n  for: 'htmlFor',\n  'http-equiv': 'httpEquiv',\n  icon: 'icon',\n  id: 'id',\n  inputmode: 'inputMode',\n  integrity: 'integrity',\n  is: 'is',\n  keyparams: 'keyParams',\n  keytype: 'keyType',\n  kind: 'kind',\n  label: 'label',\n  lang: 'lang',\n  list: 'list',\n  loop: 'loop',\n  low: 'low',\n  manifest: 'manifest',\n  marginheight: 'marginHeight',\n  marginwidth: 'marginWidth',\n  max: 'max',\n  maxlength: 'maxLength',\n  media: 'media',\n  mediagroup: 'mediaGroup',\n  method: 'method',\n  min: 'min',\n  minlength: 'minLength',\n  multiple: 'multiple',\n  muted: 'muted',\n  name: 'name',\n  nonce: 'nonce',\n  novalidate: 'noValidate',\n  open: 'open',\n  optimum: 'optimum',\n  pattern: 'pattern',\n  placeholder: 'placeholder',\n  playsinline: 'playsInline',\n  poster: 'poster',\n  preload: 'preload',\n  profile: 'profile',\n  radiogroup: 'radioGroup',\n  readonly: 'readOnly',\n  referrerpolicy: 'referrerPolicy',\n  rel: 'rel',\n  required: 'required',\n  reversed: 'reversed',\n  role: 'role',\n  rows: 'rows',\n  rowspan: 'rowSpan',\n  sandbox: 'sandbox',\n  scope: 'scope',\n  scoped: 'scoped',\n  scrolling: 'scrolling',\n  seamless: 'seamless',\n  selected: 'selected',\n  shape: 'shape',\n  size: 'size',\n  sizes: 'sizes',\n  slot: 'slot',\n  span: 'span',\n  spellcheck: 'spellCheck',\n  src: 'src',\n  srcdoc: 'srcDoc',\n  srclang: 'srcLang',\n  srcset: 'srcSet',\n  start: 'start',\n  step: 'step',\n  style: 'style',\n  summary: 'summary',\n  tabindex: 'tabIndex',\n  target: 'target',\n  title: 'title',\n  type: 'type',\n  usemap: 'useMap',\n  value: 'value',\n  width: 'width',\n  wmode: 'wmode',\n  wrap: 'wrap',\n  /**\n   * RDFa Properties\n   */\n  about: 'about',\n  datatype: 'datatype',\n  inlist: 'inlist',\n  prefix: 'prefix',\n  property: 'property',\n  resource: 'resource',\n  typeof: 'typeof',\n  vocab: 'vocab',\n  /**\n   * Non-standard Properties\n   */\n  autocapitalize: 'autoCapitalize',\n  autocorrect: 'autoCorrect',\n  autosave: 'autoSave',\n  color: 'color',\n  itemprop: 'itemProp',\n  itemscope: 'itemScope',\n  itemtype: 'itemType',\n  itemid: 'itemID',\n  itemref: 'itemRef',\n  results: 'results',\n  security: 'security',\n  unselectable: 'unselectable'\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACAD,OAAO,CAACE,OAAO,GAAG;EAChB;AACF;AACA;EACEC,MAAM,EAAE,QAAQ;EAChB,gBAAgB,EAAE,eAAe;EACjCC,SAAS,EAAE,WAAW;EACtBC,MAAM,EAAE,QAAQ;EAChBC,eAAe,EAAE,iBAAiB;EAClCC,iBAAiB,EAAE,mBAAmB;EACtCC,GAAG,EAAE,KAAK;EACVC,EAAE,EAAE,IAAI;EACRC,KAAK,EAAE,OAAO;EACdC,YAAY,EAAE,cAAc;EAC5BC,QAAQ,EAAE,UAAU;EACpBC,OAAO,EAAE,SAAS;EAClBC,WAAW,EAAE,aAAa;EAC1BC,WAAW,EAAE,aAAa;EAC1BC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,WAAW;EACtBC,OAAO,EAAE,SAAS;EAClBC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,WAAW;EAClBC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,eAAe,EAAE,iBAAiB;EAClCC,WAAW,EAAE,aAAa;EAC1BC,QAAQ,EAAE,UAAU;EACpBC,YAAY,EAAE,cAAc;EAC5BC,MAAM,EAAE,QAAQ;EAChBC,WAAW,EAAE,aAAa;EAC1BC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,UAAU;EACpB9B,OAAO,EAAE,SAAS;EAClB+B,KAAK,EAAE,OAAO;EACdC,GAAG,EAAE,KAAK;EACVC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,WAAW;EACtBC,OAAO,EAAE,SAAS;EAClBC,IAAI,EAAE,MAAM;EACZC,UAAU,EAAE,YAAY;EACxBC,WAAW,EAAE,aAAa;EAC1BC,UAAU,EAAE,YAAY;EACxBC,cAAc,EAAE,gBAAgB;EAChCC,UAAU,EAAE,YAAY;EACxBC,WAAW,EAAE,aAAa;EAC1BC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,UAAU;EACpBC,GAAG,EAAE,SAAS;EACd,YAAY,EAAE,WAAW;EACzBC,IAAI,EAAE,MAAM;EACZC,EAAE,EAAE,IAAI;EACRC,SAAS,EAAE,WAAW;EACtBC,SAAS,EAAE,WAAW;EACtBC,EAAE,EAAE,IAAI;EACRC,SAAS,EAAE,WAAW;EACtBC,OAAO,EAAE,SAAS;EAClBC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,GAAG,EAAE,KAAK;EACVC,QAAQ,EAAE,UAAU;EACpBC,YAAY,EAAE,cAAc;EAC5BC,WAAW,EAAE,aAAa;EAC1BC,GAAG,EAAE,KAAK;EACVC,SAAS,EAAE,WAAW;EACtBC,KAAK,EAAE,OAAO;EACdC,UAAU,EAAE,YAAY;EACxBC,MAAM,EAAE,QAAQ;EAChBC,GAAG,EAAE,KAAK;EACVC,SAAS,EAAE,WAAW;EACtBC,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,OAAO;EACdC,UAAU,EAAE,YAAY;EACxBC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,WAAW,EAAE,aAAa;EAC1BC,WAAW,EAAE,aAAa;EAC1BC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,UAAU,EAAE,YAAY;EACxBC,QAAQ,EAAE,UAAU;EACpBC,cAAc,EAAE,gBAAgB;EAChCC,GAAG,EAAE,KAAK;EACVC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,OAAO;EACdC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE,WAAW;EACtBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,UAAU,EAAE,YAAY;EACxBC,GAAG,EAAE,KAAK;EACVC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,OAAO;EACdC,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,QAAQ;EAChBzH,KAAK,EAAE,OAAO;EACd0H,KAAK,EAAE,OAAO;EACdC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,MAAM;EACZ;AACF;AACA;EACEC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,OAAO;EACd;AACF;AACA;EACEC,cAAc,EAAE,gBAAgB;EAChCC,WAAW,EAAE,aAAa;EAC1BC,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,WAAW;EACtBC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAE,UAAU;EACpBC,YAAY,EAAE;AAChB,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}