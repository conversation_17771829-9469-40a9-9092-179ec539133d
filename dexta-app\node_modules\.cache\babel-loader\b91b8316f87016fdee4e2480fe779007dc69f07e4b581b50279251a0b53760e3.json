{"ast": null, "code": "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nvar PatternParser = /*#__PURE__*/function () {\n  function PatternParser() {\n    _classCallCheck(this, <PERSON><PERSON>Parser);\n  }\n  _createClass(<PERSON><PERSON><PERSON>ars<PERSON>, [{\n    key: \"parse\",\n    value: function parse(pattern) {\n      this.context = [{\n        or: true,\n        instructions: []\n      }];\n      this.parsePattern(pattern);\n      if (this.context.length !== 1) {\n        throw new Error('Non-finalized contexts left when pattern parse ended');\n      }\n      var _this$context$ = this.context[0],\n        branches = _this$context$.branches,\n        instructions = _this$context$.instructions;\n      if (branches) {\n        return {\n          op: '|',\n          args: branches.concat([expandSingleElementArray(instructions)])\n        };\n      }\n      /* istanbul ignore if */\n\n      if (instructions.length === 0) {\n        throw new Error('Pattern is required');\n      }\n      if (instructions.length === 1) {\n        return instructions[0];\n      }\n      return instructions;\n    }\n  }, {\n    key: \"startContext\",\n    value: function startContext(context) {\n      this.context.push(context);\n    }\n  }, {\n    key: \"endContext\",\n    value: function endContext() {\n      this.context.pop();\n    }\n  }, {\n    key: \"getContext\",\n    value: function getContext() {\n      return this.context[this.context.length - 1];\n    }\n  }, {\n    key: \"parsePattern\",\n    value: function parsePattern(pattern) {\n      if (!pattern) {\n        throw new Error('Pattern is required');\n      }\n      var match = pattern.match(OPERATOR);\n      if (!match) {\n        if (ILLEGAL_CHARACTER_REGEXP.test(pattern)) {\n          throw new Error(\"Illegal characters found in a pattern: \".concat(pattern));\n        }\n        this.getContext().instructions = this.getContext().instructions.concat(pattern.split(''));\n        return;\n      }\n      var operator = match[1];\n      var before = pattern.slice(0, match.index);\n      var rightPart = pattern.slice(match.index + operator.length);\n      switch (operator) {\n        case '(?:':\n          if (before) {\n            this.parsePattern(before);\n          }\n          this.startContext({\n            or: true,\n            instructions: [],\n            branches: []\n          });\n          break;\n        case ')':\n          if (!this.getContext().or) {\n            throw new Error('\")\" operator must be preceded by \"(?:\" operator');\n          }\n          if (before) {\n            this.parsePattern(before);\n          }\n          if (this.getContext().instructions.length === 0) {\n            throw new Error('No instructions found after \"|\" operator in an \"or\" group');\n          }\n          var _this$getContext = this.getContext(),\n            branches = _this$getContext.branches;\n          branches.push(expandSingleElementArray(this.getContext().instructions));\n          this.endContext();\n          this.getContext().instructions.push({\n            op: '|',\n            args: branches\n          });\n          break;\n        case '|':\n          if (!this.getContext().or) {\n            throw new Error('\"|\" operator can only be used inside \"or\" groups');\n          }\n          if (before) {\n            this.parsePattern(before);\n          } // The top-level is an implicit \"or\" group, if required.\n\n          if (!this.getContext().branches) {\n            // `branches` are not defined only for the root implicit \"or\" operator.\n\n            /* istanbul ignore else */\n            if (this.context.length === 1) {\n              this.getContext().branches = [];\n            } else {\n              throw new Error('\"branches\" not found in an \"or\" group context');\n            }\n          }\n          this.getContext().branches.push(expandSingleElementArray(this.getContext().instructions));\n          this.getContext().instructions = [];\n          break;\n        case '[':\n          if (before) {\n            this.parsePattern(before);\n          }\n          this.startContext({\n            oneOfSet: true\n          });\n          break;\n        case ']':\n          if (!this.getContext().oneOfSet) {\n            throw new Error('\"]\" operator must be preceded by \"[\" operator');\n          }\n          this.endContext();\n          this.getContext().instructions.push({\n            op: '[]',\n            args: parseOneOfSet(before)\n          });\n          break;\n\n        /* istanbul ignore next */\n\n        default:\n          throw new Error(\"Unknown operator: \".concat(operator));\n      }\n      if (rightPart) {\n        this.parsePattern(rightPart);\n      }\n    }\n  }]);\n  return PatternParser;\n}();\nexport { PatternParser as default };\nfunction parseOneOfSet(pattern) {\n  var values = [];\n  var i = 0;\n  while (i < pattern.length) {\n    if (pattern[i] === '-') {\n      if (i === 0 || i === pattern.length - 1) {\n        throw new Error(\"Couldn't parse a one-of set pattern: \".concat(pattern));\n      }\n      var prevValue = pattern[i - 1].charCodeAt(0) + 1;\n      var nextValue = pattern[i + 1].charCodeAt(0) - 1;\n      var value = prevValue;\n      while (value <= nextValue) {\n        values.push(String.fromCharCode(value));\n        value++;\n      }\n    } else {\n      values.push(pattern[i]);\n    }\n    i++;\n  }\n  return values;\n}\nvar ILLEGAL_CHARACTER_REGEXP = /[\\(\\)\\[\\]\\?\\:\\|]/;\nvar OPERATOR = new RegExp(\n// any of:\n'(' +\n// or operator\n'\\\\|' +\n// or\n'|' +\n// or group start\n'\\\\(\\\\?\\\\:' +\n// or\n'|' +\n// or group end\n'\\\\)' +\n// or\n'|' +\n// one-of set start\n'\\\\[' +\n// or\n'|' +\n// one-of set end\n'\\\\]' + ')');\nfunction expandSingleElementArray(array) {\n  if (array.length === 1) {\n    return array[0];\n  }\n  return array;\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "parse", "pattern", "context", "or", "instructions", "parsePattern", "length", "Error", "_this$context$", "branches", "op", "args", "concat", "expandSingleElementArray", "startContext", "push", "endContext", "pop", "getContext", "match", "OPERATOR", "ILLEGAL_CHARACTER_REGEXP", "test", "split", "operator", "before", "slice", "index", "rightPart", "_this$getContext", "oneOfSet", "parseOneOfSet", "values", "i", "prevValue", "charCodeAt", "nextValue", "value", "String", "fromCharCode", "RegExp", "array"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\AsYouTypeFormatter.PatternParser.js"], "sourcesContent": ["export default class PatternParser {\r\n\tparse(pattern) {\r\n\t\tthis.context = [{\r\n\t\t\tor: true,\r\n\t\t\tinstructions: []\r\n\t\t}]\r\n\r\n\t\tthis.parsePattern(pattern)\r\n\r\n\t\tif (this.context.length !== 1) {\r\n\t\t\tthrow new Error('Non-finalized contexts left when pattern parse ended')\r\n\t\t}\r\n\r\n\t\tconst { branches, instructions } = this.context[0]\r\n\r\n\t\tif (branches) {\r\n\t\t\treturn {\r\n\t\t\t\top: '|',\r\n\t\t\t\targs: branches.concat([\r\n\t\t\t\t\texpandSingleElementArray(instructions)\r\n\t\t\t\t])\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t/* istanbul ignore if */\r\n\t\tif (instructions.length === 0) {\r\n\t\t\tthrow new Error('Pattern is required')\r\n\t\t}\r\n\r\n\t\tif (instructions.length === 1) {\r\n\t\t\treturn instructions[0]\r\n\t\t}\r\n\r\n\t\treturn instructions\r\n\t}\r\n\r\n\tstartContext(context) {\r\n\t\tthis.context.push(context)\r\n\t}\r\n\r\n\tendContext() {\r\n\t\tthis.context.pop()\r\n\t}\r\n\r\n\tgetContext() {\r\n\t\treturn this.context[this.context.length - 1]\r\n\t}\r\n\r\n\tparsePattern(pattern) {\r\n\t\tif (!pattern) {\r\n\t\t\tthrow new Error('Pattern is required')\r\n\t\t}\r\n\r\n\t\tconst match = pattern.match(OPERATOR)\r\n\t\tif (!match) {\r\n\t\t\tif (ILLEGAL_CHARACTER_REGEXP.test(pattern)) {\r\n\t\t\t\tthrow new Error(`Illegal characters found in a pattern: ${pattern}`)\r\n\t\t\t}\r\n\t\t\tthis.getContext().instructions = this.getContext().instructions.concat(\r\n\t\t\t\tpattern.split('')\r\n\t\t\t)\r\n\t\t\treturn\r\n\t\t}\r\n\r\n\t\tconst operator = match[1]\r\n\t\tconst before = pattern.slice(0, match.index)\r\n\t\tconst rightPart = pattern.slice(match.index + operator.length)\r\n\r\n\t\tswitch (operator) {\r\n\t\t\tcase '(?:':\r\n\t\t\t\tif (before) {\r\n\t\t\t\t\tthis.parsePattern(before)\r\n\t\t\t\t}\r\n\t\t\t\tthis.startContext({\r\n\t\t\t\t\tor: true,\r\n\t\t\t\t\tinstructions: [],\r\n\t\t\t\t\tbranches: []\r\n\t\t\t\t})\r\n\t\t\t\tbreak\r\n\r\n\t\t\tcase ')':\r\n\t\t\t\tif (!this.getContext().or) {\r\n\t\t\t\t\tthrow new Error('\")\" operator must be preceded by \"(?:\" operator')\r\n\t\t\t\t}\r\n\t\t\t\tif (before) {\r\n\t\t\t\t\tthis.parsePattern(before)\r\n\t\t\t\t}\r\n\t\t\t\tif (this.getContext().instructions.length === 0) {\r\n\t\t\t\t\tthrow new Error('No instructions found after \"|\" operator in an \"or\" group')\r\n\t\t\t\t}\r\n\t\t\t\tconst { branches } = this.getContext()\r\n\t\t\t\tbranches.push(\r\n\t\t\t\t\texpandSingleElementArray(\r\n\t\t\t\t\t\tthis.getContext().instructions\r\n\t\t\t\t\t)\r\n\t\t\t\t)\r\n\t\t\t\tthis.endContext()\r\n\t\t\t\tthis.getContext().instructions.push({\r\n\t\t\t\t\top: '|',\r\n\t\t\t\t\targs: branches\r\n\t\t\t\t})\r\n\t\t\t\tbreak\r\n\r\n\t\t\tcase '|':\r\n\t\t\t\tif (!this.getContext().or) {\r\n\t\t\t\t\tthrow new Error('\"|\" operator can only be used inside \"or\" groups')\r\n\t\t\t\t}\r\n\t\t\t\tif (before) {\r\n\t\t\t\t\tthis.parsePattern(before)\r\n\t\t\t\t}\r\n\t\t\t\t// The top-level is an implicit \"or\" group, if required.\r\n\t\t\t\tif (!this.getContext().branches) {\r\n\t\t\t\t\t// `branches` are not defined only for the root implicit \"or\" operator.\r\n\t\t\t\t\t/* istanbul ignore else */\r\n\t\t\t\t\tif (this.context.length === 1) {\r\n\t\t\t\t\t\tthis.getContext().branches = []\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthrow new Error('\"branches\" not found in an \"or\" group context')\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tthis.getContext().branches.push(\r\n\t\t\t\t\texpandSingleElementArray(\r\n\t\t\t\t\t\tthis.getContext().instructions\r\n\t\t\t\t\t)\r\n\t\t\t\t)\r\n\t\t\t\tthis.getContext().instructions = []\r\n\t\t\t\tbreak\r\n\r\n\t\t\tcase '[':\r\n\t\t\t\tif (before) {\r\n\t\t\t\t\tthis.parsePattern(before)\r\n\t\t\t\t}\r\n\t\t\t\tthis.startContext({\r\n\t\t\t\t\toneOfSet: true\r\n\t\t\t\t})\r\n\t\t\t\tbreak\r\n\r\n\t\t\tcase ']':\r\n\t\t\t\tif (!this.getContext().oneOfSet) {\r\n\t\t\t\t\tthrow new Error('\"]\" operator must be preceded by \"[\" operator')\r\n\t\t\t\t}\r\n\t\t\t\tthis.endContext()\r\n\t\t\t\tthis.getContext().instructions.push({\r\n\t\t\t\t\top: '[]',\r\n\t\t\t\t\targs: parseOneOfSet(before)\r\n\t\t\t\t})\r\n\t\t\t\tbreak\r\n\r\n\t\t\t/* istanbul ignore next */\r\n\t\t\tdefault:\r\n\t\t\t\tthrow new Error(`Unknown operator: ${operator}`)\r\n\t\t}\r\n\r\n\t\tif (rightPart) {\r\n\t\t\tthis.parsePattern(rightPart)\r\n\t\t}\r\n\t}\r\n}\r\n\r\nfunction parseOneOfSet(pattern) {\r\n\tconst values = []\r\n\tlet i = 0\r\n\twhile (i < pattern.length) {\r\n\t\tif (pattern[i] === '-') {\r\n\t\t\tif (i === 0 || i === pattern.length - 1) {\r\n\t\t\t\tthrow new Error(`Couldn't parse a one-of set pattern: ${pattern}`)\r\n\t\t\t}\r\n\t\t\tconst prevValue = pattern[i - 1].charCodeAt(0) + 1\r\n\t\t\tconst nextValue = pattern[i + 1].charCodeAt(0) - 1\r\n\t\t\tlet value = prevValue\r\n\t\t\twhile (value <= nextValue) {\r\n\t\t\t\tvalues.push(String.fromCharCode(value))\r\n\t\t\t\tvalue++\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tvalues.push(pattern[i])\r\n\t\t}\r\n\t\ti++\r\n\t}\r\n\treturn values\r\n}\r\n\r\nconst ILLEGAL_CHARACTER_REGEXP = /[\\(\\)\\[\\]\\?\\:\\|]/\r\n\r\nconst OPERATOR = new RegExp(\r\n\t// any of:\r\n\t'(' +\r\n\t\t// or operator\r\n\t\t'\\\\|' +\r\n\t\t// or\r\n\t\t'|' +\r\n\t\t// or group start\r\n\t\t'\\\\(\\\\?\\\\:' +\r\n\t\t// or\r\n\t\t'|' +\r\n\t\t// or group end\r\n\t\t'\\\\)' +\r\n\t\t// or\r\n\t\t'|' +\r\n\t\t// one-of set start\r\n\t\t'\\\\[' +\r\n\t\t// or\r\n\t\t'|' +\r\n\t\t// one-of set end\r\n\t\t'\\\\]' +\r\n\t')'\r\n)\r\n\r\nfunction expandSingleElementArray(array) {\r\n\tif (array.length === 1) {\r\n\t\treturn array[0]\r\n\t}\r\n\treturn array\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;IAAqBA,a;;;;;;WACpB,SAAAC,MAAMC,OAAN,EAAe;MACd,KAAKC,OAAL,GAAe,CAAC;QACfC,EAAE,EAAE,IADW;QAEfC,YAAY,EAAE;MAFC,CAAD,CAAf;MAKA,KAAKC,YAAL,CAAkBJ,OAAlB;MAEA,IAAI,KAAKC,OAAL,CAAaI,MAAb,KAAwB,CAA5B,EAA+B;QAC9B,MAAM,IAAIC,KAAJ,CAAU,sDAAV,CAAN;MACA;MAED,IAAAC,cAAA,GAAmC,KAAKN,OAAL,CAAa,CAAb,CAAnC;QAAQO,QAAR,GAAAD,cAAA,CAAQC,QAAR;QAAkBL,YAAlB,GAAAI,cAAA,CAAkBJ,YAAlB;MAEA,IAAIK,QAAJ,EAAc;QACb,OAAO;UACNC,EAAE,EAAE,GADE;UAENC,IAAI,EAAEF,QAAQ,CAACG,MAAT,CAAgB,CACrBC,wBAAwB,CAACT,YAAD,CADH,CAAhB;QAFA,CAAP;MAMA;MAED;;MACA,IAAIA,YAAY,CAACE,MAAb,KAAwB,CAA5B,EAA+B;QAC9B,MAAM,IAAIC,KAAJ,CAAU,qBAAV,CAAN;MACA;MAED,IAAIH,YAAY,CAACE,MAAb,KAAwB,CAA5B,EAA+B;QAC9B,OAAOF,YAAY,CAAC,CAAD,CAAnB;MACA;MAED,OAAOA,YAAP;IACA;;;WAED,SAAAU,aAAaZ,OAAb,EAAsB;MACrB,KAAKA,OAAL,CAAaa,IAAb,CAAkBb,OAAlB;IACA;;;WAED,SAAAc,WAAA,EAAa;MACZ,KAAKd,OAAL,CAAae,GAAb;IACA;;;WAED,SAAAC,WAAA,EAAa;MACZ,OAAO,KAAKhB,OAAL,CAAa,KAAKA,OAAL,CAAaI,MAAb,GAAsB,CAAnC,CAAP;IACA;;;WAED,SAAAD,aAAaJ,OAAb,EAAsB;MACrB,IAAI,CAACA,OAAL,EAAc;QACb,MAAM,IAAIM,KAAJ,CAAU,qBAAV,CAAN;MACA;MAED,IAAMY,KAAK,GAAGlB,OAAO,CAACkB,KAAR,CAAcC,QAAd,CAAd;MACA,IAAI,CAACD,KAAL,EAAY;QACX,IAAIE,wBAAwB,CAACC,IAAzB,CAA8BrB,OAA9B,CAAJ,EAA4C;UAC3C,MAAM,IAAIM,KAAJ,2CAAAK,MAAA,CAAoDX,OAApD,EAAN;QACA;QACD,KAAKiB,UAAL,GAAkBd,YAAlB,GAAiC,KAAKc,UAAL,GAAkBd,YAAlB,CAA+BQ,MAA/B,CAChCX,OAAO,CAACsB,KAAR,CAAc,EAAd,CADgC,CAAjC;QAGA;MACA;MAED,IAAMC,QAAQ,GAAGL,KAAK,CAAC,CAAD,CAAtB;MACA,IAAMM,MAAM,GAAGxB,OAAO,CAACyB,KAAR,CAAc,CAAd,EAAiBP,KAAK,CAACQ,KAAvB,CAAf;MACA,IAAMC,SAAS,GAAG3B,OAAO,CAACyB,KAAR,CAAcP,KAAK,CAACQ,KAAN,GAAcH,QAAQ,CAAClB,MAArC,CAAlB;MAEA,QAAQkB,QAAR;QACC,KAAK,KAAL;UACC,IAAIC,MAAJ,EAAY;YACX,KAAKpB,YAAL,CAAkBoB,MAAlB;UACA;UACD,KAAKX,YAAL,CAAkB;YACjBX,EAAE,EAAE,IADa;YAEjBC,YAAY,EAAE,EAFG;YAGjBK,QAAQ,EAAE;UAHO,CAAlB;UAKA;QAED,KAAK,GAAL;UACC,IAAI,CAAC,KAAKS,UAAL,GAAkBf,EAAvB,EAA2B;YAC1B,MAAM,IAAII,KAAJ,CAAU,iDAAV,CAAN;UACA;UACD,IAAIkB,MAAJ,EAAY;YACX,KAAKpB,YAAL,CAAkBoB,MAAlB;UACA;UACD,IAAI,KAAKP,UAAL,GAAkBd,YAAlB,CAA+BE,MAA/B,KAA0C,CAA9C,EAAiD;YAChD,MAAM,IAAIC,KAAJ,CAAU,2DAAV,CAAN;UACA;UACD,IAAAsB,gBAAA,GAAqB,KAAKX,UAAL,EAArB;YAAQT,QAAR,GAAAoB,gBAAA,CAAQpB,QAAR;UACAA,QAAQ,CAACM,IAAT,CACCF,wBAAwB,CACvB,KAAKK,UAAL,GAAkBd,YADK,CADzB;UAKA,KAAKY,UAAL;UACA,KAAKE,UAAL,GAAkBd,YAAlB,CAA+BW,IAA/B,CAAoC;YACnCL,EAAE,EAAE,GAD+B;YAEnCC,IAAI,EAAEF;UAF6B,CAApC;UAIA;QAED,KAAK,GAAL;UACC,IAAI,CAAC,KAAKS,UAAL,GAAkBf,EAAvB,EAA2B;YAC1B,MAAM,IAAII,KAAJ,CAAU,kDAAV,CAAN;UACA;UACD,IAAIkB,MAAJ,EAAY;YACX,KAAKpB,YAAL,CAAkBoB,MAAlB;UACA,CANF,CAOC;;UACA,IAAI,CAAC,KAAKP,UAAL,GAAkBT,QAAvB,EAAiC;YAChC;;YACA;YACA,IAAI,KAAKP,OAAL,CAAaI,MAAb,KAAwB,CAA5B,EAA+B;cAC9B,KAAKY,UAAL,GAAkBT,QAAlB,GAA6B,EAA7B;YACA,CAFD,MAEO;cACN,MAAM,IAAIF,KAAJ,CAAU,+CAAV,CAAN;YACA;UACD;UACD,KAAKW,UAAL,GAAkBT,QAAlB,CAA2BM,IAA3B,CACCF,wBAAwB,CACvB,KAAKK,UAAL,GAAkBd,YADK,CADzB;UAKA,KAAKc,UAAL,GAAkBd,YAAlB,GAAiC,EAAjC;UACA;QAED,KAAK,GAAL;UACC,IAAIqB,MAAJ,EAAY;YACX,KAAKpB,YAAL,CAAkBoB,MAAlB;UACA;UACD,KAAKX,YAAL,CAAkB;YACjBgB,QAAQ,EAAE;UADO,CAAlB;UAGA;QAED,KAAK,GAAL;UACC,IAAI,CAAC,KAAKZ,UAAL,GAAkBY,QAAvB,EAAiC;YAChC,MAAM,IAAIvB,KAAJ,CAAU,+CAAV,CAAN;UACA;UACD,KAAKS,UAAL;UACA,KAAKE,UAAL,GAAkBd,YAAlB,CAA+BW,IAA/B,CAAoC;YACnCL,EAAE,EAAE,IAD+B;YAEnCC,IAAI,EAAEoB,aAAa,CAACN,MAAD;UAFgB,CAApC;UAIA;;QAED;;QACA;UACC,MAAM,IAAIlB,KAAJ,sBAAAK,MAAA,CAA+BY,QAA/B,EAAN;MAlFF;MAqFA,IAAII,SAAJ,EAAe;QACd,KAAKvB,YAAL,CAAkBuB,SAAlB;MACA;IACD;;;;SA5JmB7B,a;AA+JrB,SAASgC,aAATA,CAAuB9B,OAAvB,EAAgC;EAC/B,IAAM+B,MAAM,GAAG,EAAf;EACA,IAAIC,CAAC,GAAG,CAAR;EACA,OAAOA,CAAC,GAAGhC,OAAO,CAACK,MAAnB,EAA2B;IAC1B,IAAIL,OAAO,CAACgC,CAAD,CAAP,KAAe,GAAnB,EAAwB;MACvB,IAAIA,CAAC,KAAK,CAAN,IAAWA,CAAC,KAAKhC,OAAO,CAACK,MAAR,GAAiB,CAAtC,EAAyC;QACxC,MAAM,IAAIC,KAAJ,yCAAAK,MAAA,CAAkDX,OAAlD,EAAN;MACA;MACD,IAAMiC,SAAS,GAAGjC,OAAO,CAACgC,CAAC,GAAG,CAAL,CAAP,CAAeE,UAAf,CAA0B,CAA1B,IAA+B,CAAjD;MACA,IAAMC,SAAS,GAAGnC,OAAO,CAACgC,CAAC,GAAG,CAAL,CAAP,CAAeE,UAAf,CAA0B,CAA1B,IAA+B,CAAjD;MACA,IAAIE,KAAK,GAAGH,SAAZ;MACA,OAAOG,KAAK,IAAID,SAAhB,EAA2B;QAC1BJ,MAAM,CAACjB,IAAP,CAAYuB,MAAM,CAACC,YAAP,CAAoBF,KAApB,CAAZ;QACAA,KAAK;MACL;IACD,CAXD,MAWO;MACNL,MAAM,CAACjB,IAAP,CAAYd,OAAO,CAACgC,CAAD,CAAnB;IACA;IACDA,CAAC;EACD;EACD,OAAOD,MAAP;AACA;AAED,IAAMX,wBAAwB,GAAG,kBAAjC;AAEA,IAAMD,QAAQ,GAAG,IAAIoB,MAAJ;AAChB;AACA;AACC;AACA,KAFD;AAGC;AACA,GAJD;AAKC;AACA,WAND;AAOC;AACA,GARD;AASC;AACA,KAVD;AAWC;AACA,GAZD;AAaC;AACA,KAdD;AAeC;AACA,GAhBD;AAiBC;AACA,KAlBD,GAmBA,GArBgB,CAAjB;AAwBA,SAAS3B,wBAATA,CAAkC4B,KAAlC,EAAyC;EACxC,IAAIA,KAAK,CAACnC,MAAN,KAAiB,CAArB,EAAwB;IACvB,OAAOmC,KAAK,CAAC,CAAD,CAAZ;EACA;EACD,OAAOA,KAAP;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}