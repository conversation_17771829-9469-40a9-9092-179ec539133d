{"ast": null, "code": "import { EmptyError } from '../util/EmptyError';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function throwIfEmpty(errorFactory) {\n  if (errorFactory === void 0) {\n    errorFactory = defaultErrorFactory;\n  }\n  return operate(function (source, subscriber) {\n    var hasValue = false;\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      hasValue = true;\n      subscriber.next(value);\n    }, function () {\n      return hasValue ? subscriber.complete() : subscriber.error(errorFactory());\n    }));\n  });\n}\nfunction defaultErrorFactory() {\n  return new EmptyError();\n}", "map": {"version": 3, "names": ["EmptyError", "operate", "createOperatorSubscriber", "throwIfEmpty", "errorFactory", "defaultErrorFactory", "source", "subscriber", "hasValue", "subscribe", "value", "next", "complete", "error"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\throwIfEmpty.ts"], "sourcesContent": ["import { EmptyError } from '../util/EmptyError';\nimport { MonoTypeOperatorFunction } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/**\n * If the source observable completes without emitting a value, it will emit\n * an error. The error will be created at that time by the optional\n * `errorFactory` argument, otherwise, the error will be {@link EmptyError}.\n *\n * ![](throwIfEmpty.png)\n *\n * ## Example\n *\n * Throw an error if the document wasn't clicked within 1 second\n *\n * ```ts\n * import { fromEvent, takeUntil, timer, throwIfEmpty } from 'rxjs';\n *\n * const click$ = fromEvent(document, 'click');\n *\n * click$.pipe(\n *   takeUntil(timer(1000)),\n *   throwIfEmpty(() => new Error('The document was not clicked within 1 second'))\n * )\n * .subscribe({\n *   next() {\n *    console.log('The document was clicked');\n *   },\n *   error(err) {\n *     console.error(err.message);\n *   }\n * });\n * ```\n *\n * @param errorFactory A factory function called to produce the\n * error to be thrown when the source observable completes without emitting a\n * value.\n * @return A function that returns an Observable that throws an error if the\n * source Observable completed without emitting.\n */\nexport function throwIfEmpty<T>(errorFactory: () => any = defaultErrorFactory): MonoTypeOperatorFunction<T> {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (value) => {\n          hasValue = true;\n          subscriber.next(value);\n        },\n        () => (hasValue ? subscriber.complete() : subscriber.error(errorFactory()))\n      )\n    );\n  });\n}\n\nfunction defaultErrorFactory() {\n  return new EmptyError();\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,oBAAoB;AAE/C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAsC/D,OAAM,SAAUC,YAAYA,CAAIC,YAA6C;EAA7C,IAAAA,YAAA;IAAAA,YAAA,GAAAC,mBAA6C;EAAA;EAC3E,OAAOJ,OAAO,CAAC,UAACK,MAAM,EAAEC,UAAU;IAChC,IAAIC,QAAQ,GAAG,KAAK;IACpBF,MAAM,CAACG,SAAS,CACdP,wBAAwB,CACtBK,UAAU,EACV,UAACG,KAAK;MACJF,QAAQ,GAAG,IAAI;MACfD,UAAU,CAACI,IAAI,CAACD,KAAK,CAAC;IACxB,CAAC,EACD;MAAM,OAACF,QAAQ,GAAGD,UAAU,CAACK,QAAQ,EAAE,GAAGL,UAAU,CAACM,KAAK,CAACT,YAAY,EAAE,CAAC;IAApE,CAAqE,CAC5E,CACF;EACH,CAAC,CAAC;AACJ;AAEA,SAASC,mBAAmBA,CAAA;EAC1B,OAAO,IAAIL,UAAU,EAAE;AACzB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}