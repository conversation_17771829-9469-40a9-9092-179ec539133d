{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\n\n// This is a port of Google Android `libphonenumber`'s\n// `phonenumberutil.js` of December 31th, 2018.\n//\n// https://github.com/googlei18n/libphonenumber/commits/master/javascript/i18n/phonenumbers/phonenumberutil.js\nimport matchesEntirely from './helpers/matchesEntirely.js';\nimport formatNationalNumberUsingFormat from './helpers/formatNationalNumberUsingFormat.js';\nimport Metadata, { getCountryCallingCode } from './metadata.js';\nimport getIddPrefix from './helpers/getIddPrefix.js';\nimport { formatRFC3966 } from './helpers/RFC3966.js';\nvar DEFAULT_OPTIONS = {\n  formatExtension: function formatExtension(formattedNumber, extension, metadata) {\n    return \"\".concat(formattedNumber).concat(metadata.ext()).concat(extension);\n  }\n};\n/**\r\n * Formats a phone number.\r\n *\r\n * format(phoneNumberInstance, 'INTERNATIONAL', { ..., v2: true }, metadata)\r\n * format(phoneNumberInstance, 'NATIONAL', { ..., v2: true }, metadata)\r\n *\r\n * format({ phone: '8005553535', country: 'RU' }, 'INTERNATIONAL', { ... }, metadata)\r\n * format({ phone: '8005553535', country: 'RU' }, 'NATIONAL', undefined, metadata)\r\n *\r\n * @param  {object|PhoneNumber} input — If `options.v2: true` flag is passed, the `input` should be a `PhoneNumber` instance. Otherwise, it should be an object of shape `{ phone: '...', country: '...' }`.\r\n * @param  {string} format\r\n * @param  {object} [options]\r\n * @param  {object} metadata\r\n * @return {string}\r\n */\n\nexport default function formatNumber(input, format, options, metadata) {\n  // Apply default options.\n  if (options) {\n    options = _objectSpread(_objectSpread({}, DEFAULT_OPTIONS), options);\n  } else {\n    options = DEFAULT_OPTIONS;\n  }\n  metadata = new Metadata(metadata);\n  if (input.country && input.country !== '001') {\n    // Validate `input.country`.\n    if (!metadata.hasCountry(input.country)) {\n      throw new Error(\"Unknown country: \".concat(input.country));\n    }\n    metadata.country(input.country);\n  } else if (input.countryCallingCode) {\n    metadata.selectNumberingPlan(input.countryCallingCode);\n  } else return input.phone || '';\n  var countryCallingCode = metadata.countryCallingCode();\n  var nationalNumber = options.v2 ? input.nationalNumber : input.phone; // This variable should have been declared inside `case`s\n  // but Babel has a bug and it says \"duplicate variable declaration\".\n\n  var number;\n  switch (format) {\n    case 'NATIONAL':\n      // Legacy argument support.\n      // (`{ country: ..., phone: '' }`)\n      if (!nationalNumber) {\n        return '';\n      }\n      number = formatNationalNumber(nationalNumber, input.carrierCode, 'NATIONAL', metadata, options);\n      return addExtension(number, input.ext, metadata, options.formatExtension);\n    case 'INTERNATIONAL':\n      // Legacy argument support.\n      // (`{ country: ..., phone: '' }`)\n      if (!nationalNumber) {\n        return \"+\".concat(countryCallingCode);\n      }\n      number = formatNationalNumber(nationalNumber, null, 'INTERNATIONAL', metadata, options);\n      number = \"+\".concat(countryCallingCode, \" \").concat(number);\n      return addExtension(number, input.ext, metadata, options.formatExtension);\n    case 'E.164':\n      // `E.164` doesn't define \"phone number extensions\".\n      return \"+\".concat(countryCallingCode).concat(nationalNumber);\n    case 'RFC3966':\n      return formatRFC3966({\n        number: \"+\".concat(countryCallingCode).concat(nationalNumber),\n        ext: input.ext\n      });\n    // For reference, here's Google's IDD formatter:\n    // https://github.com/google/libphonenumber/blob/32719cf74e68796788d1ca45abc85dcdc63ba5b9/java/libphonenumber/src/com/google/i18n/phonenumbers/PhoneNumberUtil.java#L1546\n    // Not saying that this IDD formatter replicates it 1:1, but it seems to work.\n    // Who would even need to format phone numbers in IDD format anyway?\n\n    case 'IDD':\n      if (!options.fromCountry) {\n        return; // throw new Error('`fromCountry` option not passed for IDD-prefixed formatting.')\n      }\n\n      var formattedNumber = formatIDD(nationalNumber, input.carrierCode, countryCallingCode, options.fromCountry, metadata);\n      return addExtension(formattedNumber, input.ext, metadata, options.formatExtension);\n    default:\n      throw new Error(\"Unknown \\\"format\\\" argument passed to \\\"formatNumber()\\\": \\\"\".concat(format, \"\\\"\"));\n  }\n}\nfunction formatNationalNumber(number, carrierCode, formatAs, metadata, options) {\n  var format = chooseFormatForNumber(metadata.formats(), number);\n  if (!format) {\n    return number;\n  }\n  return formatNationalNumberUsingFormat(number, format, {\n    useInternationalFormat: formatAs === 'INTERNATIONAL',\n    withNationalPrefix: format.nationalPrefixIsOptionalWhenFormattingInNationalFormat() && options && options.nationalPrefix === false ? false : true,\n    carrierCode: carrierCode,\n    metadata: metadata\n  });\n}\nexport function chooseFormatForNumber(availableFormats, nationalNnumber) {\n  for (var _iterator = _createForOfIteratorHelperLoose(availableFormats), _step; !(_step = _iterator()).done;) {\n    var format = _step.value;\n\n    // Validate leading digits.\n    // The test case for \"else path\" could be found by searching for\n    // \"format.leadingDigitsPatterns().length === 0\".\n    if (format.leadingDigitsPatterns().length > 0) {\n      // The last leading_digits_pattern is used here, as it is the most detailed\n      var lastLeadingDigitsPattern = format.leadingDigitsPatterns()[format.leadingDigitsPatterns().length - 1]; // If leading digits don't match then move on to the next phone number format\n\n      if (nationalNnumber.search(lastLeadingDigitsPattern) !== 0) {\n        continue;\n      }\n    } // Check that the national number matches the phone number format regular expression\n\n    if (matchesEntirely(nationalNnumber, format.pattern())) {\n      return format;\n    }\n  }\n}\nfunction addExtension(formattedNumber, ext, metadata, formatExtension) {\n  return ext ? formatExtension(formattedNumber, ext, metadata) : formattedNumber;\n}\nfunction formatIDD(nationalNumber, carrierCode, countryCallingCode, fromCountry, metadata) {\n  var fromCountryCallingCode = getCountryCallingCode(fromCountry, metadata.metadata); // When calling within the same country calling code.\n\n  if (fromCountryCallingCode === countryCallingCode) {\n    var formattedNumber = formatNationalNumber(nationalNumber, carrierCode, 'NATIONAL', metadata); // For NANPA regions, return the national format for these regions\n    // but prefix it with the country calling code.\n\n    if (countryCallingCode === '1') {\n      return countryCallingCode + ' ' + formattedNumber;\n    } // If regions share a country calling code, the country calling code need\n    // not be dialled. This also applies when dialling within a region, so this\n    // if clause covers both these cases. Technically this is the case for\n    // dialling from La Reunion to other overseas departments of France (French\n    // Guiana, Martinique, Guadeloupe), but not vice versa - so we don't cover\n    // this edge case for now and for those cases return the version including\n    // country calling code. Details here:\n    // http://www.petitfute.com/voyage/225-info-pratiques-reunion\n    //\n\n    return formattedNumber;\n  }\n  var iddPrefix = getIddPrefix(fromCountry, undefined, metadata.metadata);\n  if (iddPrefix) {\n    return \"\".concat(iddPrefix, \" \").concat(countryCallingCode, \" \").concat(formatNationalNumber(nationalNumber, null, 'INTERNATIONAL', metadata));\n  }\n}", "map": {"version": 3, "names": ["matchesEntirely", "formatNationalNumberUsingFormat", "<PERSON><PERSON><PERSON>", "getCountryCallingCode", "getIddPrefix", "formatRFC3966", "DEFAULT_OPTIONS", "formatExtension", "formattedNumber", "extension", "metadata", "concat", "ext", "formatNumber", "input", "format", "options", "_objectSpread", "country", "hasCountry", "Error", "countryCallingCode", "selectNumberingPlan", "phone", "nationalNumber", "v2", "number", "formatNationalNumber", "carrierCode", "addExtension", "fromCountry", "formatIDD", "formatAs", "chooseFormatForNumber", "formats", "useInternationalFormat", "withNationalPrefix", "nationalPrefixIsOptionalWhenFormattingInNationalFormat", "nationalPrefix", "availableFormats", "nationalNnumber", "_iterator", "_createForOfIteratorHelperLoose", "_step", "done", "value", "leadingDigitsPatterns", "length", "lastLeadingDigitsPattern", "search", "pattern", "fromCountryCallingCode", "iddPrefix", "undefined"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\format.js"], "sourcesContent": ["// This is a port of Google Android `libphonenumber`'s\r\n// `phonenumberutil.js` of December 31th, 2018.\r\n//\r\n// https://github.com/googlei18n/libphonenumber/commits/master/javascript/i18n/phonenumbers/phonenumberutil.js\r\n\r\nimport matchesEntirely from './helpers/matchesEntirely.js'\r\nimport formatNationalNumberUsingFormat from './helpers/formatNationalNumberUsingFormat.js'\r\nimport Metadata, { getCountryCallingCode } from './metadata.js'\r\nimport getIddPrefix from './helpers/getIddPrefix.js'\r\nimport { formatRFC3966 } from './helpers/RFC3966.js'\r\n\r\nconst DEFAULT_OPTIONS = {\r\n\tformatExtension: (formattedNumber, extension, metadata) => `${formattedNumber}${metadata.ext()}${extension}`\r\n}\r\n\r\n/**\r\n * Formats a phone number.\r\n *\r\n * format(phoneNumberInstance, 'INTERNATIONAL', { ..., v2: true }, metadata)\r\n * format(phoneNumberInstance, 'NATIONAL', { ..., v2: true }, metadata)\r\n *\r\n * format({ phone: '8005553535', country: 'RU' }, 'INTERNATIONAL', { ... }, metadata)\r\n * format({ phone: '8005553535', country: 'RU' }, 'NATIONAL', undefined, metadata)\r\n *\r\n * @param  {object|PhoneNumber} input — If `options.v2: true` flag is passed, the `input` should be a `PhoneNumber` instance. Otherwise, it should be an object of shape `{ phone: '...', country: '...' }`.\r\n * @param  {string} format\r\n * @param  {object} [options]\r\n * @param  {object} metadata\r\n * @return {string}\r\n */\r\nexport default function formatNumber(input, format, options, metadata) {\r\n\t// Apply default options.\r\n\tif (options) {\r\n\t\toptions = { ...DEFAULT_OPTIONS, ...options }\r\n\t} else {\r\n\t\toptions = DEFAULT_OPTIONS\r\n\t}\r\n\r\n\tmetadata = new Metadata(metadata)\r\n\r\n\tif (input.country && input.country !== '001') {\r\n\t\t// Validate `input.country`.\r\n\t\tif (!metadata.hasCountry(input.country)) {\r\n\t\t\tthrow new Error(`Unknown country: ${input.country}`)\r\n\t\t}\r\n\t\tmetadata.country(input.country)\r\n\t}\r\n\telse if (input.countryCallingCode) {\r\n\t\tmetadata.selectNumberingPlan(input.countryCallingCode)\r\n\t}\r\n\telse return input.phone || ''\r\n\r\n\tconst countryCallingCode = metadata.countryCallingCode()\r\n\r\n\tconst nationalNumber = options.v2 ? input.nationalNumber : input.phone\r\n\r\n\t// This variable should have been declared inside `case`s\r\n\t// but Babel has a bug and it says \"duplicate variable declaration\".\r\n\tlet number\r\n\r\n\tswitch (format) {\r\n\t\tcase 'NATIONAL':\r\n\t\t\t// Legacy argument support.\r\n\t\t\t// (`{ country: ..., phone: '' }`)\r\n\t\t\tif (!nationalNumber) {\r\n\t\t\t\treturn ''\r\n\t\t\t}\r\n\t\t\tnumber = formatNationalNumber(nationalNumber, input.carrierCode, 'NATIONAL', metadata, options)\r\n\t\t\treturn addExtension(number, input.ext, metadata, options.formatExtension)\r\n\r\n\t\tcase 'INTERNATIONAL':\r\n\t\t\t// Legacy argument support.\r\n\t\t\t// (`{ country: ..., phone: '' }`)\r\n\t\t\tif (!nationalNumber) {\r\n\t\t\t\treturn `+${countryCallingCode}`\r\n\t\t\t}\r\n\t\t\tnumber = formatNationalNumber(nationalNumber, null, 'INTERNATIONAL', metadata, options)\r\n\t\t\tnumber = `+${countryCallingCode} ${number}`\r\n\t\t\treturn addExtension(number, input.ext, metadata, options.formatExtension)\r\n\r\n\t\tcase 'E.164':\r\n\t\t\t// `E.164` doesn't define \"phone number extensions\".\r\n\t\t\treturn `+${countryCallingCode}${nationalNumber}`\r\n\r\n\t\tcase 'RFC3966':\r\n\t\t\treturn formatRFC3966({\r\n\t\t\t\tnumber: `+${countryCallingCode}${nationalNumber}`,\r\n\t\t\t\text: input.ext\r\n\t\t\t})\r\n\r\n\t\t// For reference, here's Google's IDD formatter:\r\n\t\t// https://github.com/google/libphonenumber/blob/32719cf74e68796788d1ca45abc85dcdc63ba5b9/java/libphonenumber/src/com/google/i18n/phonenumbers/PhoneNumberUtil.java#L1546\r\n\t\t// Not saying that this IDD formatter replicates it 1:1, but it seems to work.\r\n\t\t// Who would even need to format phone numbers in IDD format anyway?\r\n\t\tcase 'IDD':\r\n\t\t\tif (!options.fromCountry) {\r\n\t\t\t\treturn\r\n\t\t\t\t// throw new Error('`fromCountry` option not passed for IDD-prefixed formatting.')\r\n\t\t\t}\r\n\t\t\tconst formattedNumber = formatIDD(\r\n\t\t\t\tnationalNumber,\r\n\t\t\t\tinput.carrierCode,\r\n\t\t\t\tcountryCallingCode,\r\n\t\t\t\toptions.fromCountry,\r\n\t\t\t\tmetadata\r\n\t\t\t)\r\n\t\t\treturn addExtension(formattedNumber, input.ext, metadata, options.formatExtension)\r\n\r\n\t\tdefault:\r\n\t\t\tthrow new Error(`Unknown \"format\" argument passed to \"formatNumber()\": \"${format}\"`)\r\n\t}\r\n}\r\n\r\nfunction formatNationalNumber(number, carrierCode, formatAs, metadata, options) {\r\n\tconst format = chooseFormatForNumber(metadata.formats(), number)\r\n\tif (!format) {\r\n\t\treturn number\r\n\t}\r\n\treturn formatNationalNumberUsingFormat(\r\n\t\tnumber,\r\n\t\tformat,\r\n\t\t{\r\n\t\t\tuseInternationalFormat: formatAs === 'INTERNATIONAL',\r\n\t\t\twithNationalPrefix: format.nationalPrefixIsOptionalWhenFormattingInNationalFormat() && (options && options.nationalPrefix === false) ? false : true,\r\n\t\t\tcarrierCode,\r\n\t\t\tmetadata\r\n\t\t}\r\n\t)\r\n}\r\n\r\nexport function chooseFormatForNumber(availableFormats, nationalNnumber) {\r\n\tfor (const format of availableFormats) {\r\n\t\t// Validate leading digits.\r\n\t\t// The test case for \"else path\" could be found by searching for\r\n\t\t// \"format.leadingDigitsPatterns().length === 0\".\r\n\t\tif (format.leadingDigitsPatterns().length > 0) {\r\n\t\t\t// The last leading_digits_pattern is used here, as it is the most detailed\r\n\t\t\tconst lastLeadingDigitsPattern = format.leadingDigitsPatterns()[format.leadingDigitsPatterns().length - 1]\r\n\t\t\t// If leading digits don't match then move on to the next phone number format\r\n\t\t\tif (nationalNnumber.search(lastLeadingDigitsPattern) !== 0) {\r\n\t\t\t\tcontinue\r\n\t\t\t}\r\n\t\t}\r\n\t\t// Check that the national number matches the phone number format regular expression\r\n\t\tif (matchesEntirely(nationalNnumber, format.pattern())) {\r\n\t\t\treturn format\r\n\t\t}\r\n\t}\r\n}\r\n\r\nfunction addExtension(formattedNumber, ext, metadata, formatExtension) {\r\n\treturn ext ? formatExtension(formattedNumber, ext, metadata) : formattedNumber\r\n}\r\n\r\nfunction formatIDD(\r\n\tnationalNumber,\r\n\tcarrierCode,\r\n\tcountryCallingCode,\r\n\tfromCountry,\r\n\tmetadata\r\n) {\r\n\tconst fromCountryCallingCode = getCountryCallingCode(fromCountry, metadata.metadata)\r\n\t// When calling within the same country calling code.\r\n\tif (fromCountryCallingCode === countryCallingCode) {\r\n\t\tconst formattedNumber = formatNationalNumber(nationalNumber, carrierCode, 'NATIONAL', metadata)\r\n\t\t// For NANPA regions, return the national format for these regions\r\n\t\t// but prefix it with the country calling code.\r\n\t\tif (countryCallingCode === '1') {\r\n\t\t\treturn countryCallingCode + ' ' + formattedNumber\r\n\t\t}\r\n\t\t// If regions share a country calling code, the country calling code need\r\n\t\t// not be dialled. This also applies when dialling within a region, so this\r\n\t\t// if clause covers both these cases. Technically this is the case for\r\n\t\t// dialling from La Reunion to other overseas departments of France (French\r\n\t\t// Guiana, Martinique, Guadeloupe), but not vice versa - so we don't cover\r\n\t\t// this edge case for now and for those cases return the version including\r\n\t\t// country calling code. Details here:\r\n\t\t// http://www.petitfute.com/voyage/225-info-pratiques-reunion\r\n\t\t//\r\n\t\treturn formattedNumber\r\n\t}\r\n\tconst iddPrefix = getIddPrefix(fromCountry, undefined, metadata.metadata)\r\n\tif (iddPrefix) {\r\n\t\treturn `${iddPrefix} ${countryCallingCode} ${formatNationalNumber(nationalNumber, null, 'INTERNATIONAL', metadata)}`\r\n\t}\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AAEA,OAAOA,eAAP,MAA4B,8BAA5B;AACA,OAAOC,+BAAP,MAA4C,8CAA5C;AACA,OAAOC,QAAP,IAAmBC,qBAAnB,QAAgD,eAAhD;AACA,OAAOC,YAAP,MAAyB,2BAAzB;AACA,SAASC,aAAT,QAA8B,sBAA9B;AAEA,IAAMC,eAAe,GAAG;EACvBC,eAAe,EAAE,SAAAA,gBAACC,eAAD,EAAkBC,SAAlB,EAA6BC,QAA7B;IAAA,UAAAC,MAAA,CAA6CH,eAA7C,EAAAG,MAAA,CAA+DD,QAAQ,CAACE,GAAT,EAA/D,EAAAD,MAAA,CAAgFF,SAAhF;EAAA;AADM,CAAxB;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASI,YAATA,CAAsBC,KAAtB,EAA6BC,MAA7B,EAAqCC,OAArC,EAA8CN,QAA9C,EAAwD;EACtE;EACA,IAAIM,OAAJ,EAAa;IACZA,OAAO,GAAAC,aAAA,CAAAA,aAAA,KAAQX,eAAR,GAA4BU,OAA5B,CAAP;EACA,CAFD,MAEO;IACNA,OAAO,GAAGV,eAAV;EACA;EAEDI,QAAQ,GAAG,IAAIR,QAAJ,CAAaQ,QAAb,CAAX;EAEA,IAAII,KAAK,CAACI,OAAN,IAAiBJ,KAAK,CAACI,OAAN,KAAkB,KAAvC,EAA8C;IAC7C;IACA,IAAI,CAACR,QAAQ,CAACS,UAAT,CAAoBL,KAAK,CAACI,OAA1B,CAAL,EAAyC;MACxC,MAAM,IAAIE,KAAJ,qBAAAT,MAAA,CAA8BG,KAAK,CAACI,OAApC,EAAN;IACA;IACDR,QAAQ,CAACQ,OAAT,CAAiBJ,KAAK,CAACI,OAAvB;EACA,CAND,MAOK,IAAIJ,KAAK,CAACO,kBAAV,EAA8B;IAClCX,QAAQ,CAACY,mBAAT,CAA6BR,KAAK,CAACO,kBAAnC;EACA,CAFI,MAGA,OAAOP,KAAK,CAACS,KAAN,IAAe,EAAtB;EAEL,IAAMF,kBAAkB,GAAGX,QAAQ,CAACW,kBAAT,EAA3B;EAEA,IAAMG,cAAc,GAAGR,OAAO,CAACS,EAAR,GAAaX,KAAK,CAACU,cAAnB,GAAoCV,KAAK,CAACS,KAAjE,CAxBsE,CA0BtE;EACA;;EACA,IAAIG,MAAJ;EAEA,QAAQX,MAAR;IACC,KAAK,UAAL;MACC;MACA;MACA,IAAI,CAACS,cAAL,EAAqB;QACpB,OAAO,EAAP;MACA;MACDE,MAAM,GAAGC,oBAAoB,CAACH,cAAD,EAAiBV,KAAK,CAACc,WAAvB,EAAoC,UAApC,EAAgDlB,QAAhD,EAA0DM,OAA1D,CAA7B;MACA,OAAOa,YAAY,CAACH,MAAD,EAASZ,KAAK,CAACF,GAAf,EAAoBF,QAApB,EAA8BM,OAAO,CAACT,eAAtC,CAAnB;IAED,KAAK,eAAL;MACC;MACA;MACA,IAAI,CAACiB,cAAL,EAAqB;QACpB,WAAAb,MAAA,CAAWU,kBAAX;MACA;MACDK,MAAM,GAAGC,oBAAoB,CAACH,cAAD,EAAiB,IAAjB,EAAuB,eAAvB,EAAwCd,QAAxC,EAAkDM,OAAlD,CAA7B;MACAU,MAAM,OAAAf,MAAA,CAAOU,kBAAP,OAAAV,MAAA,CAA6Be,MAA7B,CAAN;MACA,OAAOG,YAAY,CAACH,MAAD,EAASZ,KAAK,CAACF,GAAf,EAAoBF,QAApB,EAA8BM,OAAO,CAACT,eAAtC,CAAnB;IAED,KAAK,OAAL;MACC;MACA,WAAAI,MAAA,CAAWU,kBAAX,EAAAV,MAAA,CAAgCa,cAAhC;IAED,KAAK,SAAL;MACC,OAAOnB,aAAa,CAAC;QACpBqB,MAAM,MAAAf,MAAA,CAAMU,kBAAN,EAAAV,MAAA,CAA2Ba,cAA3B,CADc;QAEpBZ,GAAG,EAAEE,KAAK,CAACF;MAFS,CAAD,CAApB;IAKD;IACA;IACA;IACA;;IACA,KAAK,KAAL;MACC,IAAI,CAACI,OAAO,CAACc,WAAb,EAA0B;QACzB,OADyB,CAEzB;MACA;;MACD,IAAMtB,eAAe,GAAGuB,SAAS,CAChCP,cADgC,EAEhCV,KAAK,CAACc,WAF0B,EAGhCP,kBAHgC,EAIhCL,OAAO,CAACc,WAJwB,EAKhCpB,QALgC,CAAjC;MAOA,OAAOmB,YAAY,CAACrB,eAAD,EAAkBM,KAAK,CAACF,GAAxB,EAA6BF,QAA7B,EAAuCM,OAAO,CAACT,eAA/C,CAAnB;IAED;MACC,MAAM,IAAIa,KAAJ,gEAAAT,MAAA,CAAoEI,MAApE,QAAN;EAjDF;AAmDA;AAED,SAASY,oBAATA,CAA8BD,MAA9B,EAAsCE,WAAtC,EAAmDI,QAAnD,EAA6DtB,QAA7D,EAAuEM,OAAvE,EAAgF;EAC/E,IAAMD,MAAM,GAAGkB,qBAAqB,CAACvB,QAAQ,CAACwB,OAAT,EAAD,EAAqBR,MAArB,CAApC;EACA,IAAI,CAACX,MAAL,EAAa;IACZ,OAAOW,MAAP;EACA;EACD,OAAOzB,+BAA+B,CACrCyB,MADqC,EAErCX,MAFqC,EAGrC;IACCoB,sBAAsB,EAAEH,QAAQ,KAAK,eADtC;IAECI,kBAAkB,EAAErB,MAAM,CAACsB,sDAAP,MAAoErB,OAAO,IAAIA,OAAO,CAACsB,cAAR,KAA2B,KAA1G,GAAmH,KAAnH,GAA2H,IAFhJ;IAGCV,WAAW,EAAXA,WAHD;IAIClB,QAAQ,EAARA;EAJD,CAHqC,CAAtC;AAUA;AAED,OAAO,SAASuB,qBAATA,CAA+BM,gBAA/B,EAAiDC,eAAjD,EAAkE;EACxE,SAAAC,SAAA,GAAAC,+BAAA,CAAqBH,gBAArB,GAAAI,KAAA,IAAAA,KAAA,GAAAF,SAAA,IAAAG,IAAA,GAAuC;IAAA,IAA5B7B,MAA4B,GAAA4B,KAAA,CAAAE,KAAA;;IACtC;IACA;IACA;IACA,IAAI9B,MAAM,CAAC+B,qBAAP,GAA+BC,MAA/B,GAAwC,CAA5C,EAA+C;MAC9C;MACA,IAAMC,wBAAwB,GAAGjC,MAAM,CAAC+B,qBAAP,GAA+B/B,MAAM,CAAC+B,qBAAP,GAA+BC,MAA/B,GAAwC,CAAvE,CAAjC,CAF8C,CAG9C;;MACA,IAAIP,eAAe,CAACS,MAAhB,CAAuBD,wBAAvB,MAAqD,CAAzD,EAA4D;QAC3D;MACA;IACD,CAXqC,CAYtC;;IACA,IAAIhD,eAAe,CAACwC,eAAD,EAAkBzB,MAAM,CAACmC,OAAP,EAAlB,CAAnB,EAAwD;MACvD,OAAOnC,MAAP;IACA;EACD;AACD;AAED,SAASc,YAATA,CAAsBrB,eAAtB,EAAuCI,GAAvC,EAA4CF,QAA5C,EAAsDH,eAAtD,EAAuE;EACtE,OAAOK,GAAG,GAAGL,eAAe,CAACC,eAAD,EAAkBI,GAAlB,EAAuBF,QAAvB,CAAlB,GAAqDF,eAA/D;AACA;AAED,SAASuB,SAATA,CACCP,cADD,EAECI,WAFD,EAGCP,kBAHD,EAICS,WAJD,EAKCpB,QALD,EAME;EACD,IAAMyC,sBAAsB,GAAGhD,qBAAqB,CAAC2B,WAAD,EAAcpB,QAAQ,CAACA,QAAvB,CAApD,CADC,CAED;;EACA,IAAIyC,sBAAsB,KAAK9B,kBAA/B,EAAmD;IAClD,IAAMb,eAAe,GAAGmB,oBAAoB,CAACH,cAAD,EAAiBI,WAAjB,EAA8B,UAA9B,EAA0ClB,QAA1C,CAA5C,CADkD,CAElD;IACA;;IACA,IAAIW,kBAAkB,KAAK,GAA3B,EAAgC;MAC/B,OAAOA,kBAAkB,GAAG,GAArB,GAA2Bb,eAAlC;IACA,CANiD,CAOlD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IACA,OAAOA,eAAP;EACA;EACD,IAAM4C,SAAS,GAAGhD,YAAY,CAAC0B,WAAD,EAAcuB,SAAd,EAAyB3C,QAAQ,CAACA,QAAlC,CAA9B;EACA,IAAI0C,SAAJ,EAAe;IACd,UAAAzC,MAAA,CAAUyC,SAAV,OAAAzC,MAAA,CAAuBU,kBAAvB,OAAAV,MAAA,CAA6CgB,oBAAoB,CAACH,cAAD,EAAiB,IAAjB,EAAuB,eAAvB,EAAwCd,QAAxC,CAAjE;EACA;AACD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}