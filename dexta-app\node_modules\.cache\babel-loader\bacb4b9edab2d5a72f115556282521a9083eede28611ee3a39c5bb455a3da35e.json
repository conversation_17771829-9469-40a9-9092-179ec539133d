{"ast": null, "code": "/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */\n/* eslint-disable node/no-deprecated-api */\nvar buffer = require('buffer');\nvar Buffer = buffer.Buffer;\n\n// alternative to using Object.keys for old browsers\nfunction copyProps(src, dst) {\n  for (var key in src) {\n    dst[key] = src[key];\n  }\n}\nif (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {\n  module.exports = buffer;\n} else {\n  // Copy properties from require('buffer')\n  copyProps(buffer, exports);\n  exports.Buffer = SafeBuffer;\n}\nfunction SafeBuffer(arg, encodingOrOffset, length) {\n  return Buffer(arg, encodingOrOffset, length);\n}\nSafeBuffer.prototype = Object.create(Buffer.prototype);\n\n// Copy static methods from Buffer\ncopyProps(Buffer, SafeBuffer);\nSafeBuffer.from = function (arg, encodingOrOffset, length) {\n  if (typeof arg === 'number') {\n    throw new TypeError('Argument must not be a number');\n  }\n  return Buffer(arg, encodingOrOffset, length);\n};\nSafeBuffer.alloc = function (size, fill, encoding) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number');\n  }\n  var buf = Buffer(size);\n  if (fill !== undefined) {\n    if (typeof encoding === 'string') {\n      buf.fill(fill, encoding);\n    } else {\n      buf.fill(fill);\n    }\n  } else {\n    buf.fill(0);\n  }\n  return buf;\n};\nSafeBuffer.allocUnsafe = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number');\n  }\n  return Buffer(size);\n};\nSafeBuffer.allocUnsafeSlow = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number');\n  }\n  return buffer.SlowBuffer(size);\n};", "map": {"version": 3, "names": ["buffer", "require", "<PERSON><PERSON><PERSON>", "copyProps", "src", "dst", "key", "from", "alloc", "allocUnsafe", "allocUnsafeSlow", "module", "exports", "SafeBuffer", "arg", "encodingOrOffset", "length", "prototype", "Object", "create", "TypeError", "size", "fill", "encoding", "buf", "undefined", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/safe-buffer/index.js"], "sourcesContent": ["/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */\n/* eslint-disable node/no-deprecated-api */\nvar buffer = require('buffer')\nvar Buffer = buffer.Buffer\n\n// alternative to using Object.keys for old browsers\nfunction copyProps (src, dst) {\n  for (var key in src) {\n    dst[key] = src[key]\n  }\n}\nif (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {\n  module.exports = buffer\n} else {\n  // Copy properties from require('buffer')\n  copyProps(buffer, exports)\n  exports.Buffer = SafeBuffer\n}\n\nfunction SafeBuffer (arg, encodingOrOffset, length) {\n  return Buffer(arg, encodingOrOffset, length)\n}\n\nSafeBuffer.prototype = Object.create(Buffer.prototype)\n\n// Copy static methods from Buffer\ncopyProps(Buffer, SafeBuffer)\n\nSafeBuffer.from = function (arg, encodingOrOffset, length) {\n  if (typeof arg === 'number') {\n    throw new TypeError('Argument must not be a number')\n  }\n  return Buffer(arg, encodingOrOffset, length)\n}\n\nSafeBuffer.alloc = function (size, fill, encoding) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  var buf = Buffer(size)\n  if (fill !== undefined) {\n    if (typeof encoding === 'string') {\n      buf.fill(fill, encoding)\n    } else {\n      buf.fill(fill)\n    }\n  } else {\n    buf.fill(0)\n  }\n  return buf\n}\n\nSafeBuffer.allocUnsafe = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return Buffer(size)\n}\n\nSafeBuffer.allocUnsafeSlow = function (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('Argument must be a number')\n  }\n  return buffer.SlowBuffer(size)\n}\n"], "mappings": "AAAA;AACA;AACA,IAAIA,MAAM,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAC9B,IAAIC,MAAM,GAAGF,MAAM,CAACE,MAAM;;AAE1B;AACA,SAASC,SAASA,CAAEC,GAAG,EAAEC,GAAG,EAAE;EAC5B,KAAK,IAAIC,GAAG,IAAIF,GAAG,EAAE;IACnBC,GAAG,CAACC,GAAG,CAAC,GAAGF,GAAG,CAACE,GAAG,CAAC;EACrB;AACF;AACA,IAAIJ,MAAM,CAACK,IAAI,IAAIL,MAAM,CAACM,KAAK,IAAIN,MAAM,CAACO,WAAW,IAAIP,MAAM,CAACQ,eAAe,EAAE;EAC/EC,MAAM,CAACC,OAAO,GAAGZ,MAAM;AACzB,CAAC,MAAM;EACL;EACAG,SAAS,CAACH,MAAM,EAAEY,OAAO,CAAC;EAC1BA,OAAO,CAACV,MAAM,GAAGW,UAAU;AAC7B;AAEA,SAASA,UAAUA,CAAEC,GAAG,EAAEC,gBAAgB,EAAEC,MAAM,EAAE;EAClD,OAAOd,MAAM,CAACY,GAAG,EAAEC,gBAAgB,EAAEC,MAAM,CAAC;AAC9C;AAEAH,UAAU,CAACI,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACjB,MAAM,CAACe,SAAS,CAAC;;AAEtD;AACAd,SAAS,CAACD,MAAM,EAAEW,UAAU,CAAC;AAE7BA,UAAU,CAACN,IAAI,GAAG,UAAUO,GAAG,EAAEC,gBAAgB,EAAEC,MAAM,EAAE;EACzD,IAAI,OAAOF,GAAG,KAAK,QAAQ,EAAE;IAC3B,MAAM,IAAIM,SAAS,CAAC,+BAA+B,CAAC;EACtD;EACA,OAAOlB,MAAM,CAACY,GAAG,EAAEC,gBAAgB,EAAEC,MAAM,CAAC;AAC9C,CAAC;AAEDH,UAAU,CAACL,KAAK,GAAG,UAAUa,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAE;EACjD,IAAI,OAAOF,IAAI,KAAK,QAAQ,EAAE;IAC5B,MAAM,IAAID,SAAS,CAAC,2BAA2B,CAAC;EAClD;EACA,IAAII,GAAG,GAAGtB,MAAM,CAACmB,IAAI,CAAC;EACtB,IAAIC,IAAI,KAAKG,SAAS,EAAE;IACtB,IAAI,OAAOF,QAAQ,KAAK,QAAQ,EAAE;MAChCC,GAAG,CAACF,IAAI,CAACA,IAAI,EAAEC,QAAQ,CAAC;IAC1B,CAAC,MAAM;MACLC,GAAG,CAACF,IAAI,CAACA,IAAI,CAAC;IAChB;EACF,CAAC,MAAM;IACLE,GAAG,CAACF,IAAI,CAAC,CAAC,CAAC;EACb;EACA,OAAOE,GAAG;AACZ,CAAC;AAEDX,UAAU,CAACJ,WAAW,GAAG,UAAUY,IAAI,EAAE;EACvC,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5B,MAAM,IAAID,SAAS,CAAC,2BAA2B,CAAC;EAClD;EACA,OAAOlB,MAAM,CAACmB,IAAI,CAAC;AACrB,CAAC;AAEDR,UAAU,CAACH,eAAe,GAAG,UAAUW,IAAI,EAAE;EAC3C,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5B,MAAM,IAAID,SAAS,CAAC,2BAA2B,CAAC;EAClD;EACA,OAAOpB,MAAM,CAAC0B,UAAU,CAACL,IAAI,CAAC;AAChC,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}