{"ast": null, "code": "// Parses the `text`.\n//\n// Returns `{ value, caret }` where `caret` is\n// the caret position inside `value`\n// corresponding to the `caret_position` inside `text`.\n//\n// The `text` is parsed by feeding each character sequentially to\n// `parse_character(character, value)` function\n// and appending the result (if it's not `undefined`) to `value`.\n//\n// Example:\n//\n// `text` is `8 (800) 555-35-35`,\n// `caret_position` is `4` (before the first `0`).\n// `parse_character` is `(character, value) =>\n//   if (character >= '0' && character <= '9') { return character }`.\n//\n// then `parse()` outputs `{ value: '88005553535', caret: 2 }`.\n//\nexport default function parse(text, caret_position, parse_character) {\n  var value = '';\n  var focused_input_character_index = 0;\n  var index = 0;\n  while (index < text.length) {\n    var character = parse_character(text[index], value);\n    if (character !== undefined) {\n      value += character;\n      if (caret_position !== undefined) {\n        if (caret_position === index) {\n          focused_input_character_index = value.length - 1;\n        } else if (caret_position > index) {\n          focused_input_character_index = value.length;\n        }\n      }\n    }\n    index++;\n  } // If caret position wasn't specified\n\n  if (caret_position === undefined) {\n    // Then set caret position to \"after the last input character\"\n    focused_input_character_index = value.length;\n  }\n  var result = {\n    value: value,\n    caret: focused_input_character_index\n  };\n  return result;\n}", "map": {"version": 3, "names": ["parse", "text", "caret_position", "parse_character", "value", "focused_input_character_index", "index", "length", "character", "undefined", "result", "caret"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\input-format\\source\\parse.js"], "sourcesContent": ["// Parses the `text`.\r\n//\r\n// Returns `{ value, caret }` where `caret` is\r\n// the caret position inside `value`\r\n// corresponding to the `caret_position` inside `text`.\r\n//\r\n// The `text` is parsed by feeding each character sequentially to\r\n// `parse_character(character, value)` function\r\n// and appending the result (if it's not `undefined`) to `value`.\r\n//\r\n// Example:\r\n//\r\n// `text` is `8 (800) 555-35-35`,\r\n// `caret_position` is `4` (before the first `0`).\r\n// `parse_character` is `(character, value) =>\r\n//   if (character >= '0' && character <= '9') { return character }`.\r\n//\r\n// then `parse()` outputs `{ value: '88005553535', caret: 2 }`.\r\n//\r\nexport default function parse(text, caret_position, parse_character)\r\n{\r\n\tlet value = ''\r\n\r\n\tlet focused_input_character_index = 0\r\n\r\n\tlet index = 0\r\n\twhile (index < text.length)\r\n\t{\r\n\t\tconst character = parse_character(text[index], value)\r\n\r\n\t\tif (character !== undefined)\r\n\t\t{\r\n\t\t\tvalue += character\r\n\r\n\t\t\tif (caret_position !== undefined)\r\n\t\t\t{\r\n\t\t\t\tif (caret_position === index)\r\n\t\t\t\t{\r\n\t\t\t\t\tfocused_input_character_index = value.length - 1;\r\n\t\t\t\t}\r\n\t\t\t\telse if (caret_position > index)\r\n\t\t\t\t{\r\n\t\t\t\t\tfocused_input_character_index = value.length\r\n\t\t\t\t}\r\n\t\t\t }\r\n\t\t}\r\n\r\n\t\tindex++\r\n\t}\r\n\r\n\t// If caret position wasn't specified\r\n\tif (caret_position === undefined)\r\n\t{\r\n\t\t// Then set caret position to \"after the last input character\"\r\n\t\tfocused_input_character_index = value.length\r\n\t}\r\n\r\n\tconst result =\r\n\t{\r\n\t\tvalue,\r\n\t\tcaret : focused_input_character_index\r\n\t}\r\n\r\n\treturn result\r\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,KAATA,CAAeC,IAAf,EAAqBC,cAArB,EAAqCC,eAArC,EACf;EACC,IAAIC,KAAK,GAAG,EAAZ;EAEA,IAAIC,6BAA6B,GAAG,CAApC;EAEA,IAAIC,KAAK,GAAG,CAAZ;EACA,OAAOA,KAAK,GAAGL,IAAI,CAACM,MAApB,EACA;IACC,IAAMC,SAAS,GAAGL,eAAe,CAACF,IAAI,CAACK,KAAD,CAAL,EAAcF,KAAd,CAAjC;IAEA,IAAII,SAAS,KAAKC,SAAlB,EACA;MACCL,KAAK,IAAII,SAAT;MAEA,IAAIN,cAAc,KAAKO,SAAvB,EACA;QACC,IAAIP,cAAc,KAAKI,KAAvB,EACA;UACCD,6BAA6B,GAAGD,KAAK,CAACG,MAAN,GAAe,CAA/C;QACA,CAHD,MAIK,IAAIL,cAAc,GAAGI,KAArB,EACL;UACCD,6BAA6B,GAAGD,KAAK,CAACG,MAAtC;QACA;MACA;IACF;IAEDD,KAAK;EACL,CA5BF,CA8BC;;EACA,IAAIJ,cAAc,KAAKO,SAAvB,EACA;IACC;IACAJ,6BAA6B,GAAGD,KAAK,CAACG,MAAtC;EACA;EAED,IAAMG,MAAM,GACZ;IACCN,KAAK,EAALA,KADD;IAECO,KAAK,EAAGN;EAFT,CADA;EAMA,OAAOK,MAAP;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}