{"ast": null, "code": "import { INIT_COORDS } from '../types.js';\nexport function setClientOffset(clientOffset, sourceClientOffset) {\n  return {\n    type: INIT_COORDS,\n    payload: {\n      sourceClientOffset: sourceClientOffset || null,\n      clientOffset: clientOffset || null\n    }\n  };\n}", "map": {"version": 3, "names": ["INIT_COORDS", "setClientOffset", "clientOffset", "sourceClientOffset", "type", "payload"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\dnd-core\\src\\actions\\dragDrop\\local\\setClientOffset.ts"], "sourcesContent": ["import type { AnyAction } from 'redux'\n\nimport type { XYCoord } from '../../../interfaces.js'\nimport { INIT_COORDS } from '../types.js'\n\nexport function setClientOffset(\n\tclientOffset: XYCoord | null | undefined,\n\tsourceClientOffset?: XYCoord | null | undefined,\n): AnyAction {\n\treturn {\n\t\ttype: INIT_COORDS,\n\t\tpayload: {\n\t\t\tsourceClientOffset: sourceClientOffset || null,\n\t\t\tclientOffset: clientOffset || null,\n\t\t},\n\t}\n}\n"], "mappings": "AAGA,SAASA,WAAW,QAAQ,aAAa;AAEzC,OAAO,SAASC,eAAeA,CAC9BC,YAAwC,EACxCC,kBAA+C,EACnC;EACZ,OAAO;IACNC,IAAI,EAAEJ,WAAW;IACjBK,OAAO,EAAE;MACRF,kBAAkB,EAAEA,kBAAkB,IAAI,IAAI;MAC9CD,YAAY,EAAEA,YAAY,IAAI;;GAE/B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}