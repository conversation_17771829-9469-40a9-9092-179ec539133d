{"ast": null, "code": "'use strict';\n\nvar IS_PURE = require('../internals/is-pure');\nvar store = require('../internals/shared-store');\n(module.exports = function (key, value) {\n  return store[key] || (store[key] = value !== undefined ? value : {});\n})('versions', []).push({\n  version: '3.32.0',\n  mode: IS_PURE ? 'pure' : 'global',\n  copyright: '© 2014-2023 <PERSON> (zloirock.ru)',\n  license: 'https://github.com/zloirock/core-js/blob/v3.32.0/LICENSE',\n  source: 'https://github.com/zloirock/core-js'\n});", "map": {"version": 3, "names": ["IS_PURE", "require", "store", "module", "exports", "key", "value", "undefined", "push", "version", "mode", "copyright", "license", "source"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/core-js-pure/internals/shared.js"], "sourcesContent": ["'use strict';\nvar IS_PURE = require('../internals/is-pure');\nvar store = require('../internals/shared-store');\n\n(module.exports = function (key, value) {\n  return store[key] || (store[key] = value !== undefined ? value : {});\n})('versions', []).push({\n  version: '3.32.0',\n  mode: IS_PURE ? 'pure' : 'global',\n  copyright: '© 2014-2023 <PERSON> (zloirock.ru)',\n  license: 'https://github.com/zloirock/core-js/blob/v3.32.0/LICENSE',\n  source: 'https://github.com/zloirock/core-js'\n});\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC7C,IAAIC,KAAK,GAAGD,OAAO,CAAC,2BAA2B,CAAC;AAEhD,CAACE,MAAM,CAACC,OAAO,GAAG,UAAUC,GAAG,EAAEC,KAAK,EAAE;EACtC,OAAOJ,KAAK,CAACG,GAAG,CAAC,KAAKH,KAAK,CAACG,GAAG,CAAC,GAAGC,KAAK,KAAKC,SAAS,GAAGD,KAAK,GAAG,CAAC,CAAC,CAAC;AACtE,CAAC,EAAE,UAAU,EAAE,EAAE,CAAC,CAACE,IAAI,CAAC;EACtBC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAEV,OAAO,GAAG,MAAM,GAAG,QAAQ;EACjCW,SAAS,EAAE,2CAA2C;EACtDC,OAAO,EAAE,0DAA0D;EACnEC,MAAM,EAAE;AACV,CAAC,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}