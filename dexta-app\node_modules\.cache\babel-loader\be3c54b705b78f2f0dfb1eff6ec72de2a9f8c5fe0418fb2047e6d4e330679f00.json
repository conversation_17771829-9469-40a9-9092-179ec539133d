{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { zip as zipStatic } from '../observable/zip';\nimport { operate } from '../util/lift';\nexport function zip() {\n  var sources = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    sources[_i] = arguments[_i];\n  }\n  return operate(function (source, subscriber) {\n    zipStatic.apply(void 0, __spreadArray([source], __read(sources))).subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["zip", "zipStatic", "operate", "sources", "_i", "arguments", "length", "source", "subscriber", "apply", "__spread<PERSON><PERSON>y", "__read", "subscribe"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\zip.ts"], "sourcesContent": ["import { zip as zipStatic } from '../observable/zip';\nimport { ObservableInput, ObservableInputTuple, OperatorFunction, Cons } from '../types';\nimport { operate } from '../util/lift';\n\n/** @deprecated Replaced with {@link zipWith}. Will be removed in v8. */\nexport function zip<T, A extends readonly unknown[]>(otherInputs: [...ObservableInputTuple<A>]): OperatorFunction<T, Cons<T, A>>;\n/** @deprecated Replaced with {@link zipWith}. Will be removed in v8. */\nexport function zip<T, A extends readonly unknown[], R>(\n  otherInputsAndProject: [...ObservableInputTuple<A>],\n  project: (...values: Cons<T, A>) => R\n): OperatorFunction<T, R>;\n/** @deprecated Replaced with {@link zipWith}. Will be removed in v8. */\nexport function zip<T, A extends readonly unknown[]>(...otherInputs: [...ObservableInputTuple<A>]): OperatorFunction<T, Cons<T, A>>;\n/** @deprecated Replaced with {@link zipWith}. Will be removed in v8. */\nexport function zip<T, A extends readonly unknown[], R>(\n  ...otherInputsAndProject: [...ObservableInputTuple<A>, (...values: Cons<T, A>) => R]\n): OperatorFunction<T, R>;\n\n/**\n * @deprecated Replaced with {@link zipWith}. Will be removed in v8.\n */\nexport function zip<T, R>(...sources: Array<ObservableInput<any> | ((...values: Array<any>) => R)>): OperatorFunction<T, any> {\n  return operate((source, subscriber) => {\n    zipStatic(source as ObservableInput<any>, ...(sources as Array<ObservableInput<any>>)).subscribe(subscriber);\n  });\n}\n"], "mappings": ";AAAA,SAASA,GAAG,IAAIC,SAAS,QAAQ,mBAAmB;AAEpD,SAASC,OAAO,QAAQ,cAAc;AAmBtC,OAAM,SAAUF,GAAGA,CAAA;EAAO,IAAAG,OAAA;OAAA,IAAAC,EAAA,IAAwE,EAAxEA,EAAA,GAAAC,SAAA,CAAAC,MAAwE,EAAxEF,EAAA,EAAwE;IAAxED,OAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EACxB,OAAOF,OAAO,CAAC,UAACK,MAAM,EAAEC,UAAU;IAChCP,SAAS,CAAAQ,KAAA,SAAAC,aAAA,EAACH,MAA8B,GAAAI,MAAA,CAAMR,OAAuC,IAAES,SAAS,CAACJ,UAAU,CAAC;EAC9G,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}