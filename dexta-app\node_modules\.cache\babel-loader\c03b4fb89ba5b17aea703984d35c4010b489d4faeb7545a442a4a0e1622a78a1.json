{"ast": null, "code": "import { AsyncSubject } from '../AsyncSubject';\nimport { ConnectableObservable } from '../observable/ConnectableObservable';\nexport function publishLast() {\n  return function (source) {\n    var subject = new AsyncSubject();\n    return new ConnectableObservable(source, function () {\n      return subject;\n    });\n  };\n}", "map": {"version": 3, "names": ["AsyncSubject", "ConnectableObservable", "publishLast", "source", "subject"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\publishLast.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { AsyncSubject } from '../AsyncSubject';\nimport { ConnectableObservable } from '../observable/ConnectableObservable';\nimport { UnaryFunction } from '../types';\n\n/**\n * Returns a connectable observable sequence that shares a single subscription to the\n * underlying sequence containing only the last notification.\n *\n * ![](publishLast.png)\n *\n * Similar to {@link publish}, but it waits until the source observable completes and stores\n * the last emitted value.\n * Similarly to {@link publishReplay} and {@link publishBehavior}, this keeps storing the last\n * value even if it has no more subscribers. If subsequent subscriptions happen, they will\n * immediately get that last stored value and complete.\n *\n * ## Example\n *\n * ```ts\n * import { ConnectableObservable, interval, publishLast, tap, take } from 'rxjs';\n *\n * const connectable = <ConnectableObservable<number>>interval(1000)\n *   .pipe(\n *     tap(x => console.log('side effect', x)),\n *     take(3),\n *     publishLast()\n *   );\n *\n * connectable.subscribe({\n *   next: x => console.log('Sub. A', x),\n *   error: err => console.log('Sub. A Error', err),\n *   complete: () => console.log('Sub. A Complete')\n * });\n *\n * connectable.subscribe({\n *   next: x => console.log('Sub. B', x),\n *   error: err => console.log('Sub. B Error', err),\n *   complete: () => console.log('Sub. B Complete')\n * });\n *\n * connectable.connect();\n *\n * // Results:\n * // 'side effect 0'   - after one second\n * // 'side effect 1'   - after two seconds\n * // 'side effect 2'   - after three seconds\n * // 'Sub. A 2'        - immediately after 'side effect 2'\n * // 'Sub. B 2'\n * // 'Sub. A Complete'\n * // 'Sub. B Complete'\n * ```\n *\n * @see {@link ConnectableObservable}\n * @see {@link publish}\n * @see {@link publishReplay}\n * @see {@link publishBehavior}\n *\n * @return A function that returns an Observable that emits elements of a\n * sequence produced by multicasting the source sequence.\n * @deprecated Will be removed in v8. To create a connectable observable with an\n * {@link AsyncSubject} under the hood, use {@link connectable}.\n * `source.pipe(publishLast())` is equivalent to\n * `connectable(source, { connector: () => new AsyncSubject(), resetOnDisconnect: false })`.\n * If you're using {@link refCount} after `publishLast`, use the {@link share} operator instead.\n * `source.pipe(publishLast(), refCount())` is equivalent to\n * `source.pipe(share({ connector: () => new AsyncSubject(), resetOnError: false, resetOnComplete: false, resetOnRefCountZero: false }))`.\n * Details: https://rxjs.dev/deprecations/multicasting\n */\nexport function publishLast<T>(): UnaryFunction<Observable<T>, ConnectableObservable<T>> {\n  // Note that this has *never* supported a selector function like `publish` and `publishReplay`.\n  return (source) => {\n    const subject = new AsyncSubject<T>();\n    return new ConnectableObservable(source, () => subject);\n  };\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,qBAAqB,QAAQ,qCAAqC;AAmE3E,OAAM,SAAUC,WAAWA,CAAA;EAEzB,OAAO,UAACC,MAAM;IACZ,IAAMC,OAAO,GAAG,IAAIJ,YAAY,EAAK;IACrC,OAAO,IAAIC,qBAAqB,CAACE,MAAM,EAAE;MAAM,OAAAC,OAAO;IAAP,CAAO,CAAC;EACzD,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}