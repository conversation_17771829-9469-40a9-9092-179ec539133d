{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"action\", \"children\", \"component\", \"components\", \"componentsProps\", \"disabled\", \"focusableWhenDisabled\", \"onBlur\", \"onClick\", \"onFocus\", \"onFocusVisible\", \"onKeyDown\", \"onKeyUp\", \"onMouseLeave\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '../composeClasses';\nimport { getButtonUnstyledUtilityClass } from './buttonUnstyledClasses';\nimport useButton from './useButton';\nimport { useSlotProps } from '../utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    active,\n    disabled,\n    focusVisible\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', focusVisible && 'focusVisible', active && 'active']\n  };\n  return composeClasses(slots, getButtonUnstyledUtilityClass, {});\n};\n/**\n * The foundation for building custom-styled buttons.\n *\n * Demos:\n *\n * - [Unstyled button](https://mui.com/base/react-button/)\n *\n * API:\n *\n * - [ButtonUnstyled API](https://mui.com/base/api/button-unstyled/)\n */\n\nconst ButtonUnstyled = /*#__PURE__*/React.forwardRef(function ButtonUnstyled(props, forwardedRef) {\n  var _ref;\n  const {\n      action,\n      children,\n      component,\n      components = {},\n      componentsProps = {},\n      focusableWhenDisabled = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const buttonRef = React.useRef();\n  const {\n    active,\n    focusVisible,\n    setFocusVisible,\n    getRootProps\n  } = useButton(_extends({}, props, {\n    focusableWhenDisabled\n  }));\n  React.useImperativeHandle(action, () => ({\n    focusVisible: () => {\n      setFocusVisible(true);\n      buttonRef.current.focus();\n    }\n  }), [setFocusVisible]);\n  const ownerState = _extends({}, props, {\n    active,\n    focusableWhenDisabled,\n    focusVisible\n  });\n  const classes = useUtilityClasses(ownerState);\n  const Root = (_ref = component != null ? component : components.Root) != null ? _ref : 'button';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    getSlotProps: getRootProps,\n    externalForwardedProps: other,\n    externalSlotProps: componentsProps.root,\n    additionalProps: {\n      ref: forwardedRef\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? ButtonUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * A ref for imperative actions. It currently only supports `focusVisible()` action.\n   */\n  action: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: PropTypes.shape({\n      focusVisible: PropTypes.func.isRequired\n    })\n  })]),\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the Button.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The props used for each slot inside the Button.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, allows a disabled button to receive focus.\n   * @default false\n   */\n  focusableWhenDisabled: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocusVisible: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyUp: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onMouseLeave: PropTypes.func\n} : void 0;\nexport default ButtonUnstyled;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "composeClasses", "getButtonUnstyledUtilityClass", "useButton", "useSlotProps", "jsx", "_jsx", "useUtilityClasses", "ownerState", "active", "disabled", "focusVisible", "slots", "root", "ButtonUnstyled", "forwardRef", "props", "forwardedRef", "_ref", "action", "children", "component", "components", "componentsProps", "focusableWhenDisabled", "other", "buttonRef", "useRef", "setFocusVisible", "getRootProps", "useImperativeHandle", "current", "focus", "classes", "Root", "rootProps", "elementType", "getSlotProps", "externalForwardedProps", "externalSlotProps", "additionalProps", "ref", "className", "process", "env", "NODE_ENV", "propTypes", "oneOfType", "func", "shape", "isRequired", "node", "object", "bool", "onBlur", "onClick", "onFocus", "onFocusVisible", "onKeyDown", "onKeyUp", "onMouseLeave"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/ButtonUnstyled/ButtonUnstyled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"action\", \"children\", \"component\", \"components\", \"componentsProps\", \"disabled\", \"focusableWhenDisabled\", \"onBlur\", \"onClick\", \"onFocus\", \"onFocusVisible\", \"onKeyDown\", \"onKeyUp\", \"onMouseLeave\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport composeClasses from '../composeClasses';\nimport { getButtonUnstyledUtilityClass } from './buttonUnstyledClasses';\nimport useButton from './useButton';\nimport { useSlotProps } from '../utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n\nconst useUtilityClasses = ownerState => {\n  const {\n    active,\n    disabled,\n    focusVisible\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', focusVisible && 'focusVisible', active && 'active']\n  };\n  return composeClasses(slots, getButtonUnstyledUtilityClass, {});\n};\n/**\n * The foundation for building custom-styled buttons.\n *\n * Demos:\n *\n * - [Unstyled button](https://mui.com/base/react-button/)\n *\n * API:\n *\n * - [ButtonUnstyled API](https://mui.com/base/api/button-unstyled/)\n */\n\n\nconst ButtonUnstyled = /*#__PURE__*/React.forwardRef(function ButtonUnstyled(props, forwardedRef) {\n  var _ref;\n\n  const {\n    action,\n    children,\n    component,\n    components = {},\n    componentsProps = {},\n    focusableWhenDisabled = false\n  } = props,\n        other = _objectWithoutPropertiesLoose(props, _excluded);\n\n  const buttonRef = React.useRef();\n  const {\n    active,\n    focusVisible,\n    setFocusVisible,\n    getRootProps\n  } = useButton(_extends({}, props, {\n    focusableWhenDisabled\n  }));\n  React.useImperativeHandle(action, () => ({\n    focusVisible: () => {\n      setFocusVisible(true);\n      buttonRef.current.focus();\n    }\n  }), [setFocusVisible]);\n\n  const ownerState = _extends({}, props, {\n    active,\n    focusableWhenDisabled,\n    focusVisible\n  });\n\n  const classes = useUtilityClasses(ownerState);\n  const Root = (_ref = component != null ? component : components.Root) != null ? _ref : 'button';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    getSlotProps: getRootProps,\n    externalForwardedProps: other,\n    externalSlotProps: componentsProps.root,\n    additionalProps: {\n      ref: forwardedRef\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? ButtonUnstyled.propTypes\n/* remove-proptypes */\n= {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * A ref for imperative actions. It currently only supports `focusVisible()` action.\n   */\n  action: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: PropTypes.shape({\n      focusVisible: PropTypes.func.isRequired\n    })\n  })]),\n\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n\n  /**\n   * The components used for each slot inside the Button.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n\n  /**\n   * The props used for each slot inside the Button.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n\n  /**\n   * If `true`, allows a disabled button to receive focus.\n   * @default false\n   */\n  focusableWhenDisabled: PropTypes.bool,\n\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n\n  /**\n   * @ignore\n   */\n  onFocusVisible: PropTypes.func,\n\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n\n  /**\n   * @ignore\n   */\n  onKeyUp: PropTypes.func,\n\n  /**\n   * @ignore\n   */\n  onMouseLeave: PropTypes.func\n} : void 0;\nexport default ButtonUnstyled;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,UAAU,EAAE,uBAAuB,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,WAAW,EAAE,SAAS,EAAE,cAAc,CAAC;AACrN,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,SAASC,6BAA6B,QAAQ,yBAAyB;AACvE,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAE/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,MAAM;IACNC,QAAQ;IACRC;EACF,CAAC,GAAGH,UAAU;EACd,MAAMI,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEH,QAAQ,IAAI,UAAU,EAAEC,YAAY,IAAI,cAAc,EAAEF,MAAM,IAAI,QAAQ;EAC3F,CAAC;EACD,OAAOR,cAAc,CAACW,KAAK,EAAEV,6BAA6B,EAAE,CAAC,CAAC,CAAC;AACjE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,MAAMY,cAAc,GAAG,aAAaf,KAAK,CAACgB,UAAU,CAAC,SAASD,cAAcA,CAACE,KAAK,EAAEC,YAAY,EAAE;EAChG,IAAIC,IAAI;EAER,MAAM;MACJC,MAAM;MACNC,QAAQ;MACRC,SAAS;MACTC,UAAU,GAAG,CAAC,CAAC;MACfC,eAAe,GAAG,CAAC,CAAC;MACpBC,qBAAqB,GAAG;IAC1B,CAAC,GAAGR,KAAK;IACHS,KAAK,GAAG5B,6BAA6B,CAACmB,KAAK,EAAElB,SAAS,CAAC;EAE7D,MAAM4B,SAAS,GAAG3B,KAAK,CAAC4B,MAAM,CAAC,CAAC;EAChC,MAAM;IACJlB,MAAM;IACNE,YAAY;IACZiB,eAAe;IACfC;EACF,CAAC,GAAG1B,SAAS,CAACP,QAAQ,CAAC,CAAC,CAAC,EAAEoB,KAAK,EAAE;IAChCQ;EACF,CAAC,CAAC,CAAC;EACHzB,KAAK,CAAC+B,mBAAmB,CAACX,MAAM,EAAE,OAAO;IACvCR,YAAY,EAAEA,CAAA,KAAM;MAClBiB,eAAe,CAAC,IAAI,CAAC;MACrBF,SAAS,CAACK,OAAO,CAACC,KAAK,CAAC,CAAC;IAC3B;EACF,CAAC,CAAC,EAAE,CAACJ,eAAe,CAAC,CAAC;EAEtB,MAAMpB,UAAU,GAAGZ,QAAQ,CAAC,CAAC,CAAC,EAAEoB,KAAK,EAAE;IACrCP,MAAM;IACNe,qBAAqB;IACrBb;EACF,CAAC,CAAC;EAEF,MAAMsB,OAAO,GAAG1B,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM0B,IAAI,GAAG,CAAChB,IAAI,GAAGG,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAGC,UAAU,CAACY,IAAI,KAAK,IAAI,GAAGhB,IAAI,GAAG,QAAQ;EAC/F,MAAMiB,SAAS,GAAG/B,YAAY,CAAC;IAC7BgC,WAAW,EAAEF,IAAI;IACjBG,YAAY,EAAER,YAAY;IAC1BS,sBAAsB,EAAEb,KAAK;IAC7Bc,iBAAiB,EAAEhB,eAAe,CAACV,IAAI;IACvC2B,eAAe,EAAE;MACfC,GAAG,EAAExB;IACP,CAAC;IACDT,UAAU;IACVkC,SAAS,EAAET,OAAO,CAACpB;EACrB,CAAC,CAAC;EACF,OAAO,aAAaP,IAAI,CAAC4B,IAAI,EAAEtC,QAAQ,CAAC,CAAC,CAAC,EAAEuC,SAAS,EAAE;IACrDf,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFuB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG/B,cAAc,CAACgC;AACvD,yBACE;EACA;EACA;EACA;EACA;;EAEA;AACF;AACA;EACE3B,MAAM,EAAEnB,SAAS,CAAC+C,SAAS,CAAC,CAAC/C,SAAS,CAACgD,IAAI,EAAEhD,SAAS,CAACiD,KAAK,CAAC;IAC3DlB,OAAO,EAAE/B,SAAS,CAACiD,KAAK,CAAC;MACvBtC,YAAY,EAAEX,SAAS,CAACgD,IAAI,CAACE;IAC/B,CAAC;EACH,CAAC,CAAC,CAAC,CAAC;EAEJ;AACF;AACA;EACE9B,QAAQ,EAAEpB,SAAS,CAACmD,IAAI;EAExB;AACF;AACA;AACA;EACE9B,SAAS,EAAErB,SAAS,CAACoC,WAAW;EAEhC;AACF;AACA;AACA;AACA;EACEd,UAAU,EAAEtB,SAAS,CAACiD,KAAK,CAAC;IAC1Bf,IAAI,EAAElC,SAAS,CAACoC;EAClB,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACEb,eAAe,EAAEvB,SAAS,CAACiD,KAAK,CAAC;IAC/BpC,IAAI,EAAEb,SAAS,CAAC+C,SAAS,CAAC,CAAC/C,SAAS,CAACgD,IAAI,EAAEhD,SAAS,CAACoD,MAAM,CAAC;EAC9D,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACE1C,QAAQ,EAAEV,SAAS,CAACqD,IAAI;EAExB;AACF;AACA;AACA;EACE7B,qBAAqB,EAAExB,SAAS,CAACqD,IAAI;EAErC;AACF;AACA;EACEC,MAAM,EAAEtD,SAAS,CAACgD,IAAI;EAEtB;AACF;AACA;EACEO,OAAO,EAAEvD,SAAS,CAACgD,IAAI;EAEvB;AACF;AACA;EACEQ,OAAO,EAAExD,SAAS,CAACgD,IAAI;EAEvB;AACF;AACA;EACES,cAAc,EAAEzD,SAAS,CAACgD,IAAI;EAE9B;AACF;AACA;EACEU,SAAS,EAAE1D,SAAS,CAACgD,IAAI;EAEzB;AACF;AACA;EACEW,OAAO,EAAE3D,SAAS,CAACgD,IAAI;EAEvB;AACF;AACA;EACEY,YAAY,EAAE5D,SAAS,CAACgD;AAC1B,CAAC,GAAG,KAAK,CAAC;AACV,eAAelC,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}