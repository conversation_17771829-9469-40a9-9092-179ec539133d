{"ast": null, "code": "export function isValidDate(value) {\n  return value instanceof Date && !isNaN(value);\n}", "map": {"version": 3, "names": ["isValidDate", "value", "Date", "isNaN"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\util\\isDate.ts"], "sourcesContent": ["/**\n * Checks to see if a value is not only a `Date` object,\n * but a *valid* `Date` object that can be converted to a\n * number. For example, `new Date('blah')` is indeed an\n * `instanceof Date`, however it cannot be converted to a\n * number.\n */\nexport function isValidDate(value: any): value is Date {\n  return value instanceof Date && !isNaN(value as any);\n}\n"], "mappings": "AAOA,OAAM,SAAUA,WAAWA,CAACC,KAAU;EACpC,OAAOA,KAAK,YAAYC,IAAI,IAAI,CAACC,KAAK,CAACF,KAAY,CAAC;AACtD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}