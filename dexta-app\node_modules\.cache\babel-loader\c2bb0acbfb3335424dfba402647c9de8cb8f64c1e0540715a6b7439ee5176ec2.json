{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { SafeSubscriber } from '../Subscriber';\nimport { operate } from '../util/lift';\nexport function share(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = options.connector,\n    connector = _a === void 0 ? function () {\n      return new Subject();\n    } : _a,\n    _b = options.resetOnError,\n    resetOnError = _b === void 0 ? true : _b,\n    _c = options.resetOnComplete,\n    resetOnComplete = _c === void 0 ? true : _c,\n    _d = options.resetOnRefCountZero,\n    resetOnRefCountZero = _d === void 0 ? true : _d;\n  return function (wrapperSource) {\n    var connection;\n    var resetConnection;\n    var subject;\n    var refCount = 0;\n    var hasCompleted = false;\n    var hasErrored = false;\n    var cancelReset = function () {\n      resetConnection === null || resetConnection === void 0 ? void 0 : resetConnection.unsubscribe();\n      resetConnection = undefined;\n    };\n    var reset = function () {\n      cancelReset();\n      connection = subject = undefined;\n      hasCompleted = hasErrored = false;\n    };\n    var resetAndUnsubscribe = function () {\n      var conn = connection;\n      reset();\n      conn === null || conn === void 0 ? void 0 : conn.unsubscribe();\n    };\n    return operate(function (source, subscriber) {\n      refCount++;\n      if (!hasErrored && !hasCompleted) {\n        cancelReset();\n      }\n      var dest = subject = subject !== null && subject !== void 0 ? subject : connector();\n      subscriber.add(function () {\n        refCount--;\n        if (refCount === 0 && !hasErrored && !hasCompleted) {\n          resetConnection = handleReset(resetAndUnsubscribe, resetOnRefCountZero);\n        }\n      });\n      dest.subscribe(subscriber);\n      if (!connection && refCount > 0) {\n        connection = new SafeSubscriber({\n          next: function (value) {\n            return dest.next(value);\n          },\n          error: function (err) {\n            hasErrored = true;\n            cancelReset();\n            resetConnection = handleReset(reset, resetOnError, err);\n            dest.error(err);\n          },\n          complete: function () {\n            hasCompleted = true;\n            cancelReset();\n            resetConnection = handleReset(reset, resetOnComplete);\n            dest.complete();\n          }\n        });\n        innerFrom(source).subscribe(connection);\n      }\n    })(wrapperSource);\n  };\n}\nfunction handleReset(reset, on) {\n  var args = [];\n  for (var _i = 2; _i < arguments.length; _i++) {\n    args[_i - 2] = arguments[_i];\n  }\n  if (on === true) {\n    reset();\n    return;\n  }\n  if (on === false) {\n    return;\n  }\n  var onSubscriber = new SafeSubscriber({\n    next: function () {\n      onSubscriber.unsubscribe();\n      reset();\n    }\n  });\n  return innerFrom(on.apply(void 0, __spreadArray([], __read(args)))).subscribe(onSubscriber);\n}", "map": {"version": 3, "names": ["innerFrom", "Subject", "SafeSubscriber", "operate", "share", "options", "_a", "connector", "_b", "resetOnError", "_c", "resetOnComplete", "_d", "resetOnRefCountZero", "wrapperSource", "connection", "resetConnection", "subject", "refCount", "hasCompleted", "hasErrored", "cancelReset", "unsubscribe", "undefined", "reset", "resetAndUnsubscribe", "conn", "source", "subscriber", "dest", "add", "handleReset", "subscribe", "next", "value", "error", "err", "complete", "on", "args", "_i", "arguments", "length", "onSubscriber", "apply", "__spread<PERSON><PERSON>y", "__read"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\share.ts"], "sourcesContent": ["import { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { SafeSubscriber } from '../Subscriber';\nimport { Subscription } from '../Subscription';\nimport { MonoTypeOperatorFunction, SubjectLike, ObservableInput } from '../types';\nimport { operate } from '../util/lift';\n\nexport interface ShareConfig<T> {\n  /**\n   * The factory used to create the subject that will connect the source observable to\n   * multicast consumers.\n   */\n  connector?: () => SubjectLike<T>;\n  /**\n   * If `true`, the resulting observable will reset internal state on error from source and return to a \"cold\" state. This\n   * allows the resulting observable to be \"retried\" in the event of an error.\n   * If `false`, when an error comes from the source it will push the error into the connecting subject, and the subject\n   * will remain the connecting subject, meaning the resulting observable will not go \"cold\" again, and subsequent retries\n   * or resubscriptions will resubscribe to that same subject. In all cases, RxJS subjects will emit the same error again, however\n   * {@link ReplaySubject} will also push its buffered values before pushing the error.\n   * It is also possible to pass a notifier factory returning an `ObservableInput` instead which grants more fine-grained\n   * control over how and when the reset should happen. This allows behaviors like conditional or delayed resets.\n   */\n  resetOnError?: boolean | ((error: any) => ObservableInput<any>);\n  /**\n   * If `true`, the resulting observable will reset internal state on completion from source and return to a \"cold\" state. This\n   * allows the resulting observable to be \"repeated\" after it is done.\n   * If `false`, when the source completes, it will push the completion through the connecting subject, and the subject\n   * will remain the connecting subject, meaning the resulting observable will not go \"cold\" again, and subsequent repeats\n   * or resubscriptions will resubscribe to that same subject.\n   * It is also possible to pass a notifier factory returning an `ObservableInput` instead which grants more fine-grained\n   * control over how and when the reset should happen. This allows behaviors like conditional or delayed resets.\n   */\n  resetOnComplete?: boolean | (() => ObservableInput<any>);\n  /**\n   * If `true`, when the number of subscribers to the resulting observable reaches zero due to those subscribers unsubscribing, the\n   * internal state will be reset and the resulting observable will return to a \"cold\" state. This means that the next\n   * time the resulting observable is subscribed to, a new subject will be created and the source will be subscribed to\n   * again.\n   * If `false`, when the number of subscribers to the resulting observable reaches zero due to unsubscription, the subject\n   * will remain connected to the source, and new subscriptions to the result will be connected through that same subject.\n   * It is also possible to pass a notifier factory returning an `ObservableInput` instead which grants more fine-grained\n   * control over how and when the reset should happen. This allows behaviors like conditional or delayed resets.\n   */\n  resetOnRefCountZero?: boolean | (() => ObservableInput<any>);\n}\n\nexport function share<T>(): MonoTypeOperatorFunction<T>;\n\nexport function share<T>(options: ShareConfig<T>): MonoTypeOperatorFunction<T>;\n\n/**\n * Returns a new Observable that multicasts (shares) the original Observable. As long as there is at least one\n * Subscriber this Observable will be subscribed and emitting data. When all subscribers have unsubscribed it will\n * unsubscribe from the source Observable. Because the Observable is multicasting it makes the stream `hot`.\n * This is an alias for `multicast(() => new Subject()), refCount()`.\n *\n * The subscription to the underlying source Observable can be reset (unsubscribe and resubscribe for new subscribers),\n * if the subscriber count to the shared observable drops to 0, or if the source Observable errors or completes. It is\n * possible to use notifier factories for the resets to allow for behaviors like conditional or delayed resets. Please\n * note that resetting on error or complete of the source Observable does not behave like a transparent retry or restart\n * of the source because the error or complete will be forwarded to all subscribers and their subscription will be\n * closed. Only new subscribers after a reset on error or complete happened will cause a fresh subscription to the\n * source. To achieve transparent retries or restarts pipe the source through appropriate operators before sharing.\n *\n * ![](share.png)\n *\n * ## Example\n *\n * Generate new multicast Observable from the `source` Observable value\n *\n * ```ts\n * import { interval, tap, map, take, share } from 'rxjs';\n *\n * const source = interval(1000).pipe(\n *   tap(x => console.log('Processing: ', x)),\n *   map(x => x * x),\n *   take(6),\n *   share()\n * );\n *\n * source.subscribe(x => console.log('subscription 1: ', x));\n * source.subscribe(x => console.log('subscription 2: ', x));\n *\n * // Logs:\n * // Processing: 0\n * // subscription 1: 0\n * // subscription 2: 0\n * // Processing: 1\n * // subscription 1: 1\n * // subscription 2: 1\n * // Processing: 2\n * // subscription 1: 4\n * // subscription 2: 4\n * // Processing: 3\n * // subscription 1: 9\n * // subscription 2: 9\n * // Processing: 4\n * // subscription 1: 16\n * // subscription 2: 16\n * // Processing: 5\n * // subscription 1: 25\n * // subscription 2: 25\n * ```\n *\n * ## Example with notifier factory: Delayed reset\n *\n * ```ts\n * import { interval, take, share, timer } from 'rxjs';\n *\n * const source = interval(1000).pipe(\n *   take(3),\n *   share({\n *     resetOnRefCountZero: () => timer(1000)\n *   })\n * );\n *\n * const subscriptionOne = source.subscribe(x => console.log('subscription 1: ', x));\n * setTimeout(() => subscriptionOne.unsubscribe(), 1300);\n *\n * setTimeout(() => source.subscribe(x => console.log('subscription 2: ', x)), 1700);\n *\n * setTimeout(() => source.subscribe(x => console.log('subscription 3: ', x)), 5000);\n *\n * // Logs:\n * // subscription 1:  0\n * // (subscription 1 unsubscribes here)\n * // (subscription 2 subscribes here ~400ms later, source was not reset)\n * // subscription 2:  1\n * // subscription 2:  2\n * // (subscription 2 unsubscribes here)\n * // (subscription 3 subscribes here ~2000ms later, source did reset before)\n * // subscription 3:  0\n * // subscription 3:  1\n * // subscription 3:  2\n * ```\n *\n * @see {@link shareReplay}\n *\n * @return A function that returns an Observable that mirrors the source.\n */\nexport function share<T>(options: ShareConfig<T> = {}): MonoTypeOperatorFunction<T> {\n  const { connector = () => new Subject<T>(), resetOnError = true, resetOnComplete = true, resetOnRefCountZero = true } = options;\n  // It's necessary to use a wrapper here, as the _operator_ must be\n  // referentially transparent. Otherwise, it cannot be used in calls to the\n  // static `pipe` function - to create a partial pipeline.\n  //\n  // The _operator function_ - the function returned by the _operator_ - will\n  // not be referentially transparent - as it shares its source - but the\n  // _operator function_ is called when the complete pipeline is composed via a\n  // call to a source observable's `pipe` method - not when the static `pipe`\n  // function is called.\n  return (wrapperSource) => {\n    let connection: SafeSubscriber<T> | undefined;\n    let resetConnection: Subscription | undefined;\n    let subject: SubjectLike<T> | undefined;\n    let refCount = 0;\n    let hasCompleted = false;\n    let hasErrored = false;\n\n    const cancelReset = () => {\n      resetConnection?.unsubscribe();\n      resetConnection = undefined;\n    };\n    // Used to reset the internal state to a \"cold\"\n    // state, as though it had never been subscribed to.\n    const reset = () => {\n      cancelReset();\n      connection = subject = undefined;\n      hasCompleted = hasErrored = false;\n    };\n    const resetAndUnsubscribe = () => {\n      // We need to capture the connection before\n      // we reset (if we need to reset).\n      const conn = connection;\n      reset();\n      conn?.unsubscribe();\n    };\n\n    return operate<T, T>((source, subscriber) => {\n      refCount++;\n      if (!hasErrored && !hasCompleted) {\n        cancelReset();\n      }\n\n      // Create the subject if we don't have one yet. Grab a local reference to\n      // it as well, which avoids non-null assertions when using it and, if we\n      // connect to it now, then error/complete need a reference after it was\n      // reset.\n      const dest = (subject = subject ?? connector());\n\n      // Add the finalization directly to the subscriber - instead of returning it -\n      // so that the handling of the subscriber's unsubscription will be wired\n      // up _before_ the subscription to the source occurs. This is done so that\n      // the assignment to the source connection's `closed` property will be seen\n      // by synchronous firehose sources.\n      subscriber.add(() => {\n        refCount--;\n\n        // If we're resetting on refCount === 0, and it's 0, we only want to do\n        // that on \"unsubscribe\", really. Resetting on error or completion is a different\n        // configuration.\n        if (refCount === 0 && !hasErrored && !hasCompleted) {\n          resetConnection = handleReset(resetAndUnsubscribe, resetOnRefCountZero);\n        }\n      });\n\n      // The following line adds the subscription to the subscriber passed.\n      // Basically, `subscriber === dest.subscribe(subscriber)` is `true`.\n      dest.subscribe(subscriber);\n\n      if (\n        !connection &&\n        // Check this shareReplay is still activate - it can be reset to 0\n        // and be \"unsubscribed\" _before_ it actually subscribes.\n        // If we were to subscribe then, it'd leak and get stuck.\n        refCount > 0\n      ) {\n        // We need to create a subscriber here - rather than pass an observer and\n        // assign the returned subscription to connection - because it's possible\n        // for reentrant subscriptions to the shared observable to occur and in\n        // those situations we want connection to be already-assigned so that we\n        // don't create another connection to the source.\n        connection = new SafeSubscriber({\n          next: (value) => dest.next(value),\n          error: (err) => {\n            hasErrored = true;\n            cancelReset();\n            resetConnection = handleReset(reset, resetOnError, err);\n            dest.error(err);\n          },\n          complete: () => {\n            hasCompleted = true;\n            cancelReset();\n            resetConnection = handleReset(reset, resetOnComplete);\n            dest.complete();\n          },\n        });\n        innerFrom(source).subscribe(connection);\n      }\n    })(wrapperSource);\n  };\n}\n\nfunction handleReset<T extends unknown[] = never[]>(\n  reset: () => void,\n  on: boolean | ((...args: T) => ObservableInput<any>),\n  ...args: T\n): Subscription | undefined {\n  if (on === true) {\n    reset();\n    return;\n  }\n\n  if (on === false) {\n    return;\n  }\n\n  const onSubscriber = new SafeSubscriber({\n    next: () => {\n      onSubscriber.unsubscribe();\n      reset();\n    },\n  });\n\n  return innerFrom(on(...args)).subscribe(onSubscriber);\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,yBAAyB;AACnD,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,cAAc,QAAQ,eAAe;AAG9C,SAASC,OAAO,QAAQ,cAAc;AAwItC,OAAM,SAAUC,KAAKA,CAAIC,OAA4B;EAA5B,IAAAA,OAAA;IAAAA,OAAA,KAA4B;EAAA;EAC3C,IAAAC,EAAA,GAAgHD,OAAO,CAAAE,SAArF;IAAlCA,SAAS,GAAAD,EAAA,cAAG;MAAM,WAAIL,OAAO,EAAK;IAAhB,CAAgB,GAAAK,EAAA;IAAEE,EAAA,GAA4EH,OAAO,CAAAI,YAAhE;IAAnBA,YAAY,GAAAD,EAAA,cAAG,IAAI,GAAAA,EAAA;IAAEE,EAAA,GAAuDL,OAAO,CAAAM,eAAxC;IAAtBA,eAAe,GAAAD,EAAA,cAAG,IAAI,GAAAA,EAAA;IAAEE,EAAA,GAA+BP,OAAO,CAAAQ,mBAAZ;IAA1BA,mBAAmB,GAAAD,EAAA,cAAG,IAAI,GAAAA,EAAA;EAUnH,OAAO,UAACE,aAAa;IACnB,IAAIC,UAAyC;IAC7C,IAAIC,eAAyC;IAC7C,IAAIC,OAAmC;IACvC,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIC,YAAY,GAAG,KAAK;IACxB,IAAIC,UAAU,GAAG,KAAK;IAEtB,IAAMC,WAAW,GAAG,SAAAA,CAAA;MAClBL,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEM,WAAW,EAAE;MAC9BN,eAAe,GAAGO,SAAS;IAC7B,CAAC;IAGD,IAAMC,KAAK,GAAG,SAAAA,CAAA;MACZH,WAAW,EAAE;MACbN,UAAU,GAAGE,OAAO,GAAGM,SAAS;MAChCJ,YAAY,GAAGC,UAAU,GAAG,KAAK;IACnC,CAAC;IACD,IAAMK,mBAAmB,GAAG,SAAAA,CAAA;MAG1B,IAAMC,IAAI,GAAGX,UAAU;MACvBS,KAAK,EAAE;MACPE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEJ,WAAW,EAAE;IACrB,CAAC;IAED,OAAOnB,OAAO,CAAO,UAACwB,MAAM,EAAEC,UAAU;MACtCV,QAAQ,EAAE;MACV,IAAI,CAACE,UAAU,IAAI,CAACD,YAAY,EAAE;QAChCE,WAAW,EAAE;;MAOf,IAAMQ,IAAI,GAAIZ,OAAO,GAAGA,OAAO,aAAPA,OAAO,cAAPA,OAAO,GAAIV,SAAS,EAAG;MAO/CqB,UAAU,CAACE,GAAG,CAAC;QACbZ,QAAQ,EAAE;QAKV,IAAIA,QAAQ,KAAK,CAAC,IAAI,CAACE,UAAU,IAAI,CAACD,YAAY,EAAE;UAClDH,eAAe,GAAGe,WAAW,CAACN,mBAAmB,EAAEZ,mBAAmB,CAAC;;MAE3E,CAAC,CAAC;MAIFgB,IAAI,CAACG,SAAS,CAACJ,UAAU,CAAC;MAE1B,IACE,CAACb,UAAU,IAIXG,QAAQ,GAAG,CAAC,EACZ;QAMAH,UAAU,GAAG,IAAIb,cAAc,CAAC;UAC9B+B,IAAI,EAAE,SAAAA,CAACC,KAAK;YAAK,OAAAL,IAAI,CAACI,IAAI,CAACC,KAAK,CAAC;UAAhB,CAAgB;UACjCC,KAAK,EAAE,SAAAA,CAACC,GAAG;YACThB,UAAU,GAAG,IAAI;YACjBC,WAAW,EAAE;YACbL,eAAe,GAAGe,WAAW,CAACP,KAAK,EAAEf,YAAY,EAAE2B,GAAG,CAAC;YACvDP,IAAI,CAACM,KAAK,CAACC,GAAG,CAAC;UACjB,CAAC;UACDC,QAAQ,EAAE,SAAAA,CAAA;YACRlB,YAAY,GAAG,IAAI;YACnBE,WAAW,EAAE;YACbL,eAAe,GAAGe,WAAW,CAACP,KAAK,EAAEb,eAAe,CAAC;YACrDkB,IAAI,CAACQ,QAAQ,EAAE;UACjB;SACD,CAAC;QACFrC,SAAS,CAAC2B,MAAM,CAAC,CAACK,SAAS,CAACjB,UAAU,CAAC;;IAE3C,CAAC,CAAC,CAACD,aAAa,CAAC;EACnB,CAAC;AACH;AAEA,SAASiB,WAAWA,CAClBP,KAAiB,EACjBc,EAAoD;EACpD,IAAAC,IAAA;OAAA,IAAAC,EAAA,IAAU,EAAVA,EAAA,GAAAC,SAAA,CAAAC,MAAU,EAAVF,EAAA,EAAU;IAAVD,IAAA,CAAAC,EAAA,QAAAC,SAAA,CAAAD,EAAA;;EAEA,IAAIF,EAAE,KAAK,IAAI,EAAE;IACfd,KAAK,EAAE;IACP;;EAGF,IAAIc,EAAE,KAAK,KAAK,EAAE;IAChB;;EAGF,IAAMK,YAAY,GAAG,IAAIzC,cAAc,CAAC;IACtC+B,IAAI,EAAE,SAAAA,CAAA;MACJU,YAAY,CAACrB,WAAW,EAAE;MAC1BE,KAAK,EAAE;IACT;GACD,CAAC;EAEF,OAAOxB,SAAS,CAACsC,EAAE,CAAAM,KAAA,SAAAC,aAAA,KAAAC,MAAA,CAAIP,IAAI,IAAE,CAACP,SAAS,CAACW,YAAY,CAAC;AACvD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}