{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nimport { count_occurences } from './helpers.js';\nimport close_braces from './closeBraces.js'; // Takes a `template` where character placeholders\n// are denoted by 'x'es (e.g. 'x (xxx) xxx-xx-xx').\n//\n// Returns a function which takes `value` characters\n// and returns the `template` filled with those characters.\n// If the `template` can only be partially filled\n// then it is cut off.\n//\n// If `should_close_braces` is `true`,\n// then it will also make sure all dangling braces are closed,\n// e.g. \"8 (8\" -> \"8 (8  )\" (iPhone style phone number input).\n//\n\nexport default function (template) {\n  var placeholder = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'x';\n  var should_close_braces = arguments.length > 2 ? arguments[2] : undefined;\n  if (!template) {\n    return function (value) {\n      return {\n        text: value\n      };\n    };\n  }\n  var characters_in_template = count_occurences(placeholder, template);\n  return function (value) {\n    if (!value) {\n      return {\n        text: '',\n        template: template\n      };\n    }\n    var value_character_index = 0;\n    var filled_in_template = ''; // Using `.split('')` here instead of normal `for ... of`\n    // because the importing application doesn't neccessarily include an ES6 polyfill.\n    // The `.split('')` approach discards \"exotic\" UTF-8 characters\n    // (the ones consisting of four bytes)\n    // but template placeholder characters don't fall into that range\n    // and appending UTF-8 characters to a string in parts still works.\n\n    for (var _iterator = _createForOfIteratorHelperLoose(template.split('')), _step; !(_step = _iterator()).done;) {\n      var character = _step.value;\n      if (character !== placeholder) {\n        filled_in_template += character;\n        continue;\n      }\n      filled_in_template += value[value_character_index];\n      value_character_index++; // If the last available value character has been filled in,\n      // then return the filled in template\n      // (either trim the right part or retain it,\n      //  if no more character placeholders in there)\n\n      if (value_character_index === value.length) {\n        // If there are more character placeholders\n        // in the right part of the template\n        // then simply trim it.\n        if (value.length < characters_in_template) {\n          break;\n        }\n      }\n    }\n    if (should_close_braces) {\n      filled_in_template = close_braces(filled_in_template, template);\n    }\n    return {\n      text: filled_in_template,\n      template: template\n    };\n  };\n}", "map": {"version": 3, "names": ["count_occurences", "close_braces", "template", "placeholder", "arguments", "length", "undefined", "should_close_braces", "value", "text", "characters_in_template", "value_character_index", "filled_in_template", "_iterator", "_createForOfIteratorHelperLoose", "split", "_step", "done", "character"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\input-format\\source\\templateFormatter.js"], "sourcesContent": ["import { count_occurences } from './helpers.js'\r\nimport close_braces from './closeBraces.js'\r\n\r\n// Takes a `template` where character placeholders\r\n// are denoted by 'x'es (e.g. 'x (xxx) xxx-xx-xx').\r\n//\r\n// Returns a function which takes `value` characters\r\n// and returns the `template` filled with those characters.\r\n// If the `template` can only be partially filled\r\n// then it is cut off.\r\n//\r\n// If `should_close_braces` is `true`,\r\n// then it will also make sure all dangling braces are closed,\r\n// e.g. \"8 (8\" -> \"8 (8  )\" (iPhone style phone number input).\r\n//\r\nexport default function(template, placeholder = 'x', should_close_braces)\r\n{\r\n\tif (!template)\r\n\t{\r\n\t\treturn value => ({ text: value })\r\n\t}\r\n\r\n\tconst characters_in_template = count_occurences(placeholder, template)\r\n\r\n\treturn function(value)\r\n\t{\r\n\t\tif (!value)\r\n\t\t{\r\n\t\t\treturn { text: '', template }\r\n\t\t}\r\n\r\n\t\tlet value_character_index = 0\r\n\t\tlet filled_in_template = ''\r\n\r\n\t\t// Using `.split('')` here instead of normal `for ... of`\r\n\t\t// because the importing application doesn't neccessarily include an ES6 polyfill.\r\n\t\t// The `.split('')` approach discards \"exotic\" UTF-8 characters\r\n\t\t// (the ones consisting of four bytes)\r\n\t\t// but template placeholder characters don't fall into that range\r\n\t\t// and appending UTF-8 characters to a string in parts still works.\r\n\t\tfor (const character of template.split(''))\r\n\t\t{\r\n\t\t\tif (character !== placeholder)\r\n\t\t\t{\r\n\t\t\t\tfilled_in_template += character\r\n\t\t\t\tcontinue\r\n\t\t\t}\r\n\r\n\t\t\tfilled_in_template += value[value_character_index]\r\n\t\t\tvalue_character_index++\r\n\r\n\t\t\t// If the last available value character has been filled in,\r\n\t\t\t// then return the filled in template\r\n\t\t\t// (either trim the right part or retain it,\r\n\t\t\t//  if no more character placeholders in there)\r\n\t\t\tif (value_character_index === value.length)\r\n\t\t\t{\r\n\t\t\t\t// If there are more character placeholders\r\n\t\t\t\t// in the right part of the template\r\n\t\t\t\t// then simply trim it.\r\n\t\t\t\tif (value.length < characters_in_template)\r\n\t\t\t\t{\r\n\t\t\t\t\tbreak\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (should_close_braces)\r\n\t\t{\r\n\t\t\tfilled_in_template = close_braces(filled_in_template, template)\r\n\t\t}\r\n\r\n\t\treturn { text: filled_in_template, template }\r\n\t}\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,gBAAT,QAAiC,cAAjC;AACA,OAAOC,YAAP,MAAyB,kBAAzB,C,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,UAASC,QAAT,EACf;EAAA,IADkCC,WAClC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MADgD,GAChD;EAAA,IADqDG,mBACrD,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACC,IAAI,CAACJ,QAAL,EACA;IACC,OAAO,UAAAM,KAAK;MAAA,OAAK;QAAEC,IAAI,EAAED;MAAR,CAAL;IAAA,CAAZ;EACA;EAED,IAAME,sBAAsB,GAAGV,gBAAgB,CAACG,WAAD,EAAcD,QAAd,CAA/C;EAEA,OAAO,UAASM,KAAT,EACP;IACC,IAAI,CAACA,KAAL,EACA;MACC,OAAO;QAAEC,IAAI,EAAE,EAAR;QAAYP,QAAQ,EAARA;MAAZ,CAAP;IACA;IAED,IAAIS,qBAAqB,GAAG,CAA5B;IACA,IAAIC,kBAAkB,GAAG,EAAzB,CAPD,CASC;IACA;IACA;IACA;IACA;IACA;;IACA,SAAAC,SAAA,GAAAC,+BAAA,CAAwBZ,QAAQ,CAACa,KAAT,CAAe,EAAf,CAAxB,GAAAC,KAAA,IAAAA,KAAA,GAAAH,SAAA,IAAAI,IAAA,GACA;MAAA,IADWC,SACX,GAAAF,KAAA,CAAAR,KAAA;MACC,IAAIU,SAAS,KAAKf,WAAlB,EACA;QACCS,kBAAkB,IAAIM,SAAtB;QACA;MACA;MAEDN,kBAAkB,IAAIJ,KAAK,CAACG,qBAAD,CAA3B;MACAA,qBAAqB,GARtB,CAUC;MACA;MACA;MACA;;MACA,IAAIA,qBAAqB,KAAKH,KAAK,CAACH,MAApC,EACA;QACC;QACA;QACA;QACA,IAAIG,KAAK,CAACH,MAAN,GAAeK,sBAAnB,EACA;UACC;QACA;MACD;IACD;IAED,IAAIH,mBAAJ,EACA;MACCK,kBAAkB,GAAGX,YAAY,CAACW,kBAAD,EAAqBV,QAArB,CAAjC;IACA;IAED,OAAO;MAAEO,IAAI,EAAEG,kBAAR;MAA4BV,QAAQ,EAARA;IAA5B,CAAP;EACA,CAjDD;AAkDA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}