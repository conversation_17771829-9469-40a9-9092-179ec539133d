{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument, unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { isFragment } from 'react-is';\nimport { useTabContext } from '../TabsUnstyled';\nimport extractEventHandlers from '../utils/extractEventHandlers';\nconst nextItem = (list, item) => {\n  if (!list) {\n    return null;\n  }\n  if (list === item) {\n    return list.firstChild;\n  }\n  if (item && item.nextElementSibling) {\n    return item.nextElementSibling;\n  }\n  return list.firstChild;\n};\nconst previousItem = (list, item) => {\n  if (!list) {\n    return null;\n  }\n  if (list === item) {\n    return list.lastChild;\n  }\n  if (item && item.previousElementSibling) {\n    return item.previousElementSibling;\n  }\n  return list.lastChild;\n};\nconst moveFocus = (list, currentFocus, traversalFunction) => {\n  let wrappedOnce = false;\n  let nextFocus = traversalFunction(list, currentFocus);\n  while (list && nextFocus) {\n    // Prevent infinite loop.\n    if (nextFocus === list.firstChild) {\n      if (wrappedOnce) {\n        return;\n      }\n      wrappedOnce = true;\n    } // Same logic as useAutocomplete.js\n\n    const nextFocusDisabled = nextFocus.disabled || nextFocus.getAttribute('aria-disabled') === 'true';\n    if (!nextFocus.hasAttribute('tabindex') || nextFocusDisabled) {\n      // Move to the next element.\n      nextFocus = traversalFunction(list, nextFocus);\n    } else {\n      nextFocus.focus();\n      return;\n    }\n  }\n};\nconst useTabsList = parameters => {\n  const {\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledBy,\n    children,\n    ref\n  } = parameters;\n  const tabsListRef = /*#__PURE__*/React.createRef();\n  const handleRef = useForkRef(tabsListRef, ref);\n  const context = useTabContext();\n  if (context === null) {\n    throw new Error('No TabContext provided');\n  }\n  const {\n    value,\n    orientation = 'horizontal',\n    direction = 'ltr'\n  } = context;\n  const isRtl = direction === 'rtl';\n  const handleKeyDown = event => {\n    const list = tabsListRef.current;\n    const currentFocus = ownerDocument(list).activeElement; // Keyboard navigation assumes that [role=\"tab\"] are siblings\n    // though we might warn in the future about nested, interactive elements\n    // as a a11y violation\n\n    const role = currentFocus == null ? void 0 : currentFocus.getAttribute('role');\n    if (role !== 'tab') {\n      return;\n    }\n    let previousItemKey = orientation === 'horizontal' ? 'ArrowLeft' : 'ArrowUp';\n    let nextItemKey = orientation === 'horizontal' ? 'ArrowRight' : 'ArrowDown';\n    if (orientation === 'horizontal' && isRtl) {\n      // swap previousItemKey with nextItemKey\n      previousItemKey = 'ArrowRight';\n      nextItemKey = 'ArrowLeft';\n    }\n    switch (event.key) {\n      case previousItemKey:\n        event.preventDefault();\n        moveFocus(list, currentFocus, previousItem);\n        break;\n      case nextItemKey:\n        event.preventDefault();\n        moveFocus(list, currentFocus, nextItem);\n        break;\n      case 'Home':\n        event.preventDefault();\n        moveFocus(list, null, nextItem);\n        break;\n      case 'End':\n        event.preventDefault();\n        moveFocus(list, null, previousItem);\n        break;\n      default:\n        break;\n    }\n  };\n  const createHandleKeyDown = otherHandlers => event => {\n    var _otherHandlers$onKeyD;\n    handleKeyDown(event);\n    (_otherHandlers$onKeyD = otherHandlers.onKeyDown) == null ? void 0 : _otherHandlers$onKeyD.call(otherHandlers, event);\n  };\n  const getRootProps = (otherHandlers = {}) => {\n    const propsEventHandlers = extractEventHandlers(parameters);\n    const externalEventHandlers = _extends({}, propsEventHandlers, otherHandlers);\n    const ownEventHandlers = {\n      onKeyDown: createHandleKeyDown(externalEventHandlers)\n    };\n    const mergedEventHandlers = _extends({}, externalEventHandlers, ownEventHandlers);\n    return _extends({\n      'aria-label': ariaLabel,\n      'aria-labelledby': ariaLabelledBy,\n      'aria-orientation': orientation === 'vertical' ? 'vertical' : undefined,\n      role: 'tablist',\n      ref: handleRef\n    }, mergedEventHandlers);\n  };\n  const processChildren = React.useCallback(() => {\n    const valueToIndex = new Map();\n    let childIndex = 0;\n    const processedChildren = React.Children.map(children, child => {\n      if (! /*#__PURE__*/React.isValidElement(child)) {\n        return null;\n      }\n      if (process.env.NODE_ENV !== 'production') {\n        if (isFragment(child)) {\n          console.error([\"MUI: The Tabs component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n        }\n      }\n      const childValue = child.props.value === undefined ? childIndex : child.props.value;\n      valueToIndex.set(childValue, childIndex);\n      childIndex += 1;\n      return /*#__PURE__*/React.cloneElement(child, _extends({\n        value: childValue\n      }, childIndex === 1 && value === false && !child.props.tabIndex || value === childValue ? {\n        tabIndex: 0\n      } : {\n        tabIndex: -1\n      }));\n    });\n    return processedChildren;\n  }, [children, value]);\n  return {\n    isRtl,\n    orientation,\n    value,\n    processChildren,\n    getRootProps\n  };\n};\nexport default useTabsList;", "map": {"version": 3, "names": ["_extends", "React", "unstable_ownerDocument", "ownerDocument", "unstable_useForkRef", "useForkRef", "isFragment", "useTabContext", "extractEventHandlers", "nextItem", "list", "item", "<PERSON><PERSON><PERSON><PERSON>", "nextElement<PERSON><PERSON>ling", "previousItem", "<PERSON><PERSON><PERSON><PERSON>", "previousElementSibling", "moveFocus", "currentFocus", "traversalFunction", "wrappedOnce", "nextFocus", "nextFocusDisabled", "disabled", "getAttribute", "hasAttribute", "focus", "useTabsList", "parameters", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "children", "ref", "tabsListRef", "createRef", "handleRef", "context", "Error", "value", "orientation", "direction", "isRtl", "handleKeyDown", "event", "current", "activeElement", "role", "previousItemKey", "nextItemKey", "key", "preventDefault", "createHandleKeyDown", "otherHandlers", "_otherHandlers$onKeyD", "onKeyDown", "call", "getRootProps", "propsEventHandlers", "externalEventHandlers", "ownEventHandlers", "mergedEventHandlers", "undefined", "processChildren", "useCallback", "valueToIndex", "Map", "childIndex", "processedChildren", "Children", "map", "child", "isValidElement", "process", "env", "NODE_ENV", "console", "error", "join", "childValue", "props", "set", "cloneElement", "tabIndex"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/TabsListUnstyled/useTabsList.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument, unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { isFragment } from 'react-is';\nimport { useTabContext } from '../TabsUnstyled';\nimport extractEventHandlers from '../utils/extractEventHandlers';\n\nconst nextItem = (list, item) => {\n  if (!list) {\n    return null;\n  }\n\n  if (list === item) {\n    return list.firstChild;\n  }\n\n  if (item && item.nextElementSibling) {\n    return item.nextElementSibling;\n  }\n\n  return list.firstChild;\n};\n\nconst previousItem = (list, item) => {\n  if (!list) {\n    return null;\n  }\n\n  if (list === item) {\n    return list.lastChild;\n  }\n\n  if (item && item.previousElementSibling) {\n    return item.previousElementSibling;\n  }\n\n  return list.lastChild;\n};\n\nconst moveFocus = (list, currentFocus, traversalFunction) => {\n  let wrappedOnce = false;\n  let nextFocus = traversalFunction(list, currentFocus);\n\n  while (list && nextFocus) {\n    // Prevent infinite loop.\n    if (nextFocus === list.firstChild) {\n      if (wrappedOnce) {\n        return;\n      }\n\n      wrappedOnce = true;\n    } // Same logic as useAutocomplete.js\n\n\n    const nextFocusDisabled = nextFocus.disabled || nextFocus.getAttribute('aria-disabled') === 'true';\n\n    if (!nextFocus.hasAttribute('tabindex') || nextFocusDisabled) {\n      // Move to the next element.\n      nextFocus = traversalFunction(list, nextFocus);\n    } else {\n      nextFocus.focus();\n      return;\n    }\n  }\n};\n\nconst useTabsList = parameters => {\n  const {\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledBy,\n    children,\n    ref\n  } = parameters;\n  const tabsListRef = /*#__PURE__*/React.createRef();\n  const handleRef = useForkRef(tabsListRef, ref);\n  const context = useTabContext();\n\n  if (context === null) {\n    throw new Error('No TabContext provided');\n  }\n\n  const {\n    value,\n    orientation = 'horizontal',\n    direction = 'ltr'\n  } = context;\n  const isRtl = direction === 'rtl';\n\n  const handleKeyDown = event => {\n    const list = tabsListRef.current;\n    const currentFocus = ownerDocument(list).activeElement; // Keyboard navigation assumes that [role=\"tab\"] are siblings\n    // though we might warn in the future about nested, interactive elements\n    // as a a11y violation\n\n    const role = currentFocus == null ? void 0 : currentFocus.getAttribute('role');\n\n    if (role !== 'tab') {\n      return;\n    }\n\n    let previousItemKey = orientation === 'horizontal' ? 'ArrowLeft' : 'ArrowUp';\n    let nextItemKey = orientation === 'horizontal' ? 'ArrowRight' : 'ArrowDown';\n\n    if (orientation === 'horizontal' && isRtl) {\n      // swap previousItemKey with nextItemKey\n      previousItemKey = 'ArrowRight';\n      nextItemKey = 'ArrowLeft';\n    }\n\n    switch (event.key) {\n      case previousItemKey:\n        event.preventDefault();\n        moveFocus(list, currentFocus, previousItem);\n        break;\n\n      case nextItemKey:\n        event.preventDefault();\n        moveFocus(list, currentFocus, nextItem);\n        break;\n\n      case 'Home':\n        event.preventDefault();\n        moveFocus(list, null, nextItem);\n        break;\n\n      case 'End':\n        event.preventDefault();\n        moveFocus(list, null, previousItem);\n        break;\n\n      default:\n        break;\n    }\n  };\n\n  const createHandleKeyDown = otherHandlers => event => {\n    var _otherHandlers$onKeyD;\n\n    handleKeyDown(event);\n    (_otherHandlers$onKeyD = otherHandlers.onKeyDown) == null ? void 0 : _otherHandlers$onKeyD.call(otherHandlers, event);\n  };\n\n  const getRootProps = (otherHandlers = {}) => {\n    const propsEventHandlers = extractEventHandlers(parameters);\n\n    const externalEventHandlers = _extends({}, propsEventHandlers, otherHandlers);\n\n    const ownEventHandlers = {\n      onKeyDown: createHandleKeyDown(externalEventHandlers)\n    };\n\n    const mergedEventHandlers = _extends({}, externalEventHandlers, ownEventHandlers);\n\n    return _extends({\n      'aria-label': ariaLabel,\n      'aria-labelledby': ariaLabelledBy,\n      'aria-orientation': orientation === 'vertical' ? 'vertical' : undefined,\n      role: 'tablist',\n      ref: handleRef\n    }, mergedEventHandlers);\n  };\n\n  const processChildren = React.useCallback(() => {\n    const valueToIndex = new Map();\n    let childIndex = 0;\n    const processedChildren = React.Children.map(children, child => {\n      if (! /*#__PURE__*/React.isValidElement(child)) {\n        return null;\n      }\n\n      if (process.env.NODE_ENV !== 'production') {\n        if (isFragment(child)) {\n          console.error([\"MUI: The Tabs component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n        }\n      }\n\n      const childValue = child.props.value === undefined ? childIndex : child.props.value;\n      valueToIndex.set(childValue, childIndex);\n      childIndex += 1;\n      return /*#__PURE__*/React.cloneElement(child, _extends({\n        value: childValue\n      }, childIndex === 1 && value === false && !child.props.tabIndex || value === childValue ? {\n        tabIndex: 0\n      } : {\n        tabIndex: -1\n      }));\n    });\n    return processedChildren;\n  }, [children, value]);\n  return {\n    isRtl,\n    orientation,\n    value,\n    processChildren,\n    getRootProps\n  };\n};\n\nexport default useTabsList;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,sBAAsB,IAAIC,aAAa,EAAEC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AACvG,SAASC,UAAU,QAAQ,UAAU;AACrC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,OAAOC,oBAAoB,MAAM,+BAA+B;AAEhE,MAAMC,QAAQ,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;EAC/B,IAAI,CAACD,IAAI,EAAE;IACT,OAAO,IAAI;EACb;EAEA,IAAIA,IAAI,KAAKC,IAAI,EAAE;IACjB,OAAOD,IAAI,CAACE,UAAU;EACxB;EAEA,IAAID,IAAI,IAAIA,IAAI,CAACE,kBAAkB,EAAE;IACnC,OAAOF,IAAI,CAACE,kBAAkB;EAChC;EAEA,OAAOH,IAAI,CAACE,UAAU;AACxB,CAAC;AAED,MAAME,YAAY,GAAGA,CAACJ,IAAI,EAAEC,IAAI,KAAK;EACnC,IAAI,CAACD,IAAI,EAAE;IACT,OAAO,IAAI;EACb;EAEA,IAAIA,IAAI,KAAKC,IAAI,EAAE;IACjB,OAAOD,IAAI,CAACK,SAAS;EACvB;EAEA,IAAIJ,IAAI,IAAIA,IAAI,CAACK,sBAAsB,EAAE;IACvC,OAAOL,IAAI,CAACK,sBAAsB;EACpC;EAEA,OAAON,IAAI,CAACK,SAAS;AACvB,CAAC;AAED,MAAME,SAAS,GAAGA,CAACP,IAAI,EAAEQ,YAAY,EAAEC,iBAAiB,KAAK;EAC3D,IAAIC,WAAW,GAAG,KAAK;EACvB,IAAIC,SAAS,GAAGF,iBAAiB,CAACT,IAAI,EAAEQ,YAAY,CAAC;EAErD,OAAOR,IAAI,IAAIW,SAAS,EAAE;IACxB;IACA,IAAIA,SAAS,KAAKX,IAAI,CAACE,UAAU,EAAE;MACjC,IAAIQ,WAAW,EAAE;QACf;MACF;MAEAA,WAAW,GAAG,IAAI;IACpB,CAAC,CAAC;;IAGF,MAAME,iBAAiB,GAAGD,SAAS,CAACE,QAAQ,IAAIF,SAAS,CAACG,YAAY,CAAC,eAAe,CAAC,KAAK,MAAM;IAElG,IAAI,CAACH,SAAS,CAACI,YAAY,CAAC,UAAU,CAAC,IAAIH,iBAAiB,EAAE;MAC5D;MACAD,SAAS,GAAGF,iBAAiB,CAACT,IAAI,EAAEW,SAAS,CAAC;IAChD,CAAC,MAAM;MACLA,SAAS,CAACK,KAAK,CAAC,CAAC;MACjB;IACF;EACF;AACF,CAAC;AAED,MAAMC,WAAW,GAAGC,UAAU,IAAI;EAChC,MAAM;IACJ,YAAY,EAAEC,SAAS;IACvB,iBAAiB,EAAEC,cAAc;IACjCC,QAAQ;IACRC;EACF,CAAC,GAAGJ,UAAU;EACd,MAAMK,WAAW,GAAG,aAAahC,KAAK,CAACiC,SAAS,CAAC,CAAC;EAClD,MAAMC,SAAS,GAAG9B,UAAU,CAAC4B,WAAW,EAAED,GAAG,CAAC;EAC9C,MAAMI,OAAO,GAAG7B,aAAa,CAAC,CAAC;EAE/B,IAAI6B,OAAO,KAAK,IAAI,EAAE;IACpB,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;EAC3C;EAEA,MAAM;IACJC,KAAK;IACLC,WAAW,GAAG,YAAY;IAC1BC,SAAS,GAAG;EACd,CAAC,GAAGJ,OAAO;EACX,MAAMK,KAAK,GAAGD,SAAS,KAAK,KAAK;EAEjC,MAAME,aAAa,GAAGC,KAAK,IAAI;IAC7B,MAAMjC,IAAI,GAAGuB,WAAW,CAACW,OAAO;IAChC,MAAM1B,YAAY,GAAGf,aAAa,CAACO,IAAI,CAAC,CAACmC,aAAa,CAAC,CAAC;IACxD;IACA;;IAEA,MAAMC,IAAI,GAAG5B,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACM,YAAY,CAAC,MAAM,CAAC;IAE9E,IAAIsB,IAAI,KAAK,KAAK,EAAE;MAClB;IACF;IAEA,IAAIC,eAAe,GAAGR,WAAW,KAAK,YAAY,GAAG,WAAW,GAAG,SAAS;IAC5E,IAAIS,WAAW,GAAGT,WAAW,KAAK,YAAY,GAAG,YAAY,GAAG,WAAW;IAE3E,IAAIA,WAAW,KAAK,YAAY,IAAIE,KAAK,EAAE;MACzC;MACAM,eAAe,GAAG,YAAY;MAC9BC,WAAW,GAAG,WAAW;IAC3B;IAEA,QAAQL,KAAK,CAACM,GAAG;MACf,KAAKF,eAAe;QAClBJ,KAAK,CAACO,cAAc,CAAC,CAAC;QACtBjC,SAAS,CAACP,IAAI,EAAEQ,YAAY,EAAEJ,YAAY,CAAC;QAC3C;MAEF,KAAKkC,WAAW;QACdL,KAAK,CAACO,cAAc,CAAC,CAAC;QACtBjC,SAAS,CAACP,IAAI,EAAEQ,YAAY,EAAET,QAAQ,CAAC;QACvC;MAEF,KAAK,MAAM;QACTkC,KAAK,CAACO,cAAc,CAAC,CAAC;QACtBjC,SAAS,CAACP,IAAI,EAAE,IAAI,EAAED,QAAQ,CAAC;QAC/B;MAEF,KAAK,KAAK;QACRkC,KAAK,CAACO,cAAc,CAAC,CAAC;QACtBjC,SAAS,CAACP,IAAI,EAAE,IAAI,EAAEI,YAAY,CAAC;QACnC;MAEF;QACE;IACJ;EACF,CAAC;EAED,MAAMqC,mBAAmB,GAAGC,aAAa,IAAIT,KAAK,IAAI;IACpD,IAAIU,qBAAqB;IAEzBX,aAAa,CAACC,KAAK,CAAC;IACpB,CAACU,qBAAqB,GAAGD,aAAa,CAACE,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,qBAAqB,CAACE,IAAI,CAACH,aAAa,EAAET,KAAK,CAAC;EACvH,CAAC;EAED,MAAMa,YAAY,GAAGA,CAACJ,aAAa,GAAG,CAAC,CAAC,KAAK;IAC3C,MAAMK,kBAAkB,GAAGjD,oBAAoB,CAACoB,UAAU,CAAC;IAE3D,MAAM8B,qBAAqB,GAAG1D,QAAQ,CAAC,CAAC,CAAC,EAAEyD,kBAAkB,EAAEL,aAAa,CAAC;IAE7E,MAAMO,gBAAgB,GAAG;MACvBL,SAAS,EAAEH,mBAAmB,CAACO,qBAAqB;IACtD,CAAC;IAED,MAAME,mBAAmB,GAAG5D,QAAQ,CAAC,CAAC,CAAC,EAAE0D,qBAAqB,EAAEC,gBAAgB,CAAC;IAEjF,OAAO3D,QAAQ,CAAC;MACd,YAAY,EAAE6B,SAAS;MACvB,iBAAiB,EAAEC,cAAc;MACjC,kBAAkB,EAAES,WAAW,KAAK,UAAU,GAAG,UAAU,GAAGsB,SAAS;MACvEf,IAAI,EAAE,SAAS;MACfd,GAAG,EAAEG;IACP,CAAC,EAAEyB,mBAAmB,CAAC;EACzB,CAAC;EAED,MAAME,eAAe,GAAG7D,KAAK,CAAC8D,WAAW,CAAC,MAAM;IAC9C,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC9B,IAAIC,UAAU,GAAG,CAAC;IAClB,MAAMC,iBAAiB,GAAGlE,KAAK,CAACmE,QAAQ,CAACC,GAAG,CAACtC,QAAQ,EAAEuC,KAAK,IAAI;MAC9D,IAAI,EAAE,aAAarE,KAAK,CAACsE,cAAc,CAACD,KAAK,CAAC,EAAE;QAC9C,OAAO,IAAI;MACb;MAEA,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAIpE,UAAU,CAACgE,KAAK,CAAC,EAAE;UACrBK,OAAO,CAACC,KAAK,CAAC,CAAC,+DAA+D,EAAE,sCAAsC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrI;MACF;MAEA,MAAMC,UAAU,GAAGR,KAAK,CAACS,KAAK,CAACzC,KAAK,KAAKuB,SAAS,GAAGK,UAAU,GAAGI,KAAK,CAACS,KAAK,CAACzC,KAAK;MACnF0B,YAAY,CAACgB,GAAG,CAACF,UAAU,EAAEZ,UAAU,CAAC;MACxCA,UAAU,IAAI,CAAC;MACf,OAAO,aAAajE,KAAK,CAACgF,YAAY,CAACX,KAAK,EAAEtE,QAAQ,CAAC;QACrDsC,KAAK,EAAEwC;MACT,CAAC,EAAEZ,UAAU,KAAK,CAAC,IAAI5B,KAAK,KAAK,KAAK,IAAI,CAACgC,KAAK,CAACS,KAAK,CAACG,QAAQ,IAAI5C,KAAK,KAAKwC,UAAU,GAAG;QACxFI,QAAQ,EAAE;MACZ,CAAC,GAAG;QACFA,QAAQ,EAAE,CAAC;MACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IACF,OAAOf,iBAAiB;EAC1B,CAAC,EAAE,CAACpC,QAAQ,EAAEO,KAAK,CAAC,CAAC;EACrB,OAAO;IACLG,KAAK;IACLF,WAAW;IACXD,KAAK;IACLwB,eAAe;IACfN;EACF,CAAC;AACH,CAAC;AAED,eAAe7B,WAAW"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}