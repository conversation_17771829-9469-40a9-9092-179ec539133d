{"ast": null, "code": "function _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\n// https://medium.com/dsinjs/implementing-lru-cache-in-javascript-94ba6755cda9\nvar Node = /*#__PURE__*/_createClass(function Node(key, value) {\n  var next = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : null;\n  var prev = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n  _classCallCheck(this, Node);\n  this.key = key;\n  this.value = value;\n  this.next = next;\n  this.prev = prev;\n});\nvar LRUCache = /*#__PURE__*/function () {\n  //set default limit of 10 if limit is not passed.\n  function LRUCache() {\n    var limit = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 10;\n    _classCallCheck(this, LRUCache);\n    this.size = 0;\n    this.limit = limit;\n    this.head = null;\n    this.tail = null;\n    this.cache = {};\n  } // Write Node to head of LinkedList\n  // update cache with Node key and Node reference\n\n  _createClass(LRUCache, [{\n    key: \"put\",\n    value: function put(key, value) {\n      this.ensureLimit();\n      if (!this.head) {\n        this.head = this.tail = new Node(key, value);\n      } else {\n        var node = new Node(key, value, this.head);\n        this.head.prev = node;\n        this.head = node;\n      } //Update the cache map\n\n      this.cache[key] = this.head;\n      this.size++;\n    } // Read from cache map and make that node as new Head of LinkedList\n  }, {\n    key: \"get\",\n    value: function get(key) {\n      if (this.cache[key]) {\n        var value = this.cache[key].value; // node removed from it's position and cache\n\n        this.remove(key); // write node again to the head of LinkedList to make it most recently used\n\n        this.put(key, value);\n        return value;\n      }\n      console.log(\"Item not available in cache for key \".concat(key));\n    }\n  }, {\n    key: \"ensureLimit\",\n    value: function ensureLimit() {\n      if (this.size === this.limit) {\n        this.remove(this.tail.key);\n      }\n    }\n  }, {\n    key: \"remove\",\n    value: function remove(key) {\n      var node = this.cache[key];\n      if (node.prev !== null) {\n        node.prev.next = node.next;\n      } else {\n        this.head = node.next;\n      }\n      if (node.next !== null) {\n        node.next.prev = node.prev;\n      } else {\n        this.tail = node.prev;\n      }\n      delete this.cache[key];\n      this.size--;\n    }\n  }, {\n    key: \"clear\",\n    value: function clear() {\n      this.head = null;\n      this.tail = null;\n      this.size = 0;\n      this.cache = {};\n    } // // Invokes the callback function with every node of the chain and the index of the node.\n    // forEach(fn) {\n    //   let node = this.head;\n    //   let counter = 0;\n    //   while (node) {\n    //     fn(node, counter);\n    //     node = node.next;\n    //     counter++;\n    //   }\n    // }\n    // // To iterate over LRU with a 'for...of' loop\n    // *[Symbol.iterator]() {\n    //   let node = this.head;\n    //   while (node) {\n    //     yield node;\n    //     node = node.next;\n    //   }\n    // }\n  }]);\n\n  return LRUCache;\n}();\nexport { LRUCache as default };", "map": {"version": 3, "names": ["Node", "key", "value", "next", "arguments", "length", "undefined", "prev", "_classCallCheck", "L<PERSON><PERSON><PERSON>", "limit", "size", "head", "tail", "cache", "put", "ensureLimit", "node", "get", "remove", "console", "log", "concat", "clear"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\findNumbers\\LRUCache.js"], "sourcesContent": ["// https://medium.com/dsinjs/implementing-lru-cache-in-javascript-94ba6755cda9\r\n\r\nclass Node {\r\n  constructor(key, value, next = null, prev = null) {\r\n    this.key = key;\r\n    this.value = value;\r\n    this.next = next;\r\n    this.prev = prev;\r\n  }\r\n}\r\n\r\nexport default class LRUCache {\r\n  //set default limit of 10 if limit is not passed.\r\n  constructor(limit = 10) {\r\n    this.size = 0;\r\n    this.limit = limit;\r\n    this.head = null;\r\n    this.tail = null;\r\n    this.cache = {};\r\n  }\r\n\r\n  // Write Node to head of LinkedList\r\n  // update cache with Node key and Node reference\r\n  put(key, value){\r\n    this.ensureLimit();\r\n\r\n    if(!this.head){\r\n      this.head = this.tail = new Node(key, value);\r\n    }else{\r\n      const node = new Node(key, value, this.head);\r\n      this.head.prev = node;\r\n      this.head = node;\r\n    }\r\n\r\n    //Update the cache map\r\n    this.cache[key] = this.head;\r\n    this.size++;\r\n  }\r\n\r\n  // Read from cache map and make that node as new Head of LinkedList\r\n  get(key){\r\n    if(this.cache[key]){\r\n      const value = this.cache[key].value;\r\n\r\n      // node removed from it's position and cache\r\n      this.remove(key)\r\n      // write node again to the head of LinkedList to make it most recently used\r\n      this.put(key, value);\r\n\r\n      return value;\r\n    }\r\n\r\n    console.log(`Item not available in cache for key ${key}`);\r\n  }\r\n\r\n  ensureLimit(){\r\n    if(this.size === this.limit){\r\n      this.remove(this.tail.key)\r\n    }\r\n  }\r\n\r\n  remove(key){\r\n    const node = this.cache[key];\r\n\r\n    if(node.prev !== null){\r\n      node.prev.next = node.next;\r\n    }else{\r\n      this.head = node.next;\r\n    }\r\n\r\n    if(node.next !== null){\r\n      node.next.prev = node.prev;\r\n    }else{\r\n      this.tail = node.prev\r\n    }\r\n\r\n    delete this.cache[key];\r\n    this.size--;\r\n  }\r\n\r\n  clear() {\r\n    this.head = null;\r\n    this.tail = null;\r\n    this.size = 0;\r\n    this.cache = {};\r\n  }\r\n\r\n  // // Invokes the callback function with every node of the chain and the index of the node.\r\n  // forEach(fn) {\r\n  //   let node = this.head;\r\n  //   let counter = 0;\r\n  //   while (node) {\r\n  //     fn(node, counter);\r\n  //     node = node.next;\r\n  //     counter++;\r\n  //   }\r\n  // }\r\n\r\n  // // To iterate over LRU with a 'for...of' loop\r\n  // *[Symbol.iterator]() {\r\n  //   let node = this.head;\r\n  //   while (node) {\r\n  //     yield node;\r\n  //     node = node.next;\r\n  //   }\r\n  // }\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;IAEMA,I,6BACJ,SAAAA,KAAYC,GAAZ,EAAiBC,KAAjB,EAAkD;EAAA,IAA1BC,IAA0B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAnB,IAAmB;EAAA,IAAbG,IAAa,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAN,IAAM;EAAAI,eAAA,OAAAR,IAAA;EAChD,KAAKC,GAAL,GAAWA,GAAX;EACA,KAAKC,KAAL,GAAaA,KAAb;EACA,KAAKC,IAAL,GAAYA,IAAZ;EACA,KAAKI,IAAL,GAAYA,IAAZ;AACD,C;IAGkBE,Q;EACnB;EACA,SAAAA,SAAA,EAAwB;IAAA,IAAZC,KAAY,GAAAN,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAJ,EAAI;IAAAI,eAAA,OAAAC,QAAA;IACtB,KAAKE,IAAL,GAAY,CAAZ;IACA,KAAKD,KAAL,GAAaA,KAAb;IACA,KAAKE,IAAL,GAAY,IAAZ;IACA,KAAKC,IAAL,GAAY,IAAZ;IACA,KAAKC,KAAL,GAAa,EAAb;EACD,C,CAED;EACA;;;;WACA,SAAAC,IAAId,GAAJ,EAASC,KAAT,EAAe;MACb,KAAKc,WAAL;MAEA,IAAG,CAAC,KAAKJ,IAAT,EAAc;QACZ,KAAKA,IAAL,GAAY,KAAKC,IAAL,GAAY,IAAIb,IAAJ,CAASC,GAAT,EAAcC,KAAd,CAAxB;MACD,CAFD,MAEK;QACH,IAAMe,IAAI,GAAG,IAAIjB,IAAJ,CAASC,GAAT,EAAcC,KAAd,EAAqB,KAAKU,IAA1B,CAAb;QACA,KAAKA,IAAL,CAAUL,IAAV,GAAiBU,IAAjB;QACA,KAAKL,IAAL,GAAYK,IAAZ;MACD,CATY,CAWb;;MACA,KAAKH,KAAL,CAAWb,GAAX,IAAkB,KAAKW,IAAvB;MACA,KAAKD,IAAL;IACD,C,CAED;;;WACA,SAAAO,IAAIjB,GAAJ,EAAQ;MACN,IAAG,KAAKa,KAAL,CAAWb,GAAX,CAAH,EAAmB;QACjB,IAAMC,KAAK,GAAG,KAAKY,KAAL,CAAWb,GAAX,EAAgBC,KAA9B,CADiB,CAGjB;;QACA,KAAKiB,MAAL,CAAYlB,GAAZ,EAJiB,CAKjB;;QACA,KAAKc,GAAL,CAASd,GAAT,EAAcC,KAAd;QAEA,OAAOA,KAAP;MACD;MAEDkB,OAAO,CAACC,GAAR,wCAAAC,MAAA,CAAmDrB,GAAnD;IACD;;;WAED,SAAAe,YAAA,EAAa;MACX,IAAG,KAAKL,IAAL,KAAc,KAAKD,KAAtB,EAA4B;QAC1B,KAAKS,MAAL,CAAY,KAAKN,IAAL,CAAUZ,GAAtB;MACD;IACF;;;WAED,SAAAkB,OAAOlB,GAAP,EAAW;MACT,IAAMgB,IAAI,GAAG,KAAKH,KAAL,CAAWb,GAAX,CAAb;MAEA,IAAGgB,IAAI,CAACV,IAAL,KAAc,IAAjB,EAAsB;QACpBU,IAAI,CAACV,IAAL,CAAUJ,IAAV,GAAiBc,IAAI,CAACd,IAAtB;MACD,CAFD,MAEK;QACH,KAAKS,IAAL,GAAYK,IAAI,CAACd,IAAjB;MACD;MAED,IAAGc,IAAI,CAACd,IAAL,KAAc,IAAjB,EAAsB;QACpBc,IAAI,CAACd,IAAL,CAAUI,IAAV,GAAiBU,IAAI,CAACV,IAAtB;MACD,CAFD,MAEK;QACH,KAAKM,IAAL,GAAYI,IAAI,CAACV,IAAjB;MACD;MAED,OAAO,KAAKO,KAAL,CAAWb,GAAX,CAAP;MACA,KAAKU,IAAL;IACD;;;WAED,SAAAY,MAAA,EAAQ;MACN,KAAKX,IAAL,GAAY,IAAZ;MACA,KAAKC,IAAL,GAAY,IAAZ;MACA,KAAKF,IAAL,GAAY,CAAZ;MACA,KAAKG,KAAL,GAAa,EAAb;IACD,C,CAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;;;SA9FmBL,Q"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}