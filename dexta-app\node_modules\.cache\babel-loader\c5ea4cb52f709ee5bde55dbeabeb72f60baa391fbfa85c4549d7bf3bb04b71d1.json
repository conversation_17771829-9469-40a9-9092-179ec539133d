{"ast": null, "code": "import checkNumberLength from './helpers/checkNumberLength.js';\nimport parseDigits from './helpers/parseDigits.js';\nimport formatNationalNumberUsingFormat from './helpers/formatNationalNumberUsingFormat.js';\nexport default function formatCompleteNumber(state, format, _ref) {\n  var metadata = _ref.metadata,\n    shouldTryNationalPrefixFormattingRule = _ref.shouldTryNationalPrefixFormattingRule,\n    getSeparatorAfterNationalPrefix = _ref.getSeparatorAfterNationalPrefix;\n  var matcher = new RegExp(\"^(?:\".concat(format.pattern(), \")$\"));\n  if (matcher.test(state.nationalSignificantNumber)) {\n    return formatNationalNumberWithAndWithoutNationalPrefixFormattingRule(state, format, {\n      metadata: metadata,\n      shouldTryNationalPrefixFormattingRule: shouldTryNationalPrefixFormattingRule,\n      getSeparatorAfterNationalPrefix: getSeparatorAfterNationalPrefix\n    });\n  }\n}\nexport function canFormatCompleteNumber(nationalSignificantNumber, metadata) {\n  return checkNumberLength(nationalSignificantNumber, metadata) === 'IS_POSSIBLE';\n}\nfunction formatNationalNumberWithAndWithoutNationalPrefixFormattingRule(state, format, _ref2) {\n  var metadata = _ref2.metadata,\n    shouldTryNationalPrefixFormattingRule = _ref2.shouldTryNationalPrefixFormattingRule,\n    getSeparatorAfterNationalPrefix = _ref2.getSeparatorAfterNationalPrefix;\n  // `format` has already been checked for `nationalPrefix` requirement.\n  var nationalSignificantNumber = state.nationalSignificantNumber,\n    international = state.international,\n    nationalPrefix = state.nationalPrefix,\n    carrierCode = state.carrierCode; // Format the number with using `national_prefix_formatting_rule`.\n  // If the resulting formatted number is a valid formatted number, then return it.\n  //\n  // Google's AsYouType formatter is different in a way that it doesn't try\n  // to format using the \"national prefix formatting rule\", and instead it\n  // simply prepends a national prefix followed by a \" \" character.\n  // This code does that too, but as a fallback.\n  // The reason is that \"national prefix formatting rule\" may use parentheses,\n  // which wouldn't be included has it used the simpler Google's way.\n  //\n\n  if (shouldTryNationalPrefixFormattingRule(format)) {\n    var formattedNumber = formatNationalNumber(state, format, {\n      useNationalPrefixFormattingRule: true,\n      getSeparatorAfterNationalPrefix: getSeparatorAfterNationalPrefix,\n      metadata: metadata\n    });\n    if (formattedNumber) {\n      return formattedNumber;\n    }\n  } // Format the number without using `national_prefix_formatting_rule`.\n\n  return formatNationalNumber(state, format, {\n    useNationalPrefixFormattingRule: false,\n    getSeparatorAfterNationalPrefix: getSeparatorAfterNationalPrefix,\n    metadata: metadata\n  });\n}\nfunction formatNationalNumber(state, format, _ref3) {\n  var metadata = _ref3.metadata,\n    useNationalPrefixFormattingRule = _ref3.useNationalPrefixFormattingRule,\n    getSeparatorAfterNationalPrefix = _ref3.getSeparatorAfterNationalPrefix;\n  var formattedNationalNumber = formatNationalNumberUsingFormat(state.nationalSignificantNumber, format, {\n    carrierCode: state.carrierCode,\n    useInternationalFormat: state.international,\n    withNationalPrefix: useNationalPrefixFormattingRule,\n    metadata: metadata\n  });\n  if (!useNationalPrefixFormattingRule) {\n    if (state.nationalPrefix) {\n      // If a national prefix was extracted, then just prepend it,\n      // followed by a \" \" character.\n      formattedNationalNumber = state.nationalPrefix + getSeparatorAfterNationalPrefix(format) + formattedNationalNumber;\n    } else if (state.complexPrefixBeforeNationalSignificantNumber) {\n      formattedNationalNumber = state.complexPrefixBeforeNationalSignificantNumber + ' ' + formattedNationalNumber;\n    }\n  }\n  if (isValidFormattedNationalNumber(formattedNationalNumber, state)) {\n    return formattedNationalNumber;\n  }\n} // Check that the formatted phone number contains exactly\n// the same digits that have been input by the user.\n// For example, when \"0111523456789\" is input for `AR` country,\n// the extracted `this.nationalSignificantNumber` is \"91123456789\",\n// which means that the national part of `this.digits` isn't simply equal to\n// `this.nationalPrefix` + `this.nationalSignificantNumber`.\n//\n// Also, a `format` can add extra digits to the `this.nationalSignificantNumber`\n// being formatted via `metadata[country].national_prefix_transform_rule`.\n// For example, for `VI` country, it prepends `340` to the national number,\n// and if this check hasn't been implemented, then there would be a bug\n// when `340` \"area coude\" is \"duplicated\" during input for `VI` country:\n// https://github.com/catamphetamine/libphonenumber-js/issues/318\n//\n// So, all these \"gotchas\" are filtered out.\n//\n// In the original Google's code, the comments say:\n// \"Check that we didn't remove nor add any extra digits when we matched\n// this formatting pattern. This usually happens after we entered the last\n// digit during AYTF. Eg: In case of MX, we swallow mobile token (1) when\n// formatted but AYTF should retain all the number entered and not change\n// in order to match a format (of same leading digits and length) display\n// in that way.\"\n// \"If it's the same (i.e entered number and format is same), then it's\n// safe to return this in formatted number as nothing is lost / added.\"\n// Otherwise, don't use this format.\n// https://github.com/google/libphonenumber/commit/3e7c1f04f5e7200f87fb131e6f85c6e99d60f510#diff-9149457fa9f5d608a11bb975c6ef4bc5\n// https://github.com/google/libphonenumber/commit/3ac88c7106e7dcb553bcc794b15f19185928a1c6#diff-2dcb77e833422ee304da348b905cde0b\n//\n\nfunction isValidFormattedNationalNumber(formattedNationalNumber, state) {\n  return parseDigits(formattedNationalNumber) === state.getNationalDigits();\n}", "map": {"version": 3, "names": ["checkNumberLength", "parseDigits", "formatNationalNumberUsingFormat", "formatCompleteNumber", "state", "format", "_ref", "metadata", "shouldTryNationalPrefixFormattingRule", "getSeparatorAfterNationalPrefix", "matcher", "RegExp", "concat", "pattern", "test", "nationalSignificantNumber", "formatNationalNumberWithAndWithoutNationalPrefixFormattingRule", "canFormatCompleteNumber", "_ref2", "international", "nationalPrefix", "carrierCode", "formattedNumber", "formatNationalNumber", "useNationalPrefixFormattingRule", "_ref3", "formattedNationalNumber", "useInternationalFormat", "withNationalPrefix", "complexPrefixBeforeNationalSignificantNumber", "isValidFormattedNationalNumber", "getNationalDigits"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\AsYouTypeFormatter.complete.js"], "sourcesContent": ["import checkNumberLength from './helpers/checkNumberLength.js'\r\nimport parseDigits from './helpers/parseDigits.js'\r\nimport formatNationalNumberUsingFormat from './helpers/formatNationalNumberUsingFormat.js'\r\n\r\nexport default function formatCompleteNumber(state, format, {\r\n\tmetadata,\r\n\tshouldTryNationalPrefixFormattingRule,\r\n\tgetSeparatorAfterNationalPrefix\r\n}) {\r\n\tconst matcher = new RegExp(`^(?:${format.pattern()})$`)\r\n\tif (matcher.test(state.nationalSignificantNumber)) {\r\n\t\treturn formatNationalNumberWithAndWithoutNationalPrefixFormattingRule(\r\n\t\t\tstate,\r\n\t\t\tformat,\r\n\t\t\t{\r\n\t\t\t\tmetadata,\r\n\t\t\t\tshouldTryNationalPrefixFormattingRule,\r\n\t\t\t\tgetSeparatorAfterNationalPrefix\r\n\t\t\t}\r\n\t\t)\r\n\t}\r\n}\r\n\r\nexport function canFormatCompleteNumber(nationalSignificantNumber, metadata) {\r\n\treturn checkNumberLength(nationalSignificantNumber, metadata) === 'IS_POSSIBLE'\r\n}\r\n\r\nfunction formatNationalNumberWithAndWithoutNationalPrefixFormattingRule(state, format, {\r\n\tmetadata,\r\n\tshouldTryNationalPrefixFormattingRule,\r\n\tgetSeparatorAfterNationalPrefix\r\n}) {\r\n\t// `format` has already been checked for `nationalPrefix` requirement.\r\n\r\n\tconst {\r\n\t\tnationalSignificantNumber,\r\n\t\tinternational,\r\n\t\tnationalPrefix,\r\n\t\tcarrierCode\r\n\t} = state\r\n\r\n\t// Format the number with using `national_prefix_formatting_rule`.\r\n\t// If the resulting formatted number is a valid formatted number, then return it.\r\n\t//\r\n\t// Google's AsYouType formatter is different in a way that it doesn't try\r\n\t// to format using the \"national prefix formatting rule\", and instead it\r\n\t// simply prepends a national prefix followed by a \" \" character.\r\n\t// This code does that too, but as a fallback.\r\n\t// The reason is that \"national prefix formatting rule\" may use parentheses,\r\n\t// which wouldn't be included has it used the simpler Google's way.\r\n\t//\r\n\tif (shouldTryNationalPrefixFormattingRule(format)) {\r\n\t\tconst formattedNumber = formatNationalNumber(state, format, {\r\n\t\t\tuseNationalPrefixFormattingRule: true,\r\n\t\t\tgetSeparatorAfterNationalPrefix,\r\n\t\t\tmetadata\r\n\t\t})\r\n\t\tif (formattedNumber) {\r\n\t\t\treturn formattedNumber\r\n\t\t}\r\n\t}\r\n\r\n\t// Format the number without using `national_prefix_formatting_rule`.\r\n\treturn formatNationalNumber(state, format, {\r\n\t\tuseNationalPrefixFormattingRule: false,\r\n\t\tgetSeparatorAfterNationalPrefix,\r\n\t\tmetadata\r\n\t})\r\n}\r\n\r\nfunction formatNationalNumber(state, format, {\r\n\tmetadata,\r\n\tuseNationalPrefixFormattingRule,\r\n\tgetSeparatorAfterNationalPrefix\r\n}) {\r\n\tlet formattedNationalNumber = formatNationalNumberUsingFormat(\r\n\t\tstate.nationalSignificantNumber,\r\n\t\tformat,\r\n\t\t{\r\n\t\t\tcarrierCode: state.carrierCode,\r\n\t\t\tuseInternationalFormat: state.international,\r\n\t\t\twithNationalPrefix: useNationalPrefixFormattingRule,\r\n\t\t\tmetadata\r\n\t\t}\r\n\t)\r\n\tif (!useNationalPrefixFormattingRule) {\r\n\t\tif (state.nationalPrefix) {\r\n\t\t\t// If a national prefix was extracted, then just prepend it,\r\n\t\t\t// followed by a \" \" character.\r\n\t\t\tformattedNationalNumber = state.nationalPrefix +\r\n\t\t\t\tgetSeparatorAfterNationalPrefix(format) +\r\n\t\t\t\tformattedNationalNumber\r\n\t\t} else if (state.complexPrefixBeforeNationalSignificantNumber) {\r\n\t\t\tformattedNationalNumber = state.complexPrefixBeforeNationalSignificantNumber +\r\n\t\t\t\t' ' +\r\n\t\t\t\tformattedNationalNumber\r\n\t\t}\r\n\t}\r\n\tif (isValidFormattedNationalNumber(formattedNationalNumber, state)) {\r\n\t\treturn formattedNationalNumber\r\n\t}\r\n}\r\n\r\n// Check that the formatted phone number contains exactly\r\n// the same digits that have been input by the user.\r\n// For example, when \"0111523456789\" is input for `AR` country,\r\n// the extracted `this.nationalSignificantNumber` is \"91123456789\",\r\n// which means that the national part of `this.digits` isn't simply equal to\r\n// `this.nationalPrefix` + `this.nationalSignificantNumber`.\r\n//\r\n// Also, a `format` can add extra digits to the `this.nationalSignificantNumber`\r\n// being formatted via `metadata[country].national_prefix_transform_rule`.\r\n// For example, for `VI` country, it prepends `340` to the national number,\r\n// and if this check hasn't been implemented, then there would be a bug\r\n// when `340` \"area coude\" is \"duplicated\" during input for `VI` country:\r\n// https://github.com/catamphetamine/libphonenumber-js/issues/318\r\n//\r\n// So, all these \"gotchas\" are filtered out.\r\n//\r\n// In the original Google's code, the comments say:\r\n// \"Check that we didn't remove nor add any extra digits when we matched\r\n// this formatting pattern. This usually happens after we entered the last\r\n// digit during AYTF. Eg: In case of MX, we swallow mobile token (1) when\r\n// formatted but AYTF should retain all the number entered and not change\r\n// in order to match a format (of same leading digits and length) display\r\n// in that way.\"\r\n// \"If it's the same (i.e entered number and format is same), then it's\r\n// safe to return this in formatted number as nothing is lost / added.\"\r\n// Otherwise, don't use this format.\r\n// https://github.com/google/libphonenumber/commit/3e7c1f04f5e7200f87fb131e6f85c6e99d60f510#diff-9149457fa9f5d608a11bb975c6ef4bc5\r\n// https://github.com/google/libphonenumber/commit/3ac88c7106e7dcb553bcc794b15f19185928a1c6#diff-2dcb77e833422ee304da348b905cde0b\r\n//\r\nfunction isValidFormattedNationalNumber(formattedNationalNumber, state) {\r\n\treturn parseDigits(formattedNationalNumber) === state.getNationalDigits()\r\n}"], "mappings": "AAAA,OAAOA,iBAAP,MAA8B,gCAA9B;AACA,OAAOC,WAAP,MAAwB,0BAAxB;AACA,OAAOC,+BAAP,MAA4C,8CAA5C;AAEA,eAAe,SAASC,oBAATA,CAA8BC,KAA9B,EAAqCC,MAArC,EAAAC,IAAA,EAIZ;EAAA,IAHFC,QAGE,GAAAD,IAAA,CAHFC,QAGE;IAFFC,qCAEE,GAAAF,IAAA,CAFFE,qCAEE;IADFC,+BACE,GAAAH,IAAA,CADFG,+BACE;EACF,IAAMC,OAAO,GAAG,IAAIC,MAAJ,QAAAC,MAAA,CAAkBP,MAAM,CAACQ,OAAP,EAAlB,QAAhB;EACA,IAAIH,OAAO,CAACI,IAAR,CAAaV,KAAK,CAACW,yBAAnB,CAAJ,EAAmD;IAClD,OAAOC,8DAA8D,CACpEZ,KADoE,EAEpEC,MAFoE,EAGpE;MACCE,QAAQ,EAARA,QADD;MAECC,qCAAqC,EAArCA,qCAFD;MAGCC,+BAA+B,EAA/BA;IAHD,CAHoE,CAArE;EASA;AACD;AAED,OAAO,SAASQ,uBAATA,CAAiCF,yBAAjC,EAA4DR,QAA5D,EAAsE;EAC5E,OAAOP,iBAAiB,CAACe,yBAAD,EAA4BR,QAA5B,CAAjB,KAA2D,aAAlE;AACA;AAED,SAASS,8DAATA,CAAwEZ,KAAxE,EAA+EC,MAA/E,EAAAa,KAAA,EAIG;EAAA,IAHFX,QAGE,GAAAW,KAAA,CAHFX,QAGE;IAFFC,qCAEE,GAAAU,KAAA,CAFFV,qCAEE;IADFC,+BACE,GAAAS,KAAA,CADFT,+BACE;EACF;EAEA,IACCM,yBADD,GAKIX,KALJ,CACCW,yBADD;IAECI,aAFD,GAKIf,KALJ,CAECe,aAFD;IAGCC,cAHD,GAKIhB,KALJ,CAGCgB,cAHD;IAICC,WAJD,GAKIjB,KALJ,CAICiB,WAJD,CAHE,CAUF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,IAAIb,qCAAqC,CAACH,MAAD,CAAzC,EAAmD;IAClD,IAAMiB,eAAe,GAAGC,oBAAoB,CAACnB,KAAD,EAAQC,MAAR,EAAgB;MAC3DmB,+BAA+B,EAAE,IAD0B;MAE3Df,+BAA+B,EAA/BA,+BAF2D;MAG3DF,QAAQ,EAARA;IAH2D,CAAhB,CAA5C;IAKA,IAAIe,eAAJ,EAAqB;MACpB,OAAOA,eAAP;IACA;EACD,CA7BC,CA+BF;;EACA,OAAOC,oBAAoB,CAACnB,KAAD,EAAQC,MAAR,EAAgB;IAC1CmB,+BAA+B,EAAE,KADS;IAE1Cf,+BAA+B,EAA/BA,+BAF0C;IAG1CF,QAAQ,EAARA;EAH0C,CAAhB,CAA3B;AAKA;AAED,SAASgB,oBAATA,CAA8BnB,KAA9B,EAAqCC,MAArC,EAAAoB,KAAA,EAIG;EAAA,IAHFlB,QAGE,GAAAkB,KAAA,CAHFlB,QAGE;IAFFiB,+BAEE,GAAAC,KAAA,CAFFD,+BAEE;IADFf,+BACE,GAAAgB,KAAA,CADFhB,+BACE;EACF,IAAIiB,uBAAuB,GAAGxB,+BAA+B,CAC5DE,KAAK,CAACW,yBADsD,EAE5DV,MAF4D,EAG5D;IACCgB,WAAW,EAAEjB,KAAK,CAACiB,WADpB;IAECM,sBAAsB,EAAEvB,KAAK,CAACe,aAF/B;IAGCS,kBAAkB,EAAEJ,+BAHrB;IAICjB,QAAQ,EAARA;EAJD,CAH4D,CAA7D;EAUA,IAAI,CAACiB,+BAAL,EAAsC;IACrC,IAAIpB,KAAK,CAACgB,cAAV,EAA0B;MACzB;MACA;MACAM,uBAAuB,GAAGtB,KAAK,CAACgB,cAAN,GACzBX,+BAA+B,CAACJ,MAAD,CADN,GAEzBqB,uBAFD;IAGA,CAND,MAMO,IAAItB,KAAK,CAACyB,4CAAV,EAAwD;MAC9DH,uBAAuB,GAAGtB,KAAK,CAACyB,4CAAN,GACzB,GADyB,GAEzBH,uBAFD;IAGA;EACD;EACD,IAAII,8BAA8B,CAACJ,uBAAD,EAA0BtB,KAA1B,CAAlC,EAAoE;IACnE,OAAOsB,uBAAP;EACA;AACD,C,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASI,8BAATA,CAAwCJ,uBAAxC,EAAiEtB,KAAjE,EAAwE;EACvE,OAAOH,WAAW,CAACyB,uBAAD,CAAX,KAAyCtB,KAAK,CAAC2B,iBAAN,EAAhD;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}