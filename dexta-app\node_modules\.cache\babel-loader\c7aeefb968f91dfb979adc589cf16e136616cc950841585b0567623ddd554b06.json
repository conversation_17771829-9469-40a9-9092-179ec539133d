{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { raceWith } from './raceWith';\nexport function race() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  return raceWith.apply(void 0, __spreadArray([], __read(argsOrArgArray(args))));\n}", "map": {"version": 3, "names": ["argsOrArgArray", "raceWith", "race", "args", "_i", "arguments", "length", "apply", "__spread<PERSON><PERSON>y", "__read"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\race.ts"], "sourcesContent": ["import { ObservableInputTuple, OperatorFunction } from '../types';\nimport { argsOrArgArray } from '../util/argsOrArgArray';\nimport { raceWith } from './raceWith';\n\n/** @deprecated Replaced with {@link raceWith}. Will be removed in v8. */\nexport function race<T, A extends readonly unknown[]>(otherSources: [...ObservableInputTuple<A>]): OperatorFunction<T, T | A[number]>;\n/** @deprecated Replaced with {@link raceWith}. Will be removed in v8. */\nexport function race<T, A extends readonly unknown[]>(...otherSources: [...ObservableInputTuple<A>]): OperatorFunction<T, T | A[number]>;\n\n/**\n * Returns an Observable that mirrors the first source Observable to emit a next,\n * error or complete notification from the combination of this Observable and supplied Observables.\n * @param args Sources used to race for which Observable emits first.\n * @return A function that returns an Observable that mirrors the output of the\n * first Observable to emit an item.\n * @deprecated Replaced with {@link raceWith}. Will be removed in v8.\n */\nexport function race<T>(...args: any[]): OperatorFunction<T, unknown> {\n  return raceWith(...argsOrArgArray(args));\n}\n"], "mappings": ";AACA,SAASA,cAAc,QAAQ,wBAAwB;AACvD,SAASC,QAAQ,QAAQ,YAAY;AAerC,OAAM,SAAUC,IAAIA,CAAA;EAAI,IAAAC,IAAA;OAAA,IAAAC,EAAA,IAAc,EAAdA,EAAA,GAAAC,SAAA,CAAAC,MAAc,EAAdF,EAAA,EAAc;IAAdD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EACtB,OAAOH,QAAQ,CAAAM,KAAA,SAAAC,aAAA,KAAAC,MAAA,CAAIT,cAAc,CAACG,IAAI,CAAC;AACzC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}