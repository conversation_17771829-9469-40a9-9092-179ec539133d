{"ast": null, "code": "var space = require('to-space-case');\n\n/**\n * Export.\n */\n\nmodule.exports = toCamelCase;\n\n/**\n * Convert a `string` to camel case.\n *\n * @param {String} string\n * @return {String}\n */\n\nfunction toCamelCase(string) {\n  return space(string).replace(/\\s(\\w)/g, function (matches, letter) {\n    return letter.toUpperCase();\n  });\n}", "map": {"version": 3, "names": ["space", "require", "module", "exports", "toCamelCase", "string", "replace", "matches", "letter", "toUpperCase"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/to-camel-case/index.js"], "sourcesContent": ["\nvar space = require('to-space-case')\n\n/**\n * Export.\n */\n\nmodule.exports = toCamelCase\n\n/**\n * Convert a `string` to camel case.\n *\n * @param {String} string\n * @return {String}\n */\n\nfunction toCamelCase(string) {\n  return space(string).replace(/\\s(\\w)/g, function (matches, letter) {\n    return letter.toUpperCase()\n  })\n}\n"], "mappings": "AACA,IAAIA,KAAK,GAAGC,OAAO,CAAC,eAAe,CAAC;;AAEpC;AACA;AACA;;AAEAC,MAAM,CAACC,OAAO,GAAGC,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,WAAWA,CAACC,MAAM,EAAE;EAC3B,OAAOL,KAAK,CAACK,MAAM,CAAC,CAACC,OAAO,CAAC,SAAS,EAAE,UAAUC,OAAO,EAAEC,MAAM,EAAE;IACjE,OAAOA,MAAM,CAACC,WAAW,CAAC,CAAC;EAC7B,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}