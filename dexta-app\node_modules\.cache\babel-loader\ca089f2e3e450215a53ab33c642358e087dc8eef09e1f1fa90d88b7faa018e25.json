{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { isScheduler } from '../util/isScheduler';\nimport { Observable } from '../Observable';\nimport { subscribeOn } from '../operators/subscribeOn';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { observeOn } from '../operators/observeOn';\nimport { AsyncSubject } from '../AsyncSubject';\nexport function bindCallbackInternals(isNodeStyle, callbackFunc, resultSelector, scheduler) {\n  if (resultSelector) {\n    if (isScheduler(resultSelector)) {\n      scheduler = resultSelector;\n    } else {\n      return function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n          args[_i] = arguments[_i];\n        }\n        return bindCallbackInternals(isNodeStyle, callbackFunc, scheduler).apply(this, args).pipe(mapOneOrManyArgs(resultSelector));\n      };\n    }\n  }\n  if (scheduler) {\n    return function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return bindCallbackInternals(isNodeStyle, callbackFunc).apply(this, args).pipe(subscribeOn(scheduler), observeOn(scheduler));\n    };\n  }\n  return function () {\n    var _this = this;\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    var subject = new AsyncSubject();\n    var uninitialized = true;\n    return new Observable(function (subscriber) {\n      var subs = subject.subscribe(subscriber);\n      if (uninitialized) {\n        uninitialized = false;\n        var isAsync_1 = false;\n        var isComplete_1 = false;\n        callbackFunc.apply(_this, __spreadArray(__spreadArray([], __read(args)), [function () {\n          var results = [];\n          for (var _i = 0; _i < arguments.length; _i++) {\n            results[_i] = arguments[_i];\n          }\n          if (isNodeStyle) {\n            var err = results.shift();\n            if (err != null) {\n              subject.error(err);\n              return;\n            }\n          }\n          subject.next(1 < results.length ? results : results[0]);\n          isComplete_1 = true;\n          if (isAsync_1) {\n            subject.complete();\n          }\n        }]));\n        if (isComplete_1) {\n          subject.complete();\n        }\n        isAsync_1 = true;\n      }\n      return subs;\n    });\n  };\n}", "map": {"version": 3, "names": ["isScheduler", "Observable", "subscribeOn", "mapOneOrManyArgs", "observeOn", "AsyncSubject", "bindCallbackInternals", "isNodeStyle", "callback<PERSON><PERSON><PERSON>", "resultSelector", "scheduler", "args", "_i", "arguments", "length", "apply", "pipe", "_this", "subject", "uninitialized", "subscriber", "subs", "subscribe", "isAsync_1", "isComplete_1", "__spread<PERSON><PERSON>y", "__read", "results", "err", "shift", "error", "next", "complete"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\observable\\bindCallbackInternals.ts"], "sourcesContent": ["import { SchedulerLike } from '../types';\nimport { isScheduler } from '../util/isScheduler';\nimport { Observable } from '../Observable';\nimport { subscribeOn } from '../operators/subscribeOn';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { observeOn } from '../operators/observeOn';\nimport { AsyncSubject } from '../AsyncSubject';\n\nexport function bindCallbackInternals(\n  isNodeStyle: boolean,\n  callbackFunc: any,\n  resultSelector?: any,\n  scheduler?: SchedulerLike\n): (...args: any[]) => Observable<unknown> {\n  if (resultSelector) {\n    if (isScheduler(resultSelector)) {\n      scheduler = resultSelector;\n    } else {\n      // The user provided a result selector.\n      return function (this: any, ...args: any[]) {\n        return (bindCallbackInternals(isNodeStyle, callbackFunc, scheduler) as any)\n          .apply(this, args)\n          .pipe(mapOneOrManyArgs(resultSelector as any));\n      };\n    }\n  }\n\n  // If a scheduler was passed, use our `subscribeOn` and `observeOn` operators\n  // to compose that behavior for the user.\n  if (scheduler) {\n    return function (this: any, ...args: any[]) {\n      return (bindCallbackInternals(isNodeStyle, callbackFunc) as any)\n        .apply(this, args)\n        .pipe(subscribeOn(scheduler!), observeOn(scheduler!));\n    };\n  }\n\n  return function (this: any, ...args: any[]): Observable<any> {\n    // We're using AsyncSubject, because it emits when it completes,\n    // and it will play the value to all late-arriving subscribers.\n    const subject = new AsyncSubject<any>();\n\n    // If this is true, then we haven't called our function yet.\n    let uninitialized = true;\n    return new Observable((subscriber) => {\n      // Add our subscriber to the subject.\n      const subs = subject.subscribe(subscriber);\n\n      if (uninitialized) {\n        uninitialized = false;\n        // We're going to execute the bound function\n        // This bit is to signal that we are hitting the callback asynchronously.\n        // Because we don't have any anti-\"Zalgo\" guarantees with whatever\n        // function we are handed, we use this bit to figure out whether or not\n        // we are getting hit in a callback synchronously during our call.\n        let isAsync = false;\n\n        // This is used to signal that the callback completed synchronously.\n        let isComplete = false;\n\n        // Call our function that has a callback. If at any time during this\n        // call, an error is thrown, it will be caught by the Observable\n        // subscription process and sent to the consumer.\n        callbackFunc.apply(\n          // Pass the appropriate `this` context.\n          this,\n          [\n            // Pass the arguments.\n            ...args,\n            // And our callback handler.\n            (...results: any[]) => {\n              if (isNodeStyle) {\n                // If this is a node callback, shift the first value off of the\n                // results and check it, as it is the error argument. By shifting,\n                // we leave only the argument(s) we want to pass to the consumer.\n                const err = results.shift();\n                if (err != null) {\n                  subject.error(err);\n                  // If we've errored, we can stop processing this function\n                  // as there's nothing else to do. Just return to escape.\n                  return;\n                }\n              }\n              // If we have one argument, notify the consumer\n              // of it as a single value, otherwise, if there's more than one, pass\n              // them as an array. Note that if there are no arguments, `undefined`\n              // will be emitted.\n              subject.next(1 < results.length ? results : results[0]);\n              // Flip this flag, so we know we can complete it in the synchronous\n              // case below.\n              isComplete = true;\n              // If we're not asynchronous, we need to defer the `complete` call\n              // until after the call to the function is over. This is because an\n              // error could be thrown in the function after it calls our callback,\n              // and if that is the case, if we complete here, we are unable to notify\n              // the consumer than an error occurred.\n              if (isAsync) {\n                subject.complete();\n              }\n            },\n          ]\n        );\n        // If we flipped `isComplete` during the call, we resolved synchronously,\n        // notify complete, because we skipped it in the callback to wait\n        // to make sure there were no errors during the call.\n        if (isComplete) {\n          subject.complete();\n        }\n\n        // We're no longer synchronous. If the callback is called at this point\n        // we can notify complete on the spot.\n        isAsync = true;\n      }\n\n      // Return the subscription from adding our subscriber to the subject.\n      return subs;\n    });\n  };\n}\n"], "mappings": ";AACA,SAASA,WAAW,QAAQ,qBAAqB;AACjD,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,OAAM,SAAUC,qBAAqBA,CACnCC,WAAoB,EACpBC,YAAiB,EACjBC,cAAoB,EACpBC,SAAyB;EAEzB,IAAID,cAAc,EAAE;IAClB,IAAIT,WAAW,CAACS,cAAc,CAAC,EAAE;MAC/BC,SAAS,GAAGD,cAAc;KAC3B,MAAM;MAEL,OAAO;QAAqB,IAAAE,IAAA;aAAA,IAAAC,EAAA,IAAc,EAAdA,EAAA,GAAAC,SAAA,CAAAC,MAAc,EAAdF,EAAA,EAAc;UAAdD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;QAC1B,OAAQN,qBAAqB,CAACC,WAAW,EAAEC,YAAY,EAAEE,SAAS,CAAS,CACxEK,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC,CACjBK,IAAI,CAACb,gBAAgB,CAACM,cAAqB,CAAC,CAAC;MAClD,CAAC;;;EAML,IAAIC,SAAS,EAAE;IACb,OAAO;MAAqB,IAAAC,IAAA;WAAA,IAAAC,EAAA,IAAc,EAAdA,EAAA,GAAAC,SAAA,CAAAC,MAAc,EAAdF,EAAA,EAAc;QAAdD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;MAC1B,OAAQN,qBAAqB,CAACC,WAAW,EAAEC,YAAY,CAAS,CAC7DO,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC,CACjBK,IAAI,CAACd,WAAW,CAACQ,SAAU,CAAC,EAAEN,SAAS,CAACM,SAAU,CAAC,CAAC;IACzD,CAAC;;EAGH,OAAO;IAAA,IAAAO,KAAA;IAAqB,IAAAN,IAAA;SAAA,IAAAC,EAAA,IAAc,EAAdA,EAAA,GAAAC,SAAA,CAAAC,MAAc,EAAdF,EAAA,EAAc;MAAdD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;IAG1B,IAAMM,OAAO,GAAG,IAAIb,YAAY,EAAO;IAGvC,IAAIc,aAAa,GAAG,IAAI;IACxB,OAAO,IAAIlB,UAAU,CAAC,UAACmB,UAAU;MAE/B,IAAMC,IAAI,GAAGH,OAAO,CAACI,SAAS,CAACF,UAAU,CAAC;MAE1C,IAAID,aAAa,EAAE;QACjBA,aAAa,GAAG,KAAK;QAMrB,IAAII,SAAO,GAAG,KAAK;QAGnB,IAAIC,YAAU,GAAG,KAAK;QAKtBhB,YAAY,CAACO,KAAK,CAEhBE,KAAI,EAAAQ,aAAA,CAAAA,aAAA,KAAAC,MAAA,CAGCf,IAAI,KAEP;UAAC,IAAAgB,OAAA;eAAA,IAAAf,EAAA,IAAiB,EAAjBA,EAAA,GAAAC,SAAA,CAAAC,MAAiB,EAAjBF,EAAA,EAAiB;YAAjBe,OAAA,CAAAf,EAAA,IAAAC,SAAA,CAAAD,EAAA;;UACC,IAAIL,WAAW,EAAE;YAIf,IAAMqB,GAAG,GAAGD,OAAO,CAACE,KAAK,EAAE;YAC3B,IAAID,GAAG,IAAI,IAAI,EAAE;cACfV,OAAO,CAACY,KAAK,CAACF,GAAG,CAAC;cAGlB;;;UAOJV,OAAO,CAACa,IAAI,CAAC,CAAC,GAAGJ,OAAO,CAACb,MAAM,GAAGa,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC,CAAC;UAGvDH,YAAU,GAAG,IAAI;UAMjB,IAAID,SAAO,EAAE;YACXL,OAAO,CAACc,QAAQ,EAAE;;QAEtB,CAAC,C,EAEJ;QAID,IAAIR,YAAU,EAAE;UACdN,OAAO,CAACc,QAAQ,EAAE;;QAKpBT,SAAO,GAAG,IAAI;;MAIhB,OAAOF,IAAI;IACb,CAAC,CAAC;EACJ,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}