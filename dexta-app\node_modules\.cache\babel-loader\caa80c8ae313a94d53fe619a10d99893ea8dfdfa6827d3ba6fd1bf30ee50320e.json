{"ast": null, "code": "import { observeNotification } from '../Notification';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function dematerialize() {\n  return operate(function (source, subscriber) {\n    source.subscribe(createOperatorSubscriber(subscriber, function (notification) {\n      return observeNotification(notification, subscriber);\n    }));\n  });\n}", "map": {"version": 3, "names": ["observeNotification", "operate", "createOperatorSubscriber", "dematerialize", "source", "subscriber", "subscribe", "notification"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\dematerialize.ts"], "sourcesContent": ["import { observeNotification } from '../Notification';\nimport { OperatorFunction, ObservableNotification, ValueFromNotification } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/**\n * Converts an Observable of {@link ObservableNotification} objects into the emissions\n * that they represent.\n *\n * <span class=\"informal\">Unwraps {@link ObservableNotification} objects as actual `next`,\n * `error` and `complete` emissions. The opposite of {@link materialize}.</span>\n *\n * ![](dematerialize.png)\n *\n * `dematerialize` is assumed to operate an Observable that only emits\n * {@link ObservableNotification} objects as `next` emissions, and does not emit any\n * `error`. Such Observable is the output of a `materialize` operation. Those\n * notifications are then unwrapped using the metadata they contain, and emitted\n * as `next`, `error`, and `complete` on the output Observable.\n *\n * Use this operator in conjunction with {@link materialize}.\n *\n * ## Example\n *\n * Convert an Observable of Notifications to an actual Observable\n *\n * ```ts\n * import { NextNotification, ErrorNotification, of, dematerialize } from 'rxjs';\n *\n * const notifA: NextNotification<string> = { kind: 'N', value: 'A' };\n * const notifB: NextNotification<string> = { kind: 'N', value: 'B' };\n * const notifE: ErrorNotification = { kind: 'E', error: new TypeError('x.toUpperCase is not a function') };\n *\n * const materialized = of(notifA, notifB, notifE);\n *\n * const upperCase = materialized.pipe(dematerialize());\n * upperCase.subscribe({\n *   next: x => console.log(x),\n *   error: e => console.error(e)\n * });\n *\n * // Results in:\n * // A\n * // B\n * // TypeError: x.toUpperCase is not a function\n * ```\n *\n * @see {@link materialize}\n *\n * @return A function that returns an Observable that emits items and\n * notifications embedded in Notification objects emitted by the source\n * Observable.\n */\nexport function dematerialize<N extends ObservableNotification<any>>(): OperatorFunction<N, ValueFromNotification<N>> {\n  return operate((source, subscriber) => {\n    source.subscribe(createOperatorSubscriber(subscriber, (notification) => observeNotification(notification, subscriber)));\n  });\n}\n"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,iBAAiB;AAErD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAkD/D,OAAM,SAAUC,aAAaA,CAAA;EAC3B,OAAOF,OAAO,CAAC,UAACG,MAAM,EAAEC,UAAU;IAChCD,MAAM,CAACE,SAAS,CAACJ,wBAAwB,CAACG,UAAU,EAAE,UAACE,YAAY;MAAK,OAAAP,mBAAmB,CAACO,YAAY,EAAEF,UAAU,CAAC;IAA7C,CAA6C,CAAC,CAAC;EACzH,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}