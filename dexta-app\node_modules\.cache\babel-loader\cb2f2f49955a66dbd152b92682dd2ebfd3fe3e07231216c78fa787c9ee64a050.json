{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useForkRef as useForkRef, unstable_useId as useId } from '@mui/utils';\nimport { ActionTypes } from './useListbox.types';\nimport defaultReducer from './defaultListboxReducer';\nimport useControllableReducer from './useControllableReducer';\nimport areArraysEqual from '../utils/areArraysEqual';\nconst TEXT_NAVIGATION_RESET_TIMEOUT = 500; // milliseconds\n\nconst defaultOptionComparer = (optionA, optionB) => optionA === optionB;\nconst defaultIsOptionDisabled = () => false;\nconst defaultOptionStringifier = option => typeof option === 'string' ? option : String(option);\nexport default function useListbox(props) {\n  var _props$optionIdGenera, _options$highlightedI;\n  const {\n    disabledItemsFocusable = false,\n    disableListWrap = false,\n    focusManagement = 'activeDescendant',\n    id: idProp,\n    isOptionDisabled = defaultIsOptionDisabled,\n    listboxRef: externalListboxRef,\n    multiple = false,\n    optionComparer = defaultOptionComparer,\n    optionStringifier = defaultOptionStringifier,\n    options,\n    stateReducer: externalReducer\n  } = props;\n  const id = useId(idProp);\n  function defaultIdGenerator(_, index) {\n    return `${id}-option-${index}`;\n  }\n  const optionIdGenerator = (_props$optionIdGenera = props.optionIdGenerator) != null ? _props$optionIdGenera : defaultIdGenerator;\n  const propsWithDefaults = _extends({}, props, {\n    disabledItemsFocusable,\n    disableListWrap,\n    focusManagement,\n    isOptionDisabled,\n    multiple,\n    optionComparer,\n    optionStringifier\n  });\n  const listboxRef = React.useRef(null);\n  const handleRef = useForkRef(externalListboxRef, listboxRef);\n  const textCriteriaRef = React.useRef({\n    searchString: '',\n    lastTime: null\n  });\n  const [{\n    highlightedValue,\n    selectedValue\n  }, dispatch] = useControllableReducer(defaultReducer, externalReducer, propsWithDefaults);\n  const highlightedIndex = React.useMemo(() => {\n    return highlightedValue == null ? -1 : options.findIndex(option => optionComparer(option, highlightedValue));\n  }, [highlightedValue, options, optionComparer]);\n  const previousOptions = React.useRef([]);\n  React.useEffect(() => {\n    if (areArraysEqual(previousOptions.current, options, optionComparer)) {\n      return;\n    }\n    dispatch({\n      type: ActionTypes.optionsChange,\n      options,\n      previousOptions: previousOptions.current,\n      props: propsWithDefaults\n    });\n    previousOptions.current = options; // No need to re-run this effect if props change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [options, optionComparer, dispatch]);\n  const setSelectedValue = React.useCallback(option => {\n    dispatch({\n      type: ActionTypes.setValue,\n      value: option\n    });\n  }, [dispatch]);\n  const setHighlightedValue = React.useCallback(option => {\n    dispatch({\n      type: ActionTypes.setHighlight,\n      highlight: option\n    });\n  }, [dispatch]);\n  const createHandleOptionClick = (option, other) => event => {\n    var _other$onClick;\n    (_other$onClick = other.onClick) == null ? void 0 : _other$onClick.call(other, event);\n    if (event.defaultPrevented) {\n      return;\n    }\n    event.preventDefault();\n    dispatch({\n      type: ActionTypes.optionClick,\n      option,\n      event,\n      props: propsWithDefaults\n    });\n  };\n  const createHandleOptionMouseOver = (option, other) => event => {\n    var _other$onMouseOver;\n    (_other$onMouseOver = other.onMouseOver) == null ? void 0 : _other$onMouseOver.call(other, event);\n    if (event.defaultPrevented) {\n      return;\n    }\n    dispatch({\n      type: ActionTypes.optionHover,\n      option,\n      event,\n      props: propsWithDefaults\n    });\n  };\n  const createHandleKeyDown = other => event => {\n    var _other$onKeyDown;\n    (_other$onKeyDown = other.onKeyDown) == null ? void 0 : _other$onKeyDown.call(other, event);\n    if (event.defaultPrevented) {\n      return;\n    }\n    const keysToPreventDefault = ['ArrowUp', 'ArrowDown', 'Home', 'End', 'PageUp', 'PageDown'];\n    if (focusManagement === 'activeDescendant') {\n      // When the child element is focused using the activeDescendant attribute,\n      // the listbox handles keyboard events on its behalf.\n      // We have to `preventDefault()` is this case to prevent the browser from\n      // scrolling the view when space is pressed or submitting forms when enter is pressed.\n      keysToPreventDefault.push(' ', 'Enter');\n    }\n    if (keysToPreventDefault.includes(event.key)) {\n      event.preventDefault();\n    }\n    dispatch({\n      type: ActionTypes.keyDown,\n      event,\n      props: propsWithDefaults\n    }); // Handle text navigation\n\n    if (event.key.length === 1 && event.key !== ' ') {\n      const textCriteria = textCriteriaRef.current;\n      const lowerKey = event.key.toLowerCase();\n      const currentTime = performance.now();\n      if (textCriteria.searchString.length > 0 && textCriteria.lastTime && currentTime - textCriteria.lastTime > TEXT_NAVIGATION_RESET_TIMEOUT) {\n        textCriteria.searchString = lowerKey;\n      } else if (textCriteria.searchString.length !== 1 || lowerKey !== textCriteria.searchString) {\n        // If there is just one character in the buffer and the key is the same, do not append\n        textCriteria.searchString += lowerKey;\n      }\n      textCriteria.lastTime = currentTime;\n      dispatch({\n        type: ActionTypes.textNavigation,\n        searchString: textCriteria.searchString,\n        props: propsWithDefaults\n      });\n    }\n  };\n  const createHandleBlur = other => event => {\n    var _other$onBlur, _listboxRef$current;\n    (_other$onBlur = other.onBlur) == null ? void 0 : _other$onBlur.call(other, event);\n    if (event.defaultPrevented) {\n      return;\n    }\n    if ((_listboxRef$current = listboxRef.current) != null && _listboxRef$current.contains(document.activeElement)) {\n      // focus is within the listbox\n      return;\n    }\n    dispatch({\n      type: ActionTypes.blur,\n      event,\n      props: propsWithDefaults\n    });\n  };\n  const getRootProps = (otherHandlers = {}) => {\n    return _extends({}, otherHandlers, {\n      'aria-activedescendant': focusManagement === 'activeDescendant' && highlightedValue != null ? optionIdGenerator(highlightedValue, highlightedIndex) : undefined,\n      id,\n      onBlur: createHandleBlur(otherHandlers),\n      onKeyDown: createHandleKeyDown(otherHandlers),\n      role: 'listbox',\n      tabIndex: focusManagement === 'DOM' ? -1 : 0,\n      ref: handleRef\n    });\n  };\n  const getOptionState = option => {\n    let selected;\n    const index = options.findIndex(opt => optionComparer(opt, option));\n    if (multiple) {\n      var _ref;\n      selected = ((_ref = selectedValue) != null ? _ref : []).some(value => value != null && optionComparer(option, value));\n    } else {\n      selected = optionComparer(option, selectedValue);\n    }\n    const disabled = isOptionDisabled(option, index);\n    return {\n      selected,\n      disabled,\n      highlighted: highlightedIndex === index\n    };\n  };\n  const getOptionTabIndex = optionState => {\n    if (focusManagement === 'activeDescendant') {\n      return undefined;\n    }\n    if (!optionState.highlighted) {\n      return -1;\n    }\n    if (optionState.disabled && !disabledItemsFocusable) {\n      return -1;\n    }\n    return 0;\n  };\n  const getOptionProps = (option, otherHandlers = {}) => {\n    const optionState = getOptionState(option);\n    const index = options.findIndex(opt => optionComparer(opt, option));\n    return _extends({}, otherHandlers, {\n      'aria-disabled': optionState.disabled || undefined,\n      'aria-selected': optionState.selected,\n      tabIndex: getOptionTabIndex(optionState),\n      id: optionIdGenerator(option, index),\n      onClick: createHandleOptionClick(option, otherHandlers),\n      onMouseOver: createHandleOptionMouseOver(option, otherHandlers),\n      role: 'option'\n    });\n  };\n  React.useDebugValue({\n    highlightedOption: options[highlightedIndex],\n    selectedOption: selectedValue\n  });\n  return {\n    getRootProps,\n    getOptionProps,\n    getOptionState,\n    highlightedOption: (_options$highlightedI = options[highlightedIndex]) != null ? _options$highlightedI : null,\n    selectedOption: selectedValue,\n    setSelectedValue,\n    setHighlightedValue\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_useForkRef", "useForkRef", "unstable_useId", "useId", "ActionTypes", "defaultReducer", "useControllableReducer", "areArraysEqual", "TEXT_NAVIGATION_RESET_TIMEOUT", "defaultOptionComparer", "optionA", "optionB", "defaultIsOptionDisabled", "defaultOptionStringifier", "option", "String", "useListbox", "props", "_props$optionIdGenera", "_options$highlightedI", "disabledItemsFocusable", "disableListWrap", "focusManagement", "id", "idProp", "isOptionDisabled", "listboxRef", "externalListboxRef", "multiple", "optionComparer", "optionStringifier", "options", "stateReducer", "externalReducer", "defaultIdGenerator", "_", "index", "optionIdGenerator", "propsWithDefaults", "useRef", "handleRef", "textCriteriaRef", "searchString", "lastTime", "highlightedValue", "selected<PERSON><PERSON><PERSON>", "dispatch", "highlightedIndex", "useMemo", "findIndex", "previousOptions", "useEffect", "current", "type", "optionsChange", "setSelectedValue", "useCallback", "setValue", "value", "setHighlightedValue", "<PERSON><PERSON><PERSON><PERSON>", "highlight", "createHandleOptionClick", "other", "event", "_other$onClick", "onClick", "call", "defaultPrevented", "preventDefault", "optionClick", "createHandleOptionMouseOver", "_other$onMouseOver", "onMouseOver", "optionHover", "createHandleKeyDown", "_other$onKeyDown", "onKeyDown", "keysToPreventDefault", "push", "includes", "key", "keyDown", "length", "textCriteria", "lowerKey", "toLowerCase", "currentTime", "performance", "now", "textNavigation", "createHandleBlur", "_other$onBlur", "_listboxRef$current", "onBlur", "contains", "document", "activeElement", "blur", "getRootProps", "otherHandlers", "undefined", "role", "tabIndex", "ref", "getOptionState", "selected", "opt", "_ref", "some", "disabled", "highlighted", "getOptionTabIndex", "optionState", "getOptionProps", "useDebugValue", "highlightedOption", "selectedOption"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/ListboxUnstyled/useListbox.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useForkRef as useForkRef, unstable_useId as useId } from '@mui/utils';\nimport { ActionTypes } from './useListbox.types';\nimport defaultReducer from './defaultListboxReducer';\nimport useControllableReducer from './useControllableReducer';\nimport areArraysEqual from '../utils/areArraysEqual';\nconst TEXT_NAVIGATION_RESET_TIMEOUT = 500; // milliseconds\n\nconst defaultOptionComparer = (optionA, optionB) => optionA === optionB;\n\nconst defaultIsOptionDisabled = () => false;\n\nconst defaultOptionStringifier = option => typeof option === 'string' ? option : String(option);\n\nexport default function useListbox(props) {\n  var _props$optionIdGenera, _options$highlightedI;\n\n  const {\n    disabledItemsFocusable = false,\n    disableListWrap = false,\n    focusManagement = 'activeDescendant',\n    id: idProp,\n    isOptionDisabled = defaultIsOptionDisabled,\n    listboxRef: externalListboxRef,\n    multiple = false,\n    optionComparer = defaultOptionComparer,\n    optionStringifier = defaultOptionStringifier,\n    options,\n    stateReducer: externalReducer\n  } = props;\n  const id = useId(idProp);\n\n  function defaultIdGenerator(_, index) {\n    return `${id}-option-${index}`;\n  }\n\n  const optionIdGenerator = (_props$optionIdGenera = props.optionIdGenerator) != null ? _props$optionIdGenera : defaultIdGenerator;\n\n  const propsWithDefaults = _extends({}, props, {\n    disabledItemsFocusable,\n    disableListWrap,\n    focusManagement,\n    isOptionDisabled,\n    multiple,\n    optionComparer,\n    optionStringifier\n  });\n\n  const listboxRef = React.useRef(null);\n  const handleRef = useForkRef(externalListboxRef, listboxRef);\n  const textCriteriaRef = React.useRef({\n    searchString: '',\n    lastTime: null\n  });\n  const [{\n    highlightedValue,\n    selectedValue\n  }, dispatch] = useControllableReducer(defaultReducer, externalReducer, propsWithDefaults);\n  const highlightedIndex = React.useMemo(() => {\n    return highlightedValue == null ? -1 : options.findIndex(option => optionComparer(option, highlightedValue));\n  }, [highlightedValue, options, optionComparer]);\n  const previousOptions = React.useRef([]);\n  React.useEffect(() => {\n    if (areArraysEqual(previousOptions.current, options, optionComparer)) {\n      return;\n    }\n\n    dispatch({\n      type: ActionTypes.optionsChange,\n      options,\n      previousOptions: previousOptions.current,\n      props: propsWithDefaults\n    });\n    previousOptions.current = options; // No need to re-run this effect if props change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [options, optionComparer, dispatch]);\n  const setSelectedValue = React.useCallback(option => {\n    dispatch({\n      type: ActionTypes.setValue,\n      value: option\n    });\n  }, [dispatch]);\n  const setHighlightedValue = React.useCallback(option => {\n    dispatch({\n      type: ActionTypes.setHighlight,\n      highlight: option\n    });\n  }, [dispatch]);\n\n  const createHandleOptionClick = (option, other) => event => {\n    var _other$onClick;\n\n    (_other$onClick = other.onClick) == null ? void 0 : _other$onClick.call(other, event);\n\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    event.preventDefault();\n    dispatch({\n      type: ActionTypes.optionClick,\n      option,\n      event,\n      props: propsWithDefaults\n    });\n  };\n\n  const createHandleOptionMouseOver = (option, other) => event => {\n    var _other$onMouseOver;\n\n    (_other$onMouseOver = other.onMouseOver) == null ? void 0 : _other$onMouseOver.call(other, event);\n\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    dispatch({\n      type: ActionTypes.optionHover,\n      option,\n      event,\n      props: propsWithDefaults\n    });\n  };\n\n  const createHandleKeyDown = other => event => {\n    var _other$onKeyDown;\n\n    (_other$onKeyDown = other.onKeyDown) == null ? void 0 : _other$onKeyDown.call(other, event);\n\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    const keysToPreventDefault = ['ArrowUp', 'ArrowDown', 'Home', 'End', 'PageUp', 'PageDown'];\n\n    if (focusManagement === 'activeDescendant') {\n      // When the child element is focused using the activeDescendant attribute,\n      // the listbox handles keyboard events on its behalf.\n      // We have to `preventDefault()` is this case to prevent the browser from\n      // scrolling the view when space is pressed or submitting forms when enter is pressed.\n      keysToPreventDefault.push(' ', 'Enter');\n    }\n\n    if (keysToPreventDefault.includes(event.key)) {\n      event.preventDefault();\n    }\n\n    dispatch({\n      type: ActionTypes.keyDown,\n      event,\n      props: propsWithDefaults\n    }); // Handle text navigation\n\n    if (event.key.length === 1 && event.key !== ' ') {\n      const textCriteria = textCriteriaRef.current;\n      const lowerKey = event.key.toLowerCase();\n      const currentTime = performance.now();\n\n      if (textCriteria.searchString.length > 0 && textCriteria.lastTime && currentTime - textCriteria.lastTime > TEXT_NAVIGATION_RESET_TIMEOUT) {\n        textCriteria.searchString = lowerKey;\n      } else if (textCriteria.searchString.length !== 1 || lowerKey !== textCriteria.searchString) {\n        // If there is just one character in the buffer and the key is the same, do not append\n        textCriteria.searchString += lowerKey;\n      }\n\n      textCriteria.lastTime = currentTime;\n      dispatch({\n        type: ActionTypes.textNavigation,\n        searchString: textCriteria.searchString,\n        props: propsWithDefaults\n      });\n    }\n  };\n\n  const createHandleBlur = other => event => {\n    var _other$onBlur, _listboxRef$current;\n\n    (_other$onBlur = other.onBlur) == null ? void 0 : _other$onBlur.call(other, event);\n\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    if ((_listboxRef$current = listboxRef.current) != null && _listboxRef$current.contains(document.activeElement)) {\n      // focus is within the listbox\n      return;\n    }\n\n    dispatch({\n      type: ActionTypes.blur,\n      event,\n      props: propsWithDefaults\n    });\n  };\n\n  const getRootProps = (otherHandlers = {}) => {\n    return _extends({}, otherHandlers, {\n      'aria-activedescendant': focusManagement === 'activeDescendant' && highlightedValue != null ? optionIdGenerator(highlightedValue, highlightedIndex) : undefined,\n      id,\n      onBlur: createHandleBlur(otherHandlers),\n      onKeyDown: createHandleKeyDown(otherHandlers),\n      role: 'listbox',\n      tabIndex: focusManagement === 'DOM' ? -1 : 0,\n      ref: handleRef\n    });\n  };\n\n  const getOptionState = option => {\n    let selected;\n    const index = options.findIndex(opt => optionComparer(opt, option));\n\n    if (multiple) {\n      var _ref;\n\n      selected = ((_ref = selectedValue) != null ? _ref : []).some(value => value != null && optionComparer(option, value));\n    } else {\n      selected = optionComparer(option, selectedValue);\n    }\n\n    const disabled = isOptionDisabled(option, index);\n    return {\n      selected,\n      disabled,\n      highlighted: highlightedIndex === index\n    };\n  };\n\n  const getOptionTabIndex = optionState => {\n    if (focusManagement === 'activeDescendant') {\n      return undefined;\n    }\n\n    if (!optionState.highlighted) {\n      return -1;\n    }\n\n    if (optionState.disabled && !disabledItemsFocusable) {\n      return -1;\n    }\n\n    return 0;\n  };\n\n  const getOptionProps = (option, otherHandlers = {}) => {\n    const optionState = getOptionState(option);\n    const index = options.findIndex(opt => optionComparer(opt, option));\n    return _extends({}, otherHandlers, {\n      'aria-disabled': optionState.disabled || undefined,\n      'aria-selected': optionState.selected,\n      tabIndex: getOptionTabIndex(optionState),\n      id: optionIdGenerator(option, index),\n      onClick: createHandleOptionClick(option, otherHandlers),\n      onMouseOver: createHandleOptionMouseOver(option, otherHandlers),\n      role: 'option'\n    });\n  };\n\n  React.useDebugValue({\n    highlightedOption: options[highlightedIndex],\n    selectedOption: selectedValue\n  });\n  return {\n    getRootProps,\n    getOptionProps,\n    getOptionState,\n    highlightedOption: (_options$highlightedI = options[highlightedIndex]) != null ? _options$highlightedI : null,\n    selectedOption: selectedValue,\n    setSelectedValue,\n    setHighlightedValue\n  };\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,mBAAmB,IAAIC,UAAU,EAAEC,cAAc,IAAIC,KAAK,QAAQ,YAAY;AACvF,SAASC,WAAW,QAAQ,oBAAoB;AAChD,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,cAAc,MAAM,yBAAyB;AACpD,MAAMC,6BAA6B,GAAG,GAAG,CAAC,CAAC;;AAE3C,MAAMC,qBAAqB,GAAGA,CAACC,OAAO,EAAEC,OAAO,KAAKD,OAAO,KAAKC,OAAO;AAEvE,MAAMC,uBAAuB,GAAGA,CAAA,KAAM,KAAK;AAE3C,MAAMC,wBAAwB,GAAGC,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGC,MAAM,CAACD,MAAM,CAAC;AAE/F,eAAe,SAASE,UAAUA,CAACC,KAAK,EAAE;EACxC,IAAIC,qBAAqB,EAAEC,qBAAqB;EAEhD,MAAM;IACJC,sBAAsB,GAAG,KAAK;IAC9BC,eAAe,GAAG,KAAK;IACvBC,eAAe,GAAG,kBAAkB;IACpCC,EAAE,EAAEC,MAAM;IACVC,gBAAgB,GAAGb,uBAAuB;IAC1Cc,UAAU,EAAEC,kBAAkB;IAC9BC,QAAQ,GAAG,KAAK;IAChBC,cAAc,GAAGpB,qBAAqB;IACtCqB,iBAAiB,GAAGjB,wBAAwB;IAC5CkB,OAAO;IACPC,YAAY,EAAEC;EAChB,CAAC,GAAGhB,KAAK;EACT,MAAMM,EAAE,GAAGpB,KAAK,CAACqB,MAAM,CAAC;EAExB,SAASU,kBAAkBA,CAACC,CAAC,EAAEC,KAAK,EAAE;IACpC,OAAQ,GAAEb,EAAG,WAAUa,KAAM,EAAC;EAChC;EAEA,MAAMC,iBAAiB,GAAG,CAACnB,qBAAqB,GAAGD,KAAK,CAACoB,iBAAiB,KAAK,IAAI,GAAGnB,qBAAqB,GAAGgB,kBAAkB;EAEhI,MAAMI,iBAAiB,GAAGxC,QAAQ,CAAC,CAAC,CAAC,EAAEmB,KAAK,EAAE;IAC5CG,sBAAsB;IACtBC,eAAe;IACfC,eAAe;IACfG,gBAAgB;IAChBG,QAAQ;IACRC,cAAc;IACdC;EACF,CAAC,CAAC;EAEF,MAAMJ,UAAU,GAAG3B,KAAK,CAACwC,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMC,SAAS,GAAGvC,UAAU,CAAC0B,kBAAkB,EAAED,UAAU,CAAC;EAC5D,MAAMe,eAAe,GAAG1C,KAAK,CAACwC,MAAM,CAAC;IACnCG,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAAC;IACLC,gBAAgB;IAChBC;EACF,CAAC,EAAEC,QAAQ,CAAC,GAAGxC,sBAAsB,CAACD,cAAc,EAAE4B,eAAe,EAAEK,iBAAiB,CAAC;EACzF,MAAMS,gBAAgB,GAAGhD,KAAK,CAACiD,OAAO,CAAC,MAAM;IAC3C,OAAOJ,gBAAgB,IAAI,IAAI,GAAG,CAAC,CAAC,GAAGb,OAAO,CAACkB,SAAS,CAACnC,MAAM,IAAIe,cAAc,CAACf,MAAM,EAAE8B,gBAAgB,CAAC,CAAC;EAC9G,CAAC,EAAE,CAACA,gBAAgB,EAAEb,OAAO,EAAEF,cAAc,CAAC,CAAC;EAC/C,MAAMqB,eAAe,GAAGnD,KAAK,CAACwC,MAAM,CAAC,EAAE,CAAC;EACxCxC,KAAK,CAACoD,SAAS,CAAC,MAAM;IACpB,IAAI5C,cAAc,CAAC2C,eAAe,CAACE,OAAO,EAAErB,OAAO,EAAEF,cAAc,CAAC,EAAE;MACpE;IACF;IAEAiB,QAAQ,CAAC;MACPO,IAAI,EAAEjD,WAAW,CAACkD,aAAa;MAC/BvB,OAAO;MACPmB,eAAe,EAAEA,eAAe,CAACE,OAAO;MACxCnC,KAAK,EAAEqB;IACT,CAAC,CAAC;IACFY,eAAe,CAACE,OAAO,GAAGrB,OAAO,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,CAACA,OAAO,EAAEF,cAAc,EAAEiB,QAAQ,CAAC,CAAC;EACvC,MAAMS,gBAAgB,GAAGxD,KAAK,CAACyD,WAAW,CAAC1C,MAAM,IAAI;IACnDgC,QAAQ,CAAC;MACPO,IAAI,EAAEjD,WAAW,CAACqD,QAAQ;MAC1BC,KAAK,EAAE5C;IACT,CAAC,CAAC;EACJ,CAAC,EAAE,CAACgC,QAAQ,CAAC,CAAC;EACd,MAAMa,mBAAmB,GAAG5D,KAAK,CAACyD,WAAW,CAAC1C,MAAM,IAAI;IACtDgC,QAAQ,CAAC;MACPO,IAAI,EAAEjD,WAAW,CAACwD,YAAY;MAC9BC,SAAS,EAAE/C;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,CAACgC,QAAQ,CAAC,CAAC;EAEd,MAAMgB,uBAAuB,GAAGA,CAAChD,MAAM,EAAEiD,KAAK,KAAKC,KAAK,IAAI;IAC1D,IAAIC,cAAc;IAElB,CAACA,cAAc,GAAGF,KAAK,CAACG,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,cAAc,CAACE,IAAI,CAACJ,KAAK,EAAEC,KAAK,CAAC;IAErF,IAAIA,KAAK,CAACI,gBAAgB,EAAE;MAC1B;IACF;IAEAJ,KAAK,CAACK,cAAc,CAAC,CAAC;IACtBvB,QAAQ,CAAC;MACPO,IAAI,EAAEjD,WAAW,CAACkE,WAAW;MAC7BxD,MAAM;MACNkD,KAAK;MACL/C,KAAK,EAAEqB;IACT,CAAC,CAAC;EACJ,CAAC;EAED,MAAMiC,2BAA2B,GAAGA,CAACzD,MAAM,EAAEiD,KAAK,KAAKC,KAAK,IAAI;IAC9D,IAAIQ,kBAAkB;IAEtB,CAACA,kBAAkB,GAAGT,KAAK,CAACU,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,kBAAkB,CAACL,IAAI,CAACJ,KAAK,EAAEC,KAAK,CAAC;IAEjG,IAAIA,KAAK,CAACI,gBAAgB,EAAE;MAC1B;IACF;IAEAtB,QAAQ,CAAC;MACPO,IAAI,EAAEjD,WAAW,CAACsE,WAAW;MAC7B5D,MAAM;MACNkD,KAAK;MACL/C,KAAK,EAAEqB;IACT,CAAC,CAAC;EACJ,CAAC;EAED,MAAMqC,mBAAmB,GAAGZ,KAAK,IAAIC,KAAK,IAAI;IAC5C,IAAIY,gBAAgB;IAEpB,CAACA,gBAAgB,GAAGb,KAAK,CAACc,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,gBAAgB,CAACT,IAAI,CAACJ,KAAK,EAAEC,KAAK,CAAC;IAE3F,IAAIA,KAAK,CAACI,gBAAgB,EAAE;MAC1B;IACF;IAEA,MAAMU,oBAAoB,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,CAAC;IAE1F,IAAIxD,eAAe,KAAK,kBAAkB,EAAE;MAC1C;MACA;MACA;MACA;MACAwD,oBAAoB,CAACC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC;IACzC;IAEA,IAAID,oBAAoB,CAACE,QAAQ,CAAChB,KAAK,CAACiB,GAAG,CAAC,EAAE;MAC5CjB,KAAK,CAACK,cAAc,CAAC,CAAC;IACxB;IAEAvB,QAAQ,CAAC;MACPO,IAAI,EAAEjD,WAAW,CAAC8E,OAAO;MACzBlB,KAAK;MACL/C,KAAK,EAAEqB;IACT,CAAC,CAAC,CAAC,CAAC;;IAEJ,IAAI0B,KAAK,CAACiB,GAAG,CAACE,MAAM,KAAK,CAAC,IAAInB,KAAK,CAACiB,GAAG,KAAK,GAAG,EAAE;MAC/C,MAAMG,YAAY,GAAG3C,eAAe,CAACW,OAAO;MAC5C,MAAMiC,QAAQ,GAAGrB,KAAK,CAACiB,GAAG,CAACK,WAAW,CAAC,CAAC;MACxC,MAAMC,WAAW,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;MAErC,IAAIL,YAAY,CAAC1C,YAAY,CAACyC,MAAM,GAAG,CAAC,IAAIC,YAAY,CAACzC,QAAQ,IAAI4C,WAAW,GAAGH,YAAY,CAACzC,QAAQ,GAAGnC,6BAA6B,EAAE;QACxI4E,YAAY,CAAC1C,YAAY,GAAG2C,QAAQ;MACtC,CAAC,MAAM,IAAID,YAAY,CAAC1C,YAAY,CAACyC,MAAM,KAAK,CAAC,IAAIE,QAAQ,KAAKD,YAAY,CAAC1C,YAAY,EAAE;QAC3F;QACA0C,YAAY,CAAC1C,YAAY,IAAI2C,QAAQ;MACvC;MAEAD,YAAY,CAACzC,QAAQ,GAAG4C,WAAW;MACnCzC,QAAQ,CAAC;QACPO,IAAI,EAAEjD,WAAW,CAACsF,cAAc;QAChChD,YAAY,EAAE0C,YAAY,CAAC1C,YAAY;QACvCzB,KAAK,EAAEqB;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMqD,gBAAgB,GAAG5B,KAAK,IAAIC,KAAK,IAAI;IACzC,IAAI4B,aAAa,EAAEC,mBAAmB;IAEtC,CAACD,aAAa,GAAG7B,KAAK,CAAC+B,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,aAAa,CAACzB,IAAI,CAACJ,KAAK,EAAEC,KAAK,CAAC;IAElF,IAAIA,KAAK,CAACI,gBAAgB,EAAE;MAC1B;IACF;IAEA,IAAI,CAACyB,mBAAmB,GAAGnE,UAAU,CAAC0B,OAAO,KAAK,IAAI,IAAIyC,mBAAmB,CAACE,QAAQ,CAACC,QAAQ,CAACC,aAAa,CAAC,EAAE;MAC9G;MACA;IACF;IAEAnD,QAAQ,CAAC;MACPO,IAAI,EAAEjD,WAAW,CAAC8F,IAAI;MACtBlC,KAAK;MACL/C,KAAK,EAAEqB;IACT,CAAC,CAAC;EACJ,CAAC;EAED,MAAM6D,YAAY,GAAGA,CAACC,aAAa,GAAG,CAAC,CAAC,KAAK;IAC3C,OAAOtG,QAAQ,CAAC,CAAC,CAAC,EAAEsG,aAAa,EAAE;MACjC,uBAAuB,EAAE9E,eAAe,KAAK,kBAAkB,IAAIsB,gBAAgB,IAAI,IAAI,GAAGP,iBAAiB,CAACO,gBAAgB,EAAEG,gBAAgB,CAAC,GAAGsD,SAAS;MAC/J9E,EAAE;MACFuE,MAAM,EAAEH,gBAAgB,CAACS,aAAa,CAAC;MACvCvB,SAAS,EAAEF,mBAAmB,CAACyB,aAAa,CAAC;MAC7CE,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAEjF,eAAe,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MAC5CkF,GAAG,EAAEhE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMiE,cAAc,GAAG3F,MAAM,IAAI;IAC/B,IAAI4F,QAAQ;IACZ,MAAMtE,KAAK,GAAGL,OAAO,CAACkB,SAAS,CAAC0D,GAAG,IAAI9E,cAAc,CAAC8E,GAAG,EAAE7F,MAAM,CAAC,CAAC;IAEnE,IAAIc,QAAQ,EAAE;MACZ,IAAIgF,IAAI;MAERF,QAAQ,GAAG,CAAC,CAACE,IAAI,GAAG/D,aAAa,KAAK,IAAI,GAAG+D,IAAI,GAAG,EAAE,EAAEC,IAAI,CAACnD,KAAK,IAAIA,KAAK,IAAI,IAAI,IAAI7B,cAAc,CAACf,MAAM,EAAE4C,KAAK,CAAC,CAAC;IACvH,CAAC,MAAM;MACLgD,QAAQ,GAAG7E,cAAc,CAACf,MAAM,EAAE+B,aAAa,CAAC;IAClD;IAEA,MAAMiE,QAAQ,GAAGrF,gBAAgB,CAACX,MAAM,EAAEsB,KAAK,CAAC;IAChD,OAAO;MACLsE,QAAQ;MACRI,QAAQ;MACRC,WAAW,EAAEhE,gBAAgB,KAAKX;IACpC,CAAC;EACH,CAAC;EAED,MAAM4E,iBAAiB,GAAGC,WAAW,IAAI;IACvC,IAAI3F,eAAe,KAAK,kBAAkB,EAAE;MAC1C,OAAO+E,SAAS;IAClB;IAEA,IAAI,CAACY,WAAW,CAACF,WAAW,EAAE;MAC5B,OAAO,CAAC,CAAC;IACX;IAEA,IAAIE,WAAW,CAACH,QAAQ,IAAI,CAAC1F,sBAAsB,EAAE;MACnD,OAAO,CAAC,CAAC;IACX;IAEA,OAAO,CAAC;EACV,CAAC;EAED,MAAM8F,cAAc,GAAGA,CAACpG,MAAM,EAAEsF,aAAa,GAAG,CAAC,CAAC,KAAK;IACrD,MAAMa,WAAW,GAAGR,cAAc,CAAC3F,MAAM,CAAC;IAC1C,MAAMsB,KAAK,GAAGL,OAAO,CAACkB,SAAS,CAAC0D,GAAG,IAAI9E,cAAc,CAAC8E,GAAG,EAAE7F,MAAM,CAAC,CAAC;IACnE,OAAOhB,QAAQ,CAAC,CAAC,CAAC,EAAEsG,aAAa,EAAE;MACjC,eAAe,EAAEa,WAAW,CAACH,QAAQ,IAAIT,SAAS;MAClD,eAAe,EAAEY,WAAW,CAACP,QAAQ;MACrCH,QAAQ,EAAES,iBAAiB,CAACC,WAAW,CAAC;MACxC1F,EAAE,EAAEc,iBAAiB,CAACvB,MAAM,EAAEsB,KAAK,CAAC;MACpC8B,OAAO,EAAEJ,uBAAuB,CAAChD,MAAM,EAAEsF,aAAa,CAAC;MACvD3B,WAAW,EAAEF,2BAA2B,CAACzD,MAAM,EAAEsF,aAAa,CAAC;MAC/DE,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;EAEDvG,KAAK,CAACoH,aAAa,CAAC;IAClBC,iBAAiB,EAAErF,OAAO,CAACgB,gBAAgB,CAAC;IAC5CsE,cAAc,EAAExE;EAClB,CAAC,CAAC;EACF,OAAO;IACLsD,YAAY;IACZe,cAAc;IACdT,cAAc;IACdW,iBAAiB,EAAE,CAACjG,qBAAqB,GAAGY,OAAO,CAACgB,gBAAgB,CAAC,KAAK,IAAI,GAAG5B,qBAAqB,GAAG,IAAI;IAC7GkG,cAAc,EAAExE,aAAa;IAC7BU,gBAAgB;IAChBI;EACF,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}