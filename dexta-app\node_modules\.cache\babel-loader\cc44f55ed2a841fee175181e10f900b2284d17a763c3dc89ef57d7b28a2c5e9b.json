{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { identity } from '../util/identity';\nimport { noop } from '../util/noop';\nimport { popResultSelector } from '../util/args';\nexport function withLatestFrom() {\n  var inputs = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    inputs[_i] = arguments[_i];\n  }\n  var project = popResultSelector(inputs);\n  return operate(function (source, subscriber) {\n    var len = inputs.length;\n    var otherValues = new Array(len);\n    var hasValue = inputs.map(function () {\n      return false;\n    });\n    var ready = false;\n    var _loop_1 = function (i) {\n      innerFrom(inputs[i]).subscribe(createOperatorSubscriber(subscriber, function (value) {\n        otherValues[i] = value;\n        if (!ready && !hasValue[i]) {\n          hasValue[i] = true;\n          (ready = hasValue.every(identity)) && (hasValue = null);\n        }\n      }, noop));\n    };\n    for (var i = 0; i < len; i++) {\n      _loop_1(i);\n    }\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      if (ready) {\n        var values = __spreadArray([value], __read(otherValues));\n        subscriber.next(project ? project.apply(void 0, __spreadArray([], __read(values))) : values);\n      }\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "innerFrom", "identity", "noop", "popResultSelector", "withLatestFrom", "inputs", "_i", "arguments", "length", "project", "source", "subscriber", "len", "otherValues", "Array", "hasValue", "map", "ready", "i", "subscribe", "value", "every", "values", "__spread<PERSON><PERSON>y", "__read", "next", "apply"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\withLatestFrom.ts"], "sourcesContent": ["import { OperatorFunction, ObservableInputTuple } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { identity } from '../util/identity';\nimport { noop } from '../util/noop';\nimport { popResultSelector } from '../util/args';\n\nexport function withLatestFrom<T, O extends unknown[]>(...inputs: [...ObservableInputTuple<O>]): OperatorFunction<T, [T, ...O]>;\n\nexport function withLatestFrom<T, O extends unknown[], R>(\n  ...inputs: [...ObservableInputTuple<O>, (...value: [T, ...O]) => R]\n): OperatorFunction<T, R>;\n\n/**\n * Combines the source Observable with other Observables to create an Observable\n * whose values are calculated from the latest values of each, only when the\n * source emits.\n *\n * <span class=\"informal\">Whenever the source Observable emits a value, it\n * computes a formula using that value plus the latest values from other input\n * Observables, then emits the output of that formula.</span>\n *\n * ![](withLatestFrom.png)\n *\n * `withLatestFrom` combines each value from the source Observable (the\n * instance) with the latest values from the other input Observables only when\n * the source emits a value, optionally using a `project` function to determine\n * the value to be emitted on the output Observable. All input Observables must\n * emit at least one value before the output Observable will emit a value.\n *\n * ## Example\n *\n * On every click event, emit an array with the latest timer event plus the click event\n *\n * ```ts\n * import { fromEvent, interval, withLatestFrom } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const timer = interval(1000);\n * const result = clicks.pipe(withLatestFrom(timer));\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link combineLatest}\n *\n * @param inputs An input Observable to combine with the source Observable. More\n * than one input Observables may be given as argument. If the last parameter is\n * a function, it will be used as a projection function for combining values\n * together. When the function is called, it receives all values in order of the\n * Observables passed, where the first parameter is a value from the source\n * Observable. (e.g.\n * `a.pipe(withLatestFrom(b, c), map(([a1, b1, c1]) => a1 + b1 + c1))`). If this\n * is not passed, arrays will be emitted on the output Observable.\n * @return A function that returns an Observable of projected values from the\n * most recent values from each input Observable, or an array of the most\n * recent values from each input Observable.\n */\nexport function withLatestFrom<T, R>(...inputs: any[]): OperatorFunction<T, R | any[]> {\n  const project = popResultSelector(inputs) as ((...args: any[]) => R) | undefined;\n\n  return operate((source, subscriber) => {\n    const len = inputs.length;\n    const otherValues = new Array(len);\n    // An array of whether or not the other sources have emitted. Matched with them by index.\n    // TODO: At somepoint, we should investigate the performance implications here, and look\n    // into using a `Set()` and checking the `size` to see if we're ready.\n    let hasValue = inputs.map(() => false);\n    // Flipped true when we have at least one value from all other sources and\n    // we are ready to start emitting values.\n    let ready = false;\n\n    // Other sources. Note that here we are not checking `subscriber.closed`,\n    // this causes all inputs to be subscribed to, even if nothing can be emitted\n    // from them. This is an important distinction because subscription constitutes\n    // a side-effect.\n    for (let i = 0; i < len; i++) {\n      innerFrom(inputs[i]).subscribe(\n        createOperatorSubscriber(\n          subscriber,\n          (value) => {\n            otherValues[i] = value;\n            if (!ready && !hasValue[i]) {\n              // If we're not ready yet, flag to show this observable has emitted.\n              hasValue[i] = true;\n              // Intentionally terse code.\n              // If all of our other observables have emitted, set `ready` to `true`,\n              // so we know we can start emitting values, then clean up the `hasValue` array,\n              // because we don't need it anymore.\n              (ready = hasValue.every(identity)) && (hasValue = null!);\n            }\n          },\n          // Completing one of the other sources has\n          // no bearing on the completion of our result.\n          noop\n        )\n      );\n    }\n\n    // Source subscription\n    source.subscribe(\n      createOperatorSubscriber(subscriber, (value) => {\n        if (ready) {\n          // We have at least one value from the other sources. Go ahead and emit.\n          const values = [value, ...otherValues];\n          subscriber.next(project ? project(...values) : values);\n        }\n      })\n    );\n  });\n}\n"], "mappings": ";AACA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,iBAAiB,QAAQ,cAAc;AAoDhD,OAAM,SAAUC,cAAcA,CAAA;EAAO,IAAAC,MAAA;OAAA,IAAAC,EAAA,IAAgB,EAAhBA,EAAA,GAAAC,SAAA,CAAAC,MAAgB,EAAhBF,EAAA,EAAgB;IAAhBD,MAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EACnC,IAAMG,OAAO,GAAGN,iBAAiB,CAACE,MAAM,CAAwC;EAEhF,OAAOP,OAAO,CAAC,UAACY,MAAM,EAAEC,UAAU;IAChC,IAAMC,GAAG,GAAGP,MAAM,CAACG,MAAM;IACzB,IAAMK,WAAW,GAAG,IAAIC,KAAK,CAACF,GAAG,CAAC;IAIlC,IAAIG,QAAQ,GAAGV,MAAM,CAACW,GAAG,CAAC;MAAM,YAAK;IAAL,CAAK,CAAC;IAGtC,IAAIC,KAAK,GAAG,KAAK;4BAMRC,CAAC;MACRlB,SAAS,CAACK,MAAM,CAACa,CAAC,CAAC,CAAC,CAACC,SAAS,CAC5BpB,wBAAwB,CACtBY,UAAU,EACV,UAACS,KAAK;QACJP,WAAW,CAACK,CAAC,CAAC,GAAGE,KAAK;QACtB,IAAI,CAACH,KAAK,IAAI,CAACF,QAAQ,CAACG,CAAC,CAAC,EAAE;UAE1BH,QAAQ,CAACG,CAAC,CAAC,GAAG,IAAI;UAKlB,CAACD,KAAK,GAAGF,QAAQ,CAACM,KAAK,CAACpB,QAAQ,CAAC,MAAMc,QAAQ,GAAG,IAAK,CAAC;;MAE5D,CAAC,EAGDb,IAAI,CACL,CACF;;IApBH,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,GAAG,EAAEM,CAAC,EAAE;cAAnBA,CAAC;;IAwBVR,MAAM,CAACS,SAAS,CACdpB,wBAAwB,CAACY,UAAU,EAAE,UAACS,KAAK;MACzC,IAAIH,KAAK,EAAE;QAET,IAAMK,MAAM,GAAAC,aAAA,EAAIH,KAAK,GAAAI,MAAA,CAAKX,WAAW,EAAC;QACtCF,UAAU,CAACc,IAAI,CAAChB,OAAO,GAAGA,OAAO,CAAAiB,KAAA,SAAAH,aAAA,KAAAC,MAAA,CAAIF,MAAM,MAAIA,MAAM,CAAC;;IAE1D,CAAC,CAAC,CACH;EACH,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}