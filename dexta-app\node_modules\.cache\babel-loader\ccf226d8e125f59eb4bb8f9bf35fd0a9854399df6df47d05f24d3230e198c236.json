{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"value\", \"components\", \"componentsProps\", \"component\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useSlotProps } from '../utils';\nimport composeClasses from '../composeClasses';\nimport { getTabPanelUnstyledUtilityClass } from './tabPanelUnstyledClasses';\nimport useTabPanel from './useTabPanel';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    hidden\n  } = ownerState;\n  const slots = {\n    root: ['root', hidden && 'hidden']\n  };\n  return composeClasses(slots, getTabPanelUnstyledUtilityClass, {});\n};\n/**\n *\n * Demos:\n *\n * - [Unstyled tabs](https://mui.com/base/react-tabs/)\n *\n * API:\n *\n * - [TabPanelUnstyled API](https://mui.com/base/api/tab-panel-unstyled/)\n */\n\nconst TabPanelUnstyled = /*#__PURE__*/React.forwardRef(function TabPanelUnstyled(props, ref) {\n  var _ref;\n  const {\n      children,\n      components = {},\n      componentsProps = {},\n      component\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    hidden,\n    getRootProps\n  } = useTabPanel(props);\n  const ownerState = _extends({}, props, {\n    hidden\n  });\n  const classes = useUtilityClasses(ownerState);\n  const TabPanelRoot = (_ref = component != null ? component : components.Root) != null ? _ref : 'div';\n  const tabPanelRootProps = useSlotProps({\n    elementType: TabPanelRoot,\n    getSlotProps: getRootProps,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      role: 'tabpanel',\n      ref\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(TabPanelRoot, _extends({}, tabPanelRootProps, {\n    children: !hidden && children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TabPanelUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the TabPanel.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The props used for each slot inside the TabPanel.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The value of the TabPanel. It will be shown when the Tab with the corresponding value is selected.\n   */\n  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired\n} : void 0;\nexport default TabPanelUnstyled;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "useSlotProps", "composeClasses", "getTabPanelUnstyledUtilityClass", "useTabPanel", "jsx", "_jsx", "useUtilityClasses", "ownerState", "hidden", "slots", "root", "TabPanelUnstyled", "forwardRef", "props", "ref", "_ref", "children", "components", "componentsProps", "component", "other", "getRootProps", "classes", "TabPanelRoot", "Root", "tabPanelRootProps", "elementType", "getSlotProps", "externalSlotProps", "externalForwardedProps", "additionalProps", "role", "className", "process", "env", "NODE_ENV", "propTypes", "node", "shape", "oneOfType", "func", "object", "value", "number", "string", "isRequired"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/TabPanelUnstyled/TabPanelUnstyled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"value\", \"components\", \"componentsProps\", \"component\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useSlotProps } from '../utils';\nimport composeClasses from '../composeClasses';\nimport { getTabPanelUnstyledUtilityClass } from './tabPanelUnstyledClasses';\nimport useTabPanel from './useTabPanel';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n\nconst useUtilityClasses = ownerState => {\n  const {\n    hidden\n  } = ownerState;\n  const slots = {\n    root: ['root', hidden && 'hidden']\n  };\n  return composeClasses(slots, getTabPanelUnstyledUtilityClass, {});\n};\n/**\n *\n * Demos:\n *\n * - [Unstyled tabs](https://mui.com/base/react-tabs/)\n *\n * API:\n *\n * - [TabPanelUnstyled API](https://mui.com/base/api/tab-panel-unstyled/)\n */\n\n\nconst TabPanelUnstyled = /*#__PURE__*/React.forwardRef(function TabPanelUnstyled(props, ref) {\n  var _ref;\n\n  const {\n    children,\n    components = {},\n    componentsProps = {},\n    component\n  } = props,\n        other = _objectWithoutPropertiesLoose(props, _excluded);\n\n  const {\n    hidden,\n    getRootProps\n  } = useTabPanel(props);\n\n  const ownerState = _extends({}, props, {\n    hidden\n  });\n\n  const classes = useUtilityClasses(ownerState);\n  const TabPanelRoot = (_ref = component != null ? component : components.Root) != null ? _ref : 'div';\n  const tabPanelRootProps = useSlotProps({\n    elementType: TabPanelRoot,\n    getSlotProps: getRootProps,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      role: 'tabpanel',\n      ref\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(TabPanelRoot, _extends({}, tabPanelRootProps, {\n    children: !hidden && children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TabPanelUnstyled.propTypes\n/* remove-proptypes */\n= {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n\n  /**\n   * The components used for each slot inside the TabPanel.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n\n  /**\n   * The props used for each slot inside the TabPanel.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n\n  /**\n   * The value of the TabPanel. It will be shown when the Tab with the corresponding value is selected.\n   */\n  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired\n} : void 0;\nexport default TabPanelUnstyled;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,YAAY,EAAE,iBAAiB,EAAE,WAAW,CAAC;AACrF,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,YAAY,QAAQ,UAAU;AACvC,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,SAASC,+BAA+B,QAAQ,2BAA2B;AAC3E,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAE/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,MAAM,IAAI,QAAQ;EACnC,CAAC;EACD,OAAOP,cAAc,CAACQ,KAAK,EAAEP,+BAA+B,EAAE,CAAC,CAAC,CAAC;AACnE,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,MAAMS,gBAAgB,GAAG,aAAab,KAAK,CAACc,UAAU,CAAC,SAASD,gBAAgBA,CAACE,KAAK,EAAEC,GAAG,EAAE;EAC3F,IAAIC,IAAI;EAER,MAAM;MACJC,QAAQ;MACRC,UAAU,GAAG,CAAC,CAAC;MACfC,eAAe,GAAG,CAAC,CAAC;MACpBC;IACF,CAAC,GAAGN,KAAK;IACHO,KAAK,GAAGxB,6BAA6B,CAACiB,KAAK,EAAEhB,SAAS,CAAC;EAE7D,MAAM;IACJW,MAAM;IACNa;EACF,CAAC,GAAGlB,WAAW,CAACU,KAAK,CAAC;EAEtB,MAAMN,UAAU,GAAGZ,QAAQ,CAAC,CAAC,CAAC,EAAEkB,KAAK,EAAE;IACrCL;EACF,CAAC,CAAC;EAEF,MAAMc,OAAO,GAAGhB,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMgB,YAAY,GAAG,CAACR,IAAI,GAAGI,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAGF,UAAU,CAACO,IAAI,KAAK,IAAI,GAAGT,IAAI,GAAG,KAAK;EACpG,MAAMU,iBAAiB,GAAGzB,YAAY,CAAC;IACrC0B,WAAW,EAAEH,YAAY;IACzBI,YAAY,EAAEN,YAAY;IAC1BO,iBAAiB,EAAEV,eAAe,CAACR,IAAI;IACvCmB,sBAAsB,EAAET,KAAK;IAC7BU,eAAe,EAAE;MACfC,IAAI,EAAE,UAAU;MAChBjB;IACF,CAAC;IACDP,UAAU;IACVyB,SAAS,EAAEV,OAAO,CAACZ;EACrB,CAAC,CAAC;EACF,OAAO,aAAaL,IAAI,CAACkB,YAAY,EAAE5B,QAAQ,CAAC,CAAC,CAAC,EAAE8B,iBAAiB,EAAE;IACrET,QAAQ,EAAE,CAACR,MAAM,IAAIQ;EACvB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFiB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxB,gBAAgB,CAACyB;AACzD,yBACE;EACA;EACA;EACA;EACA;;EAEA;AACF;AACA;EACEpB,QAAQ,EAAEjB,SAAS,CAACsC,IAAI;EAExB;AACF;AACA;AACA;EACElB,SAAS,EAAEpB,SAAS,CAAC2B,WAAW;EAEhC;AACF;AACA;AACA;AACA;EACET,UAAU,EAAElB,SAAS,CAACuC,KAAK,CAAC;IAC1Bd,IAAI,EAAEzB,SAAS,CAAC2B;EAClB,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACER,eAAe,EAAEnB,SAAS,CAACuC,KAAK,CAAC;IAC/B5B,IAAI,EAAEX,SAAS,CAACwC,SAAS,CAAC,CAACxC,SAAS,CAACyC,IAAI,EAAEzC,SAAS,CAAC0C,MAAM,CAAC;EAC9D,CAAC,CAAC;EAEF;AACF;AACA;EACEC,KAAK,EAAE3C,SAAS,CAACwC,SAAS,CAAC,CAACxC,SAAS,CAAC4C,MAAM,EAAE5C,SAAS,CAAC6C,MAAM,CAAC,CAAC,CAACC;AACnE,CAAC,GAAG,KAAK,CAAC;AACV,eAAelC,gBAAgB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}