{"ast": null, "code": "import getCountryByNationalNumber from './getCountryByNationalNumber.js';\nvar USE_NON_GEOGRAPHIC_COUNTRY_CODE = false;\nexport default function getCountryByCallingCode(callingCode, _ref) {\n  var nationalPhoneNumber = _ref.nationalNumber,\n    defaultCountry = _ref.defaultCountry,\n    metadata = _ref.metadata;\n\n  /* istanbul ignore if */\n  if (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\n    if (metadata.isNonGeographicCallingCode(callingCode)) {\n      return '001';\n    }\n  }\n  var possibleCountries = metadata.getCountryCodesForCallingCode(callingCode);\n  if (!possibleCountries) {\n    return;\n  } // If there's just one country corresponding to the country code,\n  // then just return it, without further phone number digits validation.\n\n  if (possibleCountries.length === 1) {\n    return possibleCountries[0];\n  }\n  return getCountryByNationalNumber(nationalPhoneNumber, {\n    countries: possibleCountries,\n    defaultCountry: defaultCountry,\n    metadata: metadata.metadata\n  });\n}", "map": {"version": 3, "names": ["getCountryByNationalNumber", "USE_NON_GEOGRAPHIC_COUNTRY_CODE", "getCountryByCallingCode", "callingCode", "_ref", "nationalPhoneNumber", "nationalNumber", "defaultCountry", "metadata", "isNonGeographicCallingCode", "possibleCountries", "getCountryCodesForCallingCode", "length", "countries"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\helpers\\getCountryByCallingCode.js"], "sourcesContent": ["import getCountryByNationalNumber from './getCountryByNationalNumber.js'\r\n\r\nconst USE_NON_GEOGRAPHIC_COUNTRY_CODE = false\r\n\r\nexport default function getCountryByCallingCode(callingCode, {\r\n\tnationalNumber: nationalPhoneNumber,\r\n\tdefaultCountry,\r\n\tmetadata\r\n}) {\r\n\t/* istanbul ignore if */\r\n\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\tif (metadata.isNonGeographicCallingCode(callingCode)) {\r\n\t\t\treturn '001'\r\n\t\t}\r\n\t}\r\n\tconst possibleCountries = metadata.getCountryCodesForCallingCode(callingCode)\r\n\tif (!possibleCountries) {\r\n\t\treturn\r\n\t}\r\n\t// If there's just one country corresponding to the country code,\r\n\t// then just return it, without further phone number digits validation.\r\n\tif (possibleCountries.length === 1) {\r\n\t\treturn possibleCountries[0]\r\n\t}\r\n\treturn getCountryByNationalNumber(nationalPhoneNumber, {\r\n\t\tcountries: possibleCountries,\r\n\t\tdefaultCountry,\r\n\t\tmetadata: metadata.metadata\r\n\t})\r\n}"], "mappings": "AAAA,OAAOA,0BAAP,MAAuC,iCAAvC;AAEA,IAAMC,+BAA+B,GAAG,KAAxC;AAEA,eAAe,SAASC,uBAATA,CAAiCC,WAAjC,EAAAC,IAAA,EAIZ;EAAA,IAHcC,mBAGd,GAAAD,IAAA,CAHFE,cAGE;IAFFC,cAEE,GAAAH,IAAA,CAFFG,cAEE;IADFC,QACE,GAAAJ,IAAA,CADFI,QACE;;EACF;EACA,IAAIP,+BAAJ,EAAqC;IACpC,IAAIO,QAAQ,CAACC,0BAAT,CAAoCN,WAApC,CAAJ,EAAsD;MACrD,OAAO,KAAP;IACA;EACD;EACD,IAAMO,iBAAiB,GAAGF,QAAQ,CAACG,6BAAT,CAAuCR,WAAvC,CAA1B;EACA,IAAI,CAACO,iBAAL,EAAwB;IACvB;EACA,CAVC,CAWF;EACA;;EACA,IAAIA,iBAAiB,CAACE,MAAlB,KAA6B,CAAjC,EAAoC;IACnC,OAAOF,iBAAiB,CAAC,CAAD,CAAxB;EACA;EACD,OAAOV,0BAA0B,CAACK,mBAAD,EAAsB;IACtDQ,SAAS,EAAEH,iBAD2C;IAEtDH,cAAc,EAAdA,cAFsD;IAGtDC,QAAQ,EAAEA,QAAQ,CAACA;EAHmC,CAAtB,CAAjC;AAKA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}