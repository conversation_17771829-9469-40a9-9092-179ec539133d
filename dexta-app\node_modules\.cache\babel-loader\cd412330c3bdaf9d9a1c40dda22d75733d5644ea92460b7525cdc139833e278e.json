{"ast": null, "code": "import * as React from 'react';\n\n// Constants for number words.\nvar units = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine', 'Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seven<PERSON>', 'Eighteen', 'Nineteen'];\n// Array of tens as words\nvar tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];\n// Array of scales as words\nvar scales = ['', 'Thousand', 'Million', 'Billion', 'Trillion', 'Quadrillion', 'Quintillion', 'Sextillion', 'Septillion', 'Octillion', 'Nonillion', 'Decillion', 'Undecillion', 'Duodecillion', 'Tredecillion', 'Quatttuor-decillion', 'Quindecillion', 'Sexdecillion', 'Septen-decillion', 'Octodecillion', 'Novemdecillion', 'Vigintillion', 'Centillion'];\n\n// Convert a number to words\nvar convertToWords = function (number, join) {\n  if (join === void 0) {\n    join = 'and';\n  }\n  var stringNumber = number.toString();\n  var start;\n  var end;\n  var chunks;\n  var chunksLen;\n  var chunk;\n  var ints;\n  var i;\n  var word;\n  var words = [];\n  // If the number is 0, return 'Zero'\n  if (parseInt(stringNumber) === 0) {\n    return 'Zero';\n  }\n  // Number upto 99 (0-99)\n  if (number < 100) {\n    if (number < 20) {\n      return units[number];\n    }\n    return tens[Math.floor(number / 10)] + (number % 10 ? '-' + units[number % 10] : '');\n  }\n  /* Split the number into chunks of 3 digits */\n  start = stringNumber.length;\n  chunks = [];\n  while (start > 0) {\n    end = start;\n    chunks.push(stringNumber.slice(start = Math.max(0, start - 3), end));\n  }\n  /* Check if the number is greater than the scales */\n  chunksLen = chunks.length;\n  if (chunksLen > scales.length) {\n    return '';\n  }\n  // Convert each chunk to words\n  words = [];\n  for (i = 0; i < chunksLen; i++) {\n    chunk = parseInt(chunks[i]);\n    if (chunk) {\n      // Split chunk into array of individual integers\n      ints = chunks[i].split('').reverse().map(parseFloat);\n      // If tens integer is 1, i.e. 10, then add 10 to units integer\n      if (ints[1] === 1) {\n        ints[0] += 10;\n      }\n      // Add scale word if chunk is not zero and array item exists\n      if (word = scales[i]) {\n        words.push(word);\n      }\n      // Add unit word if array item exists\n      if (word = units[ints[0]]) {\n        words.push(word);\n      }\n      // Add tens word if array item exists\n      if (word = tens[ints[1]]) {\n        words.push(word);\n      }\n      // Add 'join' value after the hundreds place\n      if (ints[0] || ints[1]) {\n        // Add 'join' value after the hundreds place\n        if (ints[2] || !i && chunksLen) {\n          words.push(join);\n        }\n      }\n      // Add hundreds word if array item exists\n      if (word = units[ints[2]]) {\n        words.push(word + ' Hundred');\n      }\n    }\n  }\n  return words.reverse().join(' ');\n};\nvar toOrdinal = function (number) {\n  var stringNumber = number.toString();\n  var lastTwoDigits = stringNumber.slice(-2);\n  var lastDigit = parseInt(lastTwoDigits.slice(-1));\n  var lastTwo = parseInt(lastTwoDigits);\n  var suffixes = ['th', 'st', 'nd', 'rd'];\n  if (lastTwo >= 11 && lastTwo <= 13) {\n    return \"\".concat(number, \"th\");\n  }\n  return \"\".concat(number).concat(suffixes[lastDigit] || 'th');\n};\nvar NumberInput = function (_a) {\n  var onChange = _a.onChange;\n  var _b = React.useState(''),\n    value = _b[0],\n    setValue = _b[1];\n  var handleChange = function (event) {\n    var newValue = event.target.value;\n    if (!isNaN(Number(newValue))) {\n      setValue(newValue);\n      onChange(Number(newValue));\n    }\n  };\n  return React.createElement(\"div\", null, React.createElement(\"label\", {\n    htmlFor: \"number\"\n  }, \"Enter an number:\"), React.createElement(\"input\", {\n    id: \"number\",\n    type: \"number\",\n    value: value,\n    onChange: handleChange,\n    style: {\n      display: 'block',\n      width: '100%',\n      padding: '10px',\n      fontSize: '16px',\n      marginTop: '10px'\n    }\n  }));\n};\nvar NumberDisplay = function (_a) {\n  var number = _a.number;\n  return React.createElement(\"div\", null, React.createElement(\"p\", null, \"Number entered: \", number), React.createElement(\"p\", null, \"Number in words: \", convertToWords(number)));\n};\nexport { NumberDisplay, NumberInput, convertToWords, toOrdinal };", "map": {"version": 3, "names": ["React", "units", "tens", "scales", "convertToWords", "number", "join", "stringNumber", "toString", "start", "end", "chunks", "chunksLen", "chunk", "ints", "i", "word", "words", "parseInt", "Math", "floor", "length", "push", "slice", "max", "split", "reverse", "map", "parseFloat", "toOrdinal", "lastTwoDigits", "lastDigit", "lastTwo", "suffixes", "concat", "NumberInput", "_a", "onChange", "_b", "useState", "value", "setValue", "handleChange", "event", "newValue", "target", "isNaN", "Number", "createElement", "htmlFor", "id", "type", "style", "display", "width", "padding", "fontSize", "marginTop", "NumberDisplay"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/react-number-to-words/dist/index.js"], "sourcesContent": ["import * as React from 'react';\n\n// Constants for number words.\nvar units = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine', 'Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seven<PERSON>', 'Eighteen', 'Nineteen'];\n// Array of tens as words\nvar tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];\n// Array of scales as words\nvar scales = ['', 'Thousand', 'Million', 'Billion', 'Trillion', 'Quadrillion', 'Quintillion', 'Sextillion', 'Septillion', 'Octillion', 'Nonillion', 'Decillion', 'Undecillion', 'Duodecillion', 'Tredecillion', 'Quatttuor-decillion', 'Quindecillion', 'Sexdecillion', 'Septen-decillion', 'Octodecillion', 'Novemdecillion', 'Vigintillion', 'Centillion'];\n\n// Convert a number to words\nvar convertToWords = function (number, join) {\n    if (join === void 0) { join = 'and'; }\n    var stringNumber = number.toString();\n    var start;\n    var end;\n    var chunks;\n    var chunksLen;\n    var chunk;\n    var ints;\n    var i;\n    var word;\n    var words = [];\n    // If the number is 0, return 'Zero'\n    if (parseInt(stringNumber) === 0) {\n        return 'Zero';\n    }\n    // Number upto 99 (0-99)\n    if (number < 100) {\n        if (number < 20) {\n            return units[number];\n        }\n        return tens[Math.floor(number / 10)] + (number % 10 ? '-' + units[number % 10] : '');\n    }\n    /* Split the number into chunks of 3 digits */\n    start = stringNumber.length;\n    chunks = [];\n    while (start > 0) {\n        end = start;\n        chunks.push(stringNumber.slice((start = Math.max(0, start - 3)), end));\n    }\n    /* Check if the number is greater than the scales */\n    chunksLen = chunks.length;\n    if (chunksLen > scales.length) {\n        return '';\n    }\n    // Convert each chunk to words\n    words = [];\n    for (i = 0; i < chunksLen; i++) {\n        chunk = parseInt(chunks[i]);\n        if (chunk) {\n            // Split chunk into array of individual integers\n            ints = chunks[i].split('').reverse().map(parseFloat);\n            // If tens integer is 1, i.e. 10, then add 10 to units integer\n            if (ints[1] === 1) {\n                ints[0] += 10;\n            }\n            // Add scale word if chunk is not zero and array item exists\n            if ((word = scales[i])) {\n                words.push(word);\n            }\n            // Add unit word if array item exists\n            if ((word = units[ints[0]])) {\n                words.push(word);\n            }\n            // Add tens word if array item exists\n            if ((word = tens[ints[1]])) {\n                words.push(word);\n            }\n            // Add 'join' value after the hundreds place\n            if (ints[0] || ints[1]) {\n                // Add 'join' value after the hundreds place\n                if (ints[2] || !i && chunksLen) {\n                    words.push(join);\n                }\n            }\n            // Add hundreds word if array item exists\n            if ((word = units[ints[2]])) {\n                words.push(word + ' Hundred');\n            }\n        }\n    }\n    return words.reverse().join(' ');\n};\n\nvar toOrdinal = function (number) {\n    var stringNumber = number.toString();\n    var lastTwoDigits = stringNumber.slice(-2);\n    var lastDigit = parseInt(lastTwoDigits.slice(-1));\n    var lastTwo = parseInt(lastTwoDigits);\n    var suffixes = ['th', 'st', 'nd', 'rd'];\n    if (lastTwo >= 11 && lastTwo <= 13) {\n        return \"\".concat(number, \"th\");\n    }\n    return \"\".concat(number).concat(suffixes[lastDigit] || 'th');\n};\n\nvar NumberInput = function (_a) {\n    var onChange = _a.onChange;\n    var _b = React.useState(''), value = _b[0], setValue = _b[1];\n    var handleChange = function (event) {\n        var newValue = event.target.value;\n        if (!isNaN(Number(newValue))) {\n            setValue(newValue);\n            onChange(Number(newValue));\n        }\n    };\n    return (React.createElement(\"div\", null,\n        React.createElement(\"label\", { htmlFor: \"number\" }, \"Enter an number:\"),\n        React.createElement(\"input\", { id: \"number\", type: \"number\", value: value, onChange: handleChange, style: {\n                display: 'block',\n                width: '100%',\n                padding: '10px',\n                fontSize: '16px',\n                marginTop: '10px',\n            } })));\n};\n\nvar NumberDisplay = function (_a) {\n    var number = _a.number;\n    return (React.createElement(\"div\", null,\n        React.createElement(\"p\", null,\n            \"Number entered: \",\n            number),\n        React.createElement(\"p\", null,\n            \"Number in words: \",\n            convertToWords(number))));\n};\n\nexport { NumberDisplay, NumberInput, convertToWords, toOrdinal };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA,IAAIC,KAAK,GAAG,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC;AACtM;AACA,IAAIC,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC;AACjG;AACA,IAAIC,MAAM,GAAG,CAAC,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,qBAAqB,EAAE,eAAe,EAAE,cAAc,EAAE,kBAAkB,EAAE,eAAe,EAAE,gBAAgB,EAAE,cAAc,EAAE,YAAY,CAAC;;AAE5V;AACA,IAAIC,cAAc,GAAG,SAAAA,CAAUC,MAAM,EAAEC,IAAI,EAAE;EACzC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAEA,IAAI,GAAG,KAAK;EAAE;EACrC,IAAIC,YAAY,GAAGF,MAAM,CAACG,QAAQ,CAAC,CAAC;EACpC,IAAIC,KAAK;EACT,IAAIC,GAAG;EACP,IAAIC,MAAM;EACV,IAAIC,SAAS;EACb,IAAIC,KAAK;EACT,IAAIC,IAAI;EACR,IAAIC,CAAC;EACL,IAAIC,IAAI;EACR,IAAIC,KAAK,GAAG,EAAE;EACd;EACA,IAAIC,QAAQ,CAACX,YAAY,CAAC,KAAK,CAAC,EAAE;IAC9B,OAAO,MAAM;EACjB;EACA;EACA,IAAIF,MAAM,GAAG,GAAG,EAAE;IACd,IAAIA,MAAM,GAAG,EAAE,EAAE;MACb,OAAOJ,KAAK,CAACI,MAAM,CAAC;IACxB;IACA,OAAOH,IAAI,CAACiB,IAAI,CAACC,KAAK,CAACf,MAAM,GAAG,EAAE,CAAC,CAAC,IAAIA,MAAM,GAAG,EAAE,GAAG,GAAG,GAAGJ,KAAK,CAACI,MAAM,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;EACxF;EACA;EACAI,KAAK,GAAGF,YAAY,CAACc,MAAM;EAC3BV,MAAM,GAAG,EAAE;EACX,OAAOF,KAAK,GAAG,CAAC,EAAE;IACdC,GAAG,GAAGD,KAAK;IACXE,MAAM,CAACW,IAAI,CAACf,YAAY,CAACgB,KAAK,CAAEd,KAAK,GAAGU,IAAI,CAACK,GAAG,CAAC,CAAC,EAAEf,KAAK,GAAG,CAAC,CAAC,EAAGC,GAAG,CAAC,CAAC;EAC1E;EACA;EACAE,SAAS,GAAGD,MAAM,CAACU,MAAM;EACzB,IAAIT,SAAS,GAAGT,MAAM,CAACkB,MAAM,EAAE;IAC3B,OAAO,EAAE;EACb;EACA;EACAJ,KAAK,GAAG,EAAE;EACV,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,EAAEG,CAAC,EAAE,EAAE;IAC5BF,KAAK,GAAGK,QAAQ,CAACP,MAAM,CAACI,CAAC,CAAC,CAAC;IAC3B,IAAIF,KAAK,EAAE;MACP;MACAC,IAAI,GAAGH,MAAM,CAACI,CAAC,CAAC,CAACU,KAAK,CAAC,EAAE,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,GAAG,CAACC,UAAU,CAAC;MACpD;MACA,IAAId,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACfA,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;MACjB;MACA;MACA,IAAKE,IAAI,GAAGb,MAAM,CAACY,CAAC,CAAC,EAAG;QACpBE,KAAK,CAACK,IAAI,CAACN,IAAI,CAAC;MACpB;MACA;MACA,IAAKA,IAAI,GAAGf,KAAK,CAACa,IAAI,CAAC,CAAC,CAAC,CAAC,EAAG;QACzBG,KAAK,CAACK,IAAI,CAACN,IAAI,CAAC;MACpB;MACA;MACA,IAAKA,IAAI,GAAGd,IAAI,CAACY,IAAI,CAAC,CAAC,CAAC,CAAC,EAAG;QACxBG,KAAK,CAACK,IAAI,CAACN,IAAI,CAAC;MACpB;MACA;MACA,IAAIF,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE;QACpB;QACA,IAAIA,IAAI,CAAC,CAAC,CAAC,IAAI,CAACC,CAAC,IAAIH,SAAS,EAAE;UAC5BK,KAAK,CAACK,IAAI,CAAChB,IAAI,CAAC;QACpB;MACJ;MACA;MACA,IAAKU,IAAI,GAAGf,KAAK,CAACa,IAAI,CAAC,CAAC,CAAC,CAAC,EAAG;QACzBG,KAAK,CAACK,IAAI,CAACN,IAAI,GAAG,UAAU,CAAC;MACjC;IACJ;EACJ;EACA,OAAOC,KAAK,CAACS,OAAO,CAAC,CAAC,CAACpB,IAAI,CAAC,GAAG,CAAC;AACpC,CAAC;AAED,IAAIuB,SAAS,GAAG,SAAAA,CAAUxB,MAAM,EAAE;EAC9B,IAAIE,YAAY,GAAGF,MAAM,CAACG,QAAQ,CAAC,CAAC;EACpC,IAAIsB,aAAa,GAAGvB,YAAY,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAC;EAC1C,IAAIQ,SAAS,GAAGb,QAAQ,CAACY,aAAa,CAACP,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD,IAAIS,OAAO,GAAGd,QAAQ,CAACY,aAAa,CAAC;EACrC,IAAIG,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACvC,IAAID,OAAO,IAAI,EAAE,IAAIA,OAAO,IAAI,EAAE,EAAE;IAChC,OAAO,EAAE,CAACE,MAAM,CAAC7B,MAAM,EAAE,IAAI,CAAC;EAClC;EACA,OAAO,EAAE,CAAC6B,MAAM,CAAC7B,MAAM,CAAC,CAAC6B,MAAM,CAACD,QAAQ,CAACF,SAAS,CAAC,IAAI,IAAI,CAAC;AAChE,CAAC;AAED,IAAII,WAAW,GAAG,SAAAA,CAAUC,EAAE,EAAE;EAC5B,IAAIC,QAAQ,GAAGD,EAAE,CAACC,QAAQ;EAC1B,IAAIC,EAAE,GAAGtC,KAAK,CAACuC,QAAQ,CAAC,EAAE,CAAC;IAAEC,KAAK,GAAGF,EAAE,CAAC,CAAC,CAAC;IAAEG,QAAQ,GAAGH,EAAE,CAAC,CAAC,CAAC;EAC5D,IAAII,YAAY,GAAG,SAAAA,CAAUC,KAAK,EAAE;IAChC,IAAIC,QAAQ,GAAGD,KAAK,CAACE,MAAM,CAACL,KAAK;IACjC,IAAI,CAACM,KAAK,CAACC,MAAM,CAACH,QAAQ,CAAC,CAAC,EAAE;MAC1BH,QAAQ,CAACG,QAAQ,CAAC;MAClBP,QAAQ,CAACU,MAAM,CAACH,QAAQ,CAAC,CAAC;IAC9B;EACJ,CAAC;EACD,OAAQ5C,KAAK,CAACgD,aAAa,CAAC,KAAK,EAAE,IAAI,EACnChD,KAAK,CAACgD,aAAa,CAAC,OAAO,EAAE;IAAEC,OAAO,EAAE;EAAS,CAAC,EAAE,kBAAkB,CAAC,EACvEjD,KAAK,CAACgD,aAAa,CAAC,OAAO,EAAE;IAAEE,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE,QAAQ;IAAEX,KAAK,EAAEA,KAAK;IAAEH,QAAQ,EAAEK,YAAY;IAAEU,KAAK,EAAE;MAClGC,OAAO,EAAE,OAAO;MAChBC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAE,MAAM;MACfC,QAAQ,EAAE,MAAM;MAChBC,SAAS,EAAE;IACf;EAAE,CAAC,CAAC,CAAC;AACjB,CAAC;AAED,IAAIC,aAAa,GAAG,SAAAA,CAAUtB,EAAE,EAAE;EAC9B,IAAI/B,MAAM,GAAG+B,EAAE,CAAC/B,MAAM;EACtB,OAAQL,KAAK,CAACgD,aAAa,CAAC,KAAK,EAAE,IAAI,EACnChD,KAAK,CAACgD,aAAa,CAAC,GAAG,EAAE,IAAI,EACzB,kBAAkB,EAClB3C,MAAM,CAAC,EACXL,KAAK,CAACgD,aAAa,CAAC,GAAG,EAAE,IAAI,EACzB,mBAAmB,EACnB5C,cAAc,CAACC,MAAM,CAAC,CAAC,CAAC;AACpC,CAAC;AAED,SAASqD,aAAa,EAAEvB,WAAW,EAAE/B,cAAc,EAAEyB,SAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}