{"ast": null, "code": "import { isFunction } from \"./isFunction\";\nexport function isPromise(value) {\n  return isFunction(value === null || value === void 0 ? void 0 : value.then);\n}", "map": {"version": 3, "names": ["isFunction", "isPromise", "value", "then"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\util\\isPromise.ts"], "sourcesContent": ["import { isFunction } from \"./isFunction\";\n\n/**\n * Tests to see if the object is \"thennable\".\n * @param value the object to test\n */\nexport function isPromise(value: any): value is PromiseLike<any> {\n  return isFunction(value?.then);\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AAMzC,OAAM,SAAUC,SAASA,CAACC,KAAU;EAClC,OAAOF,UAAU,CAACE,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEC,IAAI,CAAC;AAChC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}