{"ast": null, "code": "/**\n * Copyright (c) 2015, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n * @providesModule ExecutionEnvironment\n */\n\n/*jslint evil: true */\n\n'use strict';\n\nvar canUseDOM = !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n\n/**\n * Simple, lightweight module assisting with the detection and context of\n * Worker. Helps avoid circular dependencies and allows code to reason about\n * whether or not they are in a Worker, even if they never include the main\n * `ReactWorker` dependency.\n */\nvar ExecutionEnvironment = {\n  canUseDOM: canUseDOM,\n  canUseWorkers: typeof Worker !== 'undefined',\n  canUseEventListeners: canUseDOM && !!(window.addEventListener || window.attachEvent),\n  canUseViewport: canUseDOM && !!window.screen,\n  isInWorker: !canUseDOM // For now, this is true - might change in the future.\n};\n\nmodule.exports = ExecutionEnvironment;", "map": {"version": 3, "names": ["canUseDOM", "window", "document", "createElement", "ExecutionEnvironment", "canUseWorkers", "Worker", "canUseEventListeners", "addEventListener", "attachEvent", "canUseViewport", "screen", "isInWorker", "module", "exports"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/normalize-wheel/src/ExecutionEnvironment.js"], "sourcesContent": ["/**\n * Copyright (c) 2015, Facebook, Inc.\n * All rights reserved.\n *\n * This source code is licensed under the BSD-style license found in the\n * LICENSE file in the root directory of this source tree. An additional grant\n * of patent rights can be found in the PATENTS file in the same directory.\n *\n * @providesModule ExecutionEnvironment\n */\n\n/*jslint evil: true */\n\n'use strict';\n\nvar canUseDOM = !!(\n  typeof window !== 'undefined' &&\n  window.document &&\n  window.document.createElement\n);\n\n/**\n * Simple, lightweight module assisting with the detection and context of\n * Worker. Helps avoid circular dependencies and allows code to reason about\n * whether or not they are in a Worker, even if they never include the main\n * `ReactWorker` dependency.\n */\nvar ExecutionEnvironment = {\n\n  canUseDOM: canUseDOM,\n\n  canUseWorkers: typeof Worker !== 'undefined',\n\n  canUseEventListeners:\n    canUseDOM && !!(window.addEventListener || window.attachEvent),\n\n  canUseViewport: canUseDOM && !!window.screen,\n\n  isInWorker: !canUseDOM // For now, this is true - might change in the future.\n\n};\n\nmodule.exports = ExecutionEnvironment;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA,YAAY;;AAEZ,IAAIA,SAAS,GAAG,CAAC,EACf,OAAOC,MAAM,KAAK,WAAW,IAC7BA,MAAM,CAACC,QAAQ,IACfD,MAAM,CAACC,QAAQ,CAACC,aAAa,CAC9B;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,oBAAoB,GAAG;EAEzBJ,SAAS,EAAEA,SAAS;EAEpBK,aAAa,EAAE,OAAOC,MAAM,KAAK,WAAW;EAE5CC,oBAAoB,EAClBP,SAAS,IAAI,CAAC,EAAEC,MAAM,CAACO,gBAAgB,IAAIP,MAAM,CAACQ,WAAW,CAAC;EAEhEC,cAAc,EAAEV,SAAS,IAAI,CAAC,CAACC,MAAM,CAACU,MAAM;EAE5CC,UAAU,EAAE,CAACZ,SAAS,CAAC;AAEzB,CAAC;;AAEDa,MAAM,CAACC,OAAO,GAAGV,oBAAoB"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}