{"ast": null, "code": "import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getButtonUnstyledUtilityClass(slot) {\n  return generateUtilityClass('ButtonUnstyled', slot);\n}\nconst buttonUnstyledClasses = generateUtilityClasses('ButtonUnstyled', ['root', 'active', 'disabled', 'focusVisible']);\nexport default buttonUnstyledClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getButtonUnstyledUtilityClass", "slot", "buttonUnstyledClasses"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/ButtonUnstyled/buttonUnstyledClasses.js"], "sourcesContent": ["import generateUtilityClass from '../generateUtilityClass';\nimport generateUtilityClasses from '../generateUtilityClasses';\nexport function getButtonUnstyledUtilityClass(slot) {\n  return generateUtilityClass('ButtonUnstyled', slot);\n}\nconst buttonUnstyledClasses = generateUtilityClasses('ButtonUnstyled', ['root', 'active', 'disabled', 'focusVisible']);\nexport default buttonUnstyledClasses;"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,yBAAyB;AAC1D,OAAOC,sBAAsB,MAAM,2BAA2B;AAC9D,OAAO,SAASC,6BAA6BA,CAACC,IAAI,EAAE;EAClD,OAAOH,oBAAoB,CAAC,gBAAgB,EAAEG,IAAI,CAAC;AACrD;AACA,MAAMC,qBAAqB,GAAGH,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC;AACtH,eAAeG,qBAAqB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}