{"ast": null, "code": "var clean = require('to-no-case');\n\n/**\n * Export.\n */\n\nmodule.exports = toSpaceCase;\n\n/**\n * Convert a `string` to space case.\n *\n * @param {String} string\n * @return {String}\n */\n\nfunction toSpaceCase(string) {\n  return clean(string).replace(/[\\W_]+(.|$)/g, function (matches, match) {\n    return match ? ' ' + match : '';\n  }).trim();\n}", "map": {"version": 3, "names": ["clean", "require", "module", "exports", "toSpaceCase", "string", "replace", "matches", "match", "trim"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/to-space-case/index.js"], "sourcesContent": ["\nvar clean = require('to-no-case')\n\n/**\n * Export.\n */\n\nmodule.exports = toSpaceCase\n\n/**\n * Convert a `string` to space case.\n *\n * @param {String} string\n * @return {String}\n */\n\nfunction toSpaceCase(string) {\n  return clean(string).replace(/[\\W_]+(.|$)/g, function (matches, match) {\n    return match ? ' ' + match : ''\n  }).trim()\n}\n"], "mappings": "AACA,IAAIA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;;AAEjC;AACA;AACA;;AAEAC,MAAM,CAACC,OAAO,GAAGC,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,WAAWA,CAACC,MAAM,EAAE;EAC3B,OAAOL,KAAK,CAACK,MAAM,CAAC,CAACC,OAAO,CAAC,cAAc,EAAE,UAAUC,OAAO,EAAEC,KAAK,EAAE;IACrE,OAAOA,KAAK,GAAG,GAAG,GAAGA,KAAK,GAAG,EAAE;EACjC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;AACX"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}