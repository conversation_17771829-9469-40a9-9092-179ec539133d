{"ast": null, "code": "import { Observable } from '../Observable';\nexport function scheduleArray(input, scheduler) {\n  return new Observable(function (subscriber) {\n    var i = 0;\n    return scheduler.schedule(function () {\n      if (i === input.length) {\n        subscriber.complete();\n      } else {\n        subscriber.next(input[i++]);\n        if (!subscriber.closed) {\n          this.schedule();\n        }\n      }\n    });\n  });\n}", "map": {"version": 3, "names": ["Observable", "scheduleArray", "input", "scheduler", "subscriber", "i", "schedule", "length", "complete", "next", "closed"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\scheduled\\scheduleArray.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { SchedulerLike } from '../types';\n\nexport function scheduleArray<T>(input: ArrayLike<T>, scheduler: SchedulerLike) {\n  return new Observable<T>((subscriber) => {\n    // The current array index.\n    let i = 0;\n    // Start iterating over the array like on a schedule.\n    return scheduler.schedule(function () {\n      if (i === input.length) {\n        // If we have hit the end of the array like in the\n        // previous job, we can complete.\n        subscriber.complete();\n      } else {\n        // Otherwise let's next the value at the current index,\n        // then increment our index.\n        subscriber.next(input[i++]);\n        // If the last emission didn't cause us to close the subscriber\n        // (via take or some side effect), reschedule the job and we'll\n        // make another pass.\n        if (!subscriber.closed) {\n          this.schedule();\n        }\n      }\n    });\n  });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAG1C,OAAM,SAAUC,aAAaA,CAAIC,KAAmB,EAAEC,SAAwB;EAC5E,OAAO,IAAIH,UAAU,CAAI,UAACI,UAAU;IAElC,IAAIC,CAAC,GAAG,CAAC;IAET,OAAOF,SAAS,CAACG,QAAQ,CAAC;MACxB,IAAID,CAAC,KAAKH,KAAK,CAACK,MAAM,EAAE;QAGtBH,UAAU,CAACI,QAAQ,EAAE;OACtB,MAAM;QAGLJ,UAAU,CAACK,IAAI,CAACP,KAAK,CAACG,CAAC,EAAE,CAAC,CAAC;QAI3B,IAAI,CAACD,UAAU,CAACM,MAAM,EAAE;UACtB,IAAI,CAACJ,QAAQ,EAAE;;;IAGrB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}