{"ast": null, "code": "import { padStart, toHexString, toBinaryString } from './byte-helpers.js'; // https://aomediacodec.github.io/av1-isobmff/#av1codecconfigurationbox-syntax\n// https://developer.mozilla.org/en-US/docs/Web/Media/Formats/codecs_parameter#AV1\n\nexport var getAv1Codec = function getAv1Codec(bytes) {\n  var codec = '';\n  var profile = bytes[1] >>> 3;\n  var level = bytes[1] & 0x1F;\n  var tier = bytes[2] >>> 7;\n  var highBitDepth = (bytes[2] & 0x40) >> 6;\n  var twelveBit = (bytes[2] & 0x20) >> 5;\n  var monochrome = (bytes[2] & 0x10) >> 4;\n  var chromaSubsamplingX = (bytes[2] & 0x08) >> 3;\n  var chromaSubsamplingY = (bytes[2] & 0x04) >> 2;\n  var chromaSamplePosition = bytes[2] & 0x03;\n  codec += profile + \".\" + padStart(level, 2, '0');\n  if (tier === 0) {\n    codec += 'M';\n  } else if (tier === 1) {\n    codec += 'H';\n  }\n  var bitDepth;\n  if (profile === 2 && highBitDepth) {\n    bitDepth = twelveBit ? 12 : 10;\n  } else {\n    bitDepth = highBitDepth ? 10 : 8;\n  }\n  codec += \".\" + padStart(bitDepth, 2, '0'); // TODO: can we parse color range??\n\n  codec += \".\" + monochrome;\n  codec += \".\" + chromaSubsamplingX + chromaSubsamplingY + chromaSamplePosition;\n  return codec;\n};\nexport var getAvcCodec = function getAvcCodec(bytes) {\n  var profileId = toHexString(bytes[1]);\n  var constraintFlags = toHexString(bytes[2] & 0xFC);\n  var levelId = toHexString(bytes[3]);\n  return \"\" + profileId + constraintFlags + levelId;\n};\nexport var getHvcCodec = function getHvcCodec(bytes) {\n  var codec = '';\n  var profileSpace = bytes[1] >> 6;\n  var profileId = bytes[1] & 0x1F;\n  var tierFlag = (bytes[1] & 0x20) >> 5;\n  var profileCompat = bytes.subarray(2, 6);\n  var constraintIds = bytes.subarray(6, 12);\n  var levelId = bytes[12];\n  if (profileSpace === 1) {\n    codec += 'A';\n  } else if (profileSpace === 2) {\n    codec += 'B';\n  } else if (profileSpace === 3) {\n    codec += 'C';\n  }\n  codec += profileId + \".\"; // ffmpeg does this in big endian\n\n  var profileCompatVal = parseInt(toBinaryString(profileCompat).split('').reverse().join(''), 2); // apple does this in little endian...\n\n  if (profileCompatVal > 255) {\n    profileCompatVal = parseInt(toBinaryString(profileCompat), 2);\n  }\n  codec += profileCompatVal.toString(16) + \".\";\n  if (tierFlag === 0) {\n    codec += 'L';\n  } else {\n    codec += 'H';\n  }\n  codec += levelId;\n  var constraints = '';\n  for (var i = 0; i < constraintIds.length; i++) {\n    var v = constraintIds[i];\n    if (v) {\n      if (constraints) {\n        constraints += '.';\n      }\n      constraints += v.toString(16);\n    }\n  }\n  if (constraints) {\n    codec += \".\" + constraints;\n  }\n  return codec;\n};", "map": {"version": 3, "names": ["padStart", "toHexString", "toBinaryString", "getAv1Codec", "bytes", "codec", "profile", "level", "tier", "highBitDepth", "twelveBit", "monochrome", "chromaSubsamplingX", "chromaSubsamplingY", "chromaSamplePosition", "bitDepth", "getAvcCodec", "profileId", "constraintFlags", "levelId", "getHvcCodec", "profileSpace", "tierFlag", "profileCompat", "subarray", "constraintIds", "profileCompatVal", "parseInt", "split", "reverse", "join", "toString", "constraints", "i", "length", "v"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@videojs/vhs-utils/es/codec-helpers.js"], "sourcesContent": ["import { padStart, toHexString, toBinaryString } from './byte-helpers.js'; // https://aomediacodec.github.io/av1-isobmff/#av1codecconfigurationbox-syntax\n// https://developer.mozilla.org/en-US/docs/Web/Media/Formats/codecs_parameter#AV1\n\nexport var getAv1Codec = function getAv1Codec(bytes) {\n  var codec = '';\n  var profile = bytes[1] >>> 3;\n  var level = bytes[1] & 0x1F;\n  var tier = bytes[2] >>> 7;\n  var highBitDepth = (bytes[2] & 0x40) >> 6;\n  var twelveBit = (bytes[2] & 0x20) >> 5;\n  var monochrome = (bytes[2] & 0x10) >> 4;\n  var chromaSubsamplingX = (bytes[2] & 0x08) >> 3;\n  var chromaSubsamplingY = (bytes[2] & 0x04) >> 2;\n  var chromaSamplePosition = bytes[2] & 0x03;\n  codec += profile + \".\" + padStart(level, 2, '0');\n\n  if (tier === 0) {\n    codec += 'M';\n  } else if (tier === 1) {\n    codec += 'H';\n  }\n\n  var bitDepth;\n\n  if (profile === 2 && highBitDepth) {\n    bitDepth = twelveBit ? 12 : 10;\n  } else {\n    bitDepth = highBitDepth ? 10 : 8;\n  }\n\n  codec += \".\" + padStart(bitDepth, 2, '0'); // TODO: can we parse color range??\n\n  codec += \".\" + monochrome;\n  codec += \".\" + chromaSubsamplingX + chromaSubsamplingY + chromaSamplePosition;\n  return codec;\n};\nexport var getAvcCodec = function getAvcCodec(bytes) {\n  var profileId = toHexString(bytes[1]);\n  var constraintFlags = toHexString(bytes[2] & 0xFC);\n  var levelId = toHexString(bytes[3]);\n  return \"\" + profileId + constraintFlags + levelId;\n};\nexport var getHvcCodec = function getHvcCodec(bytes) {\n  var codec = '';\n  var profileSpace = bytes[1] >> 6;\n  var profileId = bytes[1] & 0x1F;\n  var tierFlag = (bytes[1] & 0x20) >> 5;\n  var profileCompat = bytes.subarray(2, 6);\n  var constraintIds = bytes.subarray(6, 12);\n  var levelId = bytes[12];\n\n  if (profileSpace === 1) {\n    codec += 'A';\n  } else if (profileSpace === 2) {\n    codec += 'B';\n  } else if (profileSpace === 3) {\n    codec += 'C';\n  }\n\n  codec += profileId + \".\"; // ffmpeg does this in big endian\n\n  var profileCompatVal = parseInt(toBinaryString(profileCompat).split('').reverse().join(''), 2); // apple does this in little endian...\n\n  if (profileCompatVal > 255) {\n    profileCompatVal = parseInt(toBinaryString(profileCompat), 2);\n  }\n\n  codec += profileCompatVal.toString(16) + \".\";\n\n  if (tierFlag === 0) {\n    codec += 'L';\n  } else {\n    codec += 'H';\n  }\n\n  codec += levelId;\n  var constraints = '';\n\n  for (var i = 0; i < constraintIds.length; i++) {\n    var v = constraintIds[i];\n\n    if (v) {\n      if (constraints) {\n        constraints += '.';\n      }\n\n      constraints += v.toString(16);\n    }\n  }\n\n  if (constraints) {\n    codec += \".\" + constraints;\n  }\n\n  return codec;\n};"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,WAAW,EAAEC,cAAc,QAAQ,mBAAmB,CAAC,CAAC;AAC3E;;AAEA,OAAO,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAE;EACnD,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIC,OAAO,GAAGF,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;EAC5B,IAAIG,KAAK,GAAGH,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;EAC3B,IAAII,IAAI,GAAGJ,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;EACzB,IAAIK,YAAY,GAAG,CAACL,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;EACzC,IAAIM,SAAS,GAAG,CAACN,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;EACtC,IAAIO,UAAU,GAAG,CAACP,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;EACvC,IAAIQ,kBAAkB,GAAG,CAACR,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;EAC/C,IAAIS,kBAAkB,GAAG,CAACT,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;EAC/C,IAAIU,oBAAoB,GAAGV,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;EAC1CC,KAAK,IAAIC,OAAO,GAAG,GAAG,GAAGN,QAAQ,CAACO,KAAK,EAAE,CAAC,EAAE,GAAG,CAAC;EAEhD,IAAIC,IAAI,KAAK,CAAC,EAAE;IACdH,KAAK,IAAI,GAAG;EACd,CAAC,MAAM,IAAIG,IAAI,KAAK,CAAC,EAAE;IACrBH,KAAK,IAAI,GAAG;EACd;EAEA,IAAIU,QAAQ;EAEZ,IAAIT,OAAO,KAAK,CAAC,IAAIG,YAAY,EAAE;IACjCM,QAAQ,GAAGL,SAAS,GAAG,EAAE,GAAG,EAAE;EAChC,CAAC,MAAM;IACLK,QAAQ,GAAGN,YAAY,GAAG,EAAE,GAAG,CAAC;EAClC;EAEAJ,KAAK,IAAI,GAAG,GAAGL,QAAQ,CAACe,QAAQ,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;EAE3CV,KAAK,IAAI,GAAG,GAAGM,UAAU;EACzBN,KAAK,IAAI,GAAG,GAAGO,kBAAkB,GAAGC,kBAAkB,GAAGC,oBAAoB;EAC7E,OAAOT,KAAK;AACd,CAAC;AACD,OAAO,IAAIW,WAAW,GAAG,SAASA,WAAWA,CAACZ,KAAK,EAAE;EACnD,IAAIa,SAAS,GAAGhB,WAAW,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;EACrC,IAAIc,eAAe,GAAGjB,WAAW,CAACG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;EAClD,IAAIe,OAAO,GAAGlB,WAAW,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;EACnC,OAAO,EAAE,GAAGa,SAAS,GAAGC,eAAe,GAAGC,OAAO;AACnD,CAAC;AACD,OAAO,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAAChB,KAAK,EAAE;EACnD,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIgB,YAAY,GAAGjB,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;EAChC,IAAIa,SAAS,GAAGb,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;EAC/B,IAAIkB,QAAQ,GAAG,CAAClB,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;EACrC,IAAImB,aAAa,GAAGnB,KAAK,CAACoB,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACxC,IAAIC,aAAa,GAAGrB,KAAK,CAACoB,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC;EACzC,IAAIL,OAAO,GAAGf,KAAK,CAAC,EAAE,CAAC;EAEvB,IAAIiB,YAAY,KAAK,CAAC,EAAE;IACtBhB,KAAK,IAAI,GAAG;EACd,CAAC,MAAM,IAAIgB,YAAY,KAAK,CAAC,EAAE;IAC7BhB,KAAK,IAAI,GAAG;EACd,CAAC,MAAM,IAAIgB,YAAY,KAAK,CAAC,EAAE;IAC7BhB,KAAK,IAAI,GAAG;EACd;EAEAA,KAAK,IAAIY,SAAS,GAAG,GAAG,CAAC,CAAC;;EAE1B,IAAIS,gBAAgB,GAAGC,QAAQ,CAACzB,cAAc,CAACqB,aAAa,CAAC,CAACK,KAAK,CAAC,EAAE,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAEhG,IAAIJ,gBAAgB,GAAG,GAAG,EAAE;IAC1BA,gBAAgB,GAAGC,QAAQ,CAACzB,cAAc,CAACqB,aAAa,CAAC,EAAE,CAAC,CAAC;EAC/D;EAEAlB,KAAK,IAAIqB,gBAAgB,CAACK,QAAQ,CAAC,EAAE,CAAC,GAAG,GAAG;EAE5C,IAAIT,QAAQ,KAAK,CAAC,EAAE;IAClBjB,KAAK,IAAI,GAAG;EACd,CAAC,MAAM;IACLA,KAAK,IAAI,GAAG;EACd;EAEAA,KAAK,IAAIc,OAAO;EAChB,IAAIa,WAAW,GAAG,EAAE;EAEpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,aAAa,CAACS,MAAM,EAAED,CAAC,EAAE,EAAE;IAC7C,IAAIE,CAAC,GAAGV,aAAa,CAACQ,CAAC,CAAC;IAExB,IAAIE,CAAC,EAAE;MACL,IAAIH,WAAW,EAAE;QACfA,WAAW,IAAI,GAAG;MACpB;MAEAA,WAAW,IAAIG,CAAC,CAACJ,QAAQ,CAAC,EAAE,CAAC;IAC/B;EACF;EAEA,IAAIC,WAAW,EAAE;IACf3B,KAAK,IAAI,GAAG,GAAG2B,WAAW;EAC5B;EAEA,OAAO3B,KAAK;AACd,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}