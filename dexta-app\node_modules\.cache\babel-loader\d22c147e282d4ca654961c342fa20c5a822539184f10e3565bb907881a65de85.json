{"ast": null, "code": "import { Subject } from '../Subject';\nimport { multicast } from './multicast';\nimport { connect } from './connect';\nexport function publish(selector) {\n  return selector ? function (source) {\n    return connect(selector)(source);\n  } : function (source) {\n    return multicast(new Subject())(source);\n  };\n}", "map": {"version": 3, "names": ["Subject", "multicast", "connect", "publish", "selector", "source"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\publish.ts"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { Subject } from '../Subject';\nimport { multicast } from './multicast';\nimport { ConnectableObservable } from '../observable/ConnectableObservable';\nimport { MonoTypeOperatorFunction, OperatorFunction, UnaryFunction, ObservableInput, ObservedValueOf } from '../types';\nimport { connect } from './connect';\n\n/**\n * Returns a connectable observable that, when connected, will multicast\n * all values through a single underlying {@link Subject} instance.\n *\n * @deprecated Will be removed in v8. To create a connectable observable, use {@link connectable}.\n * `source.pipe(publish())` is equivalent to\n * `connectable(source, { connector: () => new Subject(), resetOnDisconnect: false })`.\n * If you're using {@link refCount} after `publish`, use {@link share} operator instead.\n * `source.pipe(publish(), refCount())` is equivalent to\n * `source.pipe(share({ resetOnError: false, resetOnComplete: false, resetOnRefCountZero: false }))`.\n * Details: https://rxjs.dev/deprecations/multicasting\n */\nexport function publish<T>(): UnaryFunction<Observable<T>, ConnectableObservable<T>>;\n\n/**\n * Returns an observable, that when subscribed to, creates an underlying {@link Subject},\n * provides an observable view of it to a `selector` function, takes the observable result of\n * that selector function and subscribes to it, sending its values to the consumer, _then_ connects\n * the subject to the original source.\n *\n * @param selector A function used to setup multicasting prior to automatic connection.\n *\n * @deprecated Will be removed in v8. Use the {@link connect} operator instead.\n * `publish(selector)` is equivalent to `connect(selector)`.\n * Details: https://rxjs.dev/deprecations/multicasting\n */\nexport function publish<T, O extends ObservableInput<any>>(selector: (shared: Observable<T>) => O): OperatorFunction<T, ObservedValueOf<O>>;\n\n/**\n * Returns a ConnectableObservable, which is a variety of Observable that waits until its connect method is called\n * before it begins emitting items to those Observers that have subscribed to it.\n *\n * <span class=\"informal\">Makes a cold Observable hot</span>\n *\n * ![](publish.png)\n *\n * ## Examples\n *\n * Make `source$` hot by applying `publish` operator, then merge each inner observable into a single one\n * and subscribe\n *\n * ```ts\n * import { zip, interval, of, map, publish, merge, tap } from 'rxjs';\n *\n * const source$ = zip(interval(2000), of(1, 2, 3, 4, 5, 6, 7, 8, 9))\n *   .pipe(map(([, number]) => number));\n *\n * source$\n *   .pipe(\n *     publish(multicasted$ =>\n *       merge(\n *         multicasted$.pipe(tap(x => console.log('Stream 1:', x))),\n *         multicasted$.pipe(tap(x => console.log('Stream 2:', x))),\n *         multicasted$.pipe(tap(x => console.log('Stream 3:', x)))\n *       )\n *     )\n *   )\n *   .subscribe();\n *\n * // Results every two seconds\n * // Stream 1: 1\n * // Stream 2: 1\n * // Stream 3: 1\n * // ...\n * // Stream 1: 9\n * // Stream 2: 9\n * // Stream 3: 9\n * ```\n *\n * @see {@link publishLast}\n * @see {@link publishReplay}\n * @see {@link publishBehavior}\n *\n * @param selector Optional selector function which can use the multicasted source sequence as many times\n * as needed, without causing multiple subscriptions to the source sequence.\n * Subscribers to the given source will receive all notifications of the source from the time of the subscription on.\n * @return A function that returns a ConnectableObservable that upon connection\n * causes the source Observable to emit items to its Observers.\n * @deprecated Will be removed in v8. Use the {@link connectable} observable, the {@link connect} operator or the\n * {@link share} operator instead. See the overloads below for equivalent replacement examples of this operator's\n * behaviors.\n * Details: https://rxjs.dev/deprecations/multicasting\n */\nexport function publish<T, R>(selector?: OperatorFunction<T, R>): MonoTypeOperatorFunction<T> | OperatorFunction<T, R> {\n  return selector ? (source) => connect(selector)(source) : (source) => multicast(new Subject<T>())(source);\n}\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,SAAS,QAAQ,aAAa;AAGvC,SAASC,OAAO,QAAQ,WAAW;AAqFnC,OAAM,SAAUC,OAAOA,CAAOC,QAAiC;EAC7D,OAAOA,QAAQ,GAAG,UAACC,MAAM;IAAK,OAAAH,OAAO,CAACE,QAAQ,CAAC,CAACC,MAAM,CAAC;EAAzB,CAAyB,GAAG,UAACA,MAAM;IAAK,OAAAJ,SAAS,CAAC,IAAID,OAAO,EAAK,CAAC,CAACK,MAAM,CAAC;EAAnC,CAAmC;AAC3G"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}