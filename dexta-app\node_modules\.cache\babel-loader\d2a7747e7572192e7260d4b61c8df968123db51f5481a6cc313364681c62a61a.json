{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.escapeUTF8 = exports.escape = exports.encodeNonAsciiHTML = exports.encodeHTML = exports.encodeXML = void 0;\nvar xml_json_1 = __importDefault(require(\"./maps/xml.json\"));\nvar inverseXML = getInverseObj(xml_json_1.default);\nvar xmlReplacer = getInverseReplacer(inverseXML);\n/**\n * Encodes all non-ASCII characters, as well as characters not valid in XML\n * documents using XML entities.\n *\n * If a character has no equivalent entity, a\n * numeric hexadecimal reference (eg. `&#xfc;`) will be used.\n */\nexports.encodeXML = getASCIIEncoder(inverseXML);\nvar entities_json_1 = __importDefault(require(\"./maps/entities.json\"));\nvar inverseHTML = getInverseObj(entities_json_1.default);\nvar htmlReplacer = getInverseReplacer(inverseHTML);\n/**\n * Encodes all entities and non-ASCII characters in the input.\n *\n * This includes characters that are valid ASCII characters in HTML documents.\n * For example `#` will be encoded as `&num;`. To get a more compact output,\n * consider using the `encodeNonAsciiHTML` function.\n *\n * If a character has no equivalent entity, a\n * numeric hexadecimal reference (eg. `&#xfc;`) will be used.\n */\nexports.encodeHTML = getInverse(inverseHTML, htmlReplacer);\n/**\n * Encodes all non-ASCII characters, as well as characters not valid in HTML\n * documents using HTML entities.\n *\n * If a character has no equivalent entity, a\n * numeric hexadecimal reference (eg. `&#xfc;`) will be used.\n */\nexports.encodeNonAsciiHTML = getASCIIEncoder(inverseHTML);\nfunction getInverseObj(obj) {\n  return Object.keys(obj).sort().reduce(function (inverse, name) {\n    inverse[obj[name]] = \"&\" + name + \";\";\n    return inverse;\n  }, {});\n}\nfunction getInverseReplacer(inverse) {\n  var single = [];\n  var multiple = [];\n  for (var _i = 0, _a = Object.keys(inverse); _i < _a.length; _i++) {\n    var k = _a[_i];\n    if (k.length === 1) {\n      // Add value to single array\n      single.push(\"\\\\\" + k);\n    } else {\n      // Add value to multiple array\n      multiple.push(k);\n    }\n  }\n  // Add ranges to single characters.\n  single.sort();\n  for (var start = 0; start < single.length - 1; start++) {\n    // Find the end of a run of characters\n    var end = start;\n    while (end < single.length - 1 && single[end].charCodeAt(1) + 1 === single[end + 1].charCodeAt(1)) {\n      end += 1;\n    }\n    var count = 1 + end - start;\n    // We want to replace at least three characters\n    if (count < 3) continue;\n    single.splice(start, count, single[start] + \"-\" + single[end]);\n  }\n  multiple.unshift(\"[\" + single.join(\"\") + \"]\");\n  return new RegExp(multiple.join(\"|\"), \"g\");\n}\n// /[^\\0-\\x7F]/gu\nvar reNonASCII = /(?:[\\x80-\\uD7FF\\uE000-\\uFFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF])/g;\nvar getCodePoint =\n// eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\nString.prototype.codePointAt != null ?\n// eslint-disable-next-line @typescript-eslint/no-non-null-assertion\nfunction (str) {\n  return str.codePointAt(0);\n} :\n// http://mathiasbynens.be/notes/javascript-encoding#surrogate-formulae\nfunction (c) {\n  return (c.charCodeAt(0) - 0xd800) * 0x400 + c.charCodeAt(1) - 0xdc00 + 0x10000;\n};\nfunction singleCharReplacer(c) {\n  return \"&#x\" + (c.length > 1 ? getCodePoint(c) : c.charCodeAt(0)).toString(16).toUpperCase() + \";\";\n}\nfunction getInverse(inverse, re) {\n  return function (data) {\n    return data.replace(re, function (name) {\n      return inverse[name];\n    }).replace(reNonASCII, singleCharReplacer);\n  };\n}\nvar reEscapeChars = new RegExp(xmlReplacer.source + \"|\" + reNonASCII.source, \"g\");\n/**\n * Encodes all non-ASCII characters, as well as characters not valid in XML\n * documents using numeric hexadecimal reference (eg. `&#xfc;`).\n *\n * Have a look at `escapeUTF8` if you want a more concise output at the expense\n * of reduced transportability.\n *\n * @param data String to escape.\n */\nfunction escape(data) {\n  return data.replace(reEscapeChars, singleCharReplacer);\n}\nexports.escape = escape;\n/**\n * Encodes all characters not valid in XML documents using numeric hexadecimal\n * reference (eg. `&#xfc;`).\n *\n * Note that the output will be character-set dependent.\n *\n * @param data String to escape.\n */\nfunction escapeUTF8(data) {\n  return data.replace(xmlReplacer, singleCharReplacer);\n}\nexports.escapeUTF8 = escapeUTF8;\nfunction getASCIIEncoder(obj) {\n  return function (data) {\n    return data.replace(reEscapeChars, function (c) {\n      return obj[c] || singleCharReplacer(c);\n    });\n  };\n}", "map": {"version": 3, "names": ["__importDefault", "mod", "__esModule", "Object", "defineProperty", "exports", "value", "escapeUTF8", "escape", "encodeNonAsciiHTML", "encodeHTML", "encodeXML", "xml_json_1", "require", "inverseXML", "getInverseObj", "default", "xmlReplacer", "getInverseReplacer", "getASCIIEncoder", "entities_json_1", "inverseHTML", "htmlReplacer", "getInverse", "obj", "keys", "sort", "reduce", "inverse", "name", "single", "multiple", "_i", "_a", "length", "k", "push", "start", "end", "charCodeAt", "count", "splice", "unshift", "join", "RegExp", "reNonASCII", "getCodePoint", "String", "prototype", "codePointAt", "str", "c", "singleCharReplacer", "toString", "toUpperCase", "re", "data", "replace", "reEscapeChars", "source"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/entities/lib/encode.js"], "sourcesContent": ["\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.escapeUTF8 = exports.escape = exports.encodeNonAsciiHTML = exports.encodeHTML = exports.encodeXML = void 0;\nvar xml_json_1 = __importDefault(require(\"./maps/xml.json\"));\nvar inverseXML = getInverseObj(xml_json_1.default);\nvar xmlReplacer = getInverseReplacer(inverseXML);\n/**\n * Encodes all non-ASCII characters, as well as characters not valid in XML\n * documents using XML entities.\n *\n * If a character has no equivalent entity, a\n * numeric hexadecimal reference (eg. `&#xfc;`) will be used.\n */\nexports.encodeXML = getASCIIEncoder(inverseXML);\nvar entities_json_1 = __importDefault(require(\"./maps/entities.json\"));\nvar inverseHTML = getInverseObj(entities_json_1.default);\nvar htmlReplacer = getInverseReplacer(inverseHTML);\n/**\n * Encodes all entities and non-ASCII characters in the input.\n *\n * This includes characters that are valid ASCII characters in HTML documents.\n * For example `#` will be encoded as `&num;`. To get a more compact output,\n * consider using the `encodeNonAsciiHTML` function.\n *\n * If a character has no equivalent entity, a\n * numeric hexadecimal reference (eg. `&#xfc;`) will be used.\n */\nexports.encodeHTML = getInverse(inverseHTML, htmlReplacer);\n/**\n * Encodes all non-ASCII characters, as well as characters not valid in HTML\n * documents using HTML entities.\n *\n * If a character has no equivalent entity, a\n * numeric hexadecimal reference (eg. `&#xfc;`) will be used.\n */\nexports.encodeNonAsciiHTML = getASCIIEncoder(inverseHTML);\nfunction getInverseObj(obj) {\n    return Object.keys(obj)\n        .sort()\n        .reduce(function (inverse, name) {\n        inverse[obj[name]] = \"&\" + name + \";\";\n        return inverse;\n    }, {});\n}\nfunction getInverseReplacer(inverse) {\n    var single = [];\n    var multiple = [];\n    for (var _i = 0, _a = Object.keys(inverse); _i < _a.length; _i++) {\n        var k = _a[_i];\n        if (k.length === 1) {\n            // Add value to single array\n            single.push(\"\\\\\" + k);\n        }\n        else {\n            // Add value to multiple array\n            multiple.push(k);\n        }\n    }\n    // Add ranges to single characters.\n    single.sort();\n    for (var start = 0; start < single.length - 1; start++) {\n        // Find the end of a run of characters\n        var end = start;\n        while (end < single.length - 1 &&\n            single[end].charCodeAt(1) + 1 === single[end + 1].charCodeAt(1)) {\n            end += 1;\n        }\n        var count = 1 + end - start;\n        // We want to replace at least three characters\n        if (count < 3)\n            continue;\n        single.splice(start, count, single[start] + \"-\" + single[end]);\n    }\n    multiple.unshift(\"[\" + single.join(\"\") + \"]\");\n    return new RegExp(multiple.join(\"|\"), \"g\");\n}\n// /[^\\0-\\x7F]/gu\nvar reNonASCII = /(?:[\\x80-\\uD7FF\\uE000-\\uFFFF]|[\\uD800-\\uDBFF][\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF])/g;\nvar getCodePoint = \n// eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\nString.prototype.codePointAt != null\n    ? // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        function (str) { return str.codePointAt(0); }\n    : // http://mathiasbynens.be/notes/javascript-encoding#surrogate-formulae\n        function (c) {\n            return (c.charCodeAt(0) - 0xd800) * 0x400 +\n                c.charCodeAt(1) -\n                0xdc00 +\n                0x10000;\n        };\nfunction singleCharReplacer(c) {\n    return \"&#x\" + (c.length > 1 ? getCodePoint(c) : c.charCodeAt(0))\n        .toString(16)\n        .toUpperCase() + \";\";\n}\nfunction getInverse(inverse, re) {\n    return function (data) {\n        return data\n            .replace(re, function (name) { return inverse[name]; })\n            .replace(reNonASCII, singleCharReplacer);\n    };\n}\nvar reEscapeChars = new RegExp(xmlReplacer.source + \"|\" + reNonASCII.source, \"g\");\n/**\n * Encodes all non-ASCII characters, as well as characters not valid in XML\n * documents using numeric hexadecimal reference (eg. `&#xfc;`).\n *\n * Have a look at `escapeUTF8` if you want a more concise output at the expense\n * of reduced transportability.\n *\n * @param data String to escape.\n */\nfunction escape(data) {\n    return data.replace(reEscapeChars, singleCharReplacer);\n}\nexports.escape = escape;\n/**\n * Encodes all characters not valid in XML documents using numeric hexadecimal\n * reference (eg. `&#xfc;`).\n *\n * Note that the output will be character-set dependent.\n *\n * @param data String to escape.\n */\nfunction escapeUTF8(data) {\n    return data.replace(xmlReplacer, singleCharReplacer);\n}\nexports.escapeUTF8 = escapeUTF8;\nfunction getASCIIEncoder(obj) {\n    return function (data) {\n        return data.replace(reEscapeChars, function (c) { return obj[c] || singleCharReplacer(c); });\n    };\n}\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,eAAe,GAAI,IAAI,IAAI,IAAI,CAACA,eAAe,IAAK,UAAUC,GAAG,EAAE;EACnE,OAAQA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAID,GAAG,GAAG;IAAE,SAAS,EAAEA;EAAI,CAAC;AAC7D,CAAC;AACDE,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7DD,OAAO,CAACE,UAAU,GAAGF,OAAO,CAACG,MAAM,GAAGH,OAAO,CAACI,kBAAkB,GAAGJ,OAAO,CAACK,UAAU,GAAGL,OAAO,CAACM,SAAS,GAAG,KAAK,CAAC;AAClH,IAAIC,UAAU,GAAGZ,eAAe,CAACa,OAAO,CAAC,iBAAiB,CAAC,CAAC;AAC5D,IAAIC,UAAU,GAAGC,aAAa,CAACH,UAAU,CAACI,OAAO,CAAC;AAClD,IAAIC,WAAW,GAAGC,kBAAkB,CAACJ,UAAU,CAAC;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACAT,OAAO,CAACM,SAAS,GAAGQ,eAAe,CAACL,UAAU,CAAC;AAC/C,IAAIM,eAAe,GAAGpB,eAAe,CAACa,OAAO,CAAC,sBAAsB,CAAC,CAAC;AACtE,IAAIQ,WAAW,GAAGN,aAAa,CAACK,eAAe,CAACJ,OAAO,CAAC;AACxD,IAAIM,YAAY,GAAGJ,kBAAkB,CAACG,WAAW,CAAC;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAhB,OAAO,CAACK,UAAU,GAAGa,UAAU,CAACF,WAAW,EAAEC,YAAY,CAAC;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACAjB,OAAO,CAACI,kBAAkB,GAAGU,eAAe,CAACE,WAAW,CAAC;AACzD,SAASN,aAAaA,CAACS,GAAG,EAAE;EACxB,OAAOrB,MAAM,CAACsB,IAAI,CAACD,GAAG,CAAC,CAClBE,IAAI,CAAC,CAAC,CACNC,MAAM,CAAC,UAAUC,OAAO,EAAEC,IAAI,EAAE;IACjCD,OAAO,CAACJ,GAAG,CAACK,IAAI,CAAC,CAAC,GAAG,GAAG,GAAGA,IAAI,GAAG,GAAG;IACrC,OAAOD,OAAO;EAClB,CAAC,EAAE,CAAC,CAAC,CAAC;AACV;AACA,SAASV,kBAAkBA,CAACU,OAAO,EAAE;EACjC,IAAIE,MAAM,GAAG,EAAE;EACf,IAAIC,QAAQ,GAAG,EAAE;EACjB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG9B,MAAM,CAACsB,IAAI,CAACG,OAAO,CAAC,EAAEI,EAAE,GAAGC,EAAE,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;IAC9D,IAAIG,CAAC,GAAGF,EAAE,CAACD,EAAE,CAAC;IACd,IAAIG,CAAC,CAACD,MAAM,KAAK,CAAC,EAAE;MAChB;MACAJ,MAAM,CAACM,IAAI,CAAC,IAAI,GAAGD,CAAC,CAAC;IACzB,CAAC,MACI;MACD;MACAJ,QAAQ,CAACK,IAAI,CAACD,CAAC,CAAC;IACpB;EACJ;EACA;EACAL,MAAM,CAACJ,IAAI,CAAC,CAAC;EACb,KAAK,IAAIW,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGP,MAAM,CAACI,MAAM,GAAG,CAAC,EAAEG,KAAK,EAAE,EAAE;IACpD;IACA,IAAIC,GAAG,GAAGD,KAAK;IACf,OAAOC,GAAG,GAAGR,MAAM,CAACI,MAAM,GAAG,CAAC,IAC1BJ,MAAM,CAACQ,GAAG,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,KAAKT,MAAM,CAACQ,GAAG,GAAG,CAAC,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,EAAE;MACjED,GAAG,IAAI,CAAC;IACZ;IACA,IAAIE,KAAK,GAAG,CAAC,GAAGF,GAAG,GAAGD,KAAK;IAC3B;IACA,IAAIG,KAAK,GAAG,CAAC,EACT;IACJV,MAAM,CAACW,MAAM,CAACJ,KAAK,EAAEG,KAAK,EAAEV,MAAM,CAACO,KAAK,CAAC,GAAG,GAAG,GAAGP,MAAM,CAACQ,GAAG,CAAC,CAAC;EAClE;EACAP,QAAQ,CAACW,OAAO,CAAC,GAAG,GAAGZ,MAAM,CAACa,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC;EAC7C,OAAO,IAAIC,MAAM,CAACb,QAAQ,CAACY,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;AAC9C;AACA;AACA,IAAIE,UAAU,GAAG,yIAAyI;AAC1J,IAAIC,YAAY;AAChB;AACAC,MAAM,CAACC,SAAS,CAACC,WAAW,IAAI,IAAI;AAC9B;AACE,UAAUC,GAAG,EAAE;EAAE,OAAOA,GAAG,CAACD,WAAW,CAAC,CAAC,CAAC;AAAE,CAAC;AAC/C;AACE,UAAUE,CAAC,EAAE;EACT,OAAO,CAACA,CAAC,CAACZ,UAAU,CAAC,CAAC,CAAC,GAAG,MAAM,IAAI,KAAK,GACrCY,CAAC,CAACZ,UAAU,CAAC,CAAC,CAAC,GACf,MAAM,GACN,OAAO;AACf,CAAC;AACT,SAASa,kBAAkBA,CAACD,CAAC,EAAE;EAC3B,OAAO,KAAK,GAAG,CAACA,CAAC,CAACjB,MAAM,GAAG,CAAC,GAAGY,YAAY,CAACK,CAAC,CAAC,GAAGA,CAAC,CAACZ,UAAU,CAAC,CAAC,CAAC,EAC3Dc,QAAQ,CAAC,EAAE,CAAC,CACZC,WAAW,CAAC,CAAC,GAAG,GAAG;AAC5B;AACA,SAAS/B,UAAUA,CAACK,OAAO,EAAE2B,EAAE,EAAE;EAC7B,OAAO,UAAUC,IAAI,EAAE;IACnB,OAAOA,IAAI,CACNC,OAAO,CAACF,EAAE,EAAE,UAAU1B,IAAI,EAAE;MAAE,OAAOD,OAAO,CAACC,IAAI,CAAC;IAAE,CAAC,CAAC,CACtD4B,OAAO,CAACZ,UAAU,EAAEO,kBAAkB,CAAC;EAChD,CAAC;AACL;AACA,IAAIM,aAAa,GAAG,IAAId,MAAM,CAAC3B,WAAW,CAAC0C,MAAM,GAAG,GAAG,GAAGd,UAAU,CAACc,MAAM,EAAE,GAAG,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASnD,MAAMA,CAACgD,IAAI,EAAE;EAClB,OAAOA,IAAI,CAACC,OAAO,CAACC,aAAa,EAAEN,kBAAkB,CAAC;AAC1D;AACA/C,OAAO,CAACG,MAAM,GAAGA,MAAM;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,UAAUA,CAACiD,IAAI,EAAE;EACtB,OAAOA,IAAI,CAACC,OAAO,CAACxC,WAAW,EAAEmC,kBAAkB,CAAC;AACxD;AACA/C,OAAO,CAACE,UAAU,GAAGA,UAAU;AAC/B,SAASY,eAAeA,CAACK,GAAG,EAAE;EAC1B,OAAO,UAAUgC,IAAI,EAAE;IACnB,OAAOA,IAAI,CAACC,OAAO,CAACC,aAAa,EAAE,UAAUP,CAAC,EAAE;MAAE,OAAO3B,GAAG,CAAC2B,CAAC,CAAC,IAAIC,kBAAkB,CAACD,CAAC,CAAC;IAAE,CAAC,CAAC;EAChG,CAAC;AACL"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}