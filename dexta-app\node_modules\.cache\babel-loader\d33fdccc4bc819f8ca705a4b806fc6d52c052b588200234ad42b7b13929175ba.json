{"ast": null, "code": "import { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\nexport function expand(project, concurrent, scheduler) {\n  if (concurrent === void 0) {\n    concurrent = Infinity;\n  }\n  concurrent = (concurrent || 0) < 1 ? Infinity : concurrent;\n  return operate(function (source, subscriber) {\n    return mergeInternals(source, subscriber, project, concurrent, undefined, true, scheduler);\n  });\n}", "map": {"version": 3, "names": ["operate", "mergeInternals", "expand", "project", "concurrent", "scheduler", "Infinity", "source", "subscriber", "undefined"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\expand.ts"], "sourcesContent": ["import { OperatorFunction, ObservableInput, ObservedValueOf, SchedulerLike } from '../types';\nimport { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\n\n/* tslint:disable:max-line-length */\nexport function expand<T, O extends ObservableInput<unknown>>(\n  project: (value: T, index: number) => O,\n  concurrent?: number,\n  scheduler?: SchedulerLike\n): OperatorFunction<T, ObservedValueOf<O>>;\n/**\n * @deprecated The `scheduler` parameter will be removed in v8. If you need to schedule the inner subscription,\n * use `subscribeOn` within the projection function: `expand((value) => fn(value).pipe(subscribeOn(scheduler)))`.\n * Details: Details: https://rxjs.dev/deprecations/scheduler-argument\n */\nexport function expand<T, O extends ObservableInput<unknown>>(\n  project: (value: T, index: number) => O,\n  concurrent: number | undefined,\n  scheduler: SchedulerLike\n): OperatorFunction<T, ObservedValueOf<O>>;\n/* tslint:enable:max-line-length */\n\n/**\n * Recursively projects each source value to an Observable which is merged in\n * the output Observable.\n *\n * <span class=\"informal\">It's similar to {@link mergeMap}, but applies the\n * projection function to every source value as well as every output value.\n * It's recursive.</span>\n *\n * ![](expand.png)\n *\n * Returns an Observable that emits items based on applying a function that you\n * supply to each item emitted by the source Observable, where that function\n * returns an Observable, and then merging those resulting Observables and\n * emitting the results of this merger. *Expand* will re-emit on the output\n * Observable every source value. Then, each output value is given to the\n * `project` function which returns an inner Observable to be merged on the\n * output Observable. Those output values resulting from the projection are also\n * given to the `project` function to produce new output values. This is how\n * *expand* behaves recursively.\n *\n * ## Example\n *\n * Start emitting the powers of two on every click, at most 10 of them\n *\n * ```ts\n * import { fromEvent, map, expand, of, delay, take } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const powersOfTwo = clicks.pipe(\n *   map(() => 1),\n *   expand(x => of(2 * x).pipe(delay(1000))),\n *   take(10)\n * );\n * powersOfTwo.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link mergeMap}\n * @see {@link mergeScan}\n *\n * @param project A function that, when applied to an item emitted by the source\n * or the output Observable, returns an Observable.\n * @param concurrent Maximum number of input Observables being subscribed to\n * concurrently.\n * @param scheduler The {@link SchedulerLike} to use for subscribing to\n * each projected inner Observable.\n * @return A function that returns an Observable that emits the source values\n * and also result of applying the projection function to each value emitted on\n * the output Observable and merging the results of the Observables obtained\n * from this transformation.\n */\nexport function expand<T, O extends ObservableInput<unknown>>(\n  project: (value: T, index: number) => O,\n  concurrent = Infinity,\n  scheduler?: SchedulerLike\n): OperatorFunction<T, ObservedValueOf<O>> {\n  concurrent = (concurrent || 0) < 1 ? Infinity : concurrent;\n  return operate((source, subscriber) =>\n    mergeInternals(\n      // General merge params\n      source,\n      subscriber,\n      project,\n      concurrent,\n\n      // onBeforeNext\n      undefined,\n\n      // Expand-specific\n      true, // Use expand path\n      scheduler // Inner subscription scheduler\n    )\n  );\n}\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,cAAc,QAAQ,kBAAkB;AAsEjD,OAAM,SAAUC,MAAMA,CACpBC,OAAuC,EACvCC,UAAqB,EACrBC,SAAyB;EADzB,IAAAD,UAAA;IAAAA,UAAA,GAAAE,QAAqB;EAAA;EAGrBF,UAAU,GAAG,CAACA,UAAU,IAAI,CAAC,IAAI,CAAC,GAAGE,QAAQ,GAAGF,UAAU;EAC1D,OAAOJ,OAAO,CAAC,UAACO,MAAM,EAAEC,UAAU;IAChC,OAAAP,cAAc,CAEZM,MAAM,EACNC,UAAU,EACVL,OAAO,EACPC,UAAU,EAGVK,SAAS,EAGT,IAAI,EACJJ,SAAS,CACV;EAbD,CAaC,CACF;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}