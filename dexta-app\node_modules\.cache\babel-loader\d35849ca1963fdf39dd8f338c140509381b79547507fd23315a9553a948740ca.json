{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nimport PatternParser from './AsYouTypeFormatter.PatternParser.js';\nvar PatternMatcher = /*#__PURE__*/function () {\n  function PatternMatcher(pattern) {\n    _classCallCheck(this, PatternMatcher);\n    this.matchTree = new PatternParser().parse(pattern);\n  }\n  _createClass(PatternMatcher, [{\n    key: \"match\",\n    value: function match(string) {\n      var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {},\n        allowOverflow = _ref.allowOverflow;\n      if (!string) {\n        throw new Error('String is required');\n      }\n      var result = _match(string.split(''), this.matchTree, true);\n      if (result && result.match) {\n        delete result.matchedChars;\n      }\n      if (result && result.overflow) {\n        if (!allowOverflow) {\n          return;\n        }\n      }\n      return result;\n    }\n  }]);\n  return PatternMatcher;\n}();\n/**\r\n * Matches `characters` against a pattern compiled into a `tree`.\r\n * @param  {string[]} characters\r\n * @param  {Tree} tree — A pattern compiled into a `tree`. See the `*.d.ts` file for the description of the `tree` structure.\r\n * @param  {boolean} last — Whether it's the last (rightmost) subtree on its level of the match tree.\r\n * @return {object} See the `*.d.ts` file for the description of the result object.\r\n */\n\nexport { PatternMatcher as default };\nfunction _match(characters, tree, last) {\n  // If `tree` is a string, then `tree` is a single character.\n  // That's because when a pattern is parsed, multi-character-string parts\n  // of a pattern are compiled into arrays of single characters.\n  // I still wrote this piece of code for a \"general\" hypothetical case\n  // when `tree` could be a string of several characters, even though\n  // such case is not possible with the current implementation.\n  if (typeof tree === 'string') {\n    var characterString = characters.join('');\n    if (tree.indexOf(characterString) === 0) {\n      // `tree` is always a single character.\n      // If `tree.indexOf(characterString) === 0`\n      // then `characters.length === tree.length`.\n\n      /* istanbul ignore else */\n      if (characters.length === tree.length) {\n        return {\n          match: true,\n          matchedChars: characters\n        };\n      } // `tree` is always a single character.\n      // If `tree.indexOf(characterString) === 0`\n      // then `characters.length === tree.length`.\n\n      /* istanbul ignore next */\n\n      return {\n        partialMatch: true // matchedChars: characters\n      };\n    }\n\n    if (characterString.indexOf(tree) === 0) {\n      if (last) {\n        // The `else` path is not possible because `tree` is always a single character.\n        // The `else` case for `characters.length > tree.length` would be\n        // `characters.length <= tree.length` which means `characters.length <= 1`.\n        // `characters` array can't be empty, so that means `characters === [tree]`,\n        // which would also mean `tree.indexOf(characterString) === 0` and that'd mean\n        // that the `if (tree.indexOf(characterString) === 0)` condition before this\n        // `if` condition would be entered, and returned from there, not reaching this code.\n\n        /* istanbul ignore else */\n        if (characters.length > tree.length) {\n          return {\n            overflow: true\n          };\n        }\n      }\n      return {\n        match: true,\n        matchedChars: characters.slice(0, tree.length)\n      };\n    }\n    return;\n  }\n  if (Array.isArray(tree)) {\n    var restCharacters = characters.slice();\n    var i = 0;\n    while (i < tree.length) {\n      var subtree = tree[i];\n      var result = _match(restCharacters, subtree, last && i === tree.length - 1);\n      if (!result) {\n        return;\n      } else if (result.overflow) {\n        return result;\n      } else if (result.match) {\n        // Continue with the next subtree with the rest of the characters.\n        restCharacters = restCharacters.slice(result.matchedChars.length);\n        if (restCharacters.length === 0) {\n          if (i === tree.length - 1) {\n            return {\n              match: true,\n              matchedChars: characters\n            };\n          } else {\n            return {\n              partialMatch: true // matchedChars: characters\n            };\n          }\n        }\n      } else {\n        /* istanbul ignore else */\n        if (result.partialMatch) {\n          return {\n            partialMatch: true // matchedChars: characters\n          };\n        } else {\n          throw new Error(\"Unsupported match result:\\n\".concat(JSON.stringify(result, null, 2)));\n        }\n      }\n      i++;\n    } // If `last` then overflow has already been checked\n    // by the last element of the `tree` array.\n\n    /* istanbul ignore if */\n\n    if (last) {\n      return {\n        overflow: true\n      };\n    }\n    return {\n      match: true,\n      matchedChars: characters.slice(0, characters.length - restCharacters.length)\n    };\n  }\n  switch (tree.op) {\n    case '|':\n      var partialMatch;\n      for (var _iterator = _createForOfIteratorHelperLoose(tree.args), _step; !(_step = _iterator()).done;) {\n        var branch = _step.value;\n        var _result = _match(characters, branch, last);\n        if (_result) {\n          if (_result.overflow) {\n            return _result;\n          } else if (_result.match) {\n            return {\n              match: true,\n              matchedChars: _result.matchedChars\n            };\n          } else {\n            /* istanbul ignore else */\n            if (_result.partialMatch) {\n              partialMatch = true;\n            } else {\n              throw new Error(\"Unsupported match result:\\n\".concat(JSON.stringify(_result, null, 2)));\n            }\n          }\n        }\n      }\n      if (partialMatch) {\n        return {\n          partialMatch: true // matchedChars: ...\n        };\n      } // Not even a partial match.\n\n      return;\n    case '[]':\n      for (var _iterator2 = _createForOfIteratorHelperLoose(tree.args), _step2; !(_step2 = _iterator2()).done;) {\n        var _char = _step2.value;\n        if (characters[0] === _char) {\n          if (characters.length === 1) {\n            return {\n              match: true,\n              matchedChars: characters\n            };\n          }\n          if (last) {\n            return {\n              overflow: true\n            };\n          }\n          return {\n            match: true,\n            matchedChars: [_char]\n          };\n        }\n      } // No character matches.\n\n      return;\n\n    /* istanbul ignore next */\n\n    default:\n      throw new Error(\"Unsupported instruction tree: \".concat(tree));\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pattern", "_classCallCheck", "matchTree", "parse", "match", "string", "_ref", "arguments", "length", "undefined", "allowOverflow", "Error", "result", "_match", "split", "matched<PERSON><PERSON><PERSON>", "overflow", "characters", "tree", "last", "characterString", "join", "indexOf", "partialMatch", "slice", "Array", "isArray", "restCharacters", "i", "subtree", "concat", "JSON", "stringify", "op", "_iterator", "_createForOfIteratorHelperLoose", "args", "_step", "done", "branch", "value", "_result", "_iterator2", "_step2", "_char"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\AsYouTypeFormatter.PatternMatcher.js"], "sourcesContent": ["import PatternParser from './AsYouTypeFormatter.PatternParser.js'\r\n\r\nexport default class PatternMatcher {\r\n\tconstructor(pattern) {\r\n\t\tthis.matchTree = new PatternParser().parse(pattern)\r\n\t}\r\n\r\n\tmatch(string, { allowOverflow } = {}) {\r\n\t\tif (!string) {\r\n\t\t\tthrow new Error('String is required')\r\n\t\t}\r\n\t\tconst result = match(string.split(''), this.matchTree, true)\r\n\t\tif (result && result.match) {\r\n\t\t\tdelete result.matchedChars\r\n\t\t}\r\n\t\tif (result && result.overflow) {\r\n\t\t\tif (!allowOverflow) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn result\r\n\t}\r\n}\r\n\r\n/**\r\n * Matches `characters` against a pattern compiled into a `tree`.\r\n * @param  {string[]} characters\r\n * @param  {Tree} tree — A pattern compiled into a `tree`. See the `*.d.ts` file for the description of the `tree` structure.\r\n * @param  {boolean} last — Whether it's the last (rightmost) subtree on its level of the match tree.\r\n * @return {object} See the `*.d.ts` file for the description of the result object.\r\n */\r\nfunction match(characters, tree, last) {\r\n\t// If `tree` is a string, then `tree` is a single character.\r\n\t// That's because when a pattern is parsed, multi-character-string parts\r\n\t// of a pattern are compiled into arrays of single characters.\r\n\t// I still wrote this piece of code for a \"general\" hypothetical case\r\n\t// when `tree` could be a string of several characters, even though\r\n\t// such case is not possible with the current implementation.\r\n\tif (typeof tree === 'string') {\r\n\t\tconst characterString = characters.join('')\r\n\t\tif (tree.indexOf(characterString) === 0) {\r\n\t\t\t// `tree` is always a single character.\r\n\t\t\t// If `tree.indexOf(characterString) === 0`\r\n\t\t\t// then `characters.length === tree.length`.\r\n\t\t\t/* istanbul ignore else */\r\n\t\t\tif (characters.length === tree.length) {\r\n\t\t\t\treturn {\r\n\t\t\t\t\tmatch: true,\r\n\t\t\t\t\tmatchedChars: characters\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// `tree` is always a single character.\r\n\t\t\t// If `tree.indexOf(characterString) === 0`\r\n\t\t\t// then `characters.length === tree.length`.\r\n\t\t\t/* istanbul ignore next */\r\n\t\t\treturn {\r\n\t\t\t\tpartialMatch: true,\r\n\t\t\t\t// matchedChars: characters\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (characterString.indexOf(tree) === 0) {\r\n\t\t\tif (last) {\r\n\t\t\t\t// The `else` path is not possible because `tree` is always a single character.\r\n\t\t\t\t// The `else` case for `characters.length > tree.length` would be\r\n\t\t\t\t// `characters.length <= tree.length` which means `characters.length <= 1`.\r\n\t\t\t\t// `characters` array can't be empty, so that means `characters === [tree]`,\r\n\t\t\t\t// which would also mean `tree.indexOf(characterString) === 0` and that'd mean\r\n\t\t\t\t// that the `if (tree.indexOf(characterString) === 0)` condition before this\r\n\t\t\t\t// `if` condition would be entered, and returned from there, not reaching this code.\r\n\t\t\t\t/* istanbul ignore else */\r\n\t\t\t\tif (characters.length > tree.length) {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\toverflow: true\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn {\r\n\t\t\t\tmatch: true,\r\n\t\t\t\tmatchedChars: characters.slice(0, tree.length)\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn\r\n\t}\r\n\r\n\tif (Array.isArray(tree)) {\r\n\t\tlet restCharacters = characters.slice()\r\n\t\tlet i = 0\r\n\t\twhile (i < tree.length) {\r\n\t\t\tconst subtree = tree[i]\r\n\t\t\tconst result = match(restCharacters, subtree, last && (i === tree.length - 1))\r\n\t\t\tif (!result) {\r\n\t\t\t\treturn\r\n\t\t\t} else if (result.overflow) {\r\n\t\t\t\treturn result\r\n\t\t\t} else if (result.match) {\r\n\t\t\t\t// Continue with the next subtree with the rest of the characters.\r\n\t\t\t\trestCharacters = restCharacters.slice(result.matchedChars.length)\r\n\t\t\t\tif (restCharacters.length === 0) {\r\n\t\t\t\t\tif (i === tree.length - 1) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tmatch: true,\r\n\t\t\t\t\t\t\tmatchedChars: characters\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tpartialMatch: true,\r\n\t\t\t\t\t\t\t// matchedChars: characters\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\t/* istanbul ignore else */\r\n\t\t\t\tif (result.partialMatch) {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tpartialMatch: true,\r\n\t\t\t\t\t\t// matchedChars: characters\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthrow new Error(`Unsupported match result:\\n${JSON.stringify(result, null, 2)}`)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\ti++\r\n\t\t}\r\n\t\t// If `last` then overflow has already been checked\r\n\t\t// by the last element of the `tree` array.\r\n\t\t/* istanbul ignore if */\r\n\t\tif (last) {\r\n\t\t\treturn {\r\n\t\t\t\toverflow: true\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn {\r\n\t\t\tmatch: true,\r\n\t\t\tmatchedChars: characters.slice(0, characters.length - restCharacters.length)\r\n\t\t}\r\n\t}\r\n\r\n\tswitch (tree.op) {\r\n\t\tcase '|':\r\n\t\t\tlet partialMatch\r\n\t\t\tfor (const branch of tree.args) {\r\n\t\t\t\tconst result = match(characters, branch, last)\r\n\t\t\t\tif (result) {\r\n\t\t\t\t\tif (result.overflow) {\r\n\t\t\t\t\t\treturn result\r\n\t\t\t\t\t} else if (result.match) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tmatch: true,\r\n\t\t\t\t\t\t\tmatchedChars: result.matchedChars\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t/* istanbul ignore else */\r\n\t\t\t\t\t\tif (result.partialMatch) {\r\n\t\t\t\t\t\t\tpartialMatch = true\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthrow new Error(`Unsupported match result:\\n${JSON.stringify(result, null, 2)}`)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tif (partialMatch) {\r\n\t\t\t\treturn {\r\n\t\t\t\t\tpartialMatch: true,\r\n\t\t\t\t\t// matchedChars: ...\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// Not even a partial match.\r\n\t\t\treturn\r\n\r\n\t\tcase '[]':\r\n\t\t\tfor (const char of tree.args) {\r\n\t\t\t\tif (characters[0] === char) {\r\n\t\t\t\t\tif (characters.length === 1) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tmatch: true,\r\n\t\t\t\t\t\t\tmatchedChars: characters\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (last) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\toverflow: true\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tmatch: true,\r\n\t\t\t\t\t\tmatchedChars: [char]\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// No character matches.\r\n\t\t\treturn\r\n\r\n\t\t/* istanbul ignore next */\r\n\t\tdefault:\r\n\t\t\tthrow new Error(`Unsupported instruction tree: ${tree}`)\r\n\t}\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,aAAP,MAA0B,uCAA1B;IAEqBC,c;EACpB,SAAAA,eAAYC,OAAZ,EAAqB;IAAAC,eAAA,OAAAF,cAAA;IACpB,KAAKG,SAAL,GAAiB,IAAIJ,aAAJ,GAAoBK,KAApB,CAA0BH,OAA1B,CAAjB;EACA;;;WAED,SAAAI,MAAMC,MAAN,EAAsC;MAAA,IAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAJ,EAAI;QAAtBG,aAAsB,GAAAJ,IAAA,CAAtBI,aAAsB;MACrC,IAAI,CAACL,MAAL,EAAa;QACZ,MAAM,IAAIM,KAAJ,CAAU,oBAAV,CAAN;MACA;MACD,IAAMC,MAAM,GAAGC,MAAK,CAACR,MAAM,CAACS,KAAP,CAAa,EAAb,CAAD,EAAmB,KAAKZ,SAAxB,EAAmC,IAAnC,CAApB;MACA,IAAIU,MAAM,IAAIA,MAAM,CAACR,KAArB,EAA4B;QAC3B,OAAOQ,MAAM,CAACG,YAAd;MACA;MACD,IAAIH,MAAM,IAAIA,MAAM,CAACI,QAArB,EAA+B;QAC9B,IAAI,CAACN,aAAL,EAAoB;UACnB;QACA;MACD;MACD,OAAOE,MAAP;IACA;;;;AAGF;AACA;AACA;AACA;AACA;AACA;AACA;;SA5BqBb,c;AA6BrB,SAASc,MAATT,CAAea,UAAf,EAA2BC,IAA3B,EAAiCC,IAAjC,EAAuC;EACtC;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,OAAOD,IAAP,KAAgB,QAApB,EAA8B;IAC7B,IAAME,eAAe,GAAGH,UAAU,CAACI,IAAX,CAAgB,EAAhB,CAAxB;IACA,IAAIH,IAAI,CAACI,OAAL,CAAaF,eAAb,MAAkC,CAAtC,EAAyC;MACxC;MACA;MACA;;MACA;MACA,IAAIH,UAAU,CAACT,MAAX,KAAsBU,IAAI,CAACV,MAA/B,EAAuC;QACtC,OAAO;UACNJ,KAAK,EAAE,IADD;UAENW,YAAY,EAAEE;QAFR,CAAP;MAIA,CAVuC,CAWxC;MACA;MACA;;MACA;;MACA,OAAO;QACNM,YAAY,EAAE,IADR,CAEN;MAFM,CAAP;IAIA;;IACD,IAAIH,eAAe,CAACE,OAAhB,CAAwBJ,IAAxB,MAAkC,CAAtC,EAAyC;MACxC,IAAIC,IAAJ,EAAU;QACT;QACA;QACA;QACA;QACA;QACA;QACA;;QACA;QACA,IAAIF,UAAU,CAACT,MAAX,GAAoBU,IAAI,CAACV,MAA7B,EAAqC;UACpC,OAAO;YACNQ,QAAQ,EAAE;UADJ,CAAP;QAGA;MACD;MACD,OAAO;QACNZ,KAAK,EAAE,IADD;QAENW,YAAY,EAAEE,UAAU,CAACO,KAAX,CAAiB,CAAjB,EAAoBN,IAAI,CAACV,MAAzB;MAFR,CAAP;IAIA;IACD;EACA;EAED,IAAIiB,KAAK,CAACC,OAAN,CAAcR,IAAd,CAAJ,EAAyB;IACxB,IAAIS,cAAc,GAAGV,UAAU,CAACO,KAAX,EAArB;IACA,IAAII,CAAC,GAAG,CAAR;IACA,OAAOA,CAAC,GAAGV,IAAI,CAACV,MAAhB,EAAwB;MACvB,IAAMqB,OAAO,GAAGX,IAAI,CAACU,CAAD,CAApB;MACA,IAAMhB,MAAM,GAAGC,MAAK,CAACc,cAAD,EAAiBE,OAAjB,EAA0BV,IAAI,IAAKS,CAAC,KAAKV,IAAI,CAACV,MAAL,GAAc,CAAvD,CAApB;MACA,IAAI,CAACI,MAAL,EAAa;QACZ;MACA,CAFD,MAEO,IAAIA,MAAM,CAACI,QAAX,EAAqB;QAC3B,OAAOJ,MAAP;MACA,CAFM,MAEA,IAAIA,MAAM,CAACR,KAAX,EAAkB;QACxB;QACAuB,cAAc,GAAGA,cAAc,CAACH,KAAf,CAAqBZ,MAAM,CAACG,YAAP,CAAoBP,MAAzC,CAAjB;QACA,IAAImB,cAAc,CAACnB,MAAf,KAA0B,CAA9B,EAAiC;UAChC,IAAIoB,CAAC,KAAKV,IAAI,CAACV,MAAL,GAAc,CAAxB,EAA2B;YAC1B,OAAO;cACNJ,KAAK,EAAE,IADD;cAENW,YAAY,EAAEE;YAFR,CAAP;UAIA,CALD,MAKO;YACN,OAAO;cACNM,YAAY,EAAE,IADR,CAEN;YAFM,CAAP;UAIA;QACD;MACD,CAhBM,MAgBA;QACN;QACA,IAAIX,MAAM,CAACW,YAAX,EAAyB;UACxB,OAAO;YACNA,YAAY,EAAE,IADR,CAEN;UAFM,CAAP;QAIA,CALD,MAKO;UACN,MAAM,IAAIZ,KAAJ,+BAAAmB,MAAA,CAAwCC,IAAI,CAACC,SAAL,CAAepB,MAAf,EAAuB,IAAvB,EAA6B,CAA7B,CAAxC,EAAN;QACA;MACD;MACDgB,CAAC;IACD,CAtCuB,CAuCxB;IACA;;IACA;;IACA,IAAIT,IAAJ,EAAU;MACT,OAAO;QACNH,QAAQ,EAAE;MADJ,CAAP;IAGA;IACD,OAAO;MACNZ,KAAK,EAAE,IADD;MAENW,YAAY,EAAEE,UAAU,CAACO,KAAX,CAAiB,CAAjB,EAAoBP,UAAU,CAACT,MAAX,GAAoBmB,cAAc,CAACnB,MAAvD;IAFR,CAAP;EAIA;EAED,QAAQU,IAAI,CAACe,EAAb;IACC,KAAK,GAAL;MACC,IAAIV,YAAJ;MACA,SAAAW,SAAA,GAAAC,+BAAA,CAAqBjB,IAAI,CAACkB,IAA1B,GAAAC,KAAA,IAAAA,KAAA,GAAAH,SAAA,IAAAI,IAAA,GAAgC;QAAA,IAArBC,MAAqB,GAAAF,KAAA,CAAAG,KAAA;QAC/B,IAAMC,OAAM,GAAG5B,MAAK,CAACI,UAAD,EAAasB,MAAb,EAAqBpB,IAArB,CAApB;QACA,IAAIsB,OAAJ,EAAY;UACX,IAAIA,OAAM,CAACzB,QAAX,EAAqB;YACpB,OAAOyB,OAAP;UACA,CAFD,MAEO,IAAIA,OAAM,CAACrC,KAAX,EAAkB;YACxB,OAAO;cACNA,KAAK,EAAE,IADD;cAENW,YAAY,EAAE0B,OAAM,CAAC1B;YAFf,CAAP;UAIA,CALM,MAKA;YACN;YACA,IAAI0B,OAAM,CAAClB,YAAX,EAAyB;cACxBA,YAAY,GAAG,IAAf;YACA,CAFD,MAEO;cACN,MAAM,IAAIZ,KAAJ,+BAAAmB,MAAA,CAAwCC,IAAI,CAACC,SAAL,CAAeS,OAAf,EAAuB,IAAvB,EAA6B,CAA7B,CAAxC,EAAN;YACA;UACD;QACD;MACD;MACD,IAAIlB,YAAJ,EAAkB;QACjB,OAAO;UACNA,YAAY,EAAE,IADR,CAEN;QAFM,CAAP;MAIA,CA3BF,CA4BC;;MACA;IAED,KAAK,IAAL;MACC,SAAAmB,UAAA,GAAAP,+BAAA,CAAmBjB,IAAI,CAACkB,IAAxB,GAAAO,MAAA,IAAAA,MAAA,GAAAD,UAAA,IAAAJ,IAAA,GAA8B;QAAA,IAAnBM,KAAmB,GAAAD,MAAA,CAAAH,KAAA;QAC7B,IAAIvB,UAAU,CAAC,CAAD,CAAV,KAAkB2B,KAAtB,EAA4B;UAC3B,IAAI3B,UAAU,CAACT,MAAX,KAAsB,CAA1B,EAA6B;YAC5B,OAAO;cACNJ,KAAK,EAAE,IADD;cAENW,YAAY,EAAEE;YAFR,CAAP;UAIA;UACD,IAAIE,IAAJ,EAAU;YACT,OAAO;cACNH,QAAQ,EAAE;YADJ,CAAP;UAGA;UACD,OAAO;YACNZ,KAAK,EAAE,IADD;YAENW,YAAY,EAAE,CAAC6B,KAAD;UAFR,CAAP;QAIA;MACD,CAnBF,CAoBC;;MACA;;IAED;;IACA;MACC,MAAM,IAAIjC,KAAJ,kCAAAmB,MAAA,CAA2CZ,IAA3C,EAAN;EAzDF;AA2DA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}