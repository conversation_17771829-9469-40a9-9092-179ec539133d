{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function switchMap(project, resultSelector) {\n  return operate(function (source, subscriber) {\n    var innerSubscriber = null;\n    var index = 0;\n    var isComplete = false;\n    var checkComplete = function () {\n      return isComplete && !innerSubscriber && subscriber.complete();\n    };\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      innerSubscriber === null || innerSubscriber === void 0 ? void 0 : innerSubscriber.unsubscribe();\n      var innerIndex = 0;\n      var outerIndex = index++;\n      innerFrom(project(value, outerIndex)).subscribe(innerSubscriber = createOperatorSubscriber(subscriber, function (innerValue) {\n        return subscriber.next(resultSelector ? resultSelector(value, innerValue, outerIndex, innerIndex++) : innerValue);\n      }, function () {\n        innerSubscriber = null;\n        checkComplete();\n      }));\n    }, function () {\n      isComplete = true;\n      checkComplete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["innerFrom", "operate", "createOperatorSubscriber", "switchMap", "project", "resultSelector", "source", "subscriber", "innerSubscriber", "index", "isComplete", "checkComplete", "complete", "subscribe", "value", "unsubscribe", "innerIndex", "outerIndex", "innerValue", "next"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\switchMap.ts"], "sourcesContent": ["import { Subscriber } from '../Subscriber';\nimport { ObservableInput, OperatorFunction, ObservedValueOf } from '../types';\nimport { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/* tslint:disable:max-line-length */\nexport function switchMap<T, O extends ObservableInput<any>>(\n  project: (value: T, index: number) => O\n): OperatorFunction<T, ObservedValueOf<O>>;\n/** @deprecated The `resultSelector` parameter will be removed in v8. Use an inner `map` instead. Details: https://rxjs.dev/deprecations/resultSelector */\nexport function switchMap<T, O extends ObservableInput<any>>(\n  project: (value: T, index: number) => O,\n  resultSelector: undefined\n): OperatorFunction<T, ObservedValueOf<O>>;\n/** @deprecated The `resultSelector` parameter will be removed in v8. Use an inner `map` instead. Details: https://rxjs.dev/deprecations/resultSelector */\nexport function switchMap<T, R, O extends ObservableInput<any>>(\n  project: (value: T, index: number) => O,\n  resultSelector: (outerValue: T, innerValue: ObservedValueOf<O>, outerIndex: number, innerIndex: number) => R\n): OperatorFunction<T, R>;\n/* tslint:enable:max-line-length */\n\n/**\n * Projects each source value to an Observable which is merged in the output\n * Observable, emitting values only from the most recently projected Observable.\n *\n * <span class=\"informal\">Maps each value to an Observable, then flattens all of\n * these inner Observables using {@link switchAll}.</span>\n *\n * ![](switchMap.png)\n *\n * Returns an Observable that emits items based on applying a function that you\n * supply to each item emitted by the source Observable, where that function\n * returns an (so-called \"inner\") Observable. Each time it observes one of these\n * inner Observables, the output Observable begins emitting the items emitted by\n * that inner Observable. When a new inner Observable is emitted, `switchMap`\n * stops emitting items from the earlier-emitted inner Observable and begins\n * emitting items from the new one. It continues to behave like this for\n * subsequent inner Observables.\n *\n * ## Example\n *\n * Generate new Observable according to source Observable values\n *\n * ```ts\n * import { of, switchMap } from 'rxjs';\n *\n * const switched = of(1, 2, 3).pipe(switchMap(x => of(x, x ** 2, x ** 3)));\n * switched.subscribe(x => console.log(x));\n * // outputs\n * // 1\n * // 1\n * // 1\n * // 2\n * // 4\n * // 8\n * // 3\n * // 9\n * // 27\n * ```\n *\n * Restart an interval Observable on every click event\n *\n * ```ts\n * import { fromEvent, switchMap, interval } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(switchMap(() => interval(1000)));\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link concatMap}\n * @see {@link exhaustMap}\n * @see {@link mergeMap}\n * @see {@link switchAll}\n * @see {@link switchMapTo}\n *\n * @param project A function that, when applied to an item emitted by the source\n * Observable, returns an Observable.\n * @return A function that returns an Observable that emits the result of\n * applying the projection function (and the optional deprecated\n * `resultSelector`) to each item emitted by the source Observable and taking\n * only the values from the most recently projected inner Observable.\n */\nexport function switchMap<T, R, O extends ObservableInput<any>>(\n  project: (value: T, index: number) => O,\n  resultSelector?: (outerValue: T, innerValue: ObservedValueOf<O>, outerIndex: number, innerIndex: number) => R\n): OperatorFunction<T, ObservedValueOf<O> | R> {\n  return operate((source, subscriber) => {\n    let innerSubscriber: Subscriber<ObservedValueOf<O>> | null = null;\n    let index = 0;\n    // Whether or not the source subscription has completed\n    let isComplete = false;\n\n    // We only complete the result if the source is complete AND we don't have an active inner subscription.\n    // This is called both when the source completes and when the inners complete.\n    const checkComplete = () => isComplete && !innerSubscriber && subscriber.complete();\n\n    source.subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        (value) => {\n          // Cancel the previous inner subscription if there was one\n          innerSubscriber?.unsubscribe();\n          let innerIndex = 0;\n          const outerIndex = index++;\n          // Start the next inner subscription\n          innerFrom(project(value, outerIndex)).subscribe(\n            (innerSubscriber = createOperatorSubscriber(\n              subscriber,\n              // When we get a new inner value, next it through. Note that this is\n              // handling the deprecate result selector here. This is because with this architecture\n              // it ends up being smaller than using the map operator.\n              (innerValue) => subscriber.next(resultSelector ? resultSelector(value, innerValue, outerIndex, innerIndex++) : innerValue),\n              () => {\n                // The inner has completed. Null out the inner subscriber to\n                // free up memory and to signal that we have no inner subscription\n                // currently.\n                innerSubscriber = null!;\n                checkComplete();\n              }\n            ))\n          );\n        },\n        () => {\n          isComplete = true;\n          checkComplete();\n        }\n      )\n    );\n  });\n}\n"], "mappings": "AAEA,SAASA,SAAS,QAAQ,yBAAyB;AACnD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAgF/D,OAAM,SAAUC,SAASA,CACvBC,OAAuC,EACvCC,cAA6G;EAE7G,OAAOJ,OAAO,CAAC,UAACK,MAAM,EAAEC,UAAU;IAChC,IAAIC,eAAe,GAA0C,IAAI;IACjE,IAAIC,KAAK,GAAG,CAAC;IAEb,IAAIC,UAAU,GAAG,KAAK;IAItB,IAAMC,aAAa,GAAG,SAAAA,CAAA;MAAM,OAAAD,UAAU,IAAI,CAACF,eAAe,IAAID,UAAU,CAACK,QAAQ,EAAE;IAAvD,CAAuD;IAEnFN,MAAM,CAACO,SAAS,CACdX,wBAAwB,CACtBK,UAAU,EACV,UAACO,KAAK;MAEJN,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEO,WAAW,EAAE;MAC9B,IAAIC,UAAU,GAAG,CAAC;MAClB,IAAMC,UAAU,GAAGR,KAAK,EAAE;MAE1BT,SAAS,CAACI,OAAO,CAACU,KAAK,EAAEG,UAAU,CAAC,CAAC,CAACJ,SAAS,CAC5CL,eAAe,GAAGN,wBAAwB,CACzCK,UAAU,EAIV,UAACW,UAAU;QAAK,OAAAX,UAAU,CAACY,IAAI,CAACd,cAAc,GAAGA,cAAc,CAACS,KAAK,EAAEI,UAAU,EAAED,UAAU,EAAED,UAAU,EAAE,CAAC,GAAGE,UAAU,CAAC;MAA1G,CAA0G,EAC1H;QAIEV,eAAe,GAAG,IAAK;QACvBG,aAAa,EAAE;MACjB,CAAC,CACD,CACH;IACH,CAAC,EACD;MACED,UAAU,GAAG,IAAI;MACjBC,aAAa,EAAE;IACjB,CAAC,CACF,CACF;EACH,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}