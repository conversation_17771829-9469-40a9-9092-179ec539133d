{"ast": null, "code": "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nimport LRUCache from './LRUCache.js'; // A cache for frequently used country-specific regular expressions. Set to 32 to cover ~2-3\n// countries being used for the same doc with ~10 patterns for each country. Some pages will have\n// a lot more countries in use, but typically fewer numbers for each so expanding the cache for\n// that use-case won't have a lot of benefit.\n\nvar RegExpCache = /*#__PURE__*/function () {\n  function RegExpCache(size) {\n    _classCallCheck(this, RegExpCache);\n    this.cache = new LRUCache(size);\n  }\n  _createClass(RegExpCache, [{\n    key: \"getPatternForRegExp\",\n    value: function getPatternForRegExp(pattern) {\n      var regExp = this.cache.get(pattern);\n      if (!regExp) {\n        regExp = new RegExp('^' + pattern);\n        this.cache.put(pattern, regExp);\n      }\n      return regExp;\n    }\n  }]);\n  return RegExpCache;\n}();\nexport { RegExpCache as default };", "map": {"version": 3, "names": ["L<PERSON><PERSON><PERSON>", "RegExpCache", "size", "_classCallCheck", "cache", "getPatternForRegExp", "pattern", "regExp", "get", "RegExp", "put"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\findNumbers\\RegExpCache.js"], "sourcesContent": ["import LRUCache from './LRUCache.js'\r\n\r\n// A cache for frequently used country-specific regular expressions. Set to 32 to cover ~2-3\r\n// countries being used for the same doc with ~10 patterns for each country. Some pages will have\r\n// a lot more countries in use, but typically fewer numbers for each so expanding the cache for\r\n// that use-case won't have a lot of benefit.\r\nexport default class RegExpCache {\r\n\tconstructor(size) {\r\n\t\tthis.cache = new LRUCache(size)\r\n\t}\r\n\r\n\tgetPatternForRegExp(pattern) {\r\n\t\tlet regExp = this.cache.get(pattern)\r\n\t\tif (!regExp) {\r\n\t\t\tregExp = new RegExp('^' + pattern)\r\n\t\t\tthis.cache.put(pattern, regExp)\r\n\t\t}\r\n\t\treturn regExp\r\n\t}\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,QAAP,MAAqB,eAArB,C,CAEA;AACA;AACA;AACA;;IACqBC,W;EACpB,SAAAA,YAAYC,IAAZ,EAAkB;IAAAC,eAAA,OAAAF,WAAA;IACjB,KAAKG,KAAL,GAAa,IAAIJ,QAAJ,CAAaE,IAAb,CAAb;EACA;;;WAED,SAAAG,oBAAoBC,OAApB,EAA6B;MAC5B,IAAIC,MAAM,GAAG,KAAKH,KAAL,CAAWI,GAAX,CAAeF,OAAf,CAAb;MACA,IAAI,CAACC,MAAL,EAAa;QACZA,MAAM,GAAG,IAAIE,MAAJ,CAAW,MAAMH,OAAjB,CAAT;QACA,KAAKF,KAAL,CAAWM,GAAX,CAAeJ,OAAf,EAAwBC,MAAxB;MACA;MACD,OAAOA,MAAP;IACA;;;;SAZmBN,W"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}