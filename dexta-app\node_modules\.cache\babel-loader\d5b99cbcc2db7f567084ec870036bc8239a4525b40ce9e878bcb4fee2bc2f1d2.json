{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport React from 'react';\nimport { isOptionGroup } from './useSelect.types';\nexport function areOptionsEqual(option1, option2) {\n  return option1.label === option2.label && option1.value === option2.value && option1.disabled === option2.disabled;\n}\nexport function getOptionsFromChildren(children) {\n  if (children == null) {\n    return [];\n  }\n  const selectChildren = [];\n  React.Children.forEach(children, node => {\n    var _props, _props2, _element$props$disabl2;\n    const nodeChildren = node == null ? void 0 : (_props = node.props) == null ? void 0 : _props.children;\n    if ((node == null ? void 0 : (_props2 = node.props) == null ? void 0 : _props2.value) === undefined) {\n      if (nodeChildren != null) {\n        var _element$props$disabl;\n        const element = node;\n        const group = {\n          options: getOptionsFromChildren(nodeChildren),\n          label: element.props.label,\n          disabled: (_element$props$disabl = element.props.disabled) != null ? _element$props$disabl : false\n        };\n        selectChildren.push(group);\n      }\n      return;\n    }\n    const element = node;\n    const option = {\n      value: element.props.value,\n      label: element.props.label || element.props.children,\n      disabled: (_element$props$disabl2 = element.props.disabled) != null ? _element$props$disabl2 : false\n    };\n    selectChildren.push(option);\n  });\n  return selectChildren != null ? selectChildren : [];\n}\nexport function flattenOptionGroups(groupedOptions, isGroupDisabled = false) {\n  let flatOptions = [];\n  groupedOptions.forEach(optionOrGroup => {\n    if (isOptionGroup(optionOrGroup)) {\n      flatOptions = flatOptions.concat(flattenOptionGroups(optionOrGroup.options, optionOrGroup.disabled));\n    } else {\n      flatOptions.push(_extends({}, optionOrGroup, {\n        disabled: isGroupDisabled || optionOrGroup.disabled\n      }));\n    }\n  });\n  return flatOptions;\n}", "map": {"version": 3, "names": ["_extends", "React", "isOptionGroup", "areOptionsEqual", "option1", "option2", "label", "value", "disabled", "getOptionsFromChildren", "children", "select<PERSON><PERSON><PERSON><PERSON>", "Children", "for<PERSON>ach", "node", "_props", "_props2", "_element$props$disabl2", "nodeChildren", "props", "undefined", "_element$props$disabl", "element", "group", "options", "push", "option", "flattenOptionGroups", "groupedOptions", "isGroupDisabled", "flatOptions", "optionOrGroup", "concat"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/SelectUnstyled/utils.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport React from 'react';\nimport { isOptionGroup } from './useSelect.types';\nexport function areOptionsEqual(option1, option2) {\n  return option1.label === option2.label && option1.value === option2.value && option1.disabled === option2.disabled;\n}\nexport function getOptionsFromChildren(children) {\n  if (children == null) {\n    return [];\n  }\n\n  const selectChildren = [];\n  React.Children.forEach(children, node => {\n    var _props, _props2, _element$props$disabl2;\n\n    const nodeChildren = node == null ? void 0 : (_props = node.props) == null ? void 0 : _props.children;\n\n    if ((node == null ? void 0 : (_props2 = node.props) == null ? void 0 : _props2.value) === undefined) {\n      if (nodeChildren != null) {\n        var _element$props$disabl;\n\n        const element = node;\n        const group = {\n          options: getOptionsFromChildren(nodeChildren),\n          label: element.props.label,\n          disabled: (_element$props$disabl = element.props.disabled) != null ? _element$props$disabl : false\n        };\n        selectChildren.push(group);\n      }\n\n      return;\n    }\n\n    const element = node;\n    const option = {\n      value: element.props.value,\n      label: element.props.label || element.props.children,\n      disabled: (_element$props$disabl2 = element.props.disabled) != null ? _element$props$disabl2 : false\n    };\n    selectChildren.push(option);\n  });\n  return selectChildren != null ? selectChildren : [];\n}\nexport function flattenOptionGroups(groupedOptions, isGroupDisabled = false) {\n  let flatOptions = [];\n  groupedOptions.forEach(optionOrGroup => {\n    if (isOptionGroup(optionOrGroup)) {\n      flatOptions = flatOptions.concat(flattenOptionGroups(optionOrGroup.options, optionOrGroup.disabled));\n    } else {\n      flatOptions.push(_extends({}, optionOrGroup, {\n        disabled: isGroupDisabled || optionOrGroup.disabled\n      }));\n    }\n  });\n  return flatOptions;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,mBAAmB;AACjD,OAAO,SAASC,eAAeA,CAACC,OAAO,EAAEC,OAAO,EAAE;EAChD,OAAOD,OAAO,CAACE,KAAK,KAAKD,OAAO,CAACC,KAAK,IAAIF,OAAO,CAACG,KAAK,KAAKF,OAAO,CAACE,KAAK,IAAIH,OAAO,CAACI,QAAQ,KAAKH,OAAO,CAACG,QAAQ;AACpH;AACA,OAAO,SAASC,sBAAsBA,CAACC,QAAQ,EAAE;EAC/C,IAAIA,QAAQ,IAAI,IAAI,EAAE;IACpB,OAAO,EAAE;EACX;EAEA,MAAMC,cAAc,GAAG,EAAE;EACzBV,KAAK,CAACW,QAAQ,CAACC,OAAO,CAACH,QAAQ,EAAEI,IAAI,IAAI;IACvC,IAAIC,MAAM,EAAEC,OAAO,EAAEC,sBAAsB;IAE3C,MAAMC,YAAY,GAAGJ,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAACC,MAAM,GAAGD,IAAI,CAACK,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,MAAM,CAACL,QAAQ;IAErG,IAAI,CAACI,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAACE,OAAO,GAAGF,IAAI,CAACK,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,OAAO,CAACT,KAAK,MAAMa,SAAS,EAAE;MACnG,IAAIF,YAAY,IAAI,IAAI,EAAE;QACxB,IAAIG,qBAAqB;QAEzB,MAAMC,OAAO,GAAGR,IAAI;QACpB,MAAMS,KAAK,GAAG;UACZC,OAAO,EAAEf,sBAAsB,CAACS,YAAY,CAAC;UAC7CZ,KAAK,EAAEgB,OAAO,CAACH,KAAK,CAACb,KAAK;UAC1BE,QAAQ,EAAE,CAACa,qBAAqB,GAAGC,OAAO,CAACH,KAAK,CAACX,QAAQ,KAAK,IAAI,GAAGa,qBAAqB,GAAG;QAC/F,CAAC;QACDV,cAAc,CAACc,IAAI,CAACF,KAAK,CAAC;MAC5B;MAEA;IACF;IAEA,MAAMD,OAAO,GAAGR,IAAI;IACpB,MAAMY,MAAM,GAAG;MACbnB,KAAK,EAAEe,OAAO,CAACH,KAAK,CAACZ,KAAK;MAC1BD,KAAK,EAAEgB,OAAO,CAACH,KAAK,CAACb,KAAK,IAAIgB,OAAO,CAACH,KAAK,CAACT,QAAQ;MACpDF,QAAQ,EAAE,CAACS,sBAAsB,GAAGK,OAAO,CAACH,KAAK,CAACX,QAAQ,KAAK,IAAI,GAAGS,sBAAsB,GAAG;IACjG,CAAC;IACDN,cAAc,CAACc,IAAI,CAACC,MAAM,CAAC;EAC7B,CAAC,CAAC;EACF,OAAOf,cAAc,IAAI,IAAI,GAAGA,cAAc,GAAG,EAAE;AACrD;AACA,OAAO,SAASgB,mBAAmBA,CAACC,cAAc,EAAEC,eAAe,GAAG,KAAK,EAAE;EAC3E,IAAIC,WAAW,GAAG,EAAE;EACpBF,cAAc,CAACf,OAAO,CAACkB,aAAa,IAAI;IACtC,IAAI7B,aAAa,CAAC6B,aAAa,CAAC,EAAE;MAChCD,WAAW,GAAGA,WAAW,CAACE,MAAM,CAACL,mBAAmB,CAACI,aAAa,CAACP,OAAO,EAAEO,aAAa,CAACvB,QAAQ,CAAC,CAAC;IACtG,CAAC,MAAM;MACLsB,WAAW,CAACL,IAAI,CAACzB,QAAQ,CAAC,CAAC,CAAC,EAAE+B,aAAa,EAAE;QAC3CvB,QAAQ,EAAEqB,eAAe,IAAIE,aAAa,CAACvB;MAC7C,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC;EACF,OAAOsB,WAAW;AACpB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}