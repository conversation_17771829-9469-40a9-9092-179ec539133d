{"ast": null, "code": "import Metadata from './metadata.js';\nimport matchesEntirely from './helpers/matchesEntirely.js';\nimport getNumberType from './helpers/getNumberType.js';\n/**\r\n * Checks if a given phone number is valid.\r\n *\r\n * isValid(phoneNumberInstance, { ..., v2: true }, metadata)\r\n *\r\n * isPossible({ phone: '8005553535', country: 'RU' }, { ... }, metadata)\r\n * isPossible({ phone: '8005553535', country: 'RU' }, undefined, metadata)\r\n *\r\n * If the `number` is a string, it will be parsed to an object,\r\n * but only if it contains only valid phone number characters (including punctuation).\r\n * If the `number` is an object, it is used as is.\r\n *\r\n * The optional `defaultCountry` argument is the default country.\r\n * I.e. it does not restrict to just that country,\r\n * e.g. in those cases where several countries share\r\n * the same phone numbering rules (NANPA, Britain, etc).\r\n * For example, even though the number `07624 369230`\r\n * belongs to the Isle of Man (\"IM\" country code)\r\n * calling `isValidNumber('07624369230', 'GB', metadata)`\r\n * still returns `true` because the country is not restricted to `GB`,\r\n * it's just that `GB` is the default one for the phone numbering rules.\r\n * For restricting the country see `isValidNumberForRegion()`\r\n * though restricting a country might not be a good idea.\r\n * https://github.com/googlei18n/libphonenumber/blob/master/FAQ.md#when-should-i-use-isvalidnumberforregion\r\n *\r\n * Examples:\r\n *\r\n * ```js\r\n * isValidNumber('+78005553535', metadata)\r\n * isValidNumber('8005553535', 'RU', metadata)\r\n * isValidNumber('88005553535', 'RU', metadata)\r\n * isValidNumber({ phone: '8005553535', country: 'RU' }, metadata)\r\n * ```\r\n */\n\nexport default function isValidNumber(input, options, metadata) {\n  // If assigning the `{}` default value is moved to the arguments above,\n  // code coverage would decrease for some weird reason.\n  options = options || {};\n  metadata = new Metadata(metadata);\n  /**\r\n   * Checks if a phone number is \"possible\" (basically just checks its length).\r\n   *\r\n   * @param  {object|PhoneNumber} input — If `options.v2: true` flag is passed, the `input` should be a `PhoneNumber` instance. Otherwise, it should be an object of shape `{ phone: '...', country: '...' }`.\r\n   * @param  {object} [options]\r\n   * @param  {object} metadata\r\n   * @return {string}\r\n   */\n\n  metadata.selectNumberingPlan(input.country, input.countryCallingCode); // By default, countries only have type regexps when it's required for\n  // distinguishing different countries having the same `countryCallingCode`.\n\n  if (metadata.hasTypes()) {\n    return getNumberType(input, options, metadata.metadata) !== undefined;\n  } // If there are no type regexps for this country in metadata then use\n  // `nationalNumberPattern` as a \"better than nothing\" replacement.\n\n  var nationalNumber = options.v2 ? input.nationalNumber : input.phone;\n  return matchesEntirely(nationalNumber, metadata.nationalNumberPattern());\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "matchesEntirely", "getNumberType", "isValidNumber", "input", "options", "metadata", "selectNumberingPlan", "country", "countryCallingCode", "hasTypes", "undefined", "nationalNumber", "v2", "phone", "nationalNumberPattern"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\isValid.js"], "sourcesContent": ["import Metadata from './metadata.js'\r\nimport matchesEntirely from './helpers/matchesEntirely.js'\r\nimport getNumberType from './helpers/getNumberType.js'\r\n\r\n/**\r\n * Checks if a given phone number is valid.\r\n *\r\n * isValid(phoneNumberInstance, { ..., v2: true }, metadata)\r\n *\r\n * isPossible({ phone: '8005553535', country: 'RU' }, { ... }, metadata)\r\n * isPossible({ phone: '8005553535', country: 'RU' }, undefined, metadata)\r\n *\r\n * If the `number` is a string, it will be parsed to an object,\r\n * but only if it contains only valid phone number characters (including punctuation).\r\n * If the `number` is an object, it is used as is.\r\n *\r\n * The optional `defaultCountry` argument is the default country.\r\n * I.e. it does not restrict to just that country,\r\n * e.g. in those cases where several countries share\r\n * the same phone numbering rules (NANPA, Britain, etc).\r\n * For example, even though the number `07624 369230`\r\n * belongs to the Isle of Man (\"IM\" country code)\r\n * calling `isValidNumber('07624369230', 'GB', metadata)`\r\n * still returns `true` because the country is not restricted to `GB`,\r\n * it's just that `GB` is the default one for the phone numbering rules.\r\n * For restricting the country see `isValidNumberForRegion()`\r\n * though restricting a country might not be a good idea.\r\n * https://github.com/googlei18n/libphonenumber/blob/master/FAQ.md#when-should-i-use-isvalidnumberforregion\r\n *\r\n * Examples:\r\n *\r\n * ```js\r\n * isValidNumber('+78005553535', metadata)\r\n * isValidNumber('8005553535', 'RU', metadata)\r\n * isValidNumber('88005553535', 'RU', metadata)\r\n * isValidNumber({ phone: '8005553535', country: 'RU' }, metadata)\r\n * ```\r\n */\r\nexport default function isValidNumber(input, options, metadata)\r\n{\r\n\t// If assigning the `{}` default value is moved to the arguments above,\r\n\t// code coverage would decrease for some weird reason.\r\n\toptions = options || {}\r\n\r\n\tmetadata = new Metadata(metadata)\r\n\r\n/**\r\n * Checks if a phone number is \"possible\" (basically just checks its length).\r\n *\r\n * @param  {object|PhoneNumber} input — If `options.v2: true` flag is passed, the `input` should be a `PhoneNumber` instance. Otherwise, it should be an object of shape `{ phone: '...', country: '...' }`.\r\n * @param  {object} [options]\r\n * @param  {object} metadata\r\n * @return {string}\r\n */\r\n\r\n\tmetadata.selectNumberingPlan(input.country, input.countryCallingCode)\r\n\r\n\t// By default, countries only have type regexps when it's required for\r\n\t// distinguishing different countries having the same `countryCallingCode`.\r\n\tif (metadata.hasTypes()) {\r\n\t\treturn getNumberType(input, options, metadata.metadata) !== undefined\r\n\t}\r\n\r\n\t// If there are no type regexps for this country in metadata then use\r\n\t// `nationalNumberPattern` as a \"better than nothing\" replacement.\r\n\tconst nationalNumber = options.v2 ? input.nationalNumber : input.phone\r\n\treturn matchesEntirely(nationalNumber, metadata.nationalNumberPattern())\r\n}"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,eAArB;AACA,OAAOC,eAAP,MAA4B,8BAA5B;AACA,OAAOC,aAAP,MAA0B,4BAA1B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASC,aAATA,CAAuBC,KAAvB,EAA8BC,OAA9B,EAAuCC,QAAvC,EACf;EACC;EACA;EACAD,OAAO,GAAGA,OAAO,IAAI,EAArB;EAEAC,QAAQ,GAAG,IAAIN,QAAJ,CAAaM,QAAb,CAAX;EAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAECA,QAAQ,CAACC,mBAAT,CAA6BH,KAAK,CAACI,OAAnC,EAA4CJ,KAAK,CAACK,kBAAlD,EAhBD,CAkBC;EACA;;EACA,IAAIH,QAAQ,CAACI,QAAT,EAAJ,EAAyB;IACxB,OAAOR,aAAa,CAACE,KAAD,EAAQC,OAAR,EAAiBC,QAAQ,CAACA,QAA1B,CAAb,KAAqDK,SAA5D;EACA,CAtBF,CAwBC;EACA;;EACA,IAAMC,cAAc,GAAGP,OAAO,CAACQ,EAAR,GAAaT,KAAK,CAACQ,cAAnB,GAAoCR,KAAK,CAACU,KAAjE;EACA,OAAOb,eAAe,CAACW,cAAD,EAAiBN,QAAQ,CAACS,qBAAT,EAAjB,CAAtB;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}