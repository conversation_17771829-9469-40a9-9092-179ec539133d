{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"component\", \"components\", \"componentsProps\", \"colSpan\", \"count\", \"getItemAriaLabel\", \"labelDisplayedRows\", \"labelId\", \"labelRowsPerPage\", \"onPageChange\", \"onRowsPerPageChange\", \"page\", \"rowsPerPage\", \"rowsPerPageOptions\", \"selectId\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_useId as useId, chainPropTypes, integerPropType } from '@mui/utils';\nimport { useSlotProps } from '../utils';\nimport composeClasses from '../composeClasses';\nimport isHostComponent from '../utils/isHostComponent';\nimport TablePaginationActionsUnstyled from './TablePaginationActionsUnstyled';\nimport { getTablePaginationUnstyledUtilityClass } from './tablePaginationUnstyledClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { createElement as _createElement } from \"react\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction defaultLabelDisplayedRows({\n  from,\n  to,\n  count\n}) {\n  return `${from}–${to} of ${count !== -1 ? count : `more than ${to}`}`;\n}\nfunction defaultGetAriaLabel(type) {\n  return `Go to ${type} page`;\n}\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root'],\n    toolbar: ['toolbar'],\n    spacer: ['spacer'],\n    selectLabel: ['selectLabel'],\n    select: ['select'],\n    input: ['input'],\n    selectIcon: ['selectIcon'],\n    menuItem: ['menuItem'],\n    displayedRows: ['displayedRows'],\n    actions: ['actions']\n  };\n  return composeClasses(slots, getTablePaginationUnstyledUtilityClass, {});\n};\n/**\n * A pagination for tables.\n *\n * Demos:\n *\n * - [Unstyled table pagination](https://mui.com/base/react-table-pagination/)\n *\n * API:\n *\n * - [TablePaginationUnstyled API](https://mui.com/base/api/table-pagination-unstyled/)\n */\n\nconst TablePaginationUnstyled = /*#__PURE__*/React.forwardRef(function TablePaginationUnstyled(props, ref) {\n  var _ref, _components$Select, _components$Actions, _components$MenuItem, _components$SelectLab, _components$Displayed, _components$Toolbar, _components$Spacer;\n  const {\n      component,\n      components = {},\n      componentsProps = {},\n      colSpan: colSpanProp,\n      count,\n      getItemAriaLabel = defaultGetAriaLabel,\n      labelDisplayedRows = defaultLabelDisplayedRows,\n      labelId: labelIdProp,\n      labelRowsPerPage = 'Rows per page:',\n      onPageChange,\n      onRowsPerPageChange,\n      page,\n      rowsPerPage,\n      rowsPerPageOptions = [10, 25, 50, 100],\n      selectId: selectIdProp\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses();\n  let colSpan;\n  if (!component || component === 'td' || !isHostComponent(component)) {\n    colSpan = colSpanProp || 1000; // col-span over everything\n  }\n\n  const getLabelDisplayedRowsTo = () => {\n    if (count === -1) {\n      return (page + 1) * rowsPerPage;\n    }\n    return rowsPerPage === -1 ? count : Math.min(count, (page + 1) * rowsPerPage);\n  };\n  const selectId = useId(selectIdProp);\n  const labelId = useId(labelIdProp);\n  const Root = (_ref = component != null ? component : components.Root) != null ? _ref : 'td';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      colSpan,\n      ref\n    },\n    ownerState,\n    className: classes.root\n  });\n  const Select = (_components$Select = components.Select) != null ? _components$Select : 'select';\n  const selectProps = useSlotProps({\n    elementType: Select,\n    externalSlotProps: componentsProps.select,\n    additionalProps: {\n      value: rowsPerPage,\n      id: selectId,\n      onChange: e => onRowsPerPageChange && onRowsPerPageChange(e),\n      'aria-label': rowsPerPage.toString(),\n      'aria-labelledby': [labelId, selectId].filter(Boolean).join(' ') || undefined\n    },\n    ownerState,\n    className: classes.select\n  });\n  const Actions = (_components$Actions = components.Actions) != null ? _components$Actions : TablePaginationActionsUnstyled;\n  const actionsProps = useSlotProps({\n    elementType: Actions,\n    externalSlotProps: componentsProps.actions,\n    additionalProps: {\n      page,\n      rowsPerPage,\n      count,\n      onPageChange,\n      getItemAriaLabel\n    },\n    ownerState,\n    className: classes.actions\n  });\n  const MenuItem = (_components$MenuItem = components.MenuItem) != null ? _components$MenuItem : 'option';\n  const menuItemProps = useSlotProps({\n    elementType: MenuItem,\n    externalSlotProps: componentsProps.menuItem,\n    additionalProps: {\n      value: undefined\n    },\n    ownerState,\n    className: classes.menuItem\n  });\n  const SelectLabel = (_components$SelectLab = components.SelectLabel) != null ? _components$SelectLab : 'p';\n  const selectLabelProps = useSlotProps({\n    elementType: SelectLabel,\n    externalSlotProps: componentsProps.selectLabel,\n    additionalProps: {\n      id: labelId\n    },\n    ownerState,\n    className: classes.selectLabel\n  });\n  const DisplayedRows = (_components$Displayed = components.DisplayedRows) != null ? _components$Displayed : 'p';\n  const displayedRowsProps = useSlotProps({\n    elementType: DisplayedRows,\n    externalSlotProps: componentsProps.displayedRows,\n    ownerState,\n    className: classes.displayedRows\n  });\n  const Toolbar = (_components$Toolbar = components.Toolbar) != null ? _components$Toolbar : 'div';\n  const toolbarProps = useSlotProps({\n    elementType: Toolbar,\n    externalSlotProps: componentsProps.toolbar,\n    ownerState,\n    className: classes.toolbar\n  });\n  const Spacer = (_components$Spacer = components.Spacer) != null ? _components$Spacer : 'div';\n  const spacerProps = useSlotProps({\n    elementType: Spacer,\n    externalSlotProps: componentsProps.spacer,\n    ownerState,\n    className: classes.spacer\n  });\n  return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n    children: /*#__PURE__*/_jsxs(Toolbar, _extends({}, toolbarProps, {\n      children: [/*#__PURE__*/_jsx(Spacer, _extends({}, spacerProps)), rowsPerPageOptions.length > 1 && /*#__PURE__*/_jsx(SelectLabel, _extends({}, selectLabelProps, {\n        children: labelRowsPerPage\n      })), rowsPerPageOptions.length > 1 && /*#__PURE__*/_jsx(Select, _extends({}, selectProps, {\n        children: rowsPerPageOptions.map(rowsPerPageOption => /*#__PURE__*/_createElement(MenuItem, _extends({}, menuItemProps, {\n          key: typeof rowsPerPageOption !== 'number' && rowsPerPageOption.label ? rowsPerPageOption.label : rowsPerPageOption,\n          value: typeof rowsPerPageOption !== 'number' && rowsPerPageOption.value ? rowsPerPageOption.value : rowsPerPageOption\n        }), typeof rowsPerPageOption !== 'number' && rowsPerPageOption.label ? rowsPerPageOption.label : rowsPerPageOption))\n      })), /*#__PURE__*/_jsx(DisplayedRows, _extends({}, displayedRowsProps, {\n        children: labelDisplayedRows({\n          from: count === 0 ? 0 : page * rowsPerPage + 1,\n          to: getLabelDisplayedRowsTo(),\n          count: count === -1 ? -1 : count,\n          page\n        })\n      })), /*#__PURE__*/_jsx(Actions, _extends({}, actionsProps))]\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TablePaginationUnstyled.propTypes\n/* remove-proptypes */ = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  colSpan: PropTypes.number,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside the TablePagination.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Actions: PropTypes.elementType,\n    DisplayedRows: PropTypes.elementType,\n    MenuItem: PropTypes.elementType,\n    Root: PropTypes.elementType,\n    Select: PropTypes.elementType,\n    SelectLabel: PropTypes.elementType,\n    Spacer: PropTypes.elementType,\n    Toolbar: PropTypes.elementType\n  }),\n  /**\n   * The props used for each slot inside the TablePagination.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    actions: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    displayedRows: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    menuItem: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    select: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    selectLabel: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    spacer: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    toolbar: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The total number of rows.\n   *\n   * To enable server side pagination for an unknown number of items, provide -1.\n   */\n  count: PropTypes.number.isRequired,\n  /**\n   * Accepts a function which returns a string value that provides a user-friendly name for the current page.\n   * This is important for screen reader users.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @param {string} type The link or button type to format ('first' | 'last' | 'next' | 'previous').\n   * @returns {string}\n   * @default function defaultGetAriaLabel(type: ItemAriaLabelType) {\n   *   return `Go to ${type} page`;\n   * }\n   */\n  getItemAriaLabel: PropTypes.func,\n  /**\n   * Customize the displayed rows label. Invoked with a `{ from, to, count, page }`\n   * object.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default function defaultLabelDisplayedRows({ from, to, count }: LabelDisplayedRowsArgs) {\n   *   return `${from}–${to} of ${count !== -1 ? count : `more than ${to}`}`;\n   * }\n   */\n  labelDisplayedRows: PropTypes.func,\n  /**\n   * Id of the label element within the pagination.\n   */\n  labelId: PropTypes.string,\n  /**\n   * Customize the rows per page label.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default 'Rows per page:'\n   */\n  labelRowsPerPage: PropTypes.node,\n  /**\n   * Callback fired when the page is changed.\n   *\n   * @param {React.MouseEvent<HTMLButtonElement> | null} event The event source of the callback.\n   * @param {number} page The page selected.\n   */\n  onPageChange: PropTypes.func.isRequired,\n  /**\n   * Callback fired when the number of rows per page is changed.\n   *\n   * @param {React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>} event The event source of the callback.\n   */\n  onRowsPerPageChange: PropTypes.func,\n  /**\n   * The zero-based index of the current page.\n   */\n  page: chainPropTypes(integerPropType.isRequired, props => {\n    const {\n      count,\n      page,\n      rowsPerPage\n    } = props;\n    if (count === -1) {\n      return null;\n    }\n    const newLastPage = Math.max(0, Math.ceil(count / rowsPerPage) - 1);\n    if (page < 0 || page > newLastPage) {\n      return new Error('MUI: The page prop of a TablePaginationUnstyled is out of range ' + `(0 to ${newLastPage}, but page is ${page}).`);\n    }\n    return null;\n  }),\n  /**\n   * The number of rows per page.\n   *\n   * Set -1 to display all the rows.\n   */\n  rowsPerPage: integerPropType.isRequired,\n  /**\n   * Customizes the options of the rows per page select field. If less than two options are\n   * available, no select field will be displayed.\n   * Use -1 for the value with a custom label to show all the rows.\n   * @default [10, 25, 50, 100]\n   */\n  rowsPerPageOptions: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    label: PropTypes.string.isRequired,\n    value: PropTypes.number.isRequired\n  })]).isRequired),\n  /**\n   * Id of the select element within the pagination.\n   */\n  selectId: PropTypes.string\n} : void 0;\nexport default TablePaginationUnstyled;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "unstable_useId", "useId", "chainPropTypes", "integerPropType", "useSlotProps", "composeClasses", "isHostComponent", "TablePaginationActionsUnstyled", "getTablePaginationUnstyledUtilityClass", "jsx", "_jsx", "createElement", "_createElement", "jsxs", "_jsxs", "defaultLabelDisplayedRows", "from", "to", "count", "defaultGetAriaLabel", "type", "useUtilityClasses", "slots", "root", "toolbar", "spacer", "selectLabel", "select", "input", "selectIcon", "menuItem", "displayedRows", "actions", "TablePaginationUnstyled", "forwardRef", "props", "ref", "_ref", "_components$Select", "_components$Actions", "_components$MenuItem", "_components$SelectLab", "_components$Displayed", "_components$Toolbar", "_components$Spacer", "component", "components", "componentsProps", "colSpan", "colSpanProp", "getItemAriaLabel", "labelDisplayedRows", "labelId", "labelIdProp", "labelRowsPerPage", "onPageChange", "onRowsPerPageChange", "page", "rowsPerPage", "rowsPerPageOptions", "selectId", "selectIdProp", "other", "ownerState", "classes", "getLabelDisplayedRowsTo", "Math", "min", "Root", "rootProps", "elementType", "externalSlotProps", "externalForwardedProps", "additionalProps", "className", "Select", "selectProps", "value", "id", "onChange", "e", "toString", "filter", "Boolean", "join", "undefined", "Actions", "actionsProps", "MenuItem", "menuItemProps", "SelectLabel", "selectLabelProps", "DisplayedRows", "displayedRowsProps", "<PERSON><PERSON><PERSON>", "toolbarProps", "Spacer", "spacerProps", "children", "length", "map", "rowsPerPageOption", "key", "label", "process", "env", "NODE_ENV", "propTypes", "node", "number", "shape", "oneOfType", "func", "object", "isRequired", "string", "newLastPage", "max", "ceil", "Error", "arrayOf"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/TablePaginationUnstyled/TablePaginationUnstyled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"component\", \"components\", \"componentsProps\", \"colSpan\", \"count\", \"getItemAriaLabel\", \"labelDisplayedRows\", \"labelId\", \"labelRowsPerPage\", \"onPageChange\", \"onRowsPerPageChange\", \"page\", \"rowsPerPage\", \"rowsPerPageOptions\", \"selectId\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_useId as useId, chainPropTypes, integerPropType } from '@mui/utils';\nimport { useSlotProps } from '../utils';\nimport composeClasses from '../composeClasses';\nimport isHostComponent from '../utils/isHostComponent';\nimport TablePaginationActionsUnstyled from './TablePaginationActionsUnstyled';\nimport { getTablePaginationUnstyledUtilityClass } from './tablePaginationUnstyledClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { createElement as _createElement } from \"react\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\n\nfunction defaultLabelDisplayedRows({\n  from,\n  to,\n  count\n}) {\n  return `${from}–${to} of ${count !== -1 ? count : `more than ${to}`}`;\n}\n\nfunction defaultGetAriaLabel(type) {\n  return `Go to ${type} page`;\n}\n\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root'],\n    toolbar: ['toolbar'],\n    spacer: ['spacer'],\n    selectLabel: ['selectLabel'],\n    select: ['select'],\n    input: ['input'],\n    selectIcon: ['selectIcon'],\n    menuItem: ['menuItem'],\n    displayedRows: ['displayedRows'],\n    actions: ['actions']\n  };\n  return composeClasses(slots, getTablePaginationUnstyledUtilityClass, {});\n};\n/**\n * A pagination for tables.\n *\n * Demos:\n *\n * - [Unstyled table pagination](https://mui.com/base/react-table-pagination/)\n *\n * API:\n *\n * - [TablePaginationUnstyled API](https://mui.com/base/api/table-pagination-unstyled/)\n */\n\n\nconst TablePaginationUnstyled = /*#__PURE__*/React.forwardRef(function TablePaginationUnstyled(props, ref) {\n  var _ref, _components$Select, _components$Actions, _components$MenuItem, _components$SelectLab, _components$Displayed, _components$Toolbar, _components$Spacer;\n\n  const {\n    component,\n    components = {},\n    componentsProps = {},\n    colSpan: colSpanProp,\n    count,\n    getItemAriaLabel = defaultGetAriaLabel,\n    labelDisplayedRows = defaultLabelDisplayedRows,\n    labelId: labelIdProp,\n    labelRowsPerPage = 'Rows per page:',\n    onPageChange,\n    onRowsPerPageChange,\n    page,\n    rowsPerPage,\n    rowsPerPageOptions = [10, 25, 50, 100],\n    selectId: selectIdProp\n  } = props,\n        other = _objectWithoutPropertiesLoose(props, _excluded);\n\n  const ownerState = props;\n  const classes = useUtilityClasses();\n  let colSpan;\n\n  if (!component || component === 'td' || !isHostComponent(component)) {\n    colSpan = colSpanProp || 1000; // col-span over everything\n  }\n\n  const getLabelDisplayedRowsTo = () => {\n    if (count === -1) {\n      return (page + 1) * rowsPerPage;\n    }\n\n    return rowsPerPage === -1 ? count : Math.min(count, (page + 1) * rowsPerPage);\n  };\n\n  const selectId = useId(selectIdProp);\n  const labelId = useId(labelIdProp);\n  const Root = (_ref = component != null ? component : components.Root) != null ? _ref : 'td';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: componentsProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      colSpan,\n      ref\n    },\n    ownerState,\n    className: classes.root\n  });\n  const Select = (_components$Select = components.Select) != null ? _components$Select : 'select';\n  const selectProps = useSlotProps({\n    elementType: Select,\n    externalSlotProps: componentsProps.select,\n    additionalProps: {\n      value: rowsPerPage,\n      id: selectId,\n      onChange: e => onRowsPerPageChange && onRowsPerPageChange(e),\n      'aria-label': rowsPerPage.toString(),\n      'aria-labelledby': [labelId, selectId].filter(Boolean).join(' ') || undefined\n    },\n    ownerState,\n    className: classes.select\n  });\n  const Actions = (_components$Actions = components.Actions) != null ? _components$Actions : TablePaginationActionsUnstyled;\n  const actionsProps = useSlotProps({\n    elementType: Actions,\n    externalSlotProps: componentsProps.actions,\n    additionalProps: {\n      page,\n      rowsPerPage,\n      count,\n      onPageChange,\n      getItemAriaLabel\n    },\n    ownerState,\n    className: classes.actions\n  });\n  const MenuItem = (_components$MenuItem = components.MenuItem) != null ? _components$MenuItem : 'option';\n  const menuItemProps = useSlotProps({\n    elementType: MenuItem,\n    externalSlotProps: componentsProps.menuItem,\n    additionalProps: {\n      value: undefined\n    },\n    ownerState,\n    className: classes.menuItem\n  });\n  const SelectLabel = (_components$SelectLab = components.SelectLabel) != null ? _components$SelectLab : 'p';\n  const selectLabelProps = useSlotProps({\n    elementType: SelectLabel,\n    externalSlotProps: componentsProps.selectLabel,\n    additionalProps: {\n      id: labelId\n    },\n    ownerState,\n    className: classes.selectLabel\n  });\n  const DisplayedRows = (_components$Displayed = components.DisplayedRows) != null ? _components$Displayed : 'p';\n  const displayedRowsProps = useSlotProps({\n    elementType: DisplayedRows,\n    externalSlotProps: componentsProps.displayedRows,\n    ownerState,\n    className: classes.displayedRows\n  });\n  const Toolbar = (_components$Toolbar = components.Toolbar) != null ? _components$Toolbar : 'div';\n  const toolbarProps = useSlotProps({\n    elementType: Toolbar,\n    externalSlotProps: componentsProps.toolbar,\n    ownerState,\n    className: classes.toolbar\n  });\n  const Spacer = (_components$Spacer = components.Spacer) != null ? _components$Spacer : 'div';\n  const spacerProps = useSlotProps({\n    elementType: Spacer,\n    externalSlotProps: componentsProps.spacer,\n    ownerState,\n    className: classes.spacer\n  });\n  return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n    children: /*#__PURE__*/_jsxs(Toolbar, _extends({}, toolbarProps, {\n      children: [/*#__PURE__*/_jsx(Spacer, _extends({}, spacerProps)), rowsPerPageOptions.length > 1 && /*#__PURE__*/_jsx(SelectLabel, _extends({}, selectLabelProps, {\n        children: labelRowsPerPage\n      })), rowsPerPageOptions.length > 1 && /*#__PURE__*/_jsx(Select, _extends({}, selectProps, {\n        children: rowsPerPageOptions.map(rowsPerPageOption => /*#__PURE__*/_createElement(MenuItem, _extends({}, menuItemProps, {\n          key: typeof rowsPerPageOption !== 'number' && rowsPerPageOption.label ? rowsPerPageOption.label : rowsPerPageOption,\n          value: typeof rowsPerPageOption !== 'number' && rowsPerPageOption.value ? rowsPerPageOption.value : rowsPerPageOption\n        }), typeof rowsPerPageOption !== 'number' && rowsPerPageOption.label ? rowsPerPageOption.label : rowsPerPageOption))\n      })), /*#__PURE__*/_jsx(DisplayedRows, _extends({}, displayedRowsProps, {\n        children: labelDisplayedRows({\n          from: count === 0 ? 0 : page * rowsPerPage + 1,\n          to: getLabelDisplayedRowsTo(),\n          count: count === -1 ? -1 : count,\n          page\n        })\n      })), /*#__PURE__*/_jsx(Actions, _extends({}, actionsProps))]\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? TablePaginationUnstyled.propTypes\n/* remove-proptypes */\n= {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // |     To update them edit TypeScript types and run \"yarn proptypes\"  |\n  // ----------------------------------------------------------------------\n\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n\n  /**\n   * @ignore\n   */\n  colSpan: PropTypes.number,\n\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n\n  /**\n   * The components used for each slot inside the TablePagination.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Actions: PropTypes.elementType,\n    DisplayedRows: PropTypes.elementType,\n    MenuItem: PropTypes.elementType,\n    Root: PropTypes.elementType,\n    Select: PropTypes.elementType,\n    SelectLabel: PropTypes.elementType,\n    Spacer: PropTypes.elementType,\n    Toolbar: PropTypes.elementType\n  }),\n\n  /**\n   * The props used for each slot inside the TablePagination.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    actions: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    displayedRows: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    menuItem: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    select: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    selectLabel: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    spacer: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    toolbar: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n\n  /**\n   * The total number of rows.\n   *\n   * To enable server side pagination for an unknown number of items, provide -1.\n   */\n  count: PropTypes.number.isRequired,\n\n  /**\n   * Accepts a function which returns a string value that provides a user-friendly name for the current page.\n   * This is important for screen reader users.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @param {string} type The link or button type to format ('first' | 'last' | 'next' | 'previous').\n   * @returns {string}\n   * @default function defaultGetAriaLabel(type: ItemAriaLabelType) {\n   *   return `Go to ${type} page`;\n   * }\n   */\n  getItemAriaLabel: PropTypes.func,\n\n  /**\n   * Customize the displayed rows label. Invoked with a `{ from, to, count, page }`\n   * object.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default function defaultLabelDisplayedRows({ from, to, count }: LabelDisplayedRowsArgs) {\n   *   return `${from}–${to} of ${count !== -1 ? count : `more than ${to}`}`;\n   * }\n   */\n  labelDisplayedRows: PropTypes.func,\n\n  /**\n   * Id of the label element within the pagination.\n   */\n  labelId: PropTypes.string,\n\n  /**\n   * Customize the rows per page label.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default 'Rows per page:'\n   */\n  labelRowsPerPage: PropTypes.node,\n\n  /**\n   * Callback fired when the page is changed.\n   *\n   * @param {React.MouseEvent<HTMLButtonElement> | null} event The event source of the callback.\n   * @param {number} page The page selected.\n   */\n  onPageChange: PropTypes.func.isRequired,\n\n  /**\n   * Callback fired when the number of rows per page is changed.\n   *\n   * @param {React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>} event The event source of the callback.\n   */\n  onRowsPerPageChange: PropTypes.func,\n\n  /**\n   * The zero-based index of the current page.\n   */\n  page: chainPropTypes(integerPropType.isRequired, props => {\n    const {\n      count,\n      page,\n      rowsPerPage\n    } = props;\n\n    if (count === -1) {\n      return null;\n    }\n\n    const newLastPage = Math.max(0, Math.ceil(count / rowsPerPage) - 1);\n\n    if (page < 0 || page > newLastPage) {\n      return new Error('MUI: The page prop of a TablePaginationUnstyled is out of range ' + `(0 to ${newLastPage}, but page is ${page}).`);\n    }\n\n    return null;\n  }),\n\n  /**\n   * The number of rows per page.\n   *\n   * Set -1 to display all the rows.\n   */\n  rowsPerPage: integerPropType.isRequired,\n\n  /**\n   * Customizes the options of the rows per page select field. If less than two options are\n   * available, no select field will be displayed.\n   * Use -1 for the value with a custom label to show all the rows.\n   * @default [10, 25, 50, 100]\n   */\n  rowsPerPageOptions: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    label: PropTypes.string.isRequired,\n    value: PropTypes.number.isRequired\n  })]).isRequired),\n\n  /**\n   * Id of the select element within the pagination.\n   */\n  selectId: PropTypes.string\n} : void 0;\nexport default TablePaginationUnstyled;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,SAAS,EAAE,OAAO,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,SAAS,EAAE,kBAAkB,EAAE,cAAc,EAAE,qBAAqB,EAAE,MAAM,EAAE,aAAa,EAAE,oBAAoB,EAAE,UAAU,CAAC;AAC7P,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,cAAc,IAAIC,KAAK,EAAEC,cAAc,EAAEC,eAAe,QAAQ,YAAY;AACrF,SAASC,YAAY,QAAQ,UAAU;AACvC,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,eAAe,MAAM,0BAA0B;AACtD,OAAOC,8BAA8B,MAAM,kCAAkC;AAC7E,SAASC,sCAAsC,QAAQ,kCAAkC;AACzF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,aAAa,IAAIC,cAAc,QAAQ,OAAO;AACvD,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAEjD,SAASC,yBAAyBA,CAAC;EACjCC,IAAI;EACJC,EAAE;EACFC;AACF,CAAC,EAAE;EACD,OAAQ,GAAEF,IAAK,IAAGC,EAAG,OAAMC,KAAK,KAAK,CAAC,CAAC,GAAGA,KAAK,GAAI,aAAYD,EAAG,EAAE,EAAC;AACvE;AAEA,SAASE,mBAAmBA,CAACC,IAAI,EAAE;EACjC,OAAQ,SAAQA,IAAK,OAAM;AAC7B;AAEA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,OAAO,EAAE,CAAC,SAAS,CAAC;IACpBC,MAAM,EAAE,CAAC,QAAQ,CAAC;IAClBC,WAAW,EAAE,CAAC,aAAa,CAAC;IAC5BC,MAAM,EAAE,CAAC,QAAQ,CAAC;IAClBC,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBC,UAAU,EAAE,CAAC,YAAY,CAAC;IAC1BC,QAAQ,EAAE,CAAC,UAAU,CAAC;IACtBC,aAAa,EAAE,CAAC,eAAe,CAAC;IAChCC,OAAO,EAAE,CAAC,SAAS;EACrB,CAAC;EACD,OAAO3B,cAAc,CAACiB,KAAK,EAAEd,sCAAsC,EAAE,CAAC,CAAC,CAAC;AAC1E,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,MAAMyB,uBAAuB,GAAG,aAAanC,KAAK,CAACoC,UAAU,CAAC,SAASD,uBAAuBA,CAACE,KAAK,EAAEC,GAAG,EAAE;EACzG,IAAIC,IAAI,EAAEC,kBAAkB,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,kBAAkB;EAE9J,MAAM;MACJC,SAAS;MACTC,UAAU,GAAG,CAAC,CAAC;MACfC,eAAe,GAAG,CAAC,CAAC;MACpBC,OAAO,EAAEC,WAAW;MACpB/B,KAAK;MACLgC,gBAAgB,GAAG/B,mBAAmB;MACtCgC,kBAAkB,GAAGpC,yBAAyB;MAC9CqC,OAAO,EAAEC,WAAW;MACpBC,gBAAgB,GAAG,gBAAgB;MACnCC,YAAY;MACZC,mBAAmB;MACnBC,IAAI;MACJC,WAAW;MACXC,kBAAkB,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;MACtCC,QAAQ,EAAEC;IACZ,CAAC,GAAG1B,KAAK;IACH2B,KAAK,GAAGlE,6BAA6B,CAACuC,KAAK,EAAEtC,SAAS,CAAC;EAE7D,MAAMkE,UAAU,GAAG5B,KAAK;EACxB,MAAM6B,OAAO,GAAG3C,iBAAiB,CAAC,CAAC;EACnC,IAAI2B,OAAO;EAEX,IAAI,CAACH,SAAS,IAAIA,SAAS,KAAK,IAAI,IAAI,CAACvC,eAAe,CAACuC,SAAS,CAAC,EAAE;IACnEG,OAAO,GAAGC,WAAW,IAAI,IAAI,CAAC,CAAC;EACjC;;EAEA,MAAMgB,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI/C,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,OAAO,CAACuC,IAAI,GAAG,CAAC,IAAIC,WAAW;IACjC;IAEA,OAAOA,WAAW,KAAK,CAAC,CAAC,GAAGxC,KAAK,GAAGgD,IAAI,CAACC,GAAG,CAACjD,KAAK,EAAE,CAACuC,IAAI,GAAG,CAAC,IAAIC,WAAW,CAAC;EAC/E,CAAC;EAED,MAAME,QAAQ,GAAG3D,KAAK,CAAC4D,YAAY,CAAC;EACpC,MAAMT,OAAO,GAAGnD,KAAK,CAACoD,WAAW,CAAC;EAClC,MAAMe,IAAI,GAAG,CAAC/B,IAAI,GAAGQ,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAGC,UAAU,CAACsB,IAAI,KAAK,IAAI,GAAG/B,IAAI,GAAG,IAAI;EAC3F,MAAMgC,SAAS,GAAGjE,YAAY,CAAC;IAC7BkE,WAAW,EAAEF,IAAI;IACjBG,iBAAiB,EAAExB,eAAe,CAACxB,IAAI;IACvCiD,sBAAsB,EAAEV,KAAK;IAC7BW,eAAe,EAAE;MACfzB,OAAO;MACPZ;IACF,CAAC;IACD2B,UAAU;IACVW,SAAS,EAAEV,OAAO,CAACzC;EACrB,CAAC,CAAC;EACF,MAAMoD,MAAM,GAAG,CAACrC,kBAAkB,GAAGQ,UAAU,CAAC6B,MAAM,KAAK,IAAI,GAAGrC,kBAAkB,GAAG,QAAQ;EAC/F,MAAMsC,WAAW,GAAGxE,YAAY,CAAC;IAC/BkE,WAAW,EAAEK,MAAM;IACnBJ,iBAAiB,EAAExB,eAAe,CAACpB,MAAM;IACzC8C,eAAe,EAAE;MACfI,KAAK,EAAEnB,WAAW;MAClBoB,EAAE,EAAElB,QAAQ;MACZmB,QAAQ,EAAEC,CAAC,IAAIxB,mBAAmB,IAAIA,mBAAmB,CAACwB,CAAC,CAAC;MAC5D,YAAY,EAAEtB,WAAW,CAACuB,QAAQ,CAAC,CAAC;MACpC,iBAAiB,EAAE,CAAC7B,OAAO,EAAEQ,QAAQ,CAAC,CAACsB,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,IAAIC;IACtE,CAAC;IACDtB,UAAU;IACVW,SAAS,EAAEV,OAAO,CAACrC;EACrB,CAAC,CAAC;EACF,MAAM2D,OAAO,GAAG,CAAC/C,mBAAmB,GAAGO,UAAU,CAACwC,OAAO,KAAK,IAAI,GAAG/C,mBAAmB,GAAGhC,8BAA8B;EACzH,MAAMgF,YAAY,GAAGnF,YAAY,CAAC;IAChCkE,WAAW,EAAEgB,OAAO;IACpBf,iBAAiB,EAAExB,eAAe,CAACf,OAAO;IAC1CyC,eAAe,EAAE;MACfhB,IAAI;MACJC,WAAW;MACXxC,KAAK;MACLqC,YAAY;MACZL;IACF,CAAC;IACDa,UAAU;IACVW,SAAS,EAAEV,OAAO,CAAChC;EACrB,CAAC,CAAC;EACF,MAAMwD,QAAQ,GAAG,CAAChD,oBAAoB,GAAGM,UAAU,CAAC0C,QAAQ,KAAK,IAAI,GAAGhD,oBAAoB,GAAG,QAAQ;EACvG,MAAMiD,aAAa,GAAGrF,YAAY,CAAC;IACjCkE,WAAW,EAAEkB,QAAQ;IACrBjB,iBAAiB,EAAExB,eAAe,CAACjB,QAAQ;IAC3C2C,eAAe,EAAE;MACfI,KAAK,EAAEQ;IACT,CAAC;IACDtB,UAAU;IACVW,SAAS,EAAEV,OAAO,CAAClC;EACrB,CAAC,CAAC;EACF,MAAM4D,WAAW,GAAG,CAACjD,qBAAqB,GAAGK,UAAU,CAAC4C,WAAW,KAAK,IAAI,GAAGjD,qBAAqB,GAAG,GAAG;EAC1G,MAAMkD,gBAAgB,GAAGvF,YAAY,CAAC;IACpCkE,WAAW,EAAEoB,WAAW;IACxBnB,iBAAiB,EAAExB,eAAe,CAACrB,WAAW;IAC9C+C,eAAe,EAAE;MACfK,EAAE,EAAE1B;IACN,CAAC;IACDW,UAAU;IACVW,SAAS,EAAEV,OAAO,CAACtC;EACrB,CAAC,CAAC;EACF,MAAMkE,aAAa,GAAG,CAAClD,qBAAqB,GAAGI,UAAU,CAAC8C,aAAa,KAAK,IAAI,GAAGlD,qBAAqB,GAAG,GAAG;EAC9G,MAAMmD,kBAAkB,GAAGzF,YAAY,CAAC;IACtCkE,WAAW,EAAEsB,aAAa;IAC1BrB,iBAAiB,EAAExB,eAAe,CAAChB,aAAa;IAChDgC,UAAU;IACVW,SAAS,EAAEV,OAAO,CAACjC;EACrB,CAAC,CAAC;EACF,MAAM+D,OAAO,GAAG,CAACnD,mBAAmB,GAAGG,UAAU,CAACgD,OAAO,KAAK,IAAI,GAAGnD,mBAAmB,GAAG,KAAK;EAChG,MAAMoD,YAAY,GAAG3F,YAAY,CAAC;IAChCkE,WAAW,EAAEwB,OAAO;IACpBvB,iBAAiB,EAAExB,eAAe,CAACvB,OAAO;IAC1CuC,UAAU;IACVW,SAAS,EAAEV,OAAO,CAACxC;EACrB,CAAC,CAAC;EACF,MAAMwE,MAAM,GAAG,CAACpD,kBAAkB,GAAGE,UAAU,CAACkD,MAAM,KAAK,IAAI,GAAGpD,kBAAkB,GAAG,KAAK;EAC5F,MAAMqD,WAAW,GAAG7F,YAAY,CAAC;IAC/BkE,WAAW,EAAE0B,MAAM;IACnBzB,iBAAiB,EAAExB,eAAe,CAACtB,MAAM;IACzCsC,UAAU;IACVW,SAAS,EAAEV,OAAO,CAACvC;EACrB,CAAC,CAAC;EACF,OAAO,aAAaf,IAAI,CAAC0D,IAAI,EAAEzE,QAAQ,CAAC,CAAC,CAAC,EAAE0E,SAAS,EAAE;IACrD6B,QAAQ,EAAE,aAAapF,KAAK,CAACgF,OAAO,EAAEnG,QAAQ,CAAC,CAAC,CAAC,EAAEoG,YAAY,EAAE;MAC/DG,QAAQ,EAAE,CAAC,aAAaxF,IAAI,CAACsF,MAAM,EAAErG,QAAQ,CAAC,CAAC,CAAC,EAAEsG,WAAW,CAAC,CAAC,EAAEtC,kBAAkB,CAACwC,MAAM,GAAG,CAAC,IAAI,aAAazF,IAAI,CAACgF,WAAW,EAAE/F,QAAQ,CAAC,CAAC,CAAC,EAAEgG,gBAAgB,EAAE;QAC9JO,QAAQ,EAAE5C;MACZ,CAAC,CAAC,CAAC,EAAEK,kBAAkB,CAACwC,MAAM,GAAG,CAAC,IAAI,aAAazF,IAAI,CAACiE,MAAM,EAAEhF,QAAQ,CAAC,CAAC,CAAC,EAAEiF,WAAW,EAAE;QACxFsB,QAAQ,EAAEvC,kBAAkB,CAACyC,GAAG,CAACC,iBAAiB,IAAI,aAAazF,cAAc,CAAC4E,QAAQ,EAAE7F,QAAQ,CAAC,CAAC,CAAC,EAAE8F,aAAa,EAAE;UACtHa,GAAG,EAAE,OAAOD,iBAAiB,KAAK,QAAQ,IAAIA,iBAAiB,CAACE,KAAK,GAAGF,iBAAiB,CAACE,KAAK,GAAGF,iBAAiB;UACnHxB,KAAK,EAAE,OAAOwB,iBAAiB,KAAK,QAAQ,IAAIA,iBAAiB,CAACxB,KAAK,GAAGwB,iBAAiB,CAACxB,KAAK,GAAGwB;QACtG,CAAC,CAAC,EAAE,OAAOA,iBAAiB,KAAK,QAAQ,IAAIA,iBAAiB,CAACE,KAAK,GAAGF,iBAAiB,CAACE,KAAK,GAAGF,iBAAiB,CAAC;MACrH,CAAC,CAAC,CAAC,EAAE,aAAa3F,IAAI,CAACkF,aAAa,EAAEjG,QAAQ,CAAC,CAAC,CAAC,EAAEkG,kBAAkB,EAAE;QACrEK,QAAQ,EAAE/C,kBAAkB,CAAC;UAC3BnC,IAAI,EAAEE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGuC,IAAI,GAAGC,WAAW,GAAG,CAAC;UAC9CzC,EAAE,EAAEgD,uBAAuB,CAAC,CAAC;UAC7B/C,KAAK,EAAEA,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGA,KAAK;UAChCuC;QACF,CAAC;MACH,CAAC,CAAC,CAAC,EAAE,aAAa/C,IAAI,CAAC4E,OAAO,EAAE3F,QAAQ,CAAC,CAAC,CAAC,EAAE4F,YAAY,CAAC,CAAC;IAC7D,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFiB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzE,uBAAuB,CAAC0E;AAChE,yBACE;EACA;EACA;EACA;EACA;;EAEA;AACF;AACA;EACET,QAAQ,EAAEnG,SAAS,CAAC6G,IAAI;EAExB;AACF;AACA;EACE5D,OAAO,EAAEjD,SAAS,CAAC8G,MAAM;EAEzB;AACF;AACA;AACA;EACEhE,SAAS,EAAE9C,SAAS,CAACuE,WAAW;EAEhC;AACF;AACA;AACA;AACA;EACExB,UAAU,EAAE/C,SAAS,CAAC+G,KAAK,CAAC;IAC1BxB,OAAO,EAAEvF,SAAS,CAACuE,WAAW;IAC9BsB,aAAa,EAAE7F,SAAS,CAACuE,WAAW;IACpCkB,QAAQ,EAAEzF,SAAS,CAACuE,WAAW;IAC/BF,IAAI,EAAErE,SAAS,CAACuE,WAAW;IAC3BK,MAAM,EAAE5E,SAAS,CAACuE,WAAW;IAC7BoB,WAAW,EAAE3F,SAAS,CAACuE,WAAW;IAClC0B,MAAM,EAAEjG,SAAS,CAACuE,WAAW;IAC7BwB,OAAO,EAAE/F,SAAS,CAACuE;EACrB,CAAC,CAAC;EAEF;AACF;AACA;AACA;EACEvB,eAAe,EAAEhD,SAAS,CAAC+G,KAAK,CAAC;IAC/B9E,OAAO,EAAEjC,SAAS,CAACgH,SAAS,CAAC,CAAChH,SAAS,CAACiH,IAAI,EAAEjH,SAAS,CAACkH,MAAM,CAAC,CAAC;IAChElF,aAAa,EAAEhC,SAAS,CAACgH,SAAS,CAAC,CAAChH,SAAS,CAACiH,IAAI,EAAEjH,SAAS,CAACkH,MAAM,CAAC,CAAC;IACtEnF,QAAQ,EAAE/B,SAAS,CAACgH,SAAS,CAAC,CAAChH,SAAS,CAACiH,IAAI,EAAEjH,SAAS,CAACkH,MAAM,CAAC,CAAC;IACjE1F,IAAI,EAAExB,SAAS,CAACgH,SAAS,CAAC,CAAChH,SAAS,CAACiH,IAAI,EAAEjH,SAAS,CAACkH,MAAM,CAAC,CAAC;IAC7DtF,MAAM,EAAE5B,SAAS,CAACgH,SAAS,CAAC,CAAChH,SAAS,CAACiH,IAAI,EAAEjH,SAAS,CAACkH,MAAM,CAAC,CAAC;IAC/DvF,WAAW,EAAE3B,SAAS,CAACgH,SAAS,CAAC,CAAChH,SAAS,CAACiH,IAAI,EAAEjH,SAAS,CAACkH,MAAM,CAAC,CAAC;IACpExF,MAAM,EAAE1B,SAAS,CAACgH,SAAS,CAAC,CAAChH,SAAS,CAACiH,IAAI,EAAEjH,SAAS,CAACkH,MAAM,CAAC,CAAC;IAC/DzF,OAAO,EAAEzB,SAAS,CAACgH,SAAS,CAAC,CAAChH,SAAS,CAACiH,IAAI,EAAEjH,SAAS,CAACkH,MAAM,CAAC;EACjE,CAAC,CAAC;EAEF;AACF;AACA;AACA;AACA;EACE/F,KAAK,EAAEnB,SAAS,CAAC8G,MAAM,CAACK,UAAU;EAElC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEhE,gBAAgB,EAAEnD,SAAS,CAACiH,IAAI;EAEhC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE7D,kBAAkB,EAAEpD,SAAS,CAACiH,IAAI;EAElC;AACF;AACA;EACE5D,OAAO,EAAErD,SAAS,CAACoH,MAAM;EAEzB;AACF;AACA;AACA;AACA;AACA;EACE7D,gBAAgB,EAAEvD,SAAS,CAAC6G,IAAI;EAEhC;AACF;AACA;AACA;AACA;AACA;EACErD,YAAY,EAAExD,SAAS,CAACiH,IAAI,CAACE,UAAU;EAEvC;AACF;AACA;AACA;AACA;EACE1D,mBAAmB,EAAEzD,SAAS,CAACiH,IAAI;EAEnC;AACF;AACA;EACEvD,IAAI,EAAEvD,cAAc,CAACC,eAAe,CAAC+G,UAAU,EAAE/E,KAAK,IAAI;IACxD,MAAM;MACJjB,KAAK;MACLuC,IAAI;MACJC;IACF,CAAC,GAAGvB,KAAK;IAET,IAAIjB,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,OAAO,IAAI;IACb;IAEA,MAAMkG,WAAW,GAAGlD,IAAI,CAACmD,GAAG,CAAC,CAAC,EAAEnD,IAAI,CAACoD,IAAI,CAACpG,KAAK,GAAGwC,WAAW,CAAC,GAAG,CAAC,CAAC;IAEnE,IAAID,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG2D,WAAW,EAAE;MAClC,OAAO,IAAIG,KAAK,CAAC,kEAAkE,GAAI,SAAQH,WAAY,iBAAgB3D,IAAK,IAAG,CAAC;IACtI;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;EAEF;AACF;AACA;AACA;AACA;EACEC,WAAW,EAAEvD,eAAe,CAAC+G,UAAU;EAEvC;AACF;AACA;AACA;AACA;AACA;EACEvD,kBAAkB,EAAE5D,SAAS,CAACyH,OAAO,CAACzH,SAAS,CAACgH,SAAS,CAAC,CAAChH,SAAS,CAAC8G,MAAM,EAAE9G,SAAS,CAAC+G,KAAK,CAAC;IAC3FP,KAAK,EAAExG,SAAS,CAACoH,MAAM,CAACD,UAAU;IAClCrC,KAAK,EAAE9E,SAAS,CAAC8G,MAAM,CAACK;EAC1B,CAAC,CAAC,CAAC,CAAC,CAACA,UAAU,CAAC;EAEhB;AACF;AACA;EACEtD,QAAQ,EAAE7D,SAAS,CAACoH;AACtB,CAAC,GAAG,KAAK,CAAC;AACV,eAAelF,uBAAuB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}