{"ast": null, "code": "import { exhaustAll } from './exhaustAll';\nexport var exhaust = exhaustAll;", "map": {"version": 3, "names": ["exhaustAll", "exhaust"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\exhaust.ts"], "sourcesContent": ["import { exhaustAll } from './exhaustAll';\n\n/**\n * @deprecated Renamed to {@link exhaustAll}. Will be removed in v8.\n */\nexport const exhaust = exhaustAll;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AAKzC,OAAO,IAAMC,OAAO,GAAGD,UAAU"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}