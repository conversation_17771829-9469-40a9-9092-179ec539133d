{"ast": null, "code": "const o = \"@@@\",\n  n = \"@\",\n  E = \"General\";\nfunction F(T) {\n  return T === o || T === n;\n}\nfunction _(T) {\n  return T == null || T === E;\n}\nexport { E as DEFAULT_NUMBER_FORMAT, o as DEFAULT_TEXT_FORMAT, n as DEFAULT_TEXT_FORMAT_EXCEL, _ as isDefaultFormat, F as isTextFormat };", "map": {"version": 3, "names": ["o", "n", "E", "F", "T", "_", "DEFAULT_NUMBER_FORMAT", "DEFAULT_TEXT_FORMAT", "DEFAULT_TEXT_FORMAT_EXCEL", "isDefaultFormat", "isTextFormat"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@univerjs/engine-numfmt/lib/es/index.js"], "sourcesContent": ["const o = \"@@@\", n = \"@\", E = \"General\";\nfunction F(T) {\n  return T === o || T === n;\n}\nfunction _(T) {\n  return T == null || T === E;\n}\nexport {\n  E as DEFAULT_NUMBER_FORMAT,\n  o as DEFAULT_TEXT_FORMAT,\n  n as DEFAULT_TEXT_FORMAT_EXCEL,\n  _ as isDefaultFormat,\n  F as isTextFormat\n};\n"], "mappings": "AAAA,MAAMA,CAAC,GAAG,KAAK;EAAEC,CAAC,GAAG,GAAG;EAAEC,CAAC,GAAG,SAAS;AACvC,SAASC,CAACA,CAACC,CAAC,EAAE;EACZ,OAAOA,CAAC,KAAKJ,CAAC,IAAII,CAAC,KAAKH,CAAC;AAC3B;AACA,SAASI,CAACA,CAACD,CAAC,EAAE;EACZ,OAAOA,CAAC,IAAI,IAAI,IAAIA,CAAC,KAAKF,CAAC;AAC7B;AACA,SACEA,CAAC,IAAII,qBAAqB,EAC1BN,CAAC,IAAIO,mBAAmB,EACxBN,CAAC,IAAIO,yBAAyB,EAC9BH,CAAC,IAAII,eAAe,EACpBN,CAAC,IAAIO,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}