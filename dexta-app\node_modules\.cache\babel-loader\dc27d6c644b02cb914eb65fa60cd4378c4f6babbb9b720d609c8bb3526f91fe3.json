{"ast": null, "code": "export var COMPLETE_NOTIFICATION = function () {\n  return createNotification('C', undefined, undefined);\n}();\nexport function errorNotification(error) {\n  return createNotification('E', undefined, error);\n}\nexport function nextNotification(value) {\n  return createNotification('N', value, undefined);\n}\nexport function createNotification(kind, value, error) {\n  return {\n    kind: kind,\n    value: value,\n    error: error\n  };\n}", "map": {"version": 3, "names": ["COMPLETE_NOTIFICATION", "createNotification", "undefined", "errorNotification", "error", "nextNotification", "value", "kind"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\NotificationFactories.ts"], "sourcesContent": ["import { CompleteNotification, NextNotification, ErrorNotification } from './types';\n\n/**\n * A completion object optimized for memory use and created to be the\n * same \"shape\" as other notifications in v8.\n * @internal\n */\nexport const COMPLETE_NOTIFICATION = (() => createNotification('C', undefined, undefined) as CompleteNotification)();\n\n/**\n * Internal use only. Creates an optimized error notification that is the same \"shape\"\n * as other notifications.\n * @internal\n */\nexport function errorNotification(error: any): ErrorNotification {\n  return createNotification('E', undefined, error) as any;\n}\n\n/**\n * Internal use only. Creates an optimized next notification that is the same \"shape\"\n * as other notifications.\n * @internal\n */\nexport function nextNotification<T>(value: T) {\n  return createNotification('N', value, undefined) as NextNotification<T>;\n}\n\n/**\n * Ensures that all notifications created internally have the same \"shape\" in v8.\n *\n * TODO: This is only exported to support a crazy legacy test in `groupBy`.\n * @internal\n */\nexport function createNotification(kind: 'N' | 'E' | 'C', value: any, error: any) {\n  return {\n    kind,\n    value,\n    error,\n  };\n}\n"], "mappings": "AAOA,OAAO,IAAMA,qBAAqB,GAAI;EAAM,OAAAC,kBAAkB,CAAC,GAAG,EAAEC,SAAS,EAAEA,SAAS,CAAyB;AAArE,CAAqE,CAAC,CAAE;AAOpH,OAAM,SAAUC,iBAAiBA,CAACC,KAAU;EAC1C,OAAOH,kBAAkB,CAAC,GAAG,EAAEC,SAAS,EAAEE,KAAK,CAAQ;AACzD;AAOA,OAAM,SAAUC,gBAAgBA,CAAIC,KAAQ;EAC1C,OAAOL,kBAAkB,CAAC,GAAG,EAAEK,KAAK,EAAEJ,SAAS,CAAwB;AACzE;AAQA,OAAM,SAAUD,kBAAkBA,CAACM,IAAqB,EAAED,KAAU,EAAEF,KAAU;EAC9E,OAAO;IACLG,IAAI,EAAAA,IAAA;IACJD,KAAK,EAAAA,KAAA;IACLF,KAAK,EAAAA;GACN;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}