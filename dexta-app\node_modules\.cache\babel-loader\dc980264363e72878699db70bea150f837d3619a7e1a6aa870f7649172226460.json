{"ast": null, "code": "var objectConstructor = {}.constructor;\nexport default function isObject(object) {\n  return object !== undefined && object !== null && object.constructor === objectConstructor;\n}", "map": {"version": 3, "names": ["objectConstructor", "constructor", "isObject", "object", "undefined"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\helpers\\isObject.js"], "sourcesContent": ["const objectConstructor = {}.constructor;\r\n\r\nexport default function isObject(object) {\r\n  return object !== undefined && object !== null && object.constructor === objectConstructor;\r\n}\r\n"], "mappings": "AAAA,IAAMA,iBAAiB,GAAG,GAAGC,WAA7B;AAEA,eAAe,SAASC,QAATA,CAAkBC,MAAlB,EAA0B;EACvC,OAAOA,MAAM,KAAKC,SAAX,IAAwBD,MAAM,KAAK,IAAnC,IAA2CA,MAAM,CAACF,WAAP,KAAuBD,iBAAzE;AACD"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}