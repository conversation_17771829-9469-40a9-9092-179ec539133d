{"ast": null, "code": "var Nt = Object.defineProperty;\nvar Rt = (a, e, n) => e in a ? Nt(a, e, {\n  enumerable: !0,\n  configurable: !0,\n  writable: !0,\n  value: n\n}) : a[e] = n;\nvar F = (a, e, n) => Rt(a, typeof e != \"symbol\" ? e + \"\" : e, n);\nimport { sortRules as xt, sortRulesByDesc as Bt, createIdentifier as Wt, CommandType as Gt, ImageSourceType as at, ImageUploadStatusType as et, Tools as Lt, Inject as qt, Injector as Kt, IConfigService as Ft, ICommandService as Vt, Plugin as Ht, merge as zt, mergeOverrideWithDependencies as Xt, IImageIoService as Jt } from \"@univerjs/core\";\nimport { IImageIoService as In, ImageSourceType as En, ImageUploadStatusType as Dn } from \"@univerjs/core\";\nimport { Subject as be } from \"rxjs\";\nconst _n = 500,\n  vn = 500,\n  mn = 10,\n  Yt = 5 * 1024 * 1024,\n  Zt = [\"image/png\", \"image/jpeg\", \"image/jpg\", \"image/gif\", \"image/bmp\"];\nvar Ve = {},\n  Ze = {},\n  tt = {},\n  vt;\nfunction Qt() {\n  if (vt) return tt;\n  vt = 1, Object.defineProperty(tt, \"__esModule\", {\n    value: !0\n  });\n  function a(t, r) {\n    if (Array.isArray(r)) return !1;\n    for (let u in t) if (!n(t[u], r[u])) return !1;\n    for (let u in r) if (t[u] === void 0) return !1;\n    return !0;\n  }\n  function e(t, r) {\n    if (!Array.isArray(r) || t.length !== r.length) return !1;\n    for (let u = 0; u < t.length; u++) if (!n(t[u], r[u])) return !1;\n    return !0;\n  }\n  function n(t, r) {\n    return t === r ? !0 : t === null || r === null || typeof t != \"object\" || typeof r != \"object\" ? !1 : Array.isArray(t) ? e(t, r) : a(t, r);\n  }\n  return tt.default = n, tt;\n}\nvar nt = {},\n  mt;\nfunction en() {\n  if (mt) return nt;\n  mt = 1, Object.defineProperty(nt, \"__esModule\", {\n    value: !0\n  });\n  function a(e) {\n    if (e === null) return null;\n    if (Array.isArray(e)) return e.map(a);\n    if (typeof e == \"object\") {\n      const n = {};\n      for (let t in e) n[t] = a(e[t]);\n      return n;\n    } else return e;\n  }\n  return nt.default = a, nt;\n}\nvar lt = {},\n  Ot;\nfunction Mt() {\n  return Ot || (Ot = 1, function (a) {\n    Object.defineProperty(a, \"__esModule\", {\n      value: !0\n    }), a.eachChildOf = a.advancer = a.readCursor = a.writeCursor = a.WriteCursor = a.ReadCursor = a.isValidPathItem = void 0;\n    function e(_, s) {\n      if (!_) throw new Error(s);\n    }\n    const n = _ => _ != null && typeof _ == \"object\" && !Array.isArray(_),\n      t = (_, s) =>\n      // All the numbers, then all the letters. Just as the gods of ascii intended.\n      typeof _ == typeof s ? _ > s : typeof _ == \"string\" && typeof s == \"number\";\n    function r(_, s) {\n      for (let c in _) {\n        const h = c;\n        s.write(h, _[h]);\n      }\n    }\n    a.isValidPathItem = _ => typeof _ == \"number\" || typeof _ == \"string\" && _ !== \"__proto__\";\n    class u {\n      constructor(s = null) {\n        this.parents = [], this.indexes = [], this.lcIdx = -1, this.idx = -1, this.container = s;\n      }\n      ascend() {\n        e(this.parents.length === this.indexes.length / 2), this.idx === 0 ? this.parents.length ? (this.lcIdx = this.indexes.pop(), this.container = this.parents.pop(), this.idx = this.indexes.pop()) : (this.lcIdx = 0, this.idx = -1) : (e(this.idx > 0), this.idx--, n(this.container[this.idx]) && this.idx--);\n      }\n      getPath() {\n        const s = [];\n        let c = this.container,\n          h = this.parents.length - 1,\n          U = this.idx;\n        for (; U >= 0;) s.unshift(c[U]), U === 0 ? (U = this.indexes[h * 2], c = this.parents[h--]) : U -= n(c[U - 1]) ? 2 : 1;\n        return s;\n      }\n    }\n    class o extends u {\n      get() {\n        return this.container ? this.container.slice(this.idx + 1) : null;\n      }\n      // Its only valid to call this after descending into a child.\n      getKey() {\n        return e(this.container != null, \"Invalid call to getKey before cursor descended\"), this.container[this.idx];\n      }\n      getComponent() {\n        let s;\n        return this.container && this.container.length > this.idx + 1 && n(s = this.container[this.idx + 1]) ? s : null;\n      }\n      descendFirst() {\n        let s = this.idx + 1;\n        if (!this.container || s >= this.container.length || n(this.container[s]) && s + 1 >= this.container.length) return !1;\n        n(this.container[s]) && s++;\n        const c = this.container[s];\n        return Array.isArray(c) ? (this.indexes.push(this.idx), this.parents.push(this.container), this.indexes.push(s), this.idx = 0, this.container = c) : this.idx = s, !0;\n      }\n      nextSibling() {\n        if (e(this.parents.length === this.indexes.length / 2), this.idx > 0 || this.parents.length === 0) return !1;\n        const s = this.indexes[this.indexes.length - 1] + 1,\n          c = this.parents[this.parents.length - 1];\n        return s >= c.length ? !1 : (e(!isNaN(s)), this.indexes[this.indexes.length - 1] = s, this.container = c[s], !0);\n      }\n      _init(s, c, h, U) {\n        this.container = s, this.idx = c, this.parents = h.slice(), this.indexes = U.slice();\n      }\n      clone() {\n        const s = new o();\n        return s._init(this.container, this.idx, this.parents, this.indexes), s;\n      }\n      *[Symbol.iterator]() {\n        if (this.descendFirst()) {\n          do yield this.getKey(); while (this.nextSibling());\n          this.ascend();\n        }\n      }\n      // TODO(cleanup): Consider moving these functions out of cursor, since\n      // they're really just helper methods.\n      // It'd be really nice to do this using generators.\n      traverse(s, c) {\n        const h = this.getComponent();\n        h && c(h, s);\n        for (const U of this) s && s.descend(U), this.traverse(s, c), s && s.ascend();\n      }\n      eachPick(s, c) {\n        this.traverse(s, (h, U) => {\n          h.p != null && c(h.p, U);\n        });\n      }\n      eachDrop(s, c) {\n        this.traverse(s, (h, U) => {\n          h.d != null && c(h.d, U);\n        });\n      }\n    }\n    a.ReadCursor = o;\n    class l extends u {\n      constructor(s = null) {\n        super(s), this.pendingDescent = [], this._op = s;\n      }\n      flushDescent() {\n        e(this.parents.length === this.indexes.length / 2), this.container === null && (this._op = this.container = []);\n        for (let s = 0; s < this.pendingDescent.length; s++) {\n          const c = this.pendingDescent[s];\n          let h = this.idx + 1;\n          if (h < this.container.length && n(this.container[h]) && h++, e(h === this.container.length || !n(this.container[h])), h === this.container.length) this.container.push(c), this.idx = h;else if (this.container[h] === c) this.idx = h;else {\n            if (!Array.isArray(this.container[h])) {\n              const U = this.container.splice(h, this.container.length - h);\n              this.container.push(U), this.lcIdx > -1 && (this.lcIdx = h);\n            }\n            for (this.indexes.push(this.idx), this.parents.push(this.container), this.lcIdx !== -1 && (e(t(c, this.container[this.lcIdx][0])), h = this.lcIdx + 1, this.lcIdx = -1); h < this.container.length && t(c, this.container[h][0]);) h++;\n            if (this.indexes.push(h), this.idx = 0, h < this.container.length && this.container[h][0] === c) this.container = this.container[h];else {\n              const U = [c];\n              this.container.splice(h, 0, U), this.container = U;\n            }\n          }\n        }\n        this.pendingDescent.length = 0;\n      }\n      reset() {\n        this.lcIdx = -1;\n      }\n      // Creates and returns a component, creating one if need be. You should\n      // probably write to it immediately - ops are not valid with empty\n      // components.\n      getComponent() {\n        this.flushDescent();\n        const s = this.idx + 1;\n        if (s < this.container.length && n(this.container[s])) return this.container[s];\n        {\n          const c = {};\n          return this.container.splice(s, 0, c), c;\n        }\n      }\n      write(s, c) {\n        const h = this.getComponent();\n        e(h[s] == null || h[s] === c, \"Internal consistency error: Overwritten component. File a bug\"), h[s] = c;\n      }\n      get() {\n        return this._op;\n      }\n      descend(s) {\n        if (!a.isValidPathItem(s)) throw Error(\"Invalid JSON key\");\n        this.pendingDescent.push(s);\n      }\n      descendPath(s) {\n        return this.pendingDescent.push(...s), this;\n      }\n      ascend() {\n        this.pendingDescent.length ? this.pendingDescent.pop() : super.ascend();\n      }\n      mergeTree(s, c = r) {\n        if (s === null) return;\n        if (e(Array.isArray(s)), s === this._op) throw Error(\"Cannot merge into my own tree\");\n        const h = this.lcIdx,\n          U = this.parents.length;\n        let Y = 0;\n        for (let pe = 0; pe < s.length; pe++) {\n          const Z = s[pe];\n          typeof Z == \"string\" || typeof Z == \"number\" ? (Y++, this.descend(Z)) : Array.isArray(Z) ? this.mergeTree(Z, c) : typeof Z == \"object\" && c(Z, this);\n        }\n        for (; Y--;) this.ascend();\n        this.lcIdx = this.parents.length === U ? h : -1;\n      }\n      at(s, c) {\n        this.descendPath(s), c(this);\n        for (let h = 0; h < s.length; h++) this.ascend();\n        return this;\n      }\n      // This is used by helpers, so the strict ordering guarantees are\n      // relaxed.\n      writeAtPath(s, c, h) {\n        return this.at(s, () => this.write(c, h)), this.reset(), this;\n      }\n      writeMove(s, c, h = 0) {\n        return this.writeAtPath(s, \"p\", h).writeAtPath(c, \"d\", h);\n      }\n      getPath() {\n        const s = super.getPath();\n        return s.push(...this.pendingDescent), s;\n      }\n    }\n    a.WriteCursor = l, a.writeCursor = () => new l(), a.readCursor = _ => new o(_);\n    function E(_, s, c) {\n      let h, U;\n      U = h = _ ? _.descendFirst() : !1;\n      function Y(pe) {\n        let Z;\n        for (; U;) {\n          const Ce = Z = _.getKey();\n          if (pe != null) {\n            let Le = !1;\n            if (s && typeof Ce == \"number\" && (Z = s(Ce, _.getComponent()), Z < 0 && (Z = ~Z, Le = !0)), t(Z, pe)) return null;\n            if (Z === pe && !Le) return _;\n          }\n          c && typeof Z == \"number\" && c(Z, _.getComponent()), U = _.nextSibling();\n        }\n        return null;\n      }\n      return Y.end = () => {\n        h && _.ascend();\n      }, Y;\n    }\n    a.advancer = E;\n    function x(_, s, c) {\n      let h, U, Y, pe;\n      for (h = U = _ && _.descendFirst(), Y = pe = s && s.descendFirst(); h || Y;) {\n        let Z = h ? _.getKey() : null,\n          Ce = Y ? s.getKey() : null;\n        Z !== null && Ce !== null && (t(Ce, Z) ? Ce = null : Z !== Ce && (Z = null)), c(Z == null ? Ce : Z, Z != null ? _ : null, Ce != null ? s : null), Z != null && h && (h = _.nextSibling()), Ce != null && Y && (Y = s.nextSibling());\n      }\n      U && _.ascend(), pe && s.ascend();\n    }\n    a.eachChildOf = x;\n  }(lt)), lt;\n}\nvar ut = {},\n  wt;\nfunction St() {\n  return wt || (wt = 1, function (a) {\n    Object.defineProperty(a, \"__esModule\", {\n      value: !0\n    }), a.ConflictType = void 0, function (e) {\n      e[e.RM_UNEXPECTED_CONTENT = 1] = \"RM_UNEXPECTED_CONTENT\", e[e.DROP_COLLISION = 2] = \"DROP_COLLISION\", e[e.BLACKHOLE = 3] = \"BLACKHOLE\";\n    }(a.ConflictType || (a.ConflictType = {}));\n  }(ut)), ut;\n}\nvar Ue = {},\n  He = {},\n  bt;\nfunction pt() {\n  return bt || (bt = 1, Object.defineProperty(He, \"__esModule\", {\n    value: !0\n  }), He.uniToStrPos = He.strPosToUni = void 0, He.strPosToUni = (a, e = a.length) => {\n    let n = 0,\n      t = 0;\n    for (; t < e; t++) {\n      const r = a.charCodeAt(t);\n      r >= 55296 && r <= 57343 && (n++, t++);\n    }\n    if (t !== e) throw Error(\"Invalid offset - splits unicode bytes\");\n    return t - n;\n  }, He.uniToStrPos = (a, e) => {\n    let n = 0;\n    for (; e > 0; e--) {\n      const t = a.charCodeAt(n);\n      n += t >= 55296 && t <= 57343 ? 2 : 1;\n    }\n    return n;\n  }), He;\n}\nvar ct = {},\n  Ct;\nfunction ht() {\n  return Ct || (Ct = 1, function (a) {\n    Object.defineProperty(a, \"__esModule\", {\n      value: !0\n    }), a.uniSlice = a.dlen = a.eachOp = void 0;\n    const e = pt(),\n      n = f => {\n        if (!Array.isArray(f)) throw Error(\"Op must be an array of components\");\n        let y = null;\n        for (let b = 0; b < f.length; b++) {\n          const L = f[b];\n          switch (typeof L) {\n            case \"object\":\n              if (typeof L.d != \"number\" && typeof L.d != \"string\") throw Error(\"Delete must be number or string\");\n              if (a.dlen(L.d) <= 0) throw Error(\"Deletes must not be empty\");\n              break;\n            case \"string\":\n              if (!(L.length > 0)) throw Error(\"Inserts cannot be empty\");\n              break;\n            case \"number\":\n              if (!(L > 0)) throw Error(\"Skip components must be >0\");\n              if (typeof y == \"number\") throw Error(\"Adjacent skip components should be combined\");\n              break;\n          }\n          y = L;\n        }\n        if (typeof y == \"number\") throw Error(\"Op has a trailing skip\");\n      };\n    function t(f, y) {\n      let b = 0,\n        L = 0;\n      for (let z = 0; z < f.length; z++) {\n        const q = f[z];\n        switch (y(q, b, L), typeof q) {\n          case \"object\":\n            b += a.dlen(q.d);\n            break;\n          case \"string\":\n            L += e.strPosToUni(q);\n            break;\n          case \"number\":\n            b += q, L += q;\n            break;\n        }\n      }\n    }\n    a.eachOp = t;\n    function r(f, y) {\n      const b = [],\n        L = l(b);\n      return t(f, (z, q, Ee) => {\n        L(y(z, q, Ee));\n      }), s(b);\n    }\n    const u = f => f,\n      o = f => r(f, u);\n    a.dlen = f => typeof f == \"number\" ? f : e.strPosToUni(f);\n    const l = f => y => {\n        if (!(!y || y.d === 0 || y.d === \"\")) if (f.length === 0) f.push(y);else if (typeof y == typeof f[f.length - 1]) {\n          if (typeof y == \"object\") {\n            const b = f[f.length - 1];\n            b.d = typeof b.d == \"string\" && typeof y.d == \"string\" ? b.d + y.d : a.dlen(b.d) + a.dlen(y.d);\n          } else f[f.length - 1] += y;\n        } else f.push(y);\n      },\n      E = f => typeof f == \"number\" ? f : typeof f == \"string\" ? e.strPosToUni(f) : typeof f.d == \"number\" ? f.d : e.strPosToUni(f.d);\n    a.uniSlice = (f, y, b) => {\n      const L = e.uniToStrPos(f, y),\n        z = b == null ? 1 / 0 : e.uniToStrPos(f, b);\n      return f.slice(L, z);\n    };\n    const x = (f, y, b) => typeof f == \"number\" ? b == null ? f - y : Math.min(f, b) - y : a.uniSlice(f, y, b),\n      _ = f => {\n        let y = 0,\n          b = 0;\n        return {\n          take: (q, Ee) => {\n            if (y === f.length) return q === -1 ? null : q;\n            const ce = f[y];\n            let ne;\n            if (typeof ce == \"number\") return q === -1 || ce - b <= q ? (ne = ce - b, ++y, b = 0, ne) : (b += q, q);\n            if (typeof ce == \"string\") {\n              if (q === -1 || Ee === \"i\" || e.strPosToUni(ce.slice(b)) <= q) return ne = ce.slice(b), ++y, b = 0, ne;\n              {\n                const le = b + e.uniToStrPos(ce.slice(b), q);\n                return ne = ce.slice(b, le), b = le, ne;\n              }\n            } else {\n              if (q === -1 || Ee === \"d\" || a.dlen(ce.d) - b <= q) return ne = {\n                d: x(ce.d, b)\n              }, ++y, b = 0, ne;\n              {\n                let le = x(ce.d, b, b + q);\n                return b += q, {\n                  d: le\n                };\n              }\n            }\n          },\n          peek: () => f[y]\n        };\n      },\n      s = f => (f.length > 0 && typeof f[f.length - 1] == \"number\" && f.pop(), f);\n    function c(f, y, b) {\n      if (b !== \"left\" && b !== \"right\") throw Error(\"side (\" + b + \") must be 'left' or 'right'\");\n      n(f), n(y);\n      const L = [],\n        z = l(L),\n        {\n          take: q,\n          peek: Ee\n        } = _(f);\n      for (let ne = 0; ne < y.length; ne++) {\n        const le = y[ne];\n        let ge, De;\n        switch (typeof le) {\n          case \"number\":\n            for (ge = le; ge > 0;) De = q(ge, \"i\"), z(De), typeof De != \"string\" && (ge -= E(De));\n            break;\n          case \"string\":\n            b === \"left\" && typeof Ee() == \"string\" && z(q(-1)), z(e.strPosToUni(le));\n            break;\n          case \"object\":\n            for (ge = a.dlen(le.d); ge > 0;) switch (De = q(ge, \"i\"), typeof De) {\n              case \"number\":\n                ge -= De;\n                break;\n              case \"string\":\n                z(De);\n                break;\n              case \"object\":\n                ge -= a.dlen(De.d);\n            }\n            break;\n        }\n      }\n      let ce;\n      for (; ce = q(-1);) z(ce);\n      return s(L);\n    }\n    function h(f, y) {\n      n(f), n(y);\n      const b = [],\n        L = l(b),\n        {\n          take: z\n        } = _(f);\n      for (let Ee = 0; Ee < y.length; Ee++) {\n        const ce = y[Ee];\n        let ne, le;\n        switch (typeof ce) {\n          case \"number\":\n            for (ne = ce; ne > 0;) le = z(ne, \"d\"), L(le), typeof le != \"object\" && (ne -= E(le));\n            break;\n          case \"string\":\n            L(ce);\n            break;\n          case \"object\":\n            ne = a.dlen(ce.d);\n            let ge = 0;\n            for (; ge < ne;) switch (le = z(ne - ge, \"d\"), typeof le) {\n              case \"number\":\n                L({\n                  d: x(ce.d, ge, ge + le)\n                }), ge += le;\n                break;\n              case \"string\":\n                ge += e.strPosToUni(le);\n                break;\n              case \"object\":\n                L(le);\n            }\n            break;\n        }\n      }\n      let q;\n      for (; q = z(-1);) L(q);\n      return s(b);\n    }\n    const U = (f, y) => {\n        let b = 0;\n        for (let L = 0; L < y.length && f > b; L++) {\n          const z = y[L];\n          switch (typeof z) {\n            case \"number\":\n              {\n                b += z;\n                break;\n              }\n            case \"string\":\n              const q = e.strPosToUni(z);\n              b += q, f += q;\n              break;\n            case \"object\":\n              f -= Math.min(a.dlen(z.d), f - b);\n              break;\n          }\n        }\n        return f;\n      },\n      Y = (f, y) => typeof f == \"number\" ? U(f, y) : f.map(b => U(b, y));\n    function pe(f, y, b) {\n      return r(f, (L, z) => typeof L == \"object\" && typeof L.d == \"number\" ? {\n        d: b.slice(y, z, z + L.d)\n      } : L);\n    }\n    function Z(f) {\n      return r(f, y => {\n        switch (typeof y) {\n          case \"object\":\n            if (typeof y.d == \"number\") throw Error(\"Cannot invert text op: Deleted characters missing from operation. makeInvertible must be called first.\");\n            return y.d;\n          // delete -> insert\n          case \"string\":\n            return {\n              d: y\n            };\n          // Insert -> delete\n          case \"number\":\n            return y;\n        }\n      });\n    }\n    function Ce(f) {\n      return r(f, y => typeof y == \"object\" && typeof y.d == \"string\" ? {\n        d: e.strPosToUni(y.d)\n      } : y);\n    }\n    function Le(f) {\n      let y = !0;\n      return t(f, b => {\n        typeof b == \"object\" && typeof b.d == \"number\" && (y = !1);\n      }), y;\n    }\n    function ve(f) {\n      return {\n        name: \"text-unicode\",\n        uri: \"http://sharejs.org/types/text-unicode\",\n        trim: s,\n        normalize: o,\n        checkOp: n,\n        /** Create a new text snapshot.\n         *\n         * @param {string} initial - initial snapshot data. Optional. Defaults to ''.\n         * @returns {Snap} Initial document snapshot object\n         */\n        create(y = \"\") {\n          if (typeof y != \"string\") throw Error(\"Initial data must be a string\");\n          return f.create(y);\n        },\n        /** Apply an operation to a document snapshot\n         */\n        apply(y, b) {\n          n(b);\n          const L = f.builder(y);\n          for (let z = 0; z < b.length; z++) {\n            const q = b[z];\n            switch (typeof q) {\n              case \"number\":\n                L.skip(q);\n                break;\n              case \"string\":\n                L.append(q);\n                break;\n              case \"object\":\n                L.del(a.dlen(q.d));\n                break;\n            }\n          }\n          return L.build();\n        },\n        transform: c,\n        compose: h,\n        transformPosition: U,\n        transformSelection: Y,\n        isInvertible: Le,\n        makeInvertible(y, b) {\n          return pe(y, b, f);\n        },\n        stripInvertible: Ce,\n        invert: Z,\n        invertWithDoc(y, b) {\n          return Z(pe(y, b, f));\n        },\n        isNoop: y => y.length === 0\n      };\n    }\n    a.default = ve;\n  }(ct)), ct;\n}\nvar rt = {},\n  It;\nfunction tn() {\n  if (It) return rt;\n  It = 1, Object.defineProperty(rt, \"__esModule\", {\n    value: !0\n  });\n  const a = ht(),\n    e = pt();\n  function n(t, r) {\n    return {\n      // Returns the text content of the document\n      get: t,\n      // Returns the number of characters in the string\n      getLength() {\n        return t().length;\n      },\n      // Insert the specified text at the given position in the document\n      insert(u, o, l) {\n        const E = e.strPosToUni(t(), u);\n        return r([E, o], l);\n      },\n      remove(u, o, l) {\n        const E = e.strPosToUni(t(), u);\n        return r([E, {\n          d: o\n        }], l);\n      },\n      // When you use this API, you should implement these two methods\n      // in your editing context.\n      //onInsert: function(pos, text) {},\n      //onRemove: function(pos, removedLength) {},\n      _onOp(u) {\n        a.eachOp(u, (o, l, E) => {\n          switch (typeof o) {\n            case \"string\":\n              this.onInsert && this.onInsert(E, o);\n              break;\n            case \"object\":\n              const x = a.dlen(o.d);\n              this.onRemove && this.onRemove(E, x);\n          }\n        });\n      },\n      onInsert: null,\n      onRemove: null\n    };\n  }\n  return rt.default = n, n.provides = {\n    text: !0\n  }, rt;\n}\nvar Et;\nfunction nn() {\n  return Et || (Et = 1, function (a) {\n    var e = Ue && Ue.__createBinding || (Object.create ? function (c, h, U, Y) {\n        Y === void 0 && (Y = U), Object.defineProperty(c, Y, {\n          enumerable: !0,\n          get: function () {\n            return h[U];\n          }\n        });\n      } : function (c, h, U, Y) {\n        Y === void 0 && (Y = U), c[Y] = h[U];\n      }),\n      n = Ue && Ue.__setModuleDefault || (Object.create ? function (c, h) {\n        Object.defineProperty(c, \"default\", {\n          enumerable: !0,\n          value: h\n        });\n      } : function (c, h) {\n        c.default = h;\n      }),\n      t = Ue && Ue.__importStar || function (c) {\n        if (c && c.__esModule) return c;\n        var h = {};\n        if (c != null) for (var U in c) Object.hasOwnProperty.call(c, U) && e(h, c, U);\n        return n(h, c), h;\n      },\n      r = Ue && Ue.__importDefault || function (c) {\n        return c && c.__esModule ? c : {\n          default: c\n        };\n      };\n    Object.defineProperty(a, \"__esModule\", {\n      value: !0\n    }), a.type = a.remove = a.insert = void 0;\n    const u = pt(),\n      o = t(ht()),\n      l = r(tn()),\n      E = {\n        create(c) {\n          return c;\n        },\n        toString(c) {\n          return c;\n        },\n        builder(c) {\n          if (typeof c != \"string\") throw Error(\"Invalid document snapshot: \" + c);\n          const h = [];\n          return {\n            skip(U) {\n              let Y = u.uniToStrPos(c, U);\n              if (Y > c.length) throw Error(\"The op is too long for this document\");\n              h.push(c.slice(0, Y)), c = c.slice(Y);\n            },\n            append(U) {\n              h.push(U);\n            },\n            del(U) {\n              c = c.slice(u.uniToStrPos(c, U));\n            },\n            build() {\n              return h.join(\"\") + c;\n            }\n          };\n        },\n        slice: o.uniSlice\n      },\n      x = o.default(E),\n      _ = Object.assign(Object.assign({}, x), {\n        api: l.default\n      });\n    a.type = _, a.insert = (c, h) => h.length === 0 ? [] : c === 0 ? [h] : [c, h], a.remove = (c, h) => o.dlen(h) === 0 ? [] : c === 0 ? [{\n      d: h\n    }] : [c, {\n      d: h\n    }];\n    var s = ht();\n    Object.defineProperty(a, \"makeType\", {\n      enumerable: !0,\n      get: function () {\n        return s.default;\n      }\n    });\n  }(Ue)), Ue;\n}\nvar Dt;\nfunction rn() {\n  return Dt || (Dt = 1, function (a) {\n    var e = Ze && Ze.__importDefault || function (i) {\n      return i && i.__esModule ? i : {\n        default: i\n      };\n    };\n    Object.defineProperty(a, \"__esModule\", {\n      value: !0\n    }), a.editOp = a.replaceOp = a.insertOp = a.moveOp = a.removeOp = a.type = void 0;\n    const n = e(Qt()),\n      t = e(en()),\n      r = Mt(),\n      u = St();\n    function o(i, d) {\n      if (!i) throw new Error(d);\n    }\n    a.type = {\n      name: \"json1\",\n      uri: \"http://sharejs.org/types/JSONv1\",\n      readCursor: r.readCursor,\n      writeCursor: r.writeCursor,\n      create: i => i,\n      isNoop: i => i == null,\n      setDebug(i) {},\n      registerSubtype: Z,\n      checkValidOp: z,\n      normalize: q,\n      apply: Ee,\n      transformPosition: ce,\n      compose: ne,\n      tryTransform: st,\n      transform: jt,\n      makeInvertible: De,\n      invert: le,\n      invertWithDoc: Ut,\n      RM_UNEXPECTED_CONTENT: u.ConflictType.RM_UNEXPECTED_CONTENT,\n      DROP_COLLISION: u.ConflictType.DROP_COLLISION,\n      BLACKHOLE: u.ConflictType.BLACKHOLE,\n      transformNoConflict: (i, d, m) => yt(() => !0, i, d, m),\n      typeAllowingConflictsPred: i => Object.assign(Object.assign({}, a.type), {\n        transform: (d, m, D) => yt(i, d, m, D)\n      })\n    };\n    const l = i => i ? i.getComponent() : null;\n    function E(i) {\n      return i && typeof i == \"object\" && !Array.isArray(i);\n    }\n    const x = i => Array.isArray(i) ? i.slice() : i !== null && typeof i == \"object\" ? Object.assign({}, i) : i,\n      _ = i => i && (i.p != null || i.r !== void 0),\n      s = i => i && (i.d != null || i.i !== void 0);\n    function c(i, d) {\n      return o(i != null), typeof d == \"number\" ? (o(Array.isArray(i), \"Invalid key - child is not an array\"), (i = i.slice()).splice(d, 1)) : (o(E(i), \"Invalid key - child is not an object\"), delete (i = Object.assign({}, i))[d]), i;\n    }\n    function h(i, d, m) {\n      return typeof d == \"number\" ? (o(i != null, \"Container is missing for key\"), o(Array.isArray(i), \"Cannot use numerical key for object container\"), o(i.length >= d, \"Cannot insert into out of bounds index\"), i.splice(d, 0, m)) : (o(E(i), \"Cannot insert into missing item\"), o(i[d] === void 0, \"Trying to overwrite value at key. Your op needs to remove it first\"), i[d] = m), m;\n    }\n    a.removeOp = (i, d = !0) => r.writeCursor().writeAtPath(i, \"r\", d).get(), a.moveOp = (i, d) => r.writeCursor().writeMove(i, d).get(), a.insertOp = (i, d) => r.writeCursor().writeAtPath(i, \"i\", d).get(), a.replaceOp = (i, d, m) => r.writeCursor().at(i, D => {\n      D.write(\"r\", d), D.write(\"i\", m);\n    }).get(), a.editOp = (i, d, m, D = !1) => r.writeCursor().at(i, O => y(O, d, m, D)).get();\n    const U = (i, d) => i != null && (typeof d == \"number\" ? Array.isArray(i) : typeof i == \"object\"),\n      Y = (i, d) => U(i, d) ? i[d] : void 0,\n      pe = {};\n    function Z(i) {\n      let d = i.type ? i.type : i;\n      d.name && (pe[d.name] = d), d.uri && (pe[d.uri] = d);\n    }\n    const Ce = i => {\n      const d = pe[i];\n      if (d) return d;\n      throw Error(\"Missing type: \" + i);\n    };\n    Z(nn());\n    const Le = (i, d) => i + d;\n    Z({\n      name: \"number\",\n      apply: Le,\n      compose: Le,\n      invert: i => -i,\n      transform: i => i\n    });\n    const ve = i => i == null ? null : i.et ? Ce(i.et) : i.es ? pe[\"text-unicode\"] : i.ena != null ? pe.number : null,\n      f = i => i.es ? i.es : i.ena != null ? i.ena : i.e,\n      y = (i, d, m, D = !1) => {\n        const [O, I] = typeof d == \"string\" ? [Ce(d), d] : [d, d.name];\n        !D && O.isNoop && O.isNoop(m) || (I === \"number\" ? i.write(\"ena\", m) : I === \"text-unicode\" ? i.write(\"es\", m) : (i.write(\"et\", I), i.write(\"e\", m)));\n      };\n    function b(i) {\n      o(typeof i == \"number\"), o(i >= 0), o(i === (0 | i));\n    }\n    function L(i) {\n      typeof i == \"number\" ? b(i) : o(typeof i == \"string\");\n    }\n    function z(i) {\n      if (i === null) return;\n      const d = /* @__PURE__ */new Set(),\n        m = /* @__PURE__ */new Set(),\n        D = I => {\n          let R = !0,\n            $ = !1;\n          for (let p in I) {\n            const v = I[p];\n            if (R = !1, o(p === \"p\" || p === \"r\" || p === \"d\" || p === \"i\" || p === \"e\" || p === \"es\" || p === \"ena\" || p === \"et\", \"Invalid component item '\" + p + \"'\"), p === \"p\") b(v), o(!d.has(v)), d.add(v), o(I.r === void 0);else if (p === \"d\") b(v), o(!m.has(v)), m.add(v), o(I.i === void 0);else if (p === \"e\" || p === \"es\" || p === \"ena\") {\n              o(!$), $ = !0;\n              const w = ve(I);\n              o(w, \"Missing type in edit\"), w.checkValidOp && w.checkValidOp(f(I));\n            }\n          }\n          o(!R);\n        },\n        O = (I, R, $) => {\n          if (!Array.isArray(I)) throw Error(\"Op must be null or a list\");\n          if (I.length === 0) throw Error(\"Empty descent\");\n          R || L(I[0]);\n          let p = 1,\n            v = 0,\n            w = 0;\n          for (let C = 0; C < I.length; C++) {\n            const N = I[C];\n            if (o(N != null), Array.isArray(N)) {\n              const B = O(N, !1);\n              if (v) {\n                const g = typeof w,\n                  S = typeof B;\n                g === S ? o(w < B, \"descent keys are not in order\") : o(g === \"number\" && S === \"string\");\n              }\n              w = B, v++, p = 3;\n            } else typeof N == \"object\" ? (o(p === 1, `Prev not scalar - instead ${p}`), D(N), p = 2) : (o(p !== 3), L(N), o(r.isValidPathItem(N), \"Invalid path key\"), p = 1);\n          }\n          return o(v !== 1, \"Operation makes multiple descents. Remove some []\"), o(p === 2 || p === 3), I[0];\n        };\n      O(i, !0), o(d.size === m.size, \"Mismatched picks and drops in op\");\n      for (let I = 0; I < d.size; I++) o(d.has(I)), o(m.has(I));\n    }\n    function q(i) {\n      let d = 0,\n        m = [];\n      const D = r.writeCursor();\n      return D.mergeTree(i, (O, I) => {\n        const R = ve(O);\n        if (R) {\n          const p = f(O);\n          y(I, R, R.normalize ? R.normalize(p) : p);\n        }\n        for (const p of [\"r\", \"p\", \"i\", \"d\"]) if (O[p] !== void 0) {\n          const v = p === \"p\" || p === \"d\" ? ($ = O[p], m[$] == null && (m[$] = d++), m[$]) : O[p];\n          I.write(p, v);\n        }\n        var $;\n      }), D.get();\n    }\n    function Ee(i, d) {\n      if (z(d), d === null) return i;\n      const m = [];\n      return function D(O, I) {\n        let R = O,\n          $ = 0,\n          p = {\n            root: O\n          },\n          v = 0,\n          w = p,\n          C = \"root\";\n        function N() {\n          for (; v < $; v++) {\n            let B = I[v];\n            typeof B != \"object\" && (o(U(w, C)), w = w[C] = x(w[C]), C = B);\n          }\n        }\n        for (; $ < I.length; $++) {\n          const B = I[$];\n          if (Array.isArray(B)) {\n            const g = D(R, B);\n            g !== R && g !== void 0 && (N(), R = w[C] = g);\n          } else if (typeof B == \"object\") {\n            B.d != null ? (N(), R = h(w, C, m[B.d])) : B.i !== void 0 && (N(), R = h(w, C, B.i));\n            const g = ve(B);\n            if (g) N(), R = w[C] = g.apply(R, f(B));else if (B.e !== void 0) throw Error(\"Subtype \" + B.et + \" undefined\");\n          } else R = Y(R, B);\n        }\n        return p.root;\n      }(i = function D(O, I) {\n        const R = [];\n        let $ = 0;\n        for (; $ < I.length; $++) {\n          const C = I[$];\n          if (Array.isArray(C)) break;\n          typeof C != \"object\" && (R.push(O), O = Y(O, C));\n        }\n        for (let C = I.length - 1; C >= $; C--) O = D(O, I[C]);\n        for (--$; $ >= 0; $--) {\n          const C = I[$];\n          if (typeof C != \"object\") {\n            const N = R.pop();\n            O = O === Y(N, C) ? N : O === void 0 ? c(N, C) : (v = C, w = O, (p = x(p = N))[v] = w, p);\n          } else _(C) && (o(O !== void 0, \"Cannot pick up or remove undefined\"), C.p != null && (m[C.p] = O), O = void 0);\n        }\n        var p, v, w;\n        return O;\n      }(i, d), d);\n    }\n    function ce(i, d) {\n      i = i.slice(), z(d);\n      const m = r.readCursor(d);\n      let D,\n        O,\n        I = !1;\n      const R = [];\n      for (let p = 0;; p++) {\n        const v = i[p],\n          w = m.getComponent();\n        if (w && (w.r !== void 0 ? I = !0 : w.p != null && (I = !1, D = w.p, O = p)), p >= i.length) break;\n        let C = 0;\n        const N = r.advancer(m, void 0, (g, S) => {\n          _(S) && C++;\n        });\n        R.unshift(N);\n        const B = N(v);\n        if (typeof v == \"number\" && (i[p] -= C), !B) break;\n      }\n      if (R.forEach(p => p.end()), I) return null;\n      const $ = () => {\n        let p = 0;\n        if (D != null) {\n          const v = m.getPath();\n          p = v.length, i = v.concat(i.slice(O));\n        }\n        for (; p < i.length; p++) {\n          const v = i[p],\n            w = l(m),\n            C = ve(w);\n          if (C) {\n            const g = f(w);\n            C.transformPosition && (i[p] = C.transformPosition(i[p], g));\n            break;\n          }\n          let N = 0;\n          const B = r.advancer(m, (g, S) => s(S) ? ~(g - N) : g - N, (g, S) => {\n            s(S) && N++;\n          })(v);\n          if (typeof v == \"number\" && (i[p] += N), !B) break;\n        }\n      };\n      return D != null ? m.eachDrop(null, p => {\n        p === D && $();\n      }) : $(), i;\n    }\n    function ne(i, d) {\n      if (z(i), z(d), i == null) return d;\n      if (d == null) return i;\n      let m = 0;\n      const D = r.readCursor(i),\n        O = r.readCursor(d),\n        I = r.writeCursor(),\n        R = [],\n        $ = [],\n        p = [],\n        v = [],\n        w = [],\n        C = [],\n        N = /* @__PURE__ */new Set();\n      D.traverse(null, g => {\n        g.p != null && (p[g.p] = D.clone());\n      }), O.traverse(null, g => {\n        g.d != null && (v[g.d] = O.clone());\n      });\n      const B = r.writeCursor();\n      return function g(S, re, te, K, se, Ne, Oe, ye) {\n        o(re || te);\n        const oe = l(re),\n          Pe = l(te),\n          Ae = !!Pe && Pe.r !== void 0,\n          qe = !!oe && oe.i !== void 0,\n          Te = oe ? oe.d : null,\n          Ie = Pe ? Pe.p : null,\n          Re = (Ne || Ae) && Ie == null;\n        if (Ie != null) K = v[Ie], Oe = $[Ie] = new r.WriteCursor();else if (Pe && Pe.r !== void 0) K = null;else {\n          const T = l(K);\n          T && T.d != null && (K = null);\n        }\n        const Q = l(K);\n        if (Te != null) {\n          if (S = p[Te], ye = R[Te] = new r.WriteCursor(), Re) Ne && !Ae && ye.write(\"r\", !0);else {\n            const T = w[Te] = m++;\n            Oe.write(\"d\", T);\n          }\n        } else if (oe && oe.i !== void 0) S = null;else {\n          const T = l(S);\n          T && T.p != null && (S = null);\n        }\n        let A;\n        qe ? (o(se === void 0), A = oe.i) : A = se;\n        const W = (Ie == null ? !qe || Ne || Ae : A === void 0) ? null : Oe.getComponent();\n        if (Ie != null) {\n          if (!(se !== void 0 || qe)) {\n            const T = Te != null ? w[Te] : m++;\n            C[Ie] = T, ye.write(\"p\", T);\n          }\n        } else Ae && (qe || se !== void 0 || (Pe.r, ye.write(\"r\", Pe.r)));\n        const M = Re ? null : ve(oe),\n          P = ve(Q);\n        if ((M || P) && (M && M.name, P && P.name), M && P) {\n          o(M === P);\n          const T = f(oe),\n            G = f(Q),\n            he = M.compose(T, G);\n          y(Oe, M, he), N.add(Q);\n        } else M ? y(Oe, M, f(oe)) : P && (y(Oe, P, f(Q)), N.add(Q));\n        const k = typeof A == \"object\" && A != null;\n        let J = !1,\n          X = 0,\n          ee = 0,\n          fe = 0,\n          de = 0,\n          ae = 0;\n        const me = r.advancer(K, (T, G) => s(G) ? de - T - 1 : T - de, (T, G) => {\n            s(G) && de++;\n          }),\n          H = r.advancer(S, (T, G) => _(G) ? X - T - 1 : T - X, (T, G) => {\n            _(G) && X++;\n          });\n        if (r.eachChildOf(re, te, (T, G, he) => {\n          let we,\n            xe,\n            Ke = T,\n            Me = T,\n            Ye = T;\n          if (typeof T == \"number\") {\n            let _e = T + fe;\n            xe = me(_e), Me = _e + de;\n            let ue = T + ee;\n            we = H(ue), s(l(xe)) && (we = null), Ke = ue + X, Ye = T + ae, o(Ke >= 0, \"p1PickKey is negative\"), o(Me >= 0, \"p2DropKey is negative\");\n            const Se = s(l(G)),\n              Be = _(l(he));\n            (Se || Be && !Re) && ae--, Se && ee--, Be && fe--;\n          } else we = H(T), xe = me(T);\n          ye.descend(Ke), Oe.descend(Me);\n          const Xe = k && !s(l(G)) ? A[Ye] : void 0,\n            je = g(we, G, he, xe, Xe, Re, Oe, ye);\n          var $e, j, ie;\n          k && !Re ? Xe !== je && (J || (A = Array.isArray(A) ? A.slice() : Object.assign({}, A), J = !0), $e = A, ie = je, typeof (j = Ye) == \"number\" ? (o(Array.isArray($e)), o(j < $e.length)) : (o(!Array.isArray($e)), o($e[j] !== void 0)), ie === void 0 ? typeof j == \"number\" ? $e.splice(j, 1) : delete $e[j] : $e[j] = ie) : o(je === void 0), Oe.ascend(), ye.ascend();\n        }), H.end(), me.end(), W != null) W.i = A;else if (!Ne && !Ae && Ie == null) return A;\n      }(D, D.clone(), O, O.clone(), void 0, !1, I, B), I.reset(), I.mergeTree(B.get()), I.reset(), I.get(), R.map(g => g.get()), $.map(g => g.get()), D.traverse(I, (g, S) => {\n        const re = g.p;\n        if (re != null) {\n          const te = w[re];\n          te != null && S.write(\"p\", te);\n          const K = R[re];\n          K && K.get(), K && S.mergeTree(K.get());\n        } else g.r !== void 0 && S.write(\"r\", g.r);\n      }), I.reset(), I.get(), O.traverse(I, (g, S) => {\n        const re = g.d;\n        if (re != null) {\n          const K = C[re];\n          K != null && S.write(\"d\", K);\n          const se = $[re];\n          se && S.mergeTree(se.get());\n        } else g.i !== void 0 && S.write(\"i\", g.i);\n        const te = ve(g);\n        te && !N.has(g) && y(S, te, f(g));\n      }), I.get();\n    }\n    function le(i) {\n      if (i == null) return null;\n      const d = new r.ReadCursor(i),\n        m = new r.WriteCursor();\n      let D;\n      const O = [],\n        I = [];\n      return function R($, p, v) {\n        const w = $.getComponent();\n        let C,\n          N = !1;\n        if (w) {\n          w.p != null && (p.write(\"d\", w.p), O[w.p] = $.clone()), w.r !== void 0 && p.write(\"i\", w.r), w.d != null && (p.write(\"p\", w.d), v = void 0), w.i !== void 0 && (v = C = w.i);\n          const g = ve(w);\n          g && (v === void 0 ? (D || (D = /* @__PURE__ */new Set()), D.add(w)) : (f(w), v = g.apply(v, f(w)), N = !0));\n        }\n        let B = 0;\n        for (const g of $) {\n          p.descend(g);\n          const S = typeof g == \"number\" ? g - B : g,\n            re = Y(v, S);\n          s($.getComponent()) && B++;\n          const te = R($, p, re);\n          if (v !== void 0 && te !== void 0) {\n            if (N || (N = !0, v = x(v)), !U(v, S)) throw Error(\"Cannot modify child - invalid operation\");\n            v[S] = te;\n          }\n          p.ascend();\n        }\n        if (C === void 0) return N ? v : void 0;\n        p.write(\"r\", v);\n      }(d, m, void 0), D && (m.reset(), function R($, p, v) {\n        const w = p.getComponent();\n        if (w) {\n          const g = w.d;\n          if (g != null && ($ = O[g], v = I[g] = r.writeCursor()), D.has(w)) {\n            const S = ve(w);\n            if (!S.invert) throw Error(`Cannot invert subtype ${S.name}`);\n            y(v, S, S.invert(f(w)));\n          }\n        }\n        let C = 0,\n          N = 0;\n        const B = r.advancer($, (g, S) => _(S) ? C - g - 1 : g - C, (g, S) => {\n          _(S) && C++;\n        });\n        for (const g of p) if (typeof g == \"number\") {\n          const S = g - N,\n            re = B(S),\n            te = S + C;\n          v.descend(te), R(re, p, v), s(p.getComponent()) && N++, v.ascend();\n        } else v.descend(g), R(B(g), p, v), v.ascend();\n        B.end();\n      }(d.clone(), d, m), I.length && (m.reset(), d.traverse(m, (R, $) => {\n        const p = R.p;\n        if (p != null) {\n          const v = I[p];\n          v && v.get(), v && $.mergeTree(v.get());\n        }\n      }))), m.get();\n    }\n    const ge = (i, d) => i.some(m => typeof m == \"object\" && (Array.isArray(m) ? ge(m, d) : d(m)));\n    function De(i, d) {\n      if (i == null || !ge(i, p => {\n        var v;\n        return p.r !== void 0 || ((v = ve(p)) === null || v === void 0 ? void 0 : v.makeInvertible) != null;\n      })) return i;\n      const m = new r.ReadCursor(i),\n        D = new r.WriteCursor();\n      let O = !1;\n      const I = [],\n        R = [],\n        $ = (p, v, w) => {\n          const C = p.getComponent();\n          let N = !1;\n          if (C) {\n            C.d != null && v.write(\"d\", C.d), C.i !== void 0 && v.write(\"i\", C.i);\n            const g = C.p;\n            if (g != null && (I[g] = p.clone(), o(w !== void 0, \"Operation picks up at an invalid key\"), R[g] = w, v.write(\"p\", C.p)), C.r !== void 0 && w === void 0) throw Error(\"Invalid doc / op in makeInvertible: removed item missing from doc\");\n            const S = ve(C);\n            S && (S.makeInvertible ? O = !0 : y(v, S, f(C), !0));\n          }\n          let B = 0;\n          for (const g of p) {\n            v.descend(g);\n            const S = typeof g == \"number\" ? g - B : g,\n              re = Y(w, S),\n              te = $(p, v, re);\n            re !== te && (N || (N = !0, w = x(w)), te === void 0 ? (w = c(w, S), typeof g == \"number\" && B++) : w[S] = te), v.ascend();\n          }\n          return C && (C.r !== void 0 ? (v.write(\"r\", t.default(w)), w = void 0) : C.p != null && (w = void 0)), w;\n        };\n      return $(m, D, d), D.get(), O && (D.reset(), function p(v, w, C, N, B) {\n        const g = w.getComponent();\n        if (g) {\n          g.i !== void 0 ? (N = g.i, B = !0) : g.d != null && (N = R[g.d], v = I[g.d], B = !1, g.d);\n          let K = ve(g);\n          if (K && K.makeInvertible) {\n            const se = f(g);\n            y(C, K, K.makeInvertible(se, N), !0);\n          }\n        }\n        let S = 0,\n          re = 0;\n        const te = r.advancer(v, (K, se) => _(se) ? S - K - 1 : K - S, (K, se) => {\n          _(se) && S++;\n        });\n        for (const K of w) if (typeof K == \"number\") {\n          const se = K - re,\n            Ne = te(se),\n            Oe = se + S,\n            ye = Y(N, B ? se : Oe);\n          C.descend(K), p(Ne, w, C, ye, B), s(w.getComponent()) && re++, C.ascend();\n        } else {\n          const se = Y(N, K);\n          C.descend(K), p(te(K), w, C, se, B), C.ascend();\n        }\n        te.end();\n      }(m.clone(), m, D, d, !1)), D.get();\n    }\n    function Ut(i, d) {\n      return le(De(i, d));\n    }\n    const it = i => {\n      if (i == null) return null;\n      const d = i.slice();\n      for (let m = 0; m < i.length; m++) {\n        const D = d[m];\n        Array.isArray(D) && (d[m] = it(D));\n      }\n      return d;\n    };\n    function st(i, d, m) {\n      o(m === \"left\" || m === \"right\", \"Direction must be left or right\");\n      const D = m === \"left\" ? 0 : 1;\n      if (d == null) return {\n        ok: !0,\n        result: i\n      };\n      z(i), z(d);\n      let O = null;\n      const I = [],\n        R = [],\n        $ = [],\n        p = [],\n        v = [],\n        w = [],\n        C = [],\n        N = [],\n        B = [],\n        g = [],\n        S = [],\n        re = [],\n        te = [],\n        K = [],\n        se = [];\n      let Ne = 0;\n      const Oe = r.readCursor(i),\n        ye = r.readCursor(d),\n        oe = r.writeCursor();\n      if (function Q(A, W = null, M) {\n        const P = l(W);\n        P && (P.r !== void 0 ? M = W.clone() : P.p != null && (M = null, w[P.p] = A.clone()));\n        const k = A.getComponent();\n        let J;\n        k && (J = k.p) != null && (v[J] = W ? W.clone() : null, $[J] = A.clone(), M && (g[J] = !0, B[J] = M), P && P.p != null && (K[J] = P.p));\n        const X = r.advancer(W);\n        for (const ee of A) Q(A, X(ee), M);\n        X.end();\n      }(ye, Oe, null), function Q(A, W, M, P, k) {\n        const J = M.getComponent();\n        let X,\n          ee = !1;\n        J && ((X = J.d) != null ? (p[X] = M.clone(), P != null && (se[P] == null && (se[P] = []), se[P].push(X)), g[X], A = v[X] || null, W = $[X] || null, g[X] ? (k && (S[X] = !0), k = B[X] || null) : !k || D !== 1 && K[X] != null || O == null && (O = {\n          type: u.ConflictType.RM_UNEXPECTED_CONTENT,\n          op1: a.removeOp(k.getPath()),\n          op2: a.moveOp(W.getPath(), M.getPath())\n        }), ee = !0) : J.i !== void 0 && (A = W = null, ee = !0, k && O == null && (O = {\n          type: u.ConflictType.RM_UNEXPECTED_CONTENT,\n          op1: a.removeOp(k.getPath()),\n          op2: a.insertOp(M.getPath(), J.i)\n        })));\n        const fe = l(A);\n        fe && (fe.r !== void 0 ? k = A.clone() : fe.p != null && (fe.p, P = fe.p, k = null));\n        const de = ve(J);\n        de && k && O == null && (O = {\n          type: u.ConflictType.RM_UNEXPECTED_CONTENT,\n          op1: a.removeOp(k.getPath()),\n          op2: a.editOp(M.getPath(), de, f(J), !0)\n        });\n        let ae = 0,\n          me = 0;\n        const H = r.advancer(W, (G, he) => _(he) ? ae - G - 1 : G - ae, (G, he) => {\n            _(he) && ae++;\n          }),\n          T = r.advancer(A);\n        for (const G of M) if (typeof G == \"number\") {\n          const he = G - me,\n            we = H(he);\n          me += +Q(T(he + ae), we, M, P, k);\n        } else {\n          const he = H(G);\n          Q(T(G), he, M, P, k);\n        }\n        return H.end(), T.end(), ee;\n      }(Oe, ye, ye.clone(), null, null), p.map(Q => Q && Q.get()), O) return {\n        ok: !1,\n        conflict: O\n      };\n      S.map(Q => !!Q);\n      const Pe = [];\n      let Ae = null;\n      (function Q(A, W, M, P, k) {\n        let J = !1;\n        const X = l(W);\n        if (_(X)) {\n          const H = X.p;\n          H != null ? (M = p[H], P = re[H] = r.writeCursor(), J = !0, k = null) : (M = null, k = W.clone());\n        } else s(l(M)) && (M = null);\n        const ee = A.getComponent();\n        if (ee) {\n          const H = ee.p;\n          H != null ? (k && (N[H] = k), Pe[H] = k || D === 1 && J ? null : P.getComponent(), I[H] = A.clone(), M && (C[H] = M.clone())) : ee.r !== void 0 && (k || P.write(\"r\", !0), (k || J) && (Ae == null && (Ae = /* @__PURE__ */new Set()), Ae.add(ee)));\n        }\n        let fe = 0,\n          de = 0;\n        const ae = r.advancer(W, void 0, (H, T) => {\n            _(T) && fe++;\n          }),\n          me = r.advancer(M, (H, T) => s(T) ? ~(H - de) : H - de, (H, T) => {\n            s(T) && de++;\n          });\n        if (A) for (const H of A) if (typeof H == \"string\") {\n          const T = ae(H),\n            G = me(H);\n          P.descend(H), Q(A, T, G, P, k), P.ascend();\n        } else {\n          const T = ae(H),\n            G = H - fe,\n            he = _(l(T)) ? null : me(G),\n            we = G + de;\n          o(we >= 0), P.descend(we), Q(A, T, he, P, k), P.ascend();\n        }\n        ae.end(), me.end();\n      })(Oe, ye, ye.clone(), oe, null), oe.reset();\n      let qe = [];\n      if (function Q(A, W, M, P, k, J) {\n        o(W);\n        const X = W.getComponent();\n        let ee = l(P),\n          fe = !1;\n        const de = (j, ie, _e) => j ? a.moveOp(j.getPath(), ie.getPath()) : a.insertOp(ie.getPath(), _e.i);\n        if (s(X)) {\n          const j = X.d;\n          j != null && (R[j] = W.clone());\n          const ie = j != null ? Pe[j] : null;\n          let _e = !1;\n          if (X.i !== void 0 || j != null && ie) {\n            let ue;\n            ee && (ee.i !== void 0 || (ue = ee.d) != null && !g[ue]) && (_e = ue != null ? j != null && j === K[ue] : n.default(ee.i, X.i), _e || ue != null && D !== 1 && K[ue] != null || O == null && (O = {\n              type: u.ConflictType.DROP_COLLISION,\n              op1: de(j != null ? I[j] : null, W, X),\n              op2: de(ue != null ? $[ue] : null, P, ee)\n            })), _e || (J ? O == null && (O = {\n              type: u.ConflictType.RM_UNEXPECTED_CONTENT,\n              op1: de(j != null ? I[j] : null, W, X),\n              op2: a.removeOp(J.getPath())\n            }) : (j != null ? (qe[Ne] = j, k.write(\"d\", ie.p = Ne++)) : k.write(\"i\", t.default(X.i)), fe = !0));\n          } else if (j != null && !ie) {\n            const ue = N[j];\n            ue && (J = ue.clone());\n          }\n          j != null ? (A = I[j], M = w[j], P = C[j]) : X.i !== void 0 && (A = M = null, _e || (P = null));\n        } else _(l(A)) && (A = M = P = null);\n        const ae = l(A),\n          me = l(M);\n        if (_(me)) {\n          const j = me.p;\n          me.r !== void 0 && (!ae || ae.r === void 0) || g[j] ? (P = null, J = M.clone()) : j != null && (P = p[j], D !== 1 && K[j] != null || ((k = te[j]) || (k = te[j] = r.writeCursor()), k.reset(), J = null));\n        } else !s(X) && s(ee) && (P = null);\n        ee = P != null ? P.getComponent() : null;\n        const H = ve(X);\n        if (H) {\n          const j = f(X);\n          if (J) O == null && (O = {\n            type: u.ConflictType.RM_UNEXPECTED_CONTENT,\n            op1: a.editOp(W.getPath(), H, j, !0),\n            op2: a.removeOp(J.getPath())\n          });else {\n            const ie = ve(ee);\n            let _e;\n            if (ie) {\n              if (H !== ie) throw Error(\"Transforming incompatible types\");\n              const ue = f(ee);\n              _e = H.transform(j, ue, m);\n            } else _e = t.default(j);\n            y(k, H, _e);\n          }\n        }\n        let T = 0,\n          G = 0,\n          he = 0,\n          we = 0,\n          xe = 0,\n          Ke = 0,\n          Me = A != null && A.descendFirst(),\n          Ye = Me;\n        const Xe = r.advancer(M, void 0, (j, ie) => {\n          _(ie) && he++;\n        });\n        let je = P != null && P.descendFirst(),\n          $e = je;\n        for (const j of W) if (typeof j == \"number\") {\n          let ie;\n          const _e = s(W.getComponent()),\n            ue = j - G;\n          {\n            let We;\n            for (; Me && typeof (We = A.getKey()) == \"number\";) {\n              We += T;\n              const ke = A.getComponent(),\n                Je = _(ke);\n              if (We > ue || We === ue && (!Je || D === 0 && _e)) break;\n              if (Je) {\n                T--;\n                const Fe = ke.p;\n                K.includes(Fe), ke.d, l(te[ke.d]), _(l(te[ke.d])), (ke.r === void 0 || Ae && Ae.has(ke)) && (Fe == null || !Pe[Fe] || D !== 1 && K.includes(Fe)) || xe--;\n              }\n              Me = A.nextSibling();\n            }\n            ie = Me && We === ue ? A : null;\n          }\n          const Se = ue - T;\n          let Be = Xe(Se);\n          const ot = Se - he;\n          let Qe = null;\n          {\n            let We, ke;\n            for (; je && typeof (We = P.getKey()) == \"number\";) {\n              ke = We - we;\n              const Je = P.getComponent(),\n                Fe = s(Je);\n              if (ke > ot) break;\n              if (ke === ot) {\n                if (!Fe) {\n                  Qe = P;\n                  break;\n                }\n                {\n                  if (D === 0 && _e) {\n                    Qe = P;\n                    break;\n                  }\n                  const Ge = Be && _(Be.getComponent());\n                  if (D === 0 && Ge) break;\n                }\n              }\n              if (Fe) {\n                const Ge = Je.d;\n                g[Ge], K[Ge], Je.i === void 0 && (g[Ge] || K[Ge] != null && D !== 1) ? (g[Ge] || K[Ge] != null && D === 0) && (we++, Ke--) : we++;\n              }\n              je = P.nextSibling();\n            }\n          }\n          const _t = ot + we + xe + Ke;\n          o(_t >= 0, \"trying to descend to a negative index\"), k.descend(_t), _e && (ie = Be = Qe = null, G++), Q(ie, W, Be, Qe, k, J) && Ke++, k.ascend();\n        } else {\n          let ie;\n          for (; Me && (ie = A.getKey(), typeof ie != \"string\" || !(ie > j || ie === j));) Me = A.nextSibling();\n          const _e = Me && ie === j ? A : null,\n            ue = Xe(j);\n          let Se;\n          for (; je && (Se = P.getKey(), typeof Se != \"string\" || !(Se > j || Se === j));) je = P.nextSibling();\n          const Be = je && Se === j ? P : null;\n          k.descend(j), Q(_e, W, ue, Be, k, J), k.ascend();\n        }\n        return Xe.end(), Ye && A.ascend(), $e && P.ascend(), fe;\n      }(Oe, Oe.clone(), ye, ye.clone(), oe, null), O) return {\n        ok: !1,\n        conflict: O\n      };\n      oe.reset();\n      const Te = (Q, A, W) => Q.traverse(A, (M, P) => {\n        M.d != null && W(M.d, Q, P);\n      });\n      (g.length || re.length) && (Te(ye, oe, (Q, A, W) => {\n        g[Q] && !S[Q] && W.write(\"r\", !0), re[Q] && W.mergeTree(re[Q].get());\n      }), oe.reset());\n      const Ie = [],\n        Re = [];\n      if ((te.length || g.length) && !O) {\n        const Q = r.readCursor(it(oe.get()));\n        if (Te(Q, null, (A, W) => {\n          Ie[A] = W.clone();\n        }), te.forEach(A => {\n          A && Te(r.readCursor(A.get()), null, (W, M) => {\n            Ie[W] = M.clone();\n          });\n        }), function A(W, M, P, k, J, X) {\n          const ee = l(M);\n          if (ee && _(ee)) {\n            if (ee.p != null) {\n              const T = ee.p;\n              Ie[T].getPath(), P = Ie[T], k = Re[T] = r.writeCursor();\n            } else ee.r !== void 0 && (P = null);\n          } else s(l(P)) && (P = null);\n          const fe = W.getComponent();\n          if (fe) {\n            let T;\n            if ((T = fe.d) != null) {\n              const G = te[T];\n              G && (G.get(), k.mergeTree(G.get()), P = r.readCursor(G.get()));\n            }\n          }\n          let de = 0,\n            ae = 0;\n          const me = r.advancer(M, void 0, (T, G) => {\n              _(G) && de--;\n            }),\n            H = r.advancer(P, (T, G) => s(G) ? -(T - ae) - 1 : T - ae, (T, G) => {\n              s(G) && ae++;\n            });\n          for (const T of W) if (typeof T == \"number\") {\n            const G = me(T),\n              he = T + de,\n              we = H(he),\n              xe = he + ae;\n            k.descend(xe), A(W, G, we, k), k.ascend();\n          } else k.descend(T), A(W, me(T), H(T), k), k.ascend();\n          me.end(), H.end();\n        }(ye, Q, Q.clone(), oe), oe.reset(), O) return {\n          ok: !1,\n          conflict: O\n        };\n        if (oe.get(), Re.length) {\n          const A = Re.map(M => M ? M.get() : null),\n            W = r.readCursor(it(oe.get()));\n          if (Te(W, oe, (M, P, k) => {\n            const J = A[M];\n            J && (k.mergeTree(J), A[M] = null);\n          }), A.find(M => M)) {\n            const M = r.writeCursor(),\n              P = r.writeCursor();\n            let k = 0,\n              J = 0;\n            A.forEach(X => {\n              X != null && Te(r.readCursor(X), null, ee => {\n                const fe = qe[ee];\n                M.writeMove(I[fe].getPath(), R[fe].getPath(), k++);\n                const de = se[fe];\n                de && de.forEach(ae => {\n                  g[ae] || D !== 1 && K[ae] != null || P.writeMove($[ae].getPath(), p[ae].getPath(), J++);\n                });\n              });\n            }), O = {\n              type: u.ConflictType.BLACKHOLE,\n              op1: M.get(),\n              op2: P.get()\n            };\n          }\n        }\n      }\n      return O ? {\n        ok: !1,\n        conflict: O\n      } : {\n        ok: !0,\n        result: oe.get()\n      };\n    }\n    const gt = i => {\n      const d = new Error(\"Transform detected write conflict\");\n      throw d.conflict = i, d.type = d.name = \"writeConflict\", d;\n    };\n    function jt(i, d, m) {\n      const D = st(i, d, m);\n      if (D.ok) return D.result;\n      gt(D.conflict);\n    }\n    const ze = i => {\n        const d = r.writeCursor();\n        return r.readCursor(i).traverse(d, (m, D) => {\n          (s(m) || ve(m)) && D.write(\"r\", !0);\n        }), d.get();\n      },\n      $t = (i, d) => {\n        const {\n          type: m,\n          op1: D,\n          op2: O\n        } = i;\n        switch (m) {\n          case u.ConflictType.DROP_COLLISION:\n            return d === \"left\" ? [null, ze(O)] : [ze(D), null];\n          case u.ConflictType.RM_UNEXPECTED_CONTENT:\n            let I = !1;\n            return r.readCursor(D).traverse(null, R => {\n              R.r !== void 0 && (I = !0);\n            }), I ? [null, ze(O)] : [ze(D), null];\n          case u.ConflictType.BLACKHOLE:\n            return [ze(D), ze(O)];\n          default:\n            throw Error(\"Unrecognised conflict: \" + m);\n        }\n      };\n    function yt(i, d, m, D) {\n      let O = null;\n      for (;;) {\n        const I = st(d, m, D);\n        if (I.ok) return ne(O, I.result);\n        {\n          const {\n            conflict: R\n          } = I;\n          i(R) || gt(R);\n          const [$, p] = $t(R, D);\n          d = ne(q(d), $), m = ne(q(m), p), O = ne(O, p);\n        }\n      }\n    }\n  }(Ze)), Ze;\n}\nvar Pt;\nfunction sn() {\n  return Pt || (Pt = 1, function (a) {\n    var e = Ve && Ve.__createBinding || (Object.create ? function (u, o, l, E) {\n        E === void 0 && (E = l), Object.defineProperty(u, E, {\n          enumerable: !0,\n          get: function () {\n            return o[l];\n          }\n        });\n      } : function (u, o, l, E) {\n        E === void 0 && (E = l), u[E] = o[l];\n      }),\n      n = Ve && Ve.__exportStar || function (u, o) {\n        for (var l in u) l !== \"default\" && !o.hasOwnProperty(l) && e(o, u, l);\n      };\n    Object.defineProperty(a, \"__esModule\", {\n      value: !0\n    }), n(rn(), a);\n    var t = Mt();\n    Object.defineProperty(a, \"ReadCursor\", {\n      enumerable: !0,\n      get: function () {\n        return t.ReadCursor;\n      }\n    }), Object.defineProperty(a, \"WriteCursor\", {\n      enumerable: !0,\n      get: function () {\n        return t.WriteCursor;\n      }\n    });\n    var r = St();\n    Object.defineProperty(a, \"ConflictType\", {\n      enumerable: !0,\n      get: function () {\n        return r.ConflictType;\n      }\n    });\n  }(Ve)), Ve;\n}\nvar V = sn();\nclass on {\n  constructor() {\n    F(this, \"drawingManagerData\", {});\n    F(this, \"_oldDrawingManagerData\", {});\n    F(this, \"_focusDrawings\", []);\n    F(this, \"_remove$\", new be());\n    F(this, \"remove$\", this._remove$.asObservable());\n    F(this, \"_add$\", new be());\n    F(this, \"add$\", this._add$.asObservable());\n    F(this, \"_update$\", new be());\n    F(this, \"update$\", this._update$.asObservable());\n    F(this, \"_order$\", new be());\n    F(this, \"order$\", this._order$.asObservable());\n    F(this, \"_group$\", new be());\n    F(this, \"group$\", this._group$.asObservable());\n    F(this, \"_ungroup$\", new be());\n    F(this, \"ungroup$\", this._ungroup$.asObservable());\n    F(this, \"_refreshTransform$\", new be());\n    F(this, \"refreshTransform$\", this._refreshTransform$.asObservable());\n    F(this, \"_visible$\", new be());\n    F(this, \"visible$\", this._visible$.asObservable());\n    // private readonly _externalUpdate$ = new Subject<T[]>();\n    // readonly externalUpdate$ = this._externalUpdate$.asObservable();\n    F(this, \"_focus$\", new be());\n    F(this, \"focus$\", this._focus$.asObservable());\n    F(this, \"_featurePluginUpdate$\", new be());\n    F(this, \"featurePluginUpdate$\", this._featurePluginUpdate$.asObservable());\n    F(this, \"_featurePluginAdd$\", new be());\n    F(this, \"featurePluginAdd$\", this._featurePluginAdd$.asObservable());\n    F(this, \"_featurePluginRemove$\", new be());\n    F(this, \"featurePluginRemove$\", this._featurePluginRemove$.asObservable());\n    F(this, \"_featurePluginOrderUpdate$\", new be());\n    F(this, \"featurePluginOrderUpdate$\", this._featurePluginOrderUpdate$.asObservable());\n    F(this, \"_featurePluginGroupUpdate$\", new be());\n    F(this, \"featurePluginGroupUpdate$\", this._featurePluginGroupUpdate$.asObservable());\n    F(this, \"_featurePluginUngroupUpdate$\", new be());\n    F(this, \"featurePluginUngroupUpdate$\", this._featurePluginUngroupUpdate$.asObservable());\n    F(this, \"_visible\", !0);\n    F(this, \"_editable\", !0);\n  }\n  dispose() {\n    this._remove$.complete(), this._add$.complete(), this._update$.complete(), this._order$.complete(), this._focus$.complete(), this._featurePluginUpdate$.complete(), this._featurePluginAdd$.complete(), this._featurePluginRemove$.complete(), this._featurePluginOrderUpdate$.complete(), this.drawingManagerData = {}, this._oldDrawingManagerData = {};\n  }\n  visibleNotification(e) {\n    this._visible$.next(e);\n  }\n  refreshTransform(e) {\n    e.forEach(n => {\n      const t = this._getCurrentBySearch(n);\n      t != null && (t.transform = n.transform, t.transforms = n.transforms, t.isMultiTransform = n.isMultiTransform);\n    }), this.refreshTransformNotification(e);\n  }\n  getDrawingDataForUnit(e) {\n    return this.drawingManagerData[e] || {};\n  }\n  removeDrawingDataForUnit(e) {\n    const n = this.drawingManagerData[e];\n    if (n == null) return;\n    delete this.drawingManagerData[e];\n    const t = [];\n    Object.keys(n).forEach(r => {\n      const u = n[r];\n      (u == null ? void 0 : u.data) != null && Object.keys(u.data).forEach(o => {\n        t.push({\n          unitId: e,\n          subUnitId: r,\n          drawingId: o\n        });\n      });\n    }), t.length > 0 && this.removeNotification(t);\n  }\n  registerDrawingData(e, n) {\n    this.drawingManagerData[e] = n;\n  }\n  initializeNotification(e) {\n    const n = [],\n      t = this.drawingManagerData[e];\n    t != null && (Object.keys(t).forEach(r => {\n      this._establishDrawingMap(e, r);\n      const u = t[r];\n      Object.keys(u.data).forEach(o => {\n        const l = u.data[o];\n        l.unitId = e, l.subUnitId = r, n.push(l);\n      });\n    }), n.length > 0 && this.addNotification(n));\n  }\n  getDrawingData(e, n) {\n    return this._getDrawingData(e, n);\n  }\n  // Use in doc only.\n  setDrawingData(e, n, t) {\n    this.drawingManagerData[e][n].data = t;\n  }\n  getBatchAddOp(e) {\n    const n = [],\n      t = [],\n      r = [];\n    e.forEach(x => {\n      const {\n        op: _,\n        invertOp: s\n      } = this._addByParam(x);\n      n.push({\n        unitId: x.unitId,\n        subUnitId: x.subUnitId,\n        drawingId: x.drawingId\n      }), t.push(_), r.push(s);\n    });\n    const u = t.reduce(V.type.compose, null),\n      o = r.reduce(V.type.compose, null),\n      {\n        unitId: l,\n        subUnitId: E\n      } = e[0];\n    return {\n      undo: o,\n      redo: u,\n      unitId: l,\n      subUnitId: E,\n      objects: n\n    };\n  }\n  getBatchRemoveOp(e) {\n    const n = [],\n      t = [];\n    e.forEach(E => {\n      const {\n        op: x,\n        invertOp: _\n      } = this._removeByParam(E);\n      n.unshift(x), t.push(_);\n    });\n    const r = n.reduce(V.type.compose, null),\n      u = t.reduce(V.type.compose, null),\n      {\n        unitId: o,\n        subUnitId: l\n      } = e[0];\n    return {\n      undo: u,\n      redo: r,\n      unitId: o,\n      subUnitId: l,\n      objects: e\n    };\n  }\n  getBatchUpdateOp(e) {\n    const n = [],\n      t = [],\n      r = [];\n    e.forEach(x => {\n      const {\n        op: _,\n        invertOp: s\n      } = this._updateByParam(x);\n      n.push({\n        unitId: x.unitId,\n        subUnitId: x.subUnitId,\n        drawingId: x.drawingId\n      }), t.push(_), r.push(s);\n    });\n    const u = t.reduce(V.type.compose, null),\n      o = r.reduce(V.type.compose, null),\n      {\n        unitId: l,\n        subUnitId: E\n      } = e[0];\n    return {\n      undo: o,\n      redo: u,\n      unitId: l,\n      subUnitId: E,\n      objects: n\n    };\n  }\n  removeNotification(e) {\n    this._remove$.next(e);\n  }\n  addNotification(e) {\n    this._add$.next(e);\n  }\n  updateNotification(e) {\n    this._update$.next(e);\n  }\n  orderNotification(e) {\n    this._order$.next(e);\n  }\n  groupUpdateNotification(e) {\n    this._group$.next(e);\n  }\n  ungroupUpdateNotification(e) {\n    this._ungroup$.next(e);\n  }\n  refreshTransformNotification(e) {\n    this._refreshTransform$.next(e);\n  }\n  getGroupDrawingOp(e) {\n    const n = [],\n      {\n        unitId: t,\n        subUnitId: r\n      } = e[0].parent;\n    e.forEach(l => {\n      n.push(this._getGroupDrawingOp(l));\n    });\n    const u = n.reduce(V.type.compose, null);\n    return {\n      undo: V.type.invertWithDoc(u, this.drawingManagerData),\n      redo: u,\n      unitId: t,\n      subUnitId: r,\n      objects: e\n    };\n  }\n  getUngroupDrawingOp(e) {\n    const n = [],\n      {\n        unitId: t,\n        subUnitId: r\n      } = e[0].parent;\n    e.forEach(l => {\n      n.push(this._getUngroupDrawingOp(l));\n    });\n    const u = n.reduce(V.type.compose, null);\n    return {\n      undo: V.type.invertWithDoc(u, this.drawingManagerData),\n      redo: u,\n      unitId: t,\n      subUnitId: r,\n      objects: e\n    };\n  }\n  getDrawingsByGroup(e) {\n    const {\n      unitId: n,\n      subUnitId: t,\n      drawingId: r\n    } = e;\n    if (this.getDrawingByParam({\n      unitId: n,\n      subUnitId: t,\n      drawingId: r\n    }) == null) return [];\n    const o = this._getDrawingData(n, t),\n      l = [];\n    return Object.keys(o).forEach(E => {\n      const x = o[E];\n      x.groupId === r && l.push(x);\n    }), l;\n  }\n  _getGroupDrawingOp(e) {\n    const {\n        parent: n,\n        children: t\n      } = e,\n      {\n        unitId: r,\n        subUnitId: u,\n        drawingId: o\n      } = n,\n      l = [];\n    l.push(V.insertOp([r, u, \"data\", o], n));\n    let E = Number.NEGATIVE_INFINITY;\n    return t.forEach(x => {\n      const {\n          unitId: _,\n          subUnitId: s,\n          drawingId: c\n        } = x,\n        h = this._hasDrawingOrder({\n          unitId: _,\n          subUnitId: s,\n          drawingId: c\n        });\n      E = Math.max(E, h), l.push(...this._getUpdateParamCompareOp(x, this.getDrawingByParam({\n        unitId: _,\n        subUnitId: s,\n        drawingId: c\n      })));\n    }), E === Number.NEGATIVE_INFINITY && (E = this._getDrawingOrder(r, u).length), l.push(V.insertOp([r, u, \"order\", E], o)), l.reduce(V.type.compose, null);\n  }\n  _getUngroupDrawingOp(e) {\n    const {\n        parent: n,\n        children: t\n      } = e,\n      {\n        unitId: r,\n        subUnitId: u,\n        drawingId: o\n      } = n,\n      l = [];\n    return t.forEach(E => {\n      const {\n        unitId: x,\n        subUnitId: _,\n        drawingId: s\n      } = E;\n      l.push(...this._getUpdateParamCompareOp(E, this.getDrawingByParam({\n        unitId: x,\n        subUnitId: _,\n        drawingId: s\n      })));\n    }), l.push(V.removeOp([r, u, \"data\", o], !0)), l.push(V.removeOp([r, u, \"order\", this._getDrawingOrder(r, u).indexOf(o)], !0)), l.reduce(V.type.compose, null);\n  }\n  applyJson1(e, n, t) {\n    this._establishDrawingMap(e, n), this._oldDrawingManagerData = {\n      ...this.drawingManagerData\n    }, this.drawingManagerData = V.type.apply(this.drawingManagerData, t);\n  }\n  // private _fillMissingFields(jsonOp: JSONOp) {\n  //     if (jsonOp == null) {\n  //         return;\n  //     }\n  //     let object: { [key: string]: {} } = this.drawingManagerData;\n  //     for (let i = 0; i < jsonOp.length; i++) {\n  //         const op = jsonOp[i];\n  //         if (Array.isArray(op)) {\n  //             const opKey = op[0] as string;\n  //             if (!(opKey in object)) {\n  //                 object[opKey] = null as unknown as never;\n  //             }\n  //         } else if (typeof op === 'string') {\n  //             object = object[op];\n  //             if (object == null) {\n  //                 break;\n  //             }\n  //         }\n  //     }\n  // }\n  featurePluginUpdateNotification(e) {\n    this._featurePluginUpdate$.next(e);\n  }\n  featurePluginOrderUpdateNotification(e) {\n    this._featurePluginOrderUpdate$.next(e);\n  }\n  featurePluginAddNotification(e) {\n    this._featurePluginAdd$.next(e);\n  }\n  featurePluginRemoveNotification(e) {\n    this._featurePluginRemove$.next(e);\n  }\n  featurePluginGroupUpdateNotification(e) {\n    this._featurePluginGroupUpdate$.next(e);\n  }\n  featurePluginUngroupUpdateNotification(e) {\n    this._featurePluginUngroupUpdate$.next(e);\n  }\n  getDrawingByParam(e) {\n    return this._getCurrentBySearch(e);\n  }\n  getOldDrawingByParam(e) {\n    return this._getOldBySearch(e);\n  }\n  getDrawingOKey(e) {\n    const [n, t, r] = e.split(\"#-#\");\n    return this._getCurrentBySearch({\n      unitId: n,\n      subUnitId: t,\n      drawingId: r\n    });\n  }\n  focusDrawing(e) {\n    if (e == null || e.length === 0) {\n      this._focusDrawings = [], this._focus$.next([]);\n      return;\n    }\n    const n = [];\n    e.forEach(t => {\n      var E;\n      const {\n          unitId: r,\n          subUnitId: u,\n          drawingId: o\n        } = t,\n        l = (E = this._getDrawingData(r, u)) == null ? void 0 : E[o];\n      l != null && n.push(l);\n    }), n.length > 0 && (this._focusDrawings = n, this._focus$.next(n));\n  }\n  getFocusDrawings() {\n    const e = [];\n    return this._focusDrawings.forEach(n => {\n      var l;\n      const {\n          unitId: t,\n          subUnitId: r,\n          drawingId: u\n        } = n,\n        o = (l = this._getDrawingData(t, r)) == null ? void 0 : l[u];\n      o != null && e.push(o);\n    }), e;\n  }\n  getDrawingOrder(e, n) {\n    return this._getDrawingOrder(e, n);\n  }\n  // Use in doc only.\n  setDrawingOrder(e, n, t) {\n    this.drawingManagerData[e][n].order = t;\n  }\n  orderUpdateNotification(e) {\n    this._order$.next(e);\n  }\n  getForwardDrawingsOp(e) {\n    const {\n        unitId: n,\n        subUnitId: t,\n        drawingIds: r\n      } = e,\n      u = [],\n      o = this.getDrawingOrder(n, t),\n      l = [...r];\n    r.forEach(_ => {\n      const s = this._hasDrawingOrder({\n        unitId: n,\n        subUnitId: t,\n        drawingId: _\n      });\n      if (s === -1 || s === o.length - 1) return;\n      const c = V.moveOp([n, t, \"order\", s], [n, t, \"order\", s + 1]);\n      u.push(c), l.includes(o[s + 1]) || l.push(o[s + 1]);\n    });\n    const E = u.reduce(V.type.compose, null);\n    return {\n      undo: V.type.invertWithDoc(E, this.drawingManagerData),\n      redo: E,\n      unitId: n,\n      subUnitId: t,\n      objects: {\n        ...e,\n        drawingIds: l\n      }\n    };\n  }\n  getBackwardDrawingOp(e) {\n    const {\n        unitId: n,\n        subUnitId: t,\n        drawingIds: r\n      } = e,\n      u = [],\n      o = this.getDrawingOrder(n, t),\n      l = [...r];\n    r.forEach(_ => {\n      const s = this._hasDrawingOrder({\n        unitId: n,\n        subUnitId: t,\n        drawingId: _\n      });\n      if (s === -1 || s === 0) return;\n      const c = V.moveOp([n, t, \"order\", s], [n, t, \"order\", s - 1]);\n      u.push(c), l.includes(o[s - 1]) || l.push(o[s - 1]);\n    });\n    const E = u.reduce(V.type.compose, null);\n    return {\n      undo: V.type.invertWithDoc(E, this.drawingManagerData),\n      redo: E,\n      unitId: n,\n      subUnitId: t,\n      objects: {\n        ...e,\n        drawingIds: l\n      }\n    };\n  }\n  getFrontDrawingsOp(e) {\n    const {\n        unitId: n,\n        subUnitId: t,\n        drawingIds: r\n      } = e,\n      u = this._getOrderFromSearchParams(n, t, r),\n      o = [...r],\n      l = this.getDrawingOrder(n, t),\n      E = [];\n    u.forEach(s => {\n      const {\n          drawingId: c\n        } = s,\n        h = this._getDrawingCount(n, t) - 1,\n        U = V.moveOp([n, t, \"order\", this._getDrawingOrder(n, t).indexOf(c)], [n, t, \"order\", h]);\n      E.push(U), o.includes(l[h]) || o.push(l[h]);\n    });\n    const x = E.reduce(V.type.compose, null);\n    return {\n      undo: V.type.invertWithDoc(x, this.drawingManagerData),\n      redo: x,\n      unitId: n,\n      subUnitId: t,\n      objects: {\n        ...e,\n        drawingIds: o\n      }\n    };\n  }\n  getBackDrawingsOp(e) {\n    const {\n        unitId: n,\n        subUnitId: t,\n        drawingIds: r\n      } = e,\n      u = this._getOrderFromSearchParams(n, t, r, !0),\n      o = [...r],\n      l = this.getDrawingOrder(n, t),\n      E = [];\n    u.forEach(s => {\n      const {\n          drawingId: c\n        } = s,\n        h = V.moveOp([n, t, \"order\", this._getDrawingOrder(n, t).indexOf(c)], [n, t, \"order\", 0]);\n      E.push(h), o.includes(l[0]) || o.push(l[0]);\n    });\n    const x = E.reduce(V.type.compose, null);\n    return {\n      undo: V.type.invertWithDoc(x, this.drawingManagerData),\n      redo: x,\n      unitId: n,\n      subUnitId: t,\n      objects: {\n        ...e,\n        drawingIds: o\n      }\n    };\n  }\n  _getDrawingCount(e, n) {\n    return this.getDrawingOrder(e, n).length || 0;\n  }\n  _getOrderFromSearchParams(e, n, t, r = !1) {\n    return t.map(u => {\n      const o = this._hasDrawingOrder({\n        unitId: e,\n        subUnitId: n,\n        drawingId: u\n      });\n      return {\n        drawingId: u,\n        zIndex: o\n      };\n    }).sort(r === !1 ? xt : Bt);\n  }\n  _hasDrawingOrder(e) {\n    if (e == null) return -1;\n    const {\n      unitId: n,\n      subUnitId: t,\n      drawingId: r\n    } = e;\n    return this._establishDrawingMap(n, t), this._getDrawingOrder(n, t).indexOf(r);\n  }\n  _getCurrentBySearch(e) {\n    var u, o, l;\n    if (e == null) return;\n    const {\n      unitId: n,\n      subUnitId: t,\n      drawingId: r\n    } = e;\n    return (l = (o = (u = this.drawingManagerData[n]) == null ? void 0 : u[t]) == null ? void 0 : o.data) == null ? void 0 : l[r];\n  }\n  _getOldBySearch(e) {\n    var u, o, l;\n    if (e == null) return;\n    const {\n      unitId: n,\n      subUnitId: t,\n      drawingId: r\n    } = e;\n    return (l = (o = (u = this._oldDrawingManagerData[n]) == null ? void 0 : u[t]) == null ? void 0 : o.data) == null ? void 0 : l[r];\n  }\n  _establishDrawingMap(e, n, t) {\n    var r;\n    return this.drawingManagerData[e] || (this.drawingManagerData[e] = {}), this.drawingManagerData[e][n] || (this.drawingManagerData[e][n] = {\n      data: {},\n      order: []\n    }), t == null ? null : (r = this.drawingManagerData[e][n].data) == null ? void 0 : r[t];\n  }\n  _addByParam(e) {\n    const {\n      unitId: n,\n      subUnitId: t,\n      drawingId: r\n    } = e;\n    this._establishDrawingMap(n, t, r);\n    const u = V.insertOp([n, t, \"data\", r], e),\n      o = V.insertOp([n, t, \"order\", this._getDrawingOrder(n, t).length], r),\n      l = [u, o].reduce(V.type.compose, null),\n      E = V.type.invertWithDoc(l, this.drawingManagerData);\n    return {\n      op: l,\n      invertOp: E\n    };\n  }\n  _removeByParam(e) {\n    if (e == null) return {\n      op: [],\n      invertOp: []\n    };\n    const {\n      unitId: n,\n      subUnitId: t,\n      drawingId: r\n    } = e;\n    if (this._establishDrawingMap(n, t, r) == null) return {\n      op: [],\n      invertOp: []\n    };\n    const o = V.removeOp([n, t, \"data\", r], !0),\n      l = V.removeOp([n, t, \"order\", this._getDrawingOrder(n, t).indexOf(r)], !0),\n      E = [o, l].reduce(V.type.compose, null),\n      x = V.type.invertWithDoc(E, this.drawingManagerData);\n    return {\n      op: E,\n      invertOp: x\n    };\n  }\n  _updateByParam(e) {\n    const {\n        unitId: n,\n        subUnitId: t,\n        drawingId: r\n      } = e,\n      u = this._establishDrawingMap(n, t, r);\n    if (u == null) return {\n      op: [],\n      invertOp: []\n    };\n    const l = this._getUpdateParamCompareOp(e, u).reduce(V.type.compose, null),\n      E = V.type.invertWithDoc(l, this.drawingManagerData);\n    return {\n      op: l,\n      invertOp: E\n    };\n  }\n  // private _initializeDrawingData(updateParam: T, oldParam: T) {\n  //     Object.keys(updateParam).forEach((key) => {\n  //         if (!(key in oldParam)) {\n  //             oldParam[key as keyof IDrawingParam] = null as unknown as never;\n  //         }\n  //     });\n  // }\n  _getUpdateParamCompareOp(e, n) {\n    const {\n        unitId: t,\n        subUnitId: r,\n        drawingId: u\n      } = e,\n      o = [];\n    return Object.keys(e).forEach(l => {\n      const E = e[l],\n        x = n[l];\n      x !== E && o.push(V.replaceOp([t, r, \"data\", u, l], x, E));\n    }), o;\n  }\n  _getDrawingData(e, n) {\n    var t, r;\n    return ((r = (t = this.drawingManagerData[e]) == null ? void 0 : t[n]) == null ? void 0 : r.data) || {};\n  }\n  _getDrawingOrder(e, n) {\n    var t, r;\n    return ((r = (t = this.drawingManagerData[e]) == null ? void 0 : t[n]) == null ? void 0 : r.order) || [];\n  }\n  getDrawingVisible() {\n    return this._visible;\n  }\n  getDrawingEditable() {\n    return this._editable;\n  }\n  setDrawingVisible(e) {\n    this._visible = e;\n  }\n  setDrawingEditable(e) {\n    this._editable = e;\n  }\n}\nclass an extends on {}\nfunction On({\n  unitId: a,\n  subUnitId: e,\n  drawingId: n\n}, t) {\n  return typeof t == \"number\" ? `${a}#-#${e}#-#${n}#-#${t}` : `${a}#-#${e}#-#${n}`;\n}\nconst wn = async a => new Promise((e, n) => {\n    const t = new Image();\n    t.src = a, t.onload = () => {\n      e({\n        width: t.width,\n        height: t.height,\n        image: t\n      });\n    }, t.onerror = r => {\n      n(r);\n    };\n  }),\n  kt = Wt(\"univer.drawing-manager.service\"),\n  ln = {\n    id: \"drawing.operation.set-drawing-selected\",\n    type: Gt.OPERATION,\n    handler: (a, e) => {\n      const n = a.get(kt);\n      return e == null ? !1 : (n.focusDrawing(e), !0);\n    }\n  },\n  un = \"drawing.config\",\n  Tt = {};\nclass cn {\n  constructor() {\n    F(this, \"_waitCount\", 0);\n    F(this, \"_change$\", new be());\n    F(this, \"change$\", this._change$);\n    F(this, \"_imageSourceCache\", /* @__PURE__ */new Map());\n  }\n  setWaitCount(e) {\n    this._waitCount = e, this._change$.next(e);\n  }\n  getImageSourceCache(e, n) {\n    if (n === at.BASE64) {\n      const t = new Image();\n      return t.src = e, t;\n    }\n    return this._imageSourceCache.get(e);\n  }\n  addImageSourceCache(e, n, t) {\n    n === at.BASE64 || t == null || this._imageSourceCache.set(e, t);\n  }\n  async getImage(e) {\n    return Promise.resolve(e);\n  }\n  async saveImage(e) {\n    return new Promise((n, t) => {\n      if (!Zt.includes(e.type)) {\n        t(new Error(et.ERROR_IMAGE_TYPE)), this._decreaseWaiting();\n        return;\n      }\n      if (e.size > Yt) {\n        t(new Error(et.ERROR_EXCEED_SIZE)), this._decreaseWaiting();\n        return;\n      }\n      const r = new FileReader();\n      r.readAsDataURL(e), r.onload = u => {\n        var E;\n        const o = (E = u.target) == null ? void 0 : E.result;\n        if (o == null) {\n          t(new Error(et.ERROR_IMAGE)), this._decreaseWaiting();\n          return;\n        }\n        const l = Lt.generateRandomId(6);\n        n({\n          imageId: l,\n          imageSourceType: at.BASE64,\n          source: o,\n          base64Cache: o,\n          status: et.SUCCUSS\n        }), this._decreaseWaiting();\n      };\n    });\n  }\n  _decreaseWaiting() {\n    this._waitCount -= 1, this._change$.next(this._waitCount);\n  }\n}\nvar dn = Object.getOwnPropertyDescriptor,\n  fn = (a, e, n, t) => {\n    for (var r = t > 1 ? void 0 : t ? dn(e, n) : e, u = a.length - 1, o; u >= 0; u--) (o = a[u]) && (r = o(r) || r);\n    return r;\n  },\n  dt = (a, e) => (n, t) => e(n, t, a);\nconst hn = \"UNIVER_DRAWING_PLUGIN\";\nvar ft;\nlet At = (ft = class extends Ht {\n  constructor(a = Tt, e, n, t) {\n    super(), this._config = a, this._injector = e, this._configService = n, this._commandService = t;\n    const {\n      ...r\n    } = zt({}, Tt, this._config);\n    this._configService.setConfig(un, r);\n  }\n  onStarting() {\n    this._initCommands(), this._initDependencies();\n  }\n  _initDependencies() {\n    var n;\n    Xt([[Jt, {\n      useClass: cn\n    }], [kt, {\n      useClass: an\n    }]], (n = this._config) == null ? void 0 : n.override).forEach(t => this._injector.add(t));\n  }\n  _initCommands() {\n    [ln].forEach(a => this.disposeWithMe(this._commandService.registerCommand(a)));\n  }\n}, F(ft, \"pluginName\", hn), ft);\nAt = fn([dt(1, qt(Kt)), dt(2, Ft), dt(3, Vt)], At);\nexport { Zt as DRAWING_IMAGE_ALLOW_IMAGE_LIST, Yt as DRAWING_IMAGE_ALLOW_SIZE, mn as DRAWING_IMAGE_COUNT_LIMIT, vn as DRAWING_IMAGE_HEIGHT_LIMIT, _n as DRAWING_IMAGE_WIDTH_LIMIT, an as DrawingManagerService, kt as IDrawingManagerService, In as IImageIoService, cn as ImageIoService, En as ImageSourceType, Dn as ImageUploadStatusType, ln as SetDrawingSelectedOperation, on as UnitDrawingService, At as UniverDrawingPlugin, On as getDrawingShapeKeyByDrawingSearch, wn as getImageSize };", "map": {"version": 3, "names": ["Nt", "Object", "defineProperty", "Rt", "a", "e", "n", "enumerable", "configurable", "writable", "value", "F", "sortRules", "xt", "sortRulesByDesc", "Bt", "createIdentifier", "Wt", "CommandType", "Gt", "ImageSourceType", "at", "ImageUploadStatusType", "et", "Tools", "Lt", "Inject", "qt", "Injector", "Kt", "IConfigService", "Ft", "ICommandService", "Vt", "Plugin", "Ht", "merge", "zt", "mergeOverrideWithDependencies", "Xt", "IImageIoService", "Jt", "In", "En", "Dn", "Subject", "be", "_n", "vn", "mn", "Yt", "Zt", "Ve", "Ze", "tt", "vt", "Qt", "t", "r", "Array", "isArray", "u", "length", "default", "nt", "mt", "en", "map", "lt", "<PERSON>t", "Mt", "eachChildOf", "advancer", "readCursor", "writeCursor", "WriteCursor", "ReadCursor", "isValidPathItem", "_", "s", "Error", "c", "h", "write", "constructor", "parents", "indexes", "lcIdx", "idx", "container", "ascend", "pop", "<PERSON><PERSON><PERSON>", "U", "unshift", "o", "get", "slice", "<PERSON><PERSON><PERSON>", "getComponent", "<PERSON><PERSON><PERSON><PERSON>", "push", "nextS<PERSON>ling", "isNaN", "_init", "clone", "Symbol", "iterator", "traverse", "descend", "eachPick", "p", "eachDrop", "d", "l", "pendingDescent", "_op", "flushDescent", "splice", "reset", "descend<PERSON><PERSON>", "mergeTree", "Y", "pe", "Z", "writeAtPath", "writeMove", "E", "Ce", "Le", "end", "x", "ut", "wt", "St", "ConflictType", "RM_UNEXPECTED_CONTENT", "DROP_COLLISION", "BLACKHOLE", "Ue", "He", "bt", "pt", "uniToStrPos", "strPosToUni", "charCodeAt", "ct", "Ct", "ht", "uniSlice", "dlen", "eachOp", "f", "y", "b", "L", "z", "q", "Ee", "Math", "min", "take", "ce", "ne", "le", "peek", "ge", "De", "ve", "name", "uri", "trim", "normalize", "checkOp", "create", "apply", "builder", "skip", "append", "del", "build", "transform", "compose", "transformPosition", "transformSelection", "isInvertible", "makeInvertible", "stripInvertible", "invert", "invertWithDoc", "isNoop", "rt", "It", "tn", "<PERSON><PERSON><PERSON><PERSON>", "insert", "remove", "_onOp", "onInsert", "onRemove", "provides", "text", "Et", "nn", "__createBinding", "__setModuleDefault", "__importStar", "__esModule", "hasOwnProperty", "call", "__importDefault", "type", "toString", "join", "assign", "api", "Dt", "rn", "i", "editOp", "replaceOp", "insertOp", "moveOp", "removeOp", "setDebug", "registerSubtype", "checkValidOp", "tryTransform", "st", "jt", "Ut", "transformNoConflict", "m", "yt", "typeAllowingConflictsPred", "D", "O", "es", "ena", "number", "I", "Set", "R", "$", "v", "has", "add", "w", "C", "N", "B", "g", "S", "size", "root", "for<PERSON>ach", "concat", "re", "te", "K", "se", "Ne", "Oe", "ye", "oe", "Pe", "Ae", "qe", "Te", "Ie", "Re", "T", "Q", "A", "W", "M", "P", "G", "he", "k", "J", "X", "ee", "fe", "de", "ae", "me", "H", "we", "xe", "<PERSON>", "Me", "Ye", "_e", "ue", "Se", "Be", "Xe", "je", "$e", "j", "ie", "some", "it", "ok", "result", "op1", "op2", "conflict", "We", "ke", "Je", "Fe", "includes", "ot", "Qe", "Ge", "_t", "find", "gt", "ze", "$t", "Pt", "sn", "__exportStar", "V", "on", "_remove$", "asObservable", "_add$", "_update$", "_order$", "_group$", "_ungroup$", "_refreshTransform$", "_visible$", "_focus$", "_featurePluginUpdate$", "_featurePluginAdd$", "_featurePluginRemove$", "_featurePluginOrderUpdate$", "_featurePluginGroupUpdate$", "_featurePluginUngroupUpdate$", "dispose", "complete", "drawingManagerData", "_oldDrawingManagerData", "visibleNotification", "next", "refreshTransform", "_getCurrentBySearch", "transforms", "isMultiTransform", "refreshTransformNotification", "getDrawingDataForUnit", "removeDrawingDataForUnit", "keys", "data", "unitId", "subUnitId", "drawingId", "removeNotification", "registerDrawingData", "initializeNotification", "_establishDrawingMap", "addNotification", "getDrawingData", "_getDrawingData", "setDrawingData", "getBatchAddOp", "op", "invertOp", "_addByParam", "reduce", "undo", "redo", "objects", "getBatchRemoveOp", "_removeByParam", "getBatchUpdateOp", "_updateByParam", "updateNotification", "orderNotification", "groupUpdateNotification", "ungroupUpdateNotification", "getGroupDrawingOp", "parent", "_getGroupDrawingOp", "getUngroupDrawingOp", "_getUngroupDrawingOp", "getDrawingsByGroup", "getDrawingByParam", "groupId", "children", "Number", "NEGATIVE_INFINITY", "_hasDrawingOrder", "max", "_getUpdateParamCompareOp", "_getDrawingOrder", "indexOf", "applyJson1", "featurePluginUpdateNotification", "featurePluginOrderUpdateNotification", "featurePluginAddNotification", "featurePluginRemoveNotification", "featurePluginGroupUpdateNotification", "featurePluginUngroupUpdateNotification", "getOldDrawingByParam", "_getOldBySearch", "getDrawingOKey", "split", "focusDrawing", "_focusDrawings", "getFocusDrawings", "getDrawingOrder", "setDrawingOrder", "order", "orderUpdateNotification", "getForwardDrawingsOp", "drawingIds", "getBackwardDrawingOp", "getFrontDrawingsOp", "_getOrderFromSearchParams", "_getDrawingCount", "getBackDrawingsOp", "zIndex", "sort", "getDrawingVisible", "_visible", "getDrawingEditable", "_editable", "setDrawingVisible", "setDrawingEditable", "an", "On", "wn", "Promise", "Image", "src", "onload", "width", "height", "image", "onerror", "kt", "ln", "id", "OPERATION", "handler", "un", "Tt", "cn", "_change$", "Map", "setWaitCount", "_waitCount", "getImageSourceCache", "BASE64", "_imageSourceCache", "addImageSourceCache", "set", "getImage", "resolve", "saveImage", "ERROR_IMAGE_TYPE", "_decreaseWaiting", "ERROR_EXCEED_SIZE", "FileReader", "readAsDataURL", "target", "ERROR_IMAGE", "generateRandomId", "imageId", "imageSourceType", "source", "base64Cache", "status", "SUCCUSS", "dn", "getOwnPropertyDescriptor", "fn", "dt", "hn", "ft", "At", "_config", "_injector", "_configService", "_commandService", "setConfig", "onStarting", "_initCommands", "_initDependencies", "useClass", "override", "disposeWithMe", "registerCommand", "DRAWING_IMAGE_ALLOW_IMAGE_LIST", "DRAWING_IMAGE_ALLOW_SIZE", "DRAWING_IMAGE_COUNT_LIMIT", "DRAWING_IMAGE_HEIGHT_LIMIT", "DRAWING_IMAGE_WIDTH_LIMIT", "DrawingManagerService", "IDrawingManagerService", "ImageIoService", "SetDrawingSelectedOperation", "UnitDrawingService", "UniverDrawingPlugin", "getDrawingShapeKeyByDrawingSearch", "getImageSize"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@univerjs/drawing/lib/es/index.js"], "sourcesContent": ["var Nt = Object.defineProperty;\nvar Rt = (a, e, n) => e in a ? Nt(a, e, { enumerable: !0, configurable: !0, writable: !0, value: n }) : a[e] = n;\nvar F = (a, e, n) => Rt(a, typeof e != \"symbol\" ? e + \"\" : e, n);\nimport { sortRules as xt, sortRulesByDesc as Bt, createIdentifier as Wt, CommandType as Gt, ImageSourceType as at, ImageUploadStatusType as et, Tools as Lt, Inject as qt, Injector as Kt, IConfigService as Ft, ICommandService as Vt, Plugin as Ht, merge as zt, mergeOverrideWithDependencies as Xt, IImageIoService as Jt } from \"@univerjs/core\";\nimport { IImageIoService as In, ImageSourceType as En, ImageUploadStatusType as Dn } from \"@univerjs/core\";\nimport { Subject as be } from \"rxjs\";\nconst _n = 500, vn = 500, mn = 10, Yt = 5 * 1024 * 1024, Zt = [\"image/png\", \"image/jpeg\", \"image/jpg\", \"image/gif\", \"image/bmp\"];\nvar Ve = {}, Ze = {}, tt = {}, vt;\nfunction Qt() {\n  if (vt) return tt;\n  vt = 1, Object.defineProperty(tt, \"__esModule\", { value: !0 });\n  function a(t, r) {\n    if (Array.isArray(r))\n      return !1;\n    for (let u in t)\n      if (!n(t[u], r[u]))\n        return !1;\n    for (let u in r)\n      if (t[u] === void 0)\n        return !1;\n    return !0;\n  }\n  function e(t, r) {\n    if (!Array.isArray(r) || t.length !== r.length)\n      return !1;\n    for (let u = 0; u < t.length; u++)\n      if (!n(t[u], r[u]))\n        return !1;\n    return !0;\n  }\n  function n(t, r) {\n    return t === r ? !0 : t === null || r === null || typeof t != \"object\" || typeof r != \"object\" ? !1 : Array.isArray(t) ? e(t, r) : a(t, r);\n  }\n  return tt.default = n, tt;\n}\nvar nt = {}, mt;\nfunction en() {\n  if (mt) return nt;\n  mt = 1, Object.defineProperty(nt, \"__esModule\", { value: !0 });\n  function a(e) {\n    if (e === null)\n      return null;\n    if (Array.isArray(e))\n      return e.map(a);\n    if (typeof e == \"object\") {\n      const n = {};\n      for (let t in e)\n        n[t] = a(e[t]);\n      return n;\n    } else\n      return e;\n  }\n  return nt.default = a, nt;\n}\nvar lt = {}, Ot;\nfunction Mt() {\n  return Ot || (Ot = 1, function(a) {\n    Object.defineProperty(a, \"__esModule\", { value: !0 }), a.eachChildOf = a.advancer = a.readCursor = a.writeCursor = a.WriteCursor = a.ReadCursor = a.isValidPathItem = void 0;\n    function e(_, s) {\n      if (!_)\n        throw new Error(s);\n    }\n    const n = (_) => _ != null && typeof _ == \"object\" && !Array.isArray(_), t = (_, s) => (\n      // All the numbers, then all the letters. Just as the gods of ascii intended.\n      typeof _ == typeof s ? _ > s : typeof _ == \"string\" && typeof s == \"number\"\n    );\n    function r(_, s) {\n      for (let c in _) {\n        const h = c;\n        s.write(h, _[h]);\n      }\n    }\n    a.isValidPathItem = (_) => typeof _ == \"number\" || typeof _ == \"string\" && _ !== \"__proto__\";\n    class u {\n      constructor(s = null) {\n        this.parents = [], this.indexes = [], this.lcIdx = -1, this.idx = -1, this.container = s;\n      }\n      ascend() {\n        e(this.parents.length === this.indexes.length / 2), this.idx === 0 ? this.parents.length ? (this.lcIdx = this.indexes.pop(), this.container = this.parents.pop(), this.idx = this.indexes.pop()) : (this.lcIdx = 0, this.idx = -1) : (e(this.idx > 0), this.idx--, n(this.container[this.idx]) && this.idx--);\n      }\n      getPath() {\n        const s = [];\n        let c = this.container, h = this.parents.length - 1, U = this.idx;\n        for (; U >= 0; )\n          s.unshift(c[U]), U === 0 ? (U = this.indexes[h * 2], c = this.parents[h--]) : U -= n(c[U - 1]) ? 2 : 1;\n        return s;\n      }\n    }\n    class o extends u {\n      get() {\n        return this.container ? this.container.slice(this.idx + 1) : null;\n      }\n      // Its only valid to call this after descending into a child.\n      getKey() {\n        return e(this.container != null, \"Invalid call to getKey before cursor descended\"), this.container[this.idx];\n      }\n      getComponent() {\n        let s;\n        return this.container && this.container.length > this.idx + 1 && n(s = this.container[this.idx + 1]) ? s : null;\n      }\n      descendFirst() {\n        let s = this.idx + 1;\n        if (!this.container || s >= this.container.length || n(this.container[s]) && s + 1 >= this.container.length)\n          return !1;\n        n(this.container[s]) && s++;\n        const c = this.container[s];\n        return Array.isArray(c) ? (this.indexes.push(this.idx), this.parents.push(this.container), this.indexes.push(s), this.idx = 0, this.container = c) : this.idx = s, !0;\n      }\n      nextSibling() {\n        if (e(this.parents.length === this.indexes.length / 2), this.idx > 0 || this.parents.length === 0)\n          return !1;\n        const s = this.indexes[this.indexes.length - 1] + 1, c = this.parents[this.parents.length - 1];\n        return s >= c.length ? !1 : (e(!isNaN(s)), this.indexes[this.indexes.length - 1] = s, this.container = c[s], !0);\n      }\n      _init(s, c, h, U) {\n        this.container = s, this.idx = c, this.parents = h.slice(), this.indexes = U.slice();\n      }\n      clone() {\n        const s = new o();\n        return s._init(this.container, this.idx, this.parents, this.indexes), s;\n      }\n      *[Symbol.iterator]() {\n        if (this.descendFirst()) {\n          do\n            yield this.getKey();\n          while (this.nextSibling());\n          this.ascend();\n        }\n      }\n      // TODO(cleanup): Consider moving these functions out of cursor, since\n      // they're really just helper methods.\n      // It'd be really nice to do this using generators.\n      traverse(s, c) {\n        const h = this.getComponent();\n        h && c(h, s);\n        for (const U of this)\n          s && s.descend(U), this.traverse(s, c), s && s.ascend();\n      }\n      eachPick(s, c) {\n        this.traverse(s, (h, U) => {\n          h.p != null && c(h.p, U);\n        });\n      }\n      eachDrop(s, c) {\n        this.traverse(s, (h, U) => {\n          h.d != null && c(h.d, U);\n        });\n      }\n    }\n    a.ReadCursor = o;\n    class l extends u {\n      constructor(s = null) {\n        super(s), this.pendingDescent = [], this._op = s;\n      }\n      flushDescent() {\n        e(this.parents.length === this.indexes.length / 2), this.container === null && (this._op = this.container = []);\n        for (let s = 0; s < this.pendingDescent.length; s++) {\n          const c = this.pendingDescent[s];\n          let h = this.idx + 1;\n          if (h < this.container.length && n(this.container[h]) && h++, e(h === this.container.length || !n(this.container[h])), h === this.container.length)\n            this.container.push(c), this.idx = h;\n          else if (this.container[h] === c)\n            this.idx = h;\n          else {\n            if (!Array.isArray(this.container[h])) {\n              const U = this.container.splice(h, this.container.length - h);\n              this.container.push(U), this.lcIdx > -1 && (this.lcIdx = h);\n            }\n            for (this.indexes.push(this.idx), this.parents.push(this.container), this.lcIdx !== -1 && (e(t(c, this.container[this.lcIdx][0])), h = this.lcIdx + 1, this.lcIdx = -1); h < this.container.length && t(c, this.container[h][0]); )\n              h++;\n            if (this.indexes.push(h), this.idx = 0, h < this.container.length && this.container[h][0] === c)\n              this.container = this.container[h];\n            else {\n              const U = [c];\n              this.container.splice(h, 0, U), this.container = U;\n            }\n          }\n        }\n        this.pendingDescent.length = 0;\n      }\n      reset() {\n        this.lcIdx = -1;\n      }\n      // Creates and returns a component, creating one if need be. You should\n      // probably write to it immediately - ops are not valid with empty\n      // components.\n      getComponent() {\n        this.flushDescent();\n        const s = this.idx + 1;\n        if (s < this.container.length && n(this.container[s]))\n          return this.container[s];\n        {\n          const c = {};\n          return this.container.splice(s, 0, c), c;\n        }\n      }\n      write(s, c) {\n        const h = this.getComponent();\n        e(h[s] == null || h[s] === c, \"Internal consistency error: Overwritten component. File a bug\"), h[s] = c;\n      }\n      get() {\n        return this._op;\n      }\n      descend(s) {\n        if (!a.isValidPathItem(s))\n          throw Error(\"Invalid JSON key\");\n        this.pendingDescent.push(s);\n      }\n      descendPath(s) {\n        return this.pendingDescent.push(...s), this;\n      }\n      ascend() {\n        this.pendingDescent.length ? this.pendingDescent.pop() : super.ascend();\n      }\n      mergeTree(s, c = r) {\n        if (s === null)\n          return;\n        if (e(Array.isArray(s)), s === this._op)\n          throw Error(\"Cannot merge into my own tree\");\n        const h = this.lcIdx, U = this.parents.length;\n        let Y = 0;\n        for (let pe = 0; pe < s.length; pe++) {\n          const Z = s[pe];\n          typeof Z == \"string\" || typeof Z == \"number\" ? (Y++, this.descend(Z)) : Array.isArray(Z) ? this.mergeTree(Z, c) : typeof Z == \"object\" && c(Z, this);\n        }\n        for (; Y--; )\n          this.ascend();\n        this.lcIdx = this.parents.length === U ? h : -1;\n      }\n      at(s, c) {\n        this.descendPath(s), c(this);\n        for (let h = 0; h < s.length; h++)\n          this.ascend();\n        return this;\n      }\n      // This is used by helpers, so the strict ordering guarantees are\n      // relaxed.\n      writeAtPath(s, c, h) {\n        return this.at(s, () => this.write(c, h)), this.reset(), this;\n      }\n      writeMove(s, c, h = 0) {\n        return this.writeAtPath(s, \"p\", h).writeAtPath(c, \"d\", h);\n      }\n      getPath() {\n        const s = super.getPath();\n        return s.push(...this.pendingDescent), s;\n      }\n    }\n    a.WriteCursor = l, a.writeCursor = () => new l(), a.readCursor = (_) => new o(_);\n    function E(_, s, c) {\n      let h, U;\n      U = h = _ ? _.descendFirst() : !1;\n      function Y(pe) {\n        let Z;\n        for (; U; ) {\n          const Ce = Z = _.getKey();\n          if (pe != null) {\n            let Le = !1;\n            if (s && typeof Ce == \"number\" && (Z = s(Ce, _.getComponent()), Z < 0 && (Z = ~Z, Le = !0)), t(Z, pe))\n              return null;\n            if (Z === pe && !Le)\n              return _;\n          }\n          c && typeof Z == \"number\" && c(Z, _.getComponent()), U = _.nextSibling();\n        }\n        return null;\n      }\n      return Y.end = () => {\n        h && _.ascend();\n      }, Y;\n    }\n    a.advancer = E;\n    function x(_, s, c) {\n      let h, U, Y, pe;\n      for (h = U = _ && _.descendFirst(), Y = pe = s && s.descendFirst(); h || Y; ) {\n        let Z = h ? _.getKey() : null, Ce = Y ? s.getKey() : null;\n        Z !== null && Ce !== null && (t(Ce, Z) ? Ce = null : Z !== Ce && (Z = null)), c(Z == null ? Ce : Z, Z != null ? _ : null, Ce != null ? s : null), Z != null && h && (h = _.nextSibling()), Ce != null && Y && (Y = s.nextSibling());\n      }\n      U && _.ascend(), pe && s.ascend();\n    }\n    a.eachChildOf = x;\n  }(lt)), lt;\n}\nvar ut = {}, wt;\nfunction St() {\n  return wt || (wt = 1, function(a) {\n    Object.defineProperty(a, \"__esModule\", { value: !0 }), a.ConflictType = void 0, function(e) {\n      e[e.RM_UNEXPECTED_CONTENT = 1] = \"RM_UNEXPECTED_CONTENT\", e[e.DROP_COLLISION = 2] = \"DROP_COLLISION\", e[e.BLACKHOLE = 3] = \"BLACKHOLE\";\n    }(a.ConflictType || (a.ConflictType = {}));\n  }(ut)), ut;\n}\nvar Ue = {}, He = {}, bt;\nfunction pt() {\n  return bt || (bt = 1, Object.defineProperty(He, \"__esModule\", { value: !0 }), He.uniToStrPos = He.strPosToUni = void 0, He.strPosToUni = (a, e = a.length) => {\n    let n = 0, t = 0;\n    for (; t < e; t++) {\n      const r = a.charCodeAt(t);\n      r >= 55296 && r <= 57343 && (n++, t++);\n    }\n    if (t !== e)\n      throw Error(\"Invalid offset - splits unicode bytes\");\n    return t - n;\n  }, He.uniToStrPos = (a, e) => {\n    let n = 0;\n    for (; e > 0; e--) {\n      const t = a.charCodeAt(n);\n      n += t >= 55296 && t <= 57343 ? 2 : 1;\n    }\n    return n;\n  }), He;\n}\nvar ct = {}, Ct;\nfunction ht() {\n  return Ct || (Ct = 1, function(a) {\n    Object.defineProperty(a, \"__esModule\", { value: !0 }), a.uniSlice = a.dlen = a.eachOp = void 0;\n    const e = pt(), n = (f) => {\n      if (!Array.isArray(f))\n        throw Error(\"Op must be an array of components\");\n      let y = null;\n      for (let b = 0; b < f.length; b++) {\n        const L = f[b];\n        switch (typeof L) {\n          case \"object\":\n            if (typeof L.d != \"number\" && typeof L.d != \"string\")\n              throw Error(\"Delete must be number or string\");\n            if (a.dlen(L.d) <= 0)\n              throw Error(\"Deletes must not be empty\");\n            break;\n          case \"string\":\n            if (!(L.length > 0))\n              throw Error(\"Inserts cannot be empty\");\n            break;\n          case \"number\":\n            if (!(L > 0))\n              throw Error(\"Skip components must be >0\");\n            if (typeof y == \"number\")\n              throw Error(\"Adjacent skip components should be combined\");\n            break;\n        }\n        y = L;\n      }\n      if (typeof y == \"number\")\n        throw Error(\"Op has a trailing skip\");\n    };\n    function t(f, y) {\n      let b = 0, L = 0;\n      for (let z = 0; z < f.length; z++) {\n        const q = f[z];\n        switch (y(q, b, L), typeof q) {\n          case \"object\":\n            b += a.dlen(q.d);\n            break;\n          case \"string\":\n            L += e.strPosToUni(q);\n            break;\n          case \"number\":\n            b += q, L += q;\n            break;\n        }\n      }\n    }\n    a.eachOp = t;\n    function r(f, y) {\n      const b = [], L = l(b);\n      return t(f, (z, q, Ee) => {\n        L(y(z, q, Ee));\n      }), s(b);\n    }\n    const u = (f) => f, o = (f) => r(f, u);\n    a.dlen = (f) => typeof f == \"number\" ? f : e.strPosToUni(f);\n    const l = (f) => (y) => {\n      if (!(!y || y.d === 0 || y.d === \"\")) if (f.length === 0)\n        f.push(y);\n      else if (typeof y == typeof f[f.length - 1])\n        if (typeof y == \"object\") {\n          const b = f[f.length - 1];\n          b.d = typeof b.d == \"string\" && typeof y.d == \"string\" ? b.d + y.d : a.dlen(b.d) + a.dlen(y.d);\n        } else\n          f[f.length - 1] += y;\n      else\n        f.push(y);\n    }, E = (f) => typeof f == \"number\" ? f : typeof f == \"string\" ? e.strPosToUni(f) : typeof f.d == \"number\" ? f.d : e.strPosToUni(f.d);\n    a.uniSlice = (f, y, b) => {\n      const L = e.uniToStrPos(f, y), z = b == null ? 1 / 0 : e.uniToStrPos(f, b);\n      return f.slice(L, z);\n    };\n    const x = (f, y, b) => typeof f == \"number\" ? b == null ? f - y : Math.min(f, b) - y : a.uniSlice(f, y, b), _ = (f) => {\n      let y = 0, b = 0;\n      return { take: (q, Ee) => {\n        if (y === f.length)\n          return q === -1 ? null : q;\n        const ce = f[y];\n        let ne;\n        if (typeof ce == \"number\")\n          return q === -1 || ce - b <= q ? (ne = ce - b, ++y, b = 0, ne) : (b += q, q);\n        if (typeof ce == \"string\") {\n          if (q === -1 || Ee === \"i\" || e.strPosToUni(ce.slice(b)) <= q)\n            return ne = ce.slice(b), ++y, b = 0, ne;\n          {\n            const le = b + e.uniToStrPos(ce.slice(b), q);\n            return ne = ce.slice(b, le), b = le, ne;\n          }\n        } else {\n          if (q === -1 || Ee === \"d\" || a.dlen(ce.d) - b <= q)\n            return ne = { d: x(ce.d, b) }, ++y, b = 0, ne;\n          {\n            let le = x(ce.d, b, b + q);\n            return b += q, { d: le };\n          }\n        }\n      }, peek: () => f[y] };\n    }, s = (f) => (f.length > 0 && typeof f[f.length - 1] == \"number\" && f.pop(), f);\n    function c(f, y, b) {\n      if (b !== \"left\" && b !== \"right\")\n        throw Error(\"side (\" + b + \") must be 'left' or 'right'\");\n      n(f), n(y);\n      const L = [], z = l(L), { take: q, peek: Ee } = _(f);\n      for (let ne = 0; ne < y.length; ne++) {\n        const le = y[ne];\n        let ge, De;\n        switch (typeof le) {\n          case \"number\":\n            for (ge = le; ge > 0; )\n              De = q(ge, \"i\"), z(De), typeof De != \"string\" && (ge -= E(De));\n            break;\n          case \"string\":\n            b === \"left\" && typeof Ee() == \"string\" && z(q(-1)), z(e.strPosToUni(le));\n            break;\n          case \"object\":\n            for (ge = a.dlen(le.d); ge > 0; )\n              switch (De = q(ge, \"i\"), typeof De) {\n                case \"number\":\n                  ge -= De;\n                  break;\n                case \"string\":\n                  z(De);\n                  break;\n                case \"object\":\n                  ge -= a.dlen(De.d);\n              }\n            break;\n        }\n      }\n      let ce;\n      for (; ce = q(-1); )\n        z(ce);\n      return s(L);\n    }\n    function h(f, y) {\n      n(f), n(y);\n      const b = [], L = l(b), { take: z } = _(f);\n      for (let Ee = 0; Ee < y.length; Ee++) {\n        const ce = y[Ee];\n        let ne, le;\n        switch (typeof ce) {\n          case \"number\":\n            for (ne = ce; ne > 0; )\n              le = z(ne, \"d\"), L(le), typeof le != \"object\" && (ne -= E(le));\n            break;\n          case \"string\":\n            L(ce);\n            break;\n          case \"object\":\n            ne = a.dlen(ce.d);\n            let ge = 0;\n            for (; ge < ne; )\n              switch (le = z(ne - ge, \"d\"), typeof le) {\n                case \"number\":\n                  L({ d: x(ce.d, ge, ge + le) }), ge += le;\n                  break;\n                case \"string\":\n                  ge += e.strPosToUni(le);\n                  break;\n                case \"object\":\n                  L(le);\n              }\n            break;\n        }\n      }\n      let q;\n      for (; q = z(-1); )\n        L(q);\n      return s(b);\n    }\n    const U = (f, y) => {\n      let b = 0;\n      for (let L = 0; L < y.length && f > b; L++) {\n        const z = y[L];\n        switch (typeof z) {\n          case \"number\": {\n            b += z;\n            break;\n          }\n          case \"string\":\n            const q = e.strPosToUni(z);\n            b += q, f += q;\n            break;\n          case \"object\":\n            f -= Math.min(a.dlen(z.d), f - b);\n            break;\n        }\n      }\n      return f;\n    }, Y = (f, y) => typeof f == \"number\" ? U(f, y) : f.map((b) => U(b, y));\n    function pe(f, y, b) {\n      return r(f, (L, z) => typeof L == \"object\" && typeof L.d == \"number\" ? { d: b.slice(y, z, z + L.d) } : L);\n    }\n    function Z(f) {\n      return r(f, (y) => {\n        switch (typeof y) {\n          case \"object\":\n            if (typeof y.d == \"number\")\n              throw Error(\"Cannot invert text op: Deleted characters missing from operation. makeInvertible must be called first.\");\n            return y.d;\n          // delete -> insert\n          case \"string\":\n            return { d: y };\n          // Insert -> delete\n          case \"number\":\n            return y;\n        }\n      });\n    }\n    function Ce(f) {\n      return r(f, (y) => typeof y == \"object\" && typeof y.d == \"string\" ? { d: e.strPosToUni(y.d) } : y);\n    }\n    function Le(f) {\n      let y = !0;\n      return t(f, (b) => {\n        typeof b == \"object\" && typeof b.d == \"number\" && (y = !1);\n      }), y;\n    }\n    function ve(f) {\n      return {\n        name: \"text-unicode\",\n        uri: \"http://sharejs.org/types/text-unicode\",\n        trim: s,\n        normalize: o,\n        checkOp: n,\n        /** Create a new text snapshot.\n         *\n         * @param {string} initial - initial snapshot data. Optional. Defaults to ''.\n         * @returns {Snap} Initial document snapshot object\n         */\n        create(y = \"\") {\n          if (typeof y != \"string\")\n            throw Error(\"Initial data must be a string\");\n          return f.create(y);\n        },\n        /** Apply an operation to a document snapshot\n         */\n        apply(y, b) {\n          n(b);\n          const L = f.builder(y);\n          for (let z = 0; z < b.length; z++) {\n            const q = b[z];\n            switch (typeof q) {\n              case \"number\":\n                L.skip(q);\n                break;\n              case \"string\":\n                L.append(q);\n                break;\n              case \"object\":\n                L.del(a.dlen(q.d));\n                break;\n            }\n          }\n          return L.build();\n        },\n        transform: c,\n        compose: h,\n        transformPosition: U,\n        transformSelection: Y,\n        isInvertible: Le,\n        makeInvertible(y, b) {\n          return pe(y, b, f);\n        },\n        stripInvertible: Ce,\n        invert: Z,\n        invertWithDoc(y, b) {\n          return Z(pe(y, b, f));\n        },\n        isNoop: (y) => y.length === 0\n      };\n    }\n    a.default = ve;\n  }(ct)), ct;\n}\nvar rt = {}, It;\nfunction tn() {\n  if (It) return rt;\n  It = 1, Object.defineProperty(rt, \"__esModule\", { value: !0 });\n  const a = ht(), e = pt();\n  function n(t, r) {\n    return {\n      // Returns the text content of the document\n      get: t,\n      // Returns the number of characters in the string\n      getLength() {\n        return t().length;\n      },\n      // Insert the specified text at the given position in the document\n      insert(u, o, l) {\n        const E = e.strPosToUni(t(), u);\n        return r([E, o], l);\n      },\n      remove(u, o, l) {\n        const E = e.strPosToUni(t(), u);\n        return r([E, { d: o }], l);\n      },\n      // When you use this API, you should implement these two methods\n      // in your editing context.\n      //onInsert: function(pos, text) {},\n      //onRemove: function(pos, removedLength) {},\n      _onOp(u) {\n        a.eachOp(u, (o, l, E) => {\n          switch (typeof o) {\n            case \"string\":\n              this.onInsert && this.onInsert(E, o);\n              break;\n            case \"object\":\n              const x = a.dlen(o.d);\n              this.onRemove && this.onRemove(E, x);\n          }\n        });\n      },\n      onInsert: null,\n      onRemove: null\n    };\n  }\n  return rt.default = n, n.provides = { text: !0 }, rt;\n}\nvar Et;\nfunction nn() {\n  return Et || (Et = 1, function(a) {\n    var e = Ue && Ue.__createBinding || (Object.create ? function(c, h, U, Y) {\n      Y === void 0 && (Y = U), Object.defineProperty(c, Y, { enumerable: !0, get: function() {\n        return h[U];\n      } });\n    } : function(c, h, U, Y) {\n      Y === void 0 && (Y = U), c[Y] = h[U];\n    }), n = Ue && Ue.__setModuleDefault || (Object.create ? function(c, h) {\n      Object.defineProperty(c, \"default\", { enumerable: !0, value: h });\n    } : function(c, h) {\n      c.default = h;\n    }), t = Ue && Ue.__importStar || function(c) {\n      if (c && c.__esModule) return c;\n      var h = {};\n      if (c != null) for (var U in c) Object.hasOwnProperty.call(c, U) && e(h, c, U);\n      return n(h, c), h;\n    }, r = Ue && Ue.__importDefault || function(c) {\n      return c && c.__esModule ? c : { default: c };\n    };\n    Object.defineProperty(a, \"__esModule\", { value: !0 }), a.type = a.remove = a.insert = void 0;\n    const u = pt(), o = t(ht()), l = r(tn()), E = {\n      create(c) {\n        return c;\n      },\n      toString(c) {\n        return c;\n      },\n      builder(c) {\n        if (typeof c != \"string\")\n          throw Error(\"Invalid document snapshot: \" + c);\n        const h = [];\n        return {\n          skip(U) {\n            let Y = u.uniToStrPos(c, U);\n            if (Y > c.length)\n              throw Error(\"The op is too long for this document\");\n            h.push(c.slice(0, Y)), c = c.slice(Y);\n          },\n          append(U) {\n            h.push(U);\n          },\n          del(U) {\n            c = c.slice(u.uniToStrPos(c, U));\n          },\n          build() {\n            return h.join(\"\") + c;\n          }\n        };\n      },\n      slice: o.uniSlice\n    }, x = o.default(E), _ = Object.assign(Object.assign({}, x), { api: l.default });\n    a.type = _, a.insert = (c, h) => h.length === 0 ? [] : c === 0 ? [h] : [c, h], a.remove = (c, h) => o.dlen(h) === 0 ? [] : c === 0 ? [{ d: h }] : [c, { d: h }];\n    var s = ht();\n    Object.defineProperty(a, \"makeType\", { enumerable: !0, get: function() {\n      return s.default;\n    } });\n  }(Ue)), Ue;\n}\nvar Dt;\nfunction rn() {\n  return Dt || (Dt = 1, function(a) {\n    var e = Ze && Ze.__importDefault || function(i) {\n      return i && i.__esModule ? i : {\n        default: i\n      };\n    };\n    Object.defineProperty(a, \"__esModule\", {\n      value: !0\n    }), a.editOp = a.replaceOp = a.insertOp = a.moveOp = a.removeOp = a.type = void 0;\n    const n = e(Qt()), t = e(en()), r = Mt(), u = St();\n    function o(i, d) {\n      if (!i) throw new Error(d);\n    }\n    a.type = {\n      name: \"json1\",\n      uri: \"http://sharejs.org/types/JSONv1\",\n      readCursor: r.readCursor,\n      writeCursor: r.writeCursor,\n      create: (i) => i,\n      isNoop: (i) => i == null,\n      setDebug(i) {\n      },\n      registerSubtype: Z,\n      checkValidOp: z,\n      normalize: q,\n      apply: Ee,\n      transformPosition: ce,\n      compose: ne,\n      tryTransform: st,\n      transform: jt,\n      makeInvertible: De,\n      invert: le,\n      invertWithDoc: Ut,\n      RM_UNEXPECTED_CONTENT: u.ConflictType.RM_UNEXPECTED_CONTENT,\n      DROP_COLLISION: u.ConflictType.DROP_COLLISION,\n      BLACKHOLE: u.ConflictType.BLACKHOLE,\n      transformNoConflict: (i, d, m) => yt(() => !0, i, d, m),\n      typeAllowingConflictsPred: (i) => Object.assign(Object.assign({}, a.type), {\n        transform: (d, m, D) => yt(i, d, m, D)\n      })\n    };\n    const l = (i) => i ? i.getComponent() : null;\n    function E(i) {\n      return i && typeof i == \"object\" && !Array.isArray(i);\n    }\n    const x = (i) => Array.isArray(i) ? i.slice() : i !== null && typeof i == \"object\" ? Object.assign({}, i) : i, _ = (i) => i && (i.p != null || i.r !== void 0), s = (i) => i && (i.d != null || i.i !== void 0);\n    function c(i, d) {\n      return o(i != null), typeof d == \"number\" ? (o(Array.isArray(i), \"Invalid key - child is not an array\"), (i = i.slice()).splice(d, 1)) : (o(E(i), \"Invalid key - child is not an object\"), delete (i = Object.assign({}, i))[d]), i;\n    }\n    function h(i, d, m) {\n      return typeof d == \"number\" ? (o(i != null, \"Container is missing for key\"), o(Array.isArray(i), \"Cannot use numerical key for object container\"), o(i.length >= d, \"Cannot insert into out of bounds index\"), i.splice(d, 0, m)) : (o(E(i), \"Cannot insert into missing item\"), o(i[d] === void 0, \"Trying to overwrite value at key. Your op needs to remove it first\"), i[d] = m), m;\n    }\n    a.removeOp = (i, d = !0) => r.writeCursor().writeAtPath(i, \"r\", d).get(), a.moveOp = (i, d) => r.writeCursor().writeMove(i, d).get(), a.insertOp = (i, d) => r.writeCursor().writeAtPath(i, \"i\", d).get(), a.replaceOp = (i, d, m) => r.writeCursor().at(i, (D) => {\n      D.write(\"r\", d), D.write(\"i\", m);\n    }).get(), a.editOp = (i, d, m, D = !1) => r.writeCursor().at(i, (O) => y(O, d, m, D)).get();\n    const U = (i, d) => i != null && (typeof d == \"number\" ? Array.isArray(i) : typeof i == \"object\"), Y = (i, d) => U(i, d) ? i[d] : void 0, pe = {};\n    function Z(i) {\n      let d = i.type ? i.type : i;\n      d.name && (pe[d.name] = d), d.uri && (pe[d.uri] = d);\n    }\n    const Ce = (i) => {\n      const d = pe[i];\n      if (d) return d;\n      throw Error(\"Missing type: \" + i);\n    };\n    Z(nn());\n    const Le = (i, d) => i + d;\n    Z({\n      name: \"number\",\n      apply: Le,\n      compose: Le,\n      invert: (i) => -i,\n      transform: (i) => i\n    });\n    const ve = (i) => i == null ? null : i.et ? Ce(i.et) : i.es ? pe[\"text-unicode\"] : i.ena != null ? pe.number : null, f = (i) => i.es ? i.es : i.ena != null ? i.ena : i.e, y = (i, d, m, D = !1) => {\n      const [O, I] = typeof d == \"string\" ? [Ce(d), d] : [d, d.name];\n      !D && O.isNoop && O.isNoop(m) || (I === \"number\" ? i.write(\"ena\", m) : I === \"text-unicode\" ? i.write(\"es\", m) : (i.write(\"et\", I), i.write(\"e\", m)));\n    };\n    function b(i) {\n      o(typeof i == \"number\"), o(i >= 0), o(i === (0 | i));\n    }\n    function L(i) {\n      typeof i == \"number\" ? b(i) : o(typeof i == \"string\");\n    }\n    function z(i) {\n      if (i === null) return;\n      const d = /* @__PURE__ */ new Set(), m = /* @__PURE__ */ new Set(), D = (I) => {\n        let R = !0, $ = !1;\n        for (let p in I) {\n          const v = I[p];\n          if (R = !1, o(p === \"p\" || p === \"r\" || p === \"d\" || p === \"i\" || p === \"e\" || p === \"es\" || p === \"ena\" || p === \"et\", \"Invalid component item '\" + p + \"'\"), p === \"p\") b(v), o(!d.has(v)), d.add(v), o(I.r === void 0);\n          else if (p === \"d\") b(v), o(!m.has(v)), m.add(v), o(I.i === void 0);\n          else if (p === \"e\" || p === \"es\" || p === \"ena\") {\n            o(!$), $ = !0;\n            const w = ve(I);\n            o(w, \"Missing type in edit\"), w.checkValidOp && w.checkValidOp(f(I));\n          }\n        }\n        o(!R);\n      }, O = (I, R, $) => {\n        if (!Array.isArray(I)) throw Error(\"Op must be null or a list\");\n        if (I.length === 0) throw Error(\"Empty descent\");\n        R || L(I[0]);\n        let p = 1, v = 0, w = 0;\n        for (let C = 0; C < I.length; C++) {\n          const N = I[C];\n          if (o(N != null), Array.isArray(N)) {\n            const B = O(N, !1);\n            if (v) {\n              const g = typeof w, S = typeof B;\n              g === S ? o(w < B, \"descent keys are not in order\") : o(g === \"number\" && S === \"string\");\n            }\n            w = B, v++, p = 3;\n          } else typeof N == \"object\" ? (o(p === 1, `Prev not scalar - instead ${p}`), D(N), p = 2) : (o(p !== 3), L(N), o(r.isValidPathItem(N), \"Invalid path key\"), p = 1);\n        }\n        return o(v !== 1, \"Operation makes multiple descents. Remove some []\"), o(p === 2 || p === 3), I[0];\n      };\n      O(i, !0), o(d.size === m.size, \"Mismatched picks and drops in op\");\n      for (let I = 0; I < d.size; I++) o(d.has(I)), o(m.has(I));\n    }\n    function q(i) {\n      let d = 0, m = [];\n      const D = r.writeCursor();\n      return D.mergeTree(i, (O, I) => {\n        const R = ve(O);\n        if (R) {\n          const p = f(O);\n          y(I, R, R.normalize ? R.normalize(p) : p);\n        }\n        for (const p of [\"r\", \"p\", \"i\", \"d\"]) if (O[p] !== void 0) {\n          const v = p === \"p\" || p === \"d\" ? ($ = O[p], m[$] == null && (m[$] = d++), m[$]) : O[p];\n          I.write(p, v);\n        }\n        var $;\n      }), D.get();\n    }\n    function Ee(i, d) {\n      if (z(d), d === null) return i;\n      const m = [];\n      return function D(O, I) {\n        let R = O, $ = 0, p = {\n          root: O\n        }, v = 0, w = p, C = \"root\";\n        function N() {\n          for (; v < $; v++) {\n            let B = I[v];\n            typeof B != \"object\" && (o(U(w, C)), w = w[C] = x(w[C]), C = B);\n          }\n        }\n        for (; $ < I.length; $++) {\n          const B = I[$];\n          if (Array.isArray(B)) {\n            const g = D(R, B);\n            g !== R && g !== void 0 && (N(), R = w[C] = g);\n          } else if (typeof B == \"object\") {\n            B.d != null ? (N(), R = h(w, C, m[B.d])) : B.i !== void 0 && (N(), R = h(w, C, B.i));\n            const g = ve(B);\n            if (g) N(), R = w[C] = g.apply(R, f(B));\n            else if (B.e !== void 0) throw Error(\"Subtype \" + B.et + \" undefined\");\n          } else R = Y(R, B);\n        }\n        return p.root;\n      }(i = function D(O, I) {\n        const R = [];\n        let $ = 0;\n        for (; $ < I.length; $++) {\n          const C = I[$];\n          if (Array.isArray(C)) break;\n          typeof C != \"object\" && (R.push(O), O = Y(O, C));\n        }\n        for (let C = I.length - 1; C >= $; C--) O = D(O, I[C]);\n        for (--$; $ >= 0; $--) {\n          const C = I[$];\n          if (typeof C != \"object\") {\n            const N = R.pop();\n            O = O === Y(N, C) ? N : O === void 0 ? c(N, C) : (v = C, w = O, (p = x(p = N))[v] = w, p);\n          } else _(C) && (o(O !== void 0, \"Cannot pick up or remove undefined\"), C.p != null && (m[C.p] = O), O = void 0);\n        }\n        var p, v, w;\n        return O;\n      }(i, d), d);\n    }\n    function ce(i, d) {\n      i = i.slice(), z(d);\n      const m = r.readCursor(d);\n      let D, O, I = !1;\n      const R = [];\n      for (let p = 0; ; p++) {\n        const v = i[p], w = m.getComponent();\n        if (w && (w.r !== void 0 ? I = !0 : w.p != null && (I = !1, D = w.p, O = p)), p >= i.length) break;\n        let C = 0;\n        const N = r.advancer(m, void 0, (g, S) => {\n          _(S) && C++;\n        });\n        R.unshift(N);\n        const B = N(v);\n        if (typeof v == \"number\" && (i[p] -= C), !B) break;\n      }\n      if (R.forEach((p) => p.end()), I) return null;\n      const $ = () => {\n        let p = 0;\n        if (D != null) {\n          const v = m.getPath();\n          p = v.length, i = v.concat(i.slice(O));\n        }\n        for (; p < i.length; p++) {\n          const v = i[p], w = l(m), C = ve(w);\n          if (C) {\n            const g = f(w);\n            C.transformPosition && (i[p] = C.transformPosition(i[p], g));\n            break;\n          }\n          let N = 0;\n          const B = r.advancer(m, (g, S) => s(S) ? ~(g - N) : g - N, (g, S) => {\n            s(S) && N++;\n          })(v);\n          if (typeof v == \"number\" && (i[p] += N), !B) break;\n        }\n      };\n      return D != null ? m.eachDrop(null, (p) => {\n        p === D && $();\n      }) : $(), i;\n    }\n    function ne(i, d) {\n      if (z(i), z(d), i == null) return d;\n      if (d == null) return i;\n      let m = 0;\n      const D = r.readCursor(i), O = r.readCursor(d), I = r.writeCursor(), R = [], $ = [], p = [], v = [], w = [], C = [], N = /* @__PURE__ */ new Set();\n      D.traverse(null, (g) => {\n        g.p != null && (p[g.p] = D.clone());\n      }), O.traverse(null, (g) => {\n        g.d != null && (v[g.d] = O.clone());\n      });\n      const B = r.writeCursor();\n      return function g(S, re, te, K, se, Ne, Oe, ye) {\n        o(re || te);\n        const oe = l(re), Pe = l(te), Ae = !!Pe && Pe.r !== void 0, qe = !!oe && oe.i !== void 0, Te = oe ? oe.d : null, Ie = Pe ? Pe.p : null, Re = (Ne || Ae) && Ie == null;\n        if (Ie != null) K = v[Ie], Oe = $[Ie] = new r.WriteCursor();\n        else if (Pe && Pe.r !== void 0) K = null;\n        else {\n          const T = l(K);\n          T && T.d != null && (K = null);\n        }\n        const Q = l(K);\n        if (Te != null) if (S = p[Te], ye = R[Te] = new r.WriteCursor(), Re) Ne && !Ae && ye.write(\"r\", !0);\n        else {\n          const T = w[Te] = m++;\n          Oe.write(\"d\", T);\n        }\n        else if (oe && oe.i !== void 0) S = null;\n        else {\n          const T = l(S);\n          T && T.p != null && (S = null);\n        }\n        let A;\n        qe ? (o(se === void 0), A = oe.i) : A = se;\n        const W = (Ie == null ? !qe || Ne || Ae : A === void 0) ? null : Oe.getComponent();\n        if (Ie != null) {\n          if (!(se !== void 0 || qe)) {\n            const T = Te != null ? w[Te] : m++;\n            C[Ie] = T, ye.write(\"p\", T);\n          }\n        } else Ae && (qe || se !== void 0 || (Pe.r, ye.write(\"r\", Pe.r)));\n        const M = Re ? null : ve(oe), P = ve(Q);\n        if ((M || P) && (M && M.name, P && P.name), M && P) {\n          o(M === P);\n          const T = f(oe), G = f(Q), he = M.compose(T, G);\n          y(Oe, M, he), N.add(Q);\n        } else M ? y(Oe, M, f(oe)) : P && (y(Oe, P, f(Q)), N.add(Q));\n        const k = typeof A == \"object\" && A != null;\n        let J = !1, X = 0, ee = 0, fe = 0, de = 0, ae = 0;\n        const me = r.advancer(K, (T, G) => s(G) ? de - T - 1 : T - de, (T, G) => {\n          s(G) && de++;\n        }), H = r.advancer(S, (T, G) => _(G) ? X - T - 1 : T - X, (T, G) => {\n          _(G) && X++;\n        });\n        if (r.eachChildOf(re, te, (T, G, he) => {\n          let we, xe, Ke = T, Me = T, Ye = T;\n          if (typeof T == \"number\") {\n            let _e = T + fe;\n            xe = me(_e), Me = _e + de;\n            let ue = T + ee;\n            we = H(ue), s(l(xe)) && (we = null), Ke = ue + X, Ye = T + ae, o(Ke >= 0, \"p1PickKey is negative\"), o(Me >= 0, \"p2DropKey is negative\");\n            const Se = s(l(G)), Be = _(l(he));\n            (Se || Be && !Re) && ae--, Se && ee--, Be && fe--;\n          } else we = H(T), xe = me(T);\n          ye.descend(Ke), Oe.descend(Me);\n          const Xe = k && !s(l(G)) ? A[Ye] : void 0, je = g(we, G, he, xe, Xe, Re, Oe, ye);\n          var $e, j, ie;\n          k && !Re ? Xe !== je && (J || (A = Array.isArray(A) ? A.slice() : Object.assign({}, A), J = !0), $e = A, ie = je, typeof (j = Ye) == \"number\" ? (o(Array.isArray($e)), o(j < $e.length)) : (o(!Array.isArray($e)), o($e[j] !== void 0)), ie === void 0 ? typeof j == \"number\" ? $e.splice(j, 1) : delete $e[j] : $e[j] = ie) : o(je === void 0), Oe.ascend(), ye.ascend();\n        }), H.end(), me.end(), W != null) W.i = A;\n        else if (!Ne && !Ae && Ie == null) return A;\n      }(D, D.clone(), O, O.clone(), void 0, !1, I, B), I.reset(), I.mergeTree(B.get()), I.reset(), I.get(), R.map((g) => g.get()), $.map((g) => g.get()), D.traverse(I, (g, S) => {\n        const re = g.p;\n        if (re != null) {\n          const te = w[re];\n          te != null && S.write(\"p\", te);\n          const K = R[re];\n          K && K.get(), K && S.mergeTree(K.get());\n        } else g.r !== void 0 && S.write(\"r\", g.r);\n      }), I.reset(), I.get(), O.traverse(I, (g, S) => {\n        const re = g.d;\n        if (re != null) {\n          const K = C[re];\n          K != null && S.write(\"d\", K);\n          const se = $[re];\n          se && S.mergeTree(se.get());\n        } else g.i !== void 0 && S.write(\"i\", g.i);\n        const te = ve(g);\n        te && !N.has(g) && y(S, te, f(g));\n      }), I.get();\n    }\n    function le(i) {\n      if (i == null) return null;\n      const d = new r.ReadCursor(i), m = new r.WriteCursor();\n      let D;\n      const O = [], I = [];\n      return function R($, p, v) {\n        const w = $.getComponent();\n        let C, N = !1;\n        if (w) {\n          w.p != null && (p.write(\"d\", w.p), O[w.p] = $.clone()), w.r !== void 0 && p.write(\"i\", w.r), w.d != null && (p.write(\"p\", w.d), v = void 0), w.i !== void 0 && (v = C = w.i);\n          const g = ve(w);\n          g && (v === void 0 ? (D || (D = /* @__PURE__ */ new Set()), D.add(w)) : (f(w), v = g.apply(v, f(w)), N = !0));\n        }\n        let B = 0;\n        for (const g of $) {\n          p.descend(g);\n          const S = typeof g == \"number\" ? g - B : g, re = Y(v, S);\n          s($.getComponent()) && B++;\n          const te = R($, p, re);\n          if (v !== void 0 && te !== void 0) {\n            if (N || (N = !0, v = x(v)), !U(v, S)) throw Error(\"Cannot modify child - invalid operation\");\n            v[S] = te;\n          }\n          p.ascend();\n        }\n        if (C === void 0) return N ? v : void 0;\n        p.write(\"r\", v);\n      }(d, m, void 0), D && (m.reset(), function R($, p, v) {\n        const w = p.getComponent();\n        if (w) {\n          const g = w.d;\n          if (g != null && ($ = O[g], v = I[g] = r.writeCursor()), D.has(w)) {\n            const S = ve(w);\n            if (!S.invert) throw Error(`Cannot invert subtype ${S.name}`);\n            y(v, S, S.invert(f(w)));\n          }\n        }\n        let C = 0, N = 0;\n        const B = r.advancer($, (g, S) => _(S) ? C - g - 1 : g - C, (g, S) => {\n          _(S) && C++;\n        });\n        for (const g of p) if (typeof g == \"number\") {\n          const S = g - N, re = B(S), te = S + C;\n          v.descend(te), R(re, p, v), s(p.getComponent()) && N++, v.ascend();\n        } else v.descend(g), R(B(g), p, v), v.ascend();\n        B.end();\n      }(d.clone(), d, m), I.length && (m.reset(), d.traverse(m, (R, $) => {\n        const p = R.p;\n        if (p != null) {\n          const v = I[p];\n          v && v.get(), v && $.mergeTree(v.get());\n        }\n      }))), m.get();\n    }\n    const ge = (i, d) => i.some((m) => typeof m == \"object\" && (Array.isArray(m) ? ge(m, d) : d(m)));\n    function De(i, d) {\n      if (i == null || !ge(i, (p) => {\n        var v;\n        return p.r !== void 0 || ((v = ve(p)) === null || v === void 0 ? void 0 : v.makeInvertible) != null;\n      })) return i;\n      const m = new r.ReadCursor(i), D = new r.WriteCursor();\n      let O = !1;\n      const I = [], R = [], $ = (p, v, w) => {\n        const C = p.getComponent();\n        let N = !1;\n        if (C) {\n          C.d != null && v.write(\"d\", C.d), C.i !== void 0 && v.write(\"i\", C.i);\n          const g = C.p;\n          if (g != null && (I[g] = p.clone(), o(w !== void 0, \"Operation picks up at an invalid key\"), R[g] = w, v.write(\"p\", C.p)), C.r !== void 0 && w === void 0) throw Error(\"Invalid doc / op in makeInvertible: removed item missing from doc\");\n          const S = ve(C);\n          S && (S.makeInvertible ? O = !0 : y(v, S, f(C), !0));\n        }\n        let B = 0;\n        for (const g of p) {\n          v.descend(g);\n          const S = typeof g == \"number\" ? g - B : g, re = Y(w, S), te = $(p, v, re);\n          re !== te && (N || (N = !0, w = x(w)), te === void 0 ? (w = c(w, S), typeof g == \"number\" && B++) : w[S] = te), v.ascend();\n        }\n        return C && (C.r !== void 0 ? (v.write(\"r\", t.default(w)), w = void 0) : C.p != null && (w = void 0)), w;\n      };\n      return $(m, D, d), D.get(), O && (D.reset(), function p(v, w, C, N, B) {\n        const g = w.getComponent();\n        if (g) {\n          g.i !== void 0 ? (N = g.i, B = !0) : g.d != null && (N = R[g.d], v = I[g.d], B = !1, g.d);\n          let K = ve(g);\n          if (K && K.makeInvertible) {\n            const se = f(g);\n            y(C, K, K.makeInvertible(se, N), !0);\n          }\n        }\n        let S = 0, re = 0;\n        const te = r.advancer(v, (K, se) => _(se) ? S - K - 1 : K - S, (K, se) => {\n          _(se) && S++;\n        });\n        for (const K of w) if (typeof K == \"number\") {\n          const se = K - re, Ne = te(se), Oe = se + S, ye = Y(N, B ? se : Oe);\n          C.descend(K), p(Ne, w, C, ye, B), s(w.getComponent()) && re++, C.ascend();\n        } else {\n          const se = Y(N, K);\n          C.descend(K), p(te(K), w, C, se, B), C.ascend();\n        }\n        te.end();\n      }(m.clone(), m, D, d, !1)), D.get();\n    }\n    function Ut(i, d) {\n      return le(De(i, d));\n    }\n    const it = (i) => {\n      if (i == null) return null;\n      const d = i.slice();\n      for (let m = 0; m < i.length; m++) {\n        const D = d[m];\n        Array.isArray(D) && (d[m] = it(D));\n      }\n      return d;\n    };\n    function st(i, d, m) {\n      o(m === \"left\" || m === \"right\", \"Direction must be left or right\");\n      const D = m === \"left\" ? 0 : 1;\n      if (d == null) return {\n        ok: !0,\n        result: i\n      };\n      z(i), z(d);\n      let O = null;\n      const I = [], R = [], $ = [], p = [], v = [], w = [], C = [], N = [], B = [], g = [], S = [], re = [], te = [], K = [], se = [];\n      let Ne = 0;\n      const Oe = r.readCursor(i), ye = r.readCursor(d), oe = r.writeCursor();\n      if (function Q(A, W = null, M) {\n        const P = l(W);\n        P && (P.r !== void 0 ? M = W.clone() : P.p != null && (M = null, w[P.p] = A.clone()));\n        const k = A.getComponent();\n        let J;\n        k && (J = k.p) != null && (v[J] = W ? W.clone() : null, $[J] = A.clone(), M && (g[J] = !0, B[J] = M), P && P.p != null && (K[J] = P.p));\n        const X = r.advancer(W);\n        for (const ee of A) Q(A, X(ee), M);\n        X.end();\n      }(ye, Oe, null), function Q(A, W, M, P, k) {\n        const J = M.getComponent();\n        let X, ee = !1;\n        J && ((X = J.d) != null ? (p[X] = M.clone(), P != null && (se[P] == null && (se[P] = []), se[P].push(X)), g[X], A = v[X] || null, W = $[X] || null, g[X] ? (k && (S[X] = !0), k = B[X] || null) : !k || D !== 1 && K[X] != null || O == null && (O = {\n          type: u.ConflictType.RM_UNEXPECTED_CONTENT,\n          op1: a.removeOp(k.getPath()),\n          op2: a.moveOp(W.getPath(), M.getPath())\n        }), ee = !0) : J.i !== void 0 && (A = W = null, ee = !0, k && O == null && (O = {\n          type: u.ConflictType.RM_UNEXPECTED_CONTENT,\n          op1: a.removeOp(k.getPath()),\n          op2: a.insertOp(M.getPath(), J.i)\n        })));\n        const fe = l(A);\n        fe && (fe.r !== void 0 ? k = A.clone() : fe.p != null && (fe.p, P = fe.p, k = null));\n        const de = ve(J);\n        de && k && O == null && (O = {\n          type: u.ConflictType.RM_UNEXPECTED_CONTENT,\n          op1: a.removeOp(k.getPath()),\n          op2: a.editOp(M.getPath(), de, f(J), !0)\n        });\n        let ae = 0, me = 0;\n        const H = r.advancer(W, (G, he) => _(he) ? ae - G - 1 : G - ae, (G, he) => {\n          _(he) && ae++;\n        }), T = r.advancer(A);\n        for (const G of M) if (typeof G == \"number\") {\n          const he = G - me, we = H(he);\n          me += +Q(T(he + ae), we, M, P, k);\n        } else {\n          const he = H(G);\n          Q(T(G), he, M, P, k);\n        }\n        return H.end(), T.end(), ee;\n      }(Oe, ye, ye.clone(), null, null), p.map((Q) => Q && Q.get()), O) return {\n        ok: !1,\n        conflict: O\n      };\n      S.map((Q) => !!Q);\n      const Pe = [];\n      let Ae = null;\n      (function Q(A, W, M, P, k) {\n        let J = !1;\n        const X = l(W);\n        if (_(X)) {\n          const H = X.p;\n          H != null ? (M = p[H], P = re[H] = r.writeCursor(), J = !0, k = null) : (M = null, k = W.clone());\n        } else s(l(M)) && (M = null);\n        const ee = A.getComponent();\n        if (ee) {\n          const H = ee.p;\n          H != null ? (k && (N[H] = k), Pe[H] = k || D === 1 && J ? null : P.getComponent(), I[H] = A.clone(), M && (C[H] = M.clone())) : ee.r !== void 0 && (k || P.write(\"r\", !0), (k || J) && (Ae == null && (Ae = /* @__PURE__ */ new Set()), Ae.add(ee)));\n        }\n        let fe = 0, de = 0;\n        const ae = r.advancer(W, void 0, (H, T) => {\n          _(T) && fe++;\n        }), me = r.advancer(M, (H, T) => s(T) ? ~(H - de) : H - de, (H, T) => {\n          s(T) && de++;\n        });\n        if (A) for (const H of A) if (typeof H == \"string\") {\n          const T = ae(H), G = me(H);\n          P.descend(H), Q(A, T, G, P, k), P.ascend();\n        } else {\n          const T = ae(H), G = H - fe, he = _(l(T)) ? null : me(G), we = G + de;\n          o(we >= 0), P.descend(we), Q(A, T, he, P, k), P.ascend();\n        }\n        ae.end(), me.end();\n      })(Oe, ye, ye.clone(), oe, null), oe.reset();\n      let qe = [];\n      if (function Q(A, W, M, P, k, J) {\n        o(W);\n        const X = W.getComponent();\n        let ee = l(P), fe = !1;\n        const de = (j, ie, _e) => j ? a.moveOp(j.getPath(), ie.getPath()) : a.insertOp(ie.getPath(), _e.i);\n        if (s(X)) {\n          const j = X.d;\n          j != null && (R[j] = W.clone());\n          const ie = j != null ? Pe[j] : null;\n          let _e = !1;\n          if (X.i !== void 0 || j != null && ie) {\n            let ue;\n            ee && (ee.i !== void 0 || (ue = ee.d) != null && !g[ue]) && (_e = ue != null ? j != null && j === K[ue] : n.default(ee.i, X.i), _e || ue != null && D !== 1 && K[ue] != null || O == null && (O = {\n              type: u.ConflictType.DROP_COLLISION,\n              op1: de(j != null ? I[j] : null, W, X),\n              op2: de(ue != null ? $[ue] : null, P, ee)\n            })), _e || (J ? O == null && (O = {\n              type: u.ConflictType.RM_UNEXPECTED_CONTENT,\n              op1: de(j != null ? I[j] : null, W, X),\n              op2: a.removeOp(J.getPath())\n            }) : (j != null ? (qe[Ne] = j, k.write(\"d\", ie.p = Ne++)) : k.write(\"i\", t.default(X.i)), fe = !0));\n          } else if (j != null && !ie) {\n            const ue = N[j];\n            ue && (J = ue.clone());\n          }\n          j != null ? (A = I[j], M = w[j], P = C[j]) : X.i !== void 0 && (A = M = null, _e || (P = null));\n        } else _(l(A)) && (A = M = P = null);\n        const ae = l(A), me = l(M);\n        if (_(me)) {\n          const j = me.p;\n          me.r !== void 0 && (!ae || ae.r === void 0) || g[j] ? (P = null, J = M.clone()) : j != null && (P = p[j], D !== 1 && K[j] != null || ((k = te[j]) || (k = te[j] = r.writeCursor()), k.reset(), J = null));\n        } else !s(X) && s(ee) && (P = null);\n        ee = P != null ? P.getComponent() : null;\n        const H = ve(X);\n        if (H) {\n          const j = f(X);\n          if (J) O == null && (O = {\n            type: u.ConflictType.RM_UNEXPECTED_CONTENT,\n            op1: a.editOp(W.getPath(), H, j, !0),\n            op2: a.removeOp(J.getPath())\n          });\n          else {\n            const ie = ve(ee);\n            let _e;\n            if (ie) {\n              if (H !== ie) throw Error(\"Transforming incompatible types\");\n              const ue = f(ee);\n              _e = H.transform(j, ue, m);\n            } else _e = t.default(j);\n            y(k, H, _e);\n          }\n        }\n        let T = 0, G = 0, he = 0, we = 0, xe = 0, Ke = 0, Me = A != null && A.descendFirst(), Ye = Me;\n        const Xe = r.advancer(M, void 0, (j, ie) => {\n          _(ie) && he++;\n        });\n        let je = P != null && P.descendFirst(), $e = je;\n        for (const j of W) if (typeof j == \"number\") {\n          let ie;\n          const _e = s(W.getComponent()), ue = j - G;\n          {\n            let We;\n            for (; Me && typeof (We = A.getKey()) == \"number\"; ) {\n              We += T;\n              const ke = A.getComponent(), Je = _(ke);\n              if (We > ue || We === ue && (!Je || D === 0 && _e)) break;\n              if (Je) {\n                T--;\n                const Fe = ke.p;\n                K.includes(Fe), ke.d, l(te[ke.d]), _(l(te[ke.d])), (ke.r === void 0 || Ae && Ae.has(ke)) && (Fe == null || !Pe[Fe] || D !== 1 && K.includes(Fe)) || xe--;\n              }\n              Me = A.nextSibling();\n            }\n            ie = Me && We === ue ? A : null;\n          }\n          const Se = ue - T;\n          let Be = Xe(Se);\n          const ot = Se - he;\n          let Qe = null;\n          {\n            let We, ke;\n            for (; je && typeof (We = P.getKey()) == \"number\"; ) {\n              ke = We - we;\n              const Je = P.getComponent(), Fe = s(Je);\n              if (ke > ot) break;\n              if (ke === ot) {\n                if (!Fe) {\n                  Qe = P;\n                  break;\n                }\n                {\n                  if (D === 0 && _e) {\n                    Qe = P;\n                    break;\n                  }\n                  const Ge = Be && _(Be.getComponent());\n                  if (D === 0 && Ge) break;\n                }\n              }\n              if (Fe) {\n                const Ge = Je.d;\n                g[Ge], K[Ge], Je.i === void 0 && (g[Ge] || K[Ge] != null && D !== 1) ? (g[Ge] || K[Ge] != null && D === 0) && (we++, Ke--) : we++;\n              }\n              je = P.nextSibling();\n            }\n          }\n          const _t = ot + we + xe + Ke;\n          o(_t >= 0, \"trying to descend to a negative index\"), k.descend(_t), _e && (ie = Be = Qe = null, G++), Q(ie, W, Be, Qe, k, J) && Ke++, k.ascend();\n        } else {\n          let ie;\n          for (; Me && (ie = A.getKey(), typeof ie != \"string\" || !(ie > j || ie === j)); ) Me = A.nextSibling();\n          const _e = Me && ie === j ? A : null, ue = Xe(j);\n          let Se;\n          for (; je && (Se = P.getKey(), typeof Se != \"string\" || !(Se > j || Se === j)); ) je = P.nextSibling();\n          const Be = je && Se === j ? P : null;\n          k.descend(j), Q(_e, W, ue, Be, k, J), k.ascend();\n        }\n        return Xe.end(), Ye && A.ascend(), $e && P.ascend(), fe;\n      }(Oe, Oe.clone(), ye, ye.clone(), oe, null), O) return {\n        ok: !1,\n        conflict: O\n      };\n      oe.reset();\n      const Te = (Q, A, W) => Q.traverse(A, (M, P) => {\n        M.d != null && W(M.d, Q, P);\n      });\n      (g.length || re.length) && (Te(ye, oe, (Q, A, W) => {\n        g[Q] && !S[Q] && W.write(\"r\", !0), re[Q] && W.mergeTree(re[Q].get());\n      }), oe.reset());\n      const Ie = [], Re = [];\n      if ((te.length || g.length) && !O) {\n        const Q = r.readCursor(it(oe.get()));\n        if (Te(Q, null, (A, W) => {\n          Ie[A] = W.clone();\n        }), te.forEach((A) => {\n          A && Te(r.readCursor(A.get()), null, (W, M) => {\n            Ie[W] = M.clone();\n          });\n        }), function A(W, M, P, k, J, X) {\n          const ee = l(M);\n          if (ee && _(ee)) if (ee.p != null) {\n            const T = ee.p;\n            Ie[T].getPath(), P = Ie[T], k = Re[T] = r.writeCursor();\n          } else ee.r !== void 0 && (P = null);\n          else s(l(P)) && (P = null);\n          const fe = W.getComponent();\n          if (fe) {\n            let T;\n            if ((T = fe.d) != null) {\n              const G = te[T];\n              G && (G.get(), k.mergeTree(G.get()), P = r.readCursor(G.get()));\n            }\n          }\n          let de = 0, ae = 0;\n          const me = r.advancer(M, void 0, (T, G) => {\n            _(G) && de--;\n          }), H = r.advancer(P, (T, G) => s(G) ? -(T - ae) - 1 : T - ae, (T, G) => {\n            s(G) && ae++;\n          });\n          for (const T of W) if (typeof T == \"number\") {\n            const G = me(T), he = T + de, we = H(he), xe = he + ae;\n            k.descend(xe), A(W, G, we, k), k.ascend();\n          } else k.descend(T), A(W, me(T), H(T), k), k.ascend();\n          me.end(), H.end();\n        }(ye, Q, Q.clone(), oe), oe.reset(), O) return {\n          ok: !1,\n          conflict: O\n        };\n        if (oe.get(), Re.length) {\n          const A = Re.map((M) => M ? M.get() : null), W = r.readCursor(it(oe.get()));\n          if (Te(W, oe, (M, P, k) => {\n            const J = A[M];\n            J && (k.mergeTree(J), A[M] = null);\n          }), A.find((M) => M)) {\n            const M = r.writeCursor(), P = r.writeCursor();\n            let k = 0, J = 0;\n            A.forEach((X) => {\n              X != null && Te(r.readCursor(X), null, (ee) => {\n                const fe = qe[ee];\n                M.writeMove(I[fe].getPath(), R[fe].getPath(), k++);\n                const de = se[fe];\n                de && de.forEach((ae) => {\n                  g[ae] || D !== 1 && K[ae] != null || P.writeMove($[ae].getPath(), p[ae].getPath(), J++);\n                });\n              });\n            }), O = {\n              type: u.ConflictType.BLACKHOLE,\n              op1: M.get(),\n              op2: P.get()\n            };\n          }\n        }\n      }\n      return O ? {\n        ok: !1,\n        conflict: O\n      } : {\n        ok: !0,\n        result: oe.get()\n      };\n    }\n    const gt = (i) => {\n      const d = new Error(\"Transform detected write conflict\");\n      throw d.conflict = i, d.type = d.name = \"writeConflict\", d;\n    };\n    function jt(i, d, m) {\n      const D = st(i, d, m);\n      if (D.ok) return D.result;\n      gt(D.conflict);\n    }\n    const ze = (i) => {\n      const d = r.writeCursor();\n      return r.readCursor(i).traverse(d, (m, D) => {\n        (s(m) || ve(m)) && D.write(\"r\", !0);\n      }), d.get();\n    }, $t = (i, d) => {\n      const { type: m, op1: D, op2: O } = i;\n      switch (m) {\n        case u.ConflictType.DROP_COLLISION:\n          return d === \"left\" ? [null, ze(O)] : [ze(D), null];\n        case u.ConflictType.RM_UNEXPECTED_CONTENT:\n          let I = !1;\n          return r.readCursor(D).traverse(null, (R) => {\n            R.r !== void 0 && (I = !0);\n          }), I ? [null, ze(O)] : [ze(D), null];\n        case u.ConflictType.BLACKHOLE:\n          return [ze(D), ze(O)];\n        default:\n          throw Error(\"Unrecognised conflict: \" + m);\n      }\n    };\n    function yt(i, d, m, D) {\n      let O = null;\n      for (; ; ) {\n        const I = st(d, m, D);\n        if (I.ok) return ne(O, I.result);\n        {\n          const { conflict: R } = I;\n          i(R) || gt(R);\n          const [$, p] = $t(R, D);\n          d = ne(q(d), $), m = ne(q(m), p), O = ne(O, p);\n        }\n      }\n    }\n  }(Ze)), Ze;\n}\nvar Pt;\nfunction sn() {\n  return Pt || (Pt = 1, function(a) {\n    var e = Ve && Ve.__createBinding || (Object.create ? function(u, o, l, E) {\n      E === void 0 && (E = l), Object.defineProperty(u, E, { enumerable: !0, get: function() {\n        return o[l];\n      } });\n    } : function(u, o, l, E) {\n      E === void 0 && (E = l), u[E] = o[l];\n    }), n = Ve && Ve.__exportStar || function(u, o) {\n      for (var l in u) l !== \"default\" && !o.hasOwnProperty(l) && e(o, u, l);\n    };\n    Object.defineProperty(a, \"__esModule\", { value: !0 }), n(rn(), a);\n    var t = Mt();\n    Object.defineProperty(a, \"ReadCursor\", { enumerable: !0, get: function() {\n      return t.ReadCursor;\n    } }), Object.defineProperty(a, \"WriteCursor\", { enumerable: !0, get: function() {\n      return t.WriteCursor;\n    } });\n    var r = St();\n    Object.defineProperty(a, \"ConflictType\", { enumerable: !0, get: function() {\n      return r.ConflictType;\n    } });\n  }(Ve)), Ve;\n}\nvar V = sn();\nclass on {\n  constructor() {\n    F(this, \"drawingManagerData\", {});\n    F(this, \"_oldDrawingManagerData\", {});\n    F(this, \"_focusDrawings\", []);\n    F(this, \"_remove$\", new be());\n    F(this, \"remove$\", this._remove$.asObservable());\n    F(this, \"_add$\", new be());\n    F(this, \"add$\", this._add$.asObservable());\n    F(this, \"_update$\", new be());\n    F(this, \"update$\", this._update$.asObservable());\n    F(this, \"_order$\", new be());\n    F(this, \"order$\", this._order$.asObservable());\n    F(this, \"_group$\", new be());\n    F(this, \"group$\", this._group$.asObservable());\n    F(this, \"_ungroup$\", new be());\n    F(this, \"ungroup$\", this._ungroup$.asObservable());\n    F(this, \"_refreshTransform$\", new be());\n    F(this, \"refreshTransform$\", this._refreshTransform$.asObservable());\n    F(this, \"_visible$\", new be());\n    F(this, \"visible$\", this._visible$.asObservable());\n    // private readonly _externalUpdate$ = new Subject<T[]>();\n    // readonly externalUpdate$ = this._externalUpdate$.asObservable();\n    F(this, \"_focus$\", new be());\n    F(this, \"focus$\", this._focus$.asObservable());\n    F(this, \"_featurePluginUpdate$\", new be());\n    F(this, \"featurePluginUpdate$\", this._featurePluginUpdate$.asObservable());\n    F(this, \"_featurePluginAdd$\", new be());\n    F(this, \"featurePluginAdd$\", this._featurePluginAdd$.asObservable());\n    F(this, \"_featurePluginRemove$\", new be());\n    F(this, \"featurePluginRemove$\", this._featurePluginRemove$.asObservable());\n    F(this, \"_featurePluginOrderUpdate$\", new be());\n    F(this, \"featurePluginOrderUpdate$\", this._featurePluginOrderUpdate$.asObservable());\n    F(this, \"_featurePluginGroupUpdate$\", new be());\n    F(this, \"featurePluginGroupUpdate$\", this._featurePluginGroupUpdate$.asObservable());\n    F(this, \"_featurePluginUngroupUpdate$\", new be());\n    F(this, \"featurePluginUngroupUpdate$\", this._featurePluginUngroupUpdate$.asObservable());\n    F(this, \"_visible\", !0);\n    F(this, \"_editable\", !0);\n  }\n  dispose() {\n    this._remove$.complete(), this._add$.complete(), this._update$.complete(), this._order$.complete(), this._focus$.complete(), this._featurePluginUpdate$.complete(), this._featurePluginAdd$.complete(), this._featurePluginRemove$.complete(), this._featurePluginOrderUpdate$.complete(), this.drawingManagerData = {}, this._oldDrawingManagerData = {};\n  }\n  visibleNotification(e) {\n    this._visible$.next(e);\n  }\n  refreshTransform(e) {\n    e.forEach((n) => {\n      const t = this._getCurrentBySearch(n);\n      t != null && (t.transform = n.transform, t.transforms = n.transforms, t.isMultiTransform = n.isMultiTransform);\n    }), this.refreshTransformNotification(e);\n  }\n  getDrawingDataForUnit(e) {\n    return this.drawingManagerData[e] || {};\n  }\n  removeDrawingDataForUnit(e) {\n    const n = this.drawingManagerData[e];\n    if (n == null)\n      return;\n    delete this.drawingManagerData[e];\n    const t = [];\n    Object.keys(n).forEach((r) => {\n      const u = n[r];\n      (u == null ? void 0 : u.data) != null && Object.keys(u.data).forEach((o) => {\n        t.push({ unitId: e, subUnitId: r, drawingId: o });\n      });\n    }), t.length > 0 && this.removeNotification(t);\n  }\n  registerDrawingData(e, n) {\n    this.drawingManagerData[e] = n;\n  }\n  initializeNotification(e) {\n    const n = [], t = this.drawingManagerData[e];\n    t != null && (Object.keys(t).forEach((r) => {\n      this._establishDrawingMap(e, r);\n      const u = t[r];\n      Object.keys(u.data).forEach((o) => {\n        const l = u.data[o];\n        l.unitId = e, l.subUnitId = r, n.push(l);\n      });\n    }), n.length > 0 && this.addNotification(n));\n  }\n  getDrawingData(e, n) {\n    return this._getDrawingData(e, n);\n  }\n  // Use in doc only.\n  setDrawingData(e, n, t) {\n    this.drawingManagerData[e][n].data = t;\n  }\n  getBatchAddOp(e) {\n    const n = [], t = [], r = [];\n    e.forEach((x) => {\n      const { op: _, invertOp: s } = this._addByParam(x);\n      n.push({ unitId: x.unitId, subUnitId: x.subUnitId, drawingId: x.drawingId }), t.push(_), r.push(s);\n    });\n    const u = t.reduce(V.type.compose, null), o = r.reduce(V.type.compose, null), { unitId: l, subUnitId: E } = e[0];\n    return { undo: o, redo: u, unitId: l, subUnitId: E, objects: n };\n  }\n  getBatchRemoveOp(e) {\n    const n = [], t = [];\n    e.forEach((E) => {\n      const { op: x, invertOp: _ } = this._removeByParam(E);\n      n.unshift(x), t.push(_);\n    });\n    const r = n.reduce(V.type.compose, null), u = t.reduce(V.type.compose, null), { unitId: o, subUnitId: l } = e[0];\n    return { undo: u, redo: r, unitId: o, subUnitId: l, objects: e };\n  }\n  getBatchUpdateOp(e) {\n    const n = [], t = [], r = [];\n    e.forEach((x) => {\n      const { op: _, invertOp: s } = this._updateByParam(x);\n      n.push({ unitId: x.unitId, subUnitId: x.subUnitId, drawingId: x.drawingId }), t.push(_), r.push(s);\n    });\n    const u = t.reduce(V.type.compose, null), o = r.reduce(V.type.compose, null), { unitId: l, subUnitId: E } = e[0];\n    return { undo: o, redo: u, unitId: l, subUnitId: E, objects: n };\n  }\n  removeNotification(e) {\n    this._remove$.next(e);\n  }\n  addNotification(e) {\n    this._add$.next(e);\n  }\n  updateNotification(e) {\n    this._update$.next(e);\n  }\n  orderNotification(e) {\n    this._order$.next(e);\n  }\n  groupUpdateNotification(e) {\n    this._group$.next(e);\n  }\n  ungroupUpdateNotification(e) {\n    this._ungroup$.next(e);\n  }\n  refreshTransformNotification(e) {\n    this._refreshTransform$.next(e);\n  }\n  getGroupDrawingOp(e) {\n    const n = [], { unitId: t, subUnitId: r } = e[0].parent;\n    e.forEach((l) => {\n      n.push(this._getGroupDrawingOp(l));\n    });\n    const u = n.reduce(V.type.compose, null);\n    return { undo: V.type.invertWithDoc(u, this.drawingManagerData), redo: u, unitId: t, subUnitId: r, objects: e };\n  }\n  getUngroupDrawingOp(e) {\n    const n = [], { unitId: t, subUnitId: r } = e[0].parent;\n    e.forEach((l) => {\n      n.push(this._getUngroupDrawingOp(l));\n    });\n    const u = n.reduce(V.type.compose, null);\n    return { undo: V.type.invertWithDoc(u, this.drawingManagerData), redo: u, unitId: t, subUnitId: r, objects: e };\n  }\n  getDrawingsByGroup(e) {\n    const { unitId: n, subUnitId: t, drawingId: r } = e;\n    if (this.getDrawingByParam({ unitId: n, subUnitId: t, drawingId: r }) == null)\n      return [];\n    const o = this._getDrawingData(n, t), l = [];\n    return Object.keys(o).forEach((E) => {\n      const x = o[E];\n      x.groupId === r && l.push(x);\n    }), l;\n  }\n  _getGroupDrawingOp(e) {\n    const { parent: n, children: t } = e, { unitId: r, subUnitId: u, drawingId: o } = n, l = [];\n    l.push(\n      V.insertOp([r, u, \"data\", o], n)\n    );\n    let E = Number.NEGATIVE_INFINITY;\n    return t.forEach((x) => {\n      const { unitId: _, subUnitId: s, drawingId: c } = x, h = this._hasDrawingOrder({ unitId: _, subUnitId: s, drawingId: c });\n      E = Math.max(E, h), l.push(\n        ...this._getUpdateParamCompareOp(x, this.getDrawingByParam({ unitId: _, subUnitId: s, drawingId: c }))\n      );\n    }), E === Number.NEGATIVE_INFINITY && (E = this._getDrawingOrder(r, u).length), l.push(\n      V.insertOp([r, u, \"order\", E], o)\n    ), l.reduce(V.type.compose, null);\n  }\n  _getUngroupDrawingOp(e) {\n    const { parent: n, children: t } = e, { unitId: r, subUnitId: u, drawingId: o } = n, l = [];\n    return t.forEach((E) => {\n      const { unitId: x, subUnitId: _, drawingId: s } = E;\n      l.push(\n        ...this._getUpdateParamCompareOp(E, this.getDrawingByParam({ unitId: x, subUnitId: _, drawingId: s }))\n      );\n    }), l.push(\n      V.removeOp([r, u, \"data\", o], !0)\n    ), l.push(\n      V.removeOp([r, u, \"order\", this._getDrawingOrder(r, u).indexOf(o)], !0)\n    ), l.reduce(V.type.compose, null);\n  }\n  applyJson1(e, n, t) {\n    this._establishDrawingMap(e, n), this._oldDrawingManagerData = { ...this.drawingManagerData }, this.drawingManagerData = V.type.apply(this.drawingManagerData, t);\n  }\n  // private _fillMissingFields(jsonOp: JSONOp) {\n  //     if (jsonOp == null) {\n  //         return;\n  //     }\n  //     let object: { [key: string]: {} } = this.drawingManagerData;\n  //     for (let i = 0; i < jsonOp.length; i++) {\n  //         const op = jsonOp[i];\n  //         if (Array.isArray(op)) {\n  //             const opKey = op[0] as string;\n  //             if (!(opKey in object)) {\n  //                 object[opKey] = null as unknown as never;\n  //             }\n  //         } else if (typeof op === 'string') {\n  //             object = object[op];\n  //             if (object == null) {\n  //                 break;\n  //             }\n  //         }\n  //     }\n  // }\n  featurePluginUpdateNotification(e) {\n    this._featurePluginUpdate$.next(e);\n  }\n  featurePluginOrderUpdateNotification(e) {\n    this._featurePluginOrderUpdate$.next(e);\n  }\n  featurePluginAddNotification(e) {\n    this._featurePluginAdd$.next(e);\n  }\n  featurePluginRemoveNotification(e) {\n    this._featurePluginRemove$.next(e);\n  }\n  featurePluginGroupUpdateNotification(e) {\n    this._featurePluginGroupUpdate$.next(e);\n  }\n  featurePluginUngroupUpdateNotification(e) {\n    this._featurePluginUngroupUpdate$.next(e);\n  }\n  getDrawingByParam(e) {\n    return this._getCurrentBySearch(e);\n  }\n  getOldDrawingByParam(e) {\n    return this._getOldBySearch(e);\n  }\n  getDrawingOKey(e) {\n    const [n, t, r] = e.split(\"#-#\");\n    return this._getCurrentBySearch({ unitId: n, subUnitId: t, drawingId: r });\n  }\n  focusDrawing(e) {\n    if (e == null || e.length === 0) {\n      this._focusDrawings = [], this._focus$.next([]);\n      return;\n    }\n    const n = [];\n    e.forEach((t) => {\n      var E;\n      const { unitId: r, subUnitId: u, drawingId: o } = t, l = (E = this._getDrawingData(r, u)) == null ? void 0 : E[o];\n      l != null && n.push(l);\n    }), n.length > 0 && (this._focusDrawings = n, this._focus$.next(n));\n  }\n  getFocusDrawings() {\n    const e = [];\n    return this._focusDrawings.forEach((n) => {\n      var l;\n      const { unitId: t, subUnitId: r, drawingId: u } = n, o = (l = this._getDrawingData(t, r)) == null ? void 0 : l[u];\n      o != null && e.push(o);\n    }), e;\n  }\n  getDrawingOrder(e, n) {\n    return this._getDrawingOrder(e, n);\n  }\n  // Use in doc only.\n  setDrawingOrder(e, n, t) {\n    this.drawingManagerData[e][n].order = t;\n  }\n  orderUpdateNotification(e) {\n    this._order$.next(e);\n  }\n  getForwardDrawingsOp(e) {\n    const { unitId: n, subUnitId: t, drawingIds: r } = e, u = [], o = this.getDrawingOrder(n, t), l = [...r];\n    r.forEach((_) => {\n      const s = this._hasDrawingOrder({ unitId: n, subUnitId: t, drawingId: _ });\n      if (s === -1 || s === o.length - 1)\n        return;\n      const c = V.moveOp([n, t, \"order\", s], [n, t, \"order\", s + 1]);\n      u.push(c), l.includes(o[s + 1]) || l.push(o[s + 1]);\n    });\n    const E = u.reduce(V.type.compose, null);\n    return { undo: V.type.invertWithDoc(E, this.drawingManagerData), redo: E, unitId: n, subUnitId: t, objects: { ...e, drawingIds: l } };\n  }\n  getBackwardDrawingOp(e) {\n    const { unitId: n, subUnitId: t, drawingIds: r } = e, u = [], o = this.getDrawingOrder(n, t), l = [...r];\n    r.forEach((_) => {\n      const s = this._hasDrawingOrder({ unitId: n, subUnitId: t, drawingId: _ });\n      if (s === -1 || s === 0)\n        return;\n      const c = V.moveOp([n, t, \"order\", s], [n, t, \"order\", s - 1]);\n      u.push(c), l.includes(o[s - 1]) || l.push(o[s - 1]);\n    });\n    const E = u.reduce(V.type.compose, null);\n    return { undo: V.type.invertWithDoc(E, this.drawingManagerData), redo: E, unitId: n, subUnitId: t, objects: { ...e, drawingIds: l } };\n  }\n  getFrontDrawingsOp(e) {\n    const { unitId: n, subUnitId: t, drawingIds: r } = e, u = this._getOrderFromSearchParams(n, t, r), o = [...r], l = this.getDrawingOrder(n, t), E = [];\n    u.forEach((s) => {\n      const { drawingId: c } = s, h = this._getDrawingCount(n, t) - 1, U = V.moveOp([n, t, \"order\", this._getDrawingOrder(n, t).indexOf(c)], [n, t, \"order\", h]);\n      E.push(U), o.includes(l[h]) || o.push(l[h]);\n    });\n    const x = E.reduce(V.type.compose, null);\n    return { undo: V.type.invertWithDoc(x, this.drawingManagerData), redo: x, unitId: n, subUnitId: t, objects: { ...e, drawingIds: o } };\n  }\n  getBackDrawingsOp(e) {\n    const { unitId: n, subUnitId: t, drawingIds: r } = e, u = this._getOrderFromSearchParams(n, t, r, !0), o = [...r], l = this.getDrawingOrder(n, t), E = [];\n    u.forEach((s) => {\n      const { drawingId: c } = s, h = V.moveOp([n, t, \"order\", this._getDrawingOrder(n, t).indexOf(c)], [n, t, \"order\", 0]);\n      E.push(h), o.includes(l[0]) || o.push(l[0]);\n    });\n    const x = E.reduce(V.type.compose, null);\n    return { undo: V.type.invertWithDoc(x, this.drawingManagerData), redo: x, unitId: n, subUnitId: t, objects: { ...e, drawingIds: o } };\n  }\n  _getDrawingCount(e, n) {\n    return this.getDrawingOrder(e, n).length || 0;\n  }\n  _getOrderFromSearchParams(e, n, t, r = !1) {\n    return t.map((u) => {\n      const o = this._hasDrawingOrder({ unitId: e, subUnitId: n, drawingId: u });\n      return { drawingId: u, zIndex: o };\n    }).sort(r === !1 ? xt : Bt);\n  }\n  _hasDrawingOrder(e) {\n    if (e == null)\n      return -1;\n    const { unitId: n, subUnitId: t, drawingId: r } = e;\n    return this._establishDrawingMap(n, t), this._getDrawingOrder(n, t).indexOf(r);\n  }\n  _getCurrentBySearch(e) {\n    var u, o, l;\n    if (e == null)\n      return;\n    const { unitId: n, subUnitId: t, drawingId: r } = e;\n    return (l = (o = (u = this.drawingManagerData[n]) == null ? void 0 : u[t]) == null ? void 0 : o.data) == null ? void 0 : l[r];\n  }\n  _getOldBySearch(e) {\n    var u, o, l;\n    if (e == null)\n      return;\n    const { unitId: n, subUnitId: t, drawingId: r } = e;\n    return (l = (o = (u = this._oldDrawingManagerData[n]) == null ? void 0 : u[t]) == null ? void 0 : o.data) == null ? void 0 : l[r];\n  }\n  _establishDrawingMap(e, n, t) {\n    var r;\n    return this.drawingManagerData[e] || (this.drawingManagerData[e] = {}), this.drawingManagerData[e][n] || (this.drawingManagerData[e][n] = {\n      data: {},\n      order: []\n    }), t == null ? null : (r = this.drawingManagerData[e][n].data) == null ? void 0 : r[t];\n  }\n  _addByParam(e) {\n    const { unitId: n, subUnitId: t, drawingId: r } = e;\n    this._establishDrawingMap(n, t, r);\n    const u = V.insertOp([n, t, \"data\", r], e), o = V.insertOp([n, t, \"order\", this._getDrawingOrder(n, t).length], r), l = [u, o].reduce(V.type.compose, null), E = V.type.invertWithDoc(l, this.drawingManagerData);\n    return { op: l, invertOp: E };\n  }\n  _removeByParam(e) {\n    if (e == null)\n      return { op: [], invertOp: [] };\n    const { unitId: n, subUnitId: t, drawingId: r } = e;\n    if (this._establishDrawingMap(n, t, r) == null)\n      return { op: [], invertOp: [] };\n    const o = V.removeOp([n, t, \"data\", r], !0), l = V.removeOp([n, t, \"order\", this._getDrawingOrder(n, t).indexOf(r)], !0), E = [o, l].reduce(V.type.compose, null), x = V.type.invertWithDoc(E, this.drawingManagerData);\n    return { op: E, invertOp: x };\n  }\n  _updateByParam(e) {\n    const { unitId: n, subUnitId: t, drawingId: r } = e, u = this._establishDrawingMap(n, t, r);\n    if (u == null)\n      return { op: [], invertOp: [] };\n    const l = this._getUpdateParamCompareOp(e, u).reduce(V.type.compose, null), E = V.type.invertWithDoc(l, this.drawingManagerData);\n    return { op: l, invertOp: E };\n  }\n  // private _initializeDrawingData(updateParam: T, oldParam: T) {\n  //     Object.keys(updateParam).forEach((key) => {\n  //         if (!(key in oldParam)) {\n  //             oldParam[key as keyof IDrawingParam] = null as unknown as never;\n  //         }\n  //     });\n  // }\n  _getUpdateParamCompareOp(e, n) {\n    const { unitId: t, subUnitId: r, drawingId: u } = e, o = [];\n    return Object.keys(e).forEach((l) => {\n      const E = e[l], x = n[l];\n      x !== E && o.push(\n        V.replaceOp([t, r, \"data\", u, l], x, E)\n      );\n    }), o;\n  }\n  _getDrawingData(e, n) {\n    var t, r;\n    return ((r = (t = this.drawingManagerData[e]) == null ? void 0 : t[n]) == null ? void 0 : r.data) || {};\n  }\n  _getDrawingOrder(e, n) {\n    var t, r;\n    return ((r = (t = this.drawingManagerData[e]) == null ? void 0 : t[n]) == null ? void 0 : r.order) || [];\n  }\n  getDrawingVisible() {\n    return this._visible;\n  }\n  getDrawingEditable() {\n    return this._editable;\n  }\n  setDrawingVisible(e) {\n    this._visible = e;\n  }\n  setDrawingEditable(e) {\n    this._editable = e;\n  }\n}\nclass an extends on {\n}\nfunction On({ unitId: a, subUnitId: e, drawingId: n }, t) {\n  return typeof t == \"number\" ? `${a}#-#${e}#-#${n}#-#${t}` : `${a}#-#${e}#-#${n}`;\n}\nconst wn = async (a) => new Promise((e, n) => {\n  const t = new Image();\n  t.src = a, t.onload = () => {\n    e({\n      width: t.width,\n      height: t.height,\n      image: t\n    });\n  }, t.onerror = (r) => {\n    n(r);\n  };\n}), kt = Wt(\"univer.drawing-manager.service\"), ln = {\n  id: \"drawing.operation.set-drawing-selected\",\n  type: Gt.OPERATION,\n  handler: (a, e) => {\n    const n = a.get(kt);\n    return e == null ? !1 : (n.focusDrawing(e), !0);\n  }\n}, un = \"drawing.config\", Tt = {};\nclass cn {\n  constructor() {\n    F(this, \"_waitCount\", 0);\n    F(this, \"_change$\", new be());\n    F(this, \"change$\", this._change$);\n    F(this, \"_imageSourceCache\", /* @__PURE__ */ new Map());\n  }\n  setWaitCount(e) {\n    this._waitCount = e, this._change$.next(e);\n  }\n  getImageSourceCache(e, n) {\n    if (n === at.BASE64) {\n      const t = new Image();\n      return t.src = e, t;\n    }\n    return this._imageSourceCache.get(e);\n  }\n  addImageSourceCache(e, n, t) {\n    n === at.BASE64 || t == null || this._imageSourceCache.set(e, t);\n  }\n  async getImage(e) {\n    return Promise.resolve(e);\n  }\n  async saveImage(e) {\n    return new Promise((n, t) => {\n      if (!Zt.includes(e.type)) {\n        t(new Error(et.ERROR_IMAGE_TYPE)), this._decreaseWaiting();\n        return;\n      }\n      if (e.size > Yt) {\n        t(new Error(et.ERROR_EXCEED_SIZE)), this._decreaseWaiting();\n        return;\n      }\n      const r = new FileReader();\n      r.readAsDataURL(e), r.onload = (u) => {\n        var E;\n        const o = (E = u.target) == null ? void 0 : E.result;\n        if (o == null) {\n          t(new Error(et.ERROR_IMAGE)), this._decreaseWaiting();\n          return;\n        }\n        const l = Lt.generateRandomId(6);\n        n({\n          imageId: l,\n          imageSourceType: at.BASE64,\n          source: o,\n          base64Cache: o,\n          status: et.SUCCUSS\n        }), this._decreaseWaiting();\n      };\n    });\n  }\n  _decreaseWaiting() {\n    this._waitCount -= 1, this._change$.next(this._waitCount);\n  }\n}\nvar dn = Object.getOwnPropertyDescriptor, fn = (a, e, n, t) => {\n  for (var r = t > 1 ? void 0 : t ? dn(e, n) : e, u = a.length - 1, o; u >= 0; u--)\n    (o = a[u]) && (r = o(r) || r);\n  return r;\n}, dt = (a, e) => (n, t) => e(n, t, a);\nconst hn = \"UNIVER_DRAWING_PLUGIN\";\nvar ft;\nlet At = (ft = class extends Ht {\n  constructor(a = Tt, e, n, t) {\n    super(), this._config = a, this._injector = e, this._configService = n, this._commandService = t;\n    const { ...r } = zt(\n      {},\n      Tt,\n      this._config\n    );\n    this._configService.setConfig(un, r);\n  }\n  onStarting() {\n    this._initCommands(), this._initDependencies();\n  }\n  _initDependencies() {\n    var n;\n    Xt([\n      [Jt, { useClass: cn }],\n      [kt, { useClass: an }]\n    ], (n = this._config) == null ? void 0 : n.override).forEach((t) => this._injector.add(t));\n  }\n  _initCommands() {\n    [\n      ln\n    ].forEach((a) => this.disposeWithMe(this._commandService.registerCommand(a)));\n  }\n}, F(ft, \"pluginName\", hn), ft);\nAt = fn([\n  dt(1, qt(Kt)),\n  dt(2, Ft),\n  dt(3, Vt)\n], At);\nexport {\n  Zt as DRAWING_IMAGE_ALLOW_IMAGE_LIST,\n  Yt as DRAWING_IMAGE_ALLOW_SIZE,\n  mn as DRAWING_IMAGE_COUNT_LIMIT,\n  vn as DRAWING_IMAGE_HEIGHT_LIMIT,\n  _n as DRAWING_IMAGE_WIDTH_LIMIT,\n  an as DrawingManagerService,\n  kt as IDrawingManagerService,\n  In as IImageIoService,\n  cn as ImageIoService,\n  En as ImageSourceType,\n  Dn as ImageUploadStatusType,\n  ln as SetDrawingSelectedOperation,\n  on as UnitDrawingService,\n  At as UniverDrawingPlugin,\n  On as getDrawingShapeKeyByDrawingSearch,\n  wn as getImageSize\n};\n"], "mappings": "AAAA,IAAIA,EAAE,GAAGC,MAAM,CAACC,cAAc;AAC9B,IAAIC,EAAE,GAAGA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAKD,CAAC,IAAID,CAAC,GAAGJ,EAAE,CAACI,CAAC,EAAEC,CAAC,EAAE;EAAEE,UAAU,EAAE,CAAC,CAAC;EAAEC,YAAY,EAAE,CAAC,CAAC;EAAEC,QAAQ,EAAE,CAAC,CAAC;EAAEC,KAAK,EAAEJ;AAAE,CAAC,CAAC,GAAGF,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC;AAChH,IAAIK,CAAC,GAAGA,CAACP,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAKH,EAAE,CAACC,CAAC,EAAE,OAAOC,CAAC,IAAI,QAAQ,GAAGA,CAAC,GAAG,EAAE,GAAGA,CAAC,EAAEC,CAAC,CAAC;AAChE,SAASM,SAAS,IAAIC,EAAE,EAAEC,eAAe,IAAIC,EAAE,EAAEC,gBAAgB,IAAIC,EAAE,EAAEC,WAAW,IAAIC,EAAE,EAAEC,eAAe,IAAIC,EAAE,EAAEC,qBAAqB,IAAIC,EAAE,EAAEC,KAAK,IAAIC,EAAE,EAAEC,MAAM,IAAIC,EAAE,EAAEC,QAAQ,IAAIC,EAAE,EAAEC,cAAc,IAAIC,EAAE,EAAEC,eAAe,IAAIC,EAAE,EAAEC,MAAM,IAAIC,EAAE,EAAEC,KAAK,IAAIC,EAAE,EAAEC,6BAA6B,IAAIC,EAAE,EAAEC,eAAe,IAAIC,EAAE,QAAQ,gBAAgB;AACrV,SAASD,eAAe,IAAIE,EAAE,EAAEtB,eAAe,IAAIuB,EAAE,EAAErB,qBAAqB,IAAIsB,EAAE,QAAQ,gBAAgB;AAC1G,SAASC,OAAO,IAAIC,EAAE,QAAQ,MAAM;AACpC,MAAMC,EAAE,GAAG,GAAG;EAAEC,EAAE,GAAG,GAAG;EAAEC,EAAE,GAAG,EAAE;EAAEC,EAAE,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI;EAAEC,EAAE,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC;AAChI,IAAIC,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE;AACjC,SAASC,EAAEA,CAAA,EAAG;EACZ,IAAID,EAAE,EAAE,OAAOD,EAAE;EACjBC,EAAE,GAAG,CAAC,EAAEtD,MAAM,CAACC,cAAc,CAACoD,EAAE,EAAE,YAAY,EAAE;IAAE5C,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC;EAC9D,SAASN,CAACA,CAACqD,CAAC,EAAEC,CAAC,EAAE;IACf,IAAIC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,EAClB,OAAO,CAAC,CAAC;IACX,KAAK,IAAIG,CAAC,IAAIJ,CAAC,EACb,IAAI,CAACnD,CAAC,CAACmD,CAAC,CAACI,CAAC,CAAC,EAAEH,CAAC,CAACG,CAAC,CAAC,CAAC,EAChB,OAAO,CAAC,CAAC;IACb,KAAK,IAAIA,CAAC,IAAIH,CAAC,EACb,IAAID,CAAC,CAACI,CAAC,CAAC,KAAK,KAAK,CAAC,EACjB,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;EACX;EACA,SAASxD,CAACA,CAACoD,CAAC,EAAEC,CAAC,EAAE;IACf,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,IAAID,CAAC,CAACK,MAAM,KAAKJ,CAAC,CAACI,MAAM,EAC5C,OAAO,CAAC,CAAC;IACX,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,CAACK,MAAM,EAAED,CAAC,EAAE,EAC/B,IAAI,CAACvD,CAAC,CAACmD,CAAC,CAACI,CAAC,CAAC,EAAEH,CAAC,CAACG,CAAC,CAAC,CAAC,EAChB,OAAO,CAAC,CAAC;IACb,OAAO,CAAC,CAAC;EACX;EACA,SAASvD,CAACA,CAACmD,CAAC,EAAEC,CAAC,EAAE;IACf,OAAOD,CAAC,KAAKC,CAAC,GAAG,CAAC,CAAC,GAAGD,CAAC,KAAK,IAAI,IAAIC,CAAC,KAAK,IAAI,IAAI,OAAOD,CAAC,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,QAAQ,GAAG,CAAC,CAAC,GAAGC,KAAK,CAACC,OAAO,CAACH,CAAC,CAAC,GAAGpD,CAAC,CAACoD,CAAC,EAAEC,CAAC,CAAC,GAAGtD,CAAC,CAACqD,CAAC,EAAEC,CAAC,CAAC;EAC5I;EACA,OAAOJ,EAAE,CAACS,OAAO,GAAGzD,CAAC,EAAEgD,EAAE;AAC3B;AACA,IAAIU,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE;AACf,SAASC,EAAEA,CAAA,EAAG;EACZ,IAAID,EAAE,EAAE,OAAOD,EAAE;EACjBC,EAAE,GAAG,CAAC,EAAEhE,MAAM,CAACC,cAAc,CAAC8D,EAAE,EAAE,YAAY,EAAE;IAAEtD,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC;EAC9D,SAASN,CAACA,CAACC,CAAC,EAAE;IACZ,IAAIA,CAAC,KAAK,IAAI,EACZ,OAAO,IAAI;IACb,IAAIsD,KAAK,CAACC,OAAO,CAACvD,CAAC,CAAC,EAClB,OAAOA,CAAC,CAAC8D,GAAG,CAAC/D,CAAC,CAAC;IACjB,IAAI,OAAOC,CAAC,IAAI,QAAQ,EAAE;MACxB,MAAMC,CAAC,GAAG,CAAC,CAAC;MACZ,KAAK,IAAImD,CAAC,IAAIpD,CAAC,EACbC,CAAC,CAACmD,CAAC,CAAC,GAAGrD,CAAC,CAACC,CAAC,CAACoD,CAAC,CAAC,CAAC;MAChB,OAAOnD,CAAC;IACV,CAAC,MACC,OAAOD,CAAC;EACZ;EACA,OAAO2D,EAAE,CAACD,OAAO,GAAG3D,CAAC,EAAE4D,EAAE;AAC3B;AACA,IAAII,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE;AACf,SAASC,EAAEA,CAAA,EAAG;EACZ,OAAOD,EAAE,KAAKA,EAAE,GAAG,CAAC,EAAE,UAASjE,CAAC,EAAE;IAChCH,MAAM,CAACC,cAAc,CAACE,CAAC,EAAE,YAAY,EAAE;MAAEM,KAAK,EAAE,CAAC;IAAE,CAAC,CAAC,EAAEN,CAAC,CAACmE,WAAW,GAAGnE,CAAC,CAACoE,QAAQ,GAAGpE,CAAC,CAACqE,UAAU,GAAGrE,CAAC,CAACsE,WAAW,GAAGtE,CAAC,CAACuE,WAAW,GAAGvE,CAAC,CAACwE,UAAU,GAAGxE,CAAC,CAACyE,eAAe,GAAG,KAAK,CAAC;IAC5K,SAASxE,CAACA,CAACyE,CAAC,EAAEC,CAAC,EAAE;MACf,IAAI,CAACD,CAAC,EACJ,MAAM,IAAIE,KAAK,CAACD,CAAC,CAAC;IACtB;IACA,MAAMzE,CAAC,GAAIwE,CAAC,IAAKA,CAAC,IAAI,IAAI,IAAI,OAAOA,CAAC,IAAI,QAAQ,IAAI,CAACnB,KAAK,CAACC,OAAO,CAACkB,CAAC,CAAC;MAAErB,CAAC,GAAGA,CAACqB,CAAC,EAAEC,CAAC;MAChF;MACA,OAAOD,CAAC,IAAI,OAAOC,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,OAAOD,CAAC,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,QACpE;IACD,SAASrB,CAACA,CAACoB,CAAC,EAAEC,CAAC,EAAE;MACf,KAAK,IAAIE,CAAC,IAAIH,CAAC,EAAE;QACf,MAAMI,CAAC,GAAGD,CAAC;QACXF,CAAC,CAACI,KAAK,CAACD,CAAC,EAAEJ,CAAC,CAACI,CAAC,CAAC,CAAC;MAClB;IACF;IACA9E,CAAC,CAACyE,eAAe,GAAIC,CAAC,IAAK,OAAOA,CAAC,IAAI,QAAQ,IAAI,OAAOA,CAAC,IAAI,QAAQ,IAAIA,CAAC,KAAK,WAAW;IAC5F,MAAMjB,CAAC,CAAC;MACNuB,WAAWA,CAACL,CAAC,GAAG,IAAI,EAAE;QACpB,IAAI,CAACM,OAAO,GAAG,EAAE,EAAE,IAAI,CAACC,OAAO,GAAG,EAAE,EAAE,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,GAAG,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,SAAS,GAAGV,CAAC;MAC1F;MACAW,MAAMA,CAAA,EAAG;QACPrF,CAAC,CAAC,IAAI,CAACgF,OAAO,CAACvB,MAAM,KAAK,IAAI,CAACwB,OAAO,CAACxB,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC0B,GAAG,KAAK,CAAC,GAAG,IAAI,CAACH,OAAO,CAACvB,MAAM,IAAI,IAAI,CAACyB,KAAK,GAAG,IAAI,CAACD,OAAO,CAACK,GAAG,CAAC,CAAC,EAAE,IAAI,CAACF,SAAS,GAAG,IAAI,CAACJ,OAAO,CAACM,GAAG,CAAC,CAAC,EAAE,IAAI,CAACH,GAAG,GAAG,IAAI,CAACF,OAAO,CAACK,GAAG,CAAC,CAAC,KAAK,IAAI,CAACJ,KAAK,GAAG,CAAC,EAAE,IAAI,CAACC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAInF,CAAC,CAAC,IAAI,CAACmF,GAAG,GAAG,CAAC,CAAC,EAAE,IAAI,CAACA,GAAG,EAAE,EAAElF,CAAC,CAAC,IAAI,CAACmF,SAAS,CAAC,IAAI,CAACD,GAAG,CAAC,CAAC,IAAI,IAAI,CAACA,GAAG,EAAE,CAAC;MAC/S;MACAI,OAAOA,CAAA,EAAG;QACR,MAAMb,CAAC,GAAG,EAAE;QACZ,IAAIE,CAAC,GAAG,IAAI,CAACQ,SAAS;UAAEP,CAAC,GAAG,IAAI,CAACG,OAAO,CAACvB,MAAM,GAAG,CAAC;UAAE+B,CAAC,GAAG,IAAI,CAACL,GAAG;QACjE,OAAOK,CAAC,IAAI,CAAC,GACXd,CAAC,CAACe,OAAO,CAACb,CAAC,CAACY,CAAC,CAAC,CAAC,EAAEA,CAAC,KAAK,CAAC,IAAIA,CAAC,GAAG,IAAI,CAACP,OAAO,CAACJ,CAAC,GAAG,CAAC,CAAC,EAAED,CAAC,GAAG,IAAI,CAACI,OAAO,CAACH,CAAC,EAAE,CAAC,IAAIW,CAAC,IAAIvF,CAAC,CAAC2E,CAAC,CAACY,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QACxG,OAAOd,CAAC;MACV;IACF;IACA,MAAMgB,CAAC,SAASlC,CAAC,CAAC;MAChBmC,GAAGA,CAAA,EAAG;QACJ,OAAO,IAAI,CAACP,SAAS,GAAG,IAAI,CAACA,SAAS,CAACQ,KAAK,CAAC,IAAI,CAACT,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI;MACnE;MACA;MACAU,MAAMA,CAAA,EAAG;QACP,OAAO7F,CAAC,CAAC,IAAI,CAACoF,SAAS,IAAI,IAAI,EAAE,gDAAgD,CAAC,EAAE,IAAI,CAACA,SAAS,CAAC,IAAI,CAACD,GAAG,CAAC;MAC9G;MACAW,YAAYA,CAAA,EAAG;QACb,IAAIpB,CAAC;QACL,OAAO,IAAI,CAACU,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC3B,MAAM,GAAG,IAAI,CAAC0B,GAAG,GAAG,CAAC,IAAIlF,CAAC,CAACyE,CAAC,GAAG,IAAI,CAACU,SAAS,CAAC,IAAI,CAACD,GAAG,GAAG,CAAC,CAAC,CAAC,GAAGT,CAAC,GAAG,IAAI;MACjH;MACAqB,YAAYA,CAAA,EAAG;QACb,IAAIrB,CAAC,GAAG,IAAI,CAACS,GAAG,GAAG,CAAC;QACpB,IAAI,CAAC,IAAI,CAACC,SAAS,IAAIV,CAAC,IAAI,IAAI,CAACU,SAAS,CAAC3B,MAAM,IAAIxD,CAAC,CAAC,IAAI,CAACmF,SAAS,CAACV,CAAC,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,IAAI,IAAI,CAACU,SAAS,CAAC3B,MAAM,EACzG,OAAO,CAAC,CAAC;QACXxD,CAAC,CAAC,IAAI,CAACmF,SAAS,CAACV,CAAC,CAAC,CAAC,IAAIA,CAAC,EAAE;QAC3B,MAAME,CAAC,GAAG,IAAI,CAACQ,SAAS,CAACV,CAAC,CAAC;QAC3B,OAAOpB,KAAK,CAACC,OAAO,CAACqB,CAAC,CAAC,IAAI,IAAI,CAACK,OAAO,CAACe,IAAI,CAAC,IAAI,CAACb,GAAG,CAAC,EAAE,IAAI,CAACH,OAAO,CAACgB,IAAI,CAAC,IAAI,CAACZ,SAAS,CAAC,EAAE,IAAI,CAACH,OAAO,CAACe,IAAI,CAACtB,CAAC,CAAC,EAAE,IAAI,CAACS,GAAG,GAAG,CAAC,EAAE,IAAI,CAACC,SAAS,GAAGR,CAAC,IAAI,IAAI,CAACO,GAAG,GAAGT,CAAC,EAAE,CAAC,CAAC;MACvK;MACAuB,WAAWA,CAAA,EAAG;QACZ,IAAIjG,CAAC,CAAC,IAAI,CAACgF,OAAO,CAACvB,MAAM,KAAK,IAAI,CAACwB,OAAO,CAACxB,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC0B,GAAG,GAAG,CAAC,IAAI,IAAI,CAACH,OAAO,CAACvB,MAAM,KAAK,CAAC,EAC/F,OAAO,CAAC,CAAC;QACX,MAAMiB,CAAC,GAAG,IAAI,CAACO,OAAO,CAAC,IAAI,CAACA,OAAO,CAACxB,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;UAAEmB,CAAC,GAAG,IAAI,CAACI,OAAO,CAAC,IAAI,CAACA,OAAO,CAACvB,MAAM,GAAG,CAAC,CAAC;QAC9F,OAAOiB,CAAC,IAAIE,CAAC,CAACnB,MAAM,GAAG,CAAC,CAAC,IAAIzD,CAAC,CAAC,CAACkG,KAAK,CAACxB,CAAC,CAAC,CAAC,EAAE,IAAI,CAACO,OAAO,CAAC,IAAI,CAACA,OAAO,CAACxB,MAAM,GAAG,CAAC,CAAC,GAAGiB,CAAC,EAAE,IAAI,CAACU,SAAS,GAAGR,CAAC,CAACF,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAClH;MACAyB,KAAKA,CAACzB,CAAC,EAAEE,CAAC,EAAEC,CAAC,EAAEW,CAAC,EAAE;QAChB,IAAI,CAACJ,SAAS,GAAGV,CAAC,EAAE,IAAI,CAACS,GAAG,GAAGP,CAAC,EAAE,IAAI,CAACI,OAAO,GAAGH,CAAC,CAACe,KAAK,CAAC,CAAC,EAAE,IAAI,CAACX,OAAO,GAAGO,CAAC,CAACI,KAAK,CAAC,CAAC;MACtF;MACAQ,KAAKA,CAAA,EAAG;QACN,MAAM1B,CAAC,GAAG,IAAIgB,CAAC,CAAC,CAAC;QACjB,OAAOhB,CAAC,CAACyB,KAAK,CAAC,IAAI,CAACf,SAAS,EAAE,IAAI,CAACD,GAAG,EAAE,IAAI,CAACH,OAAO,EAAE,IAAI,CAACC,OAAO,CAAC,EAAEP,CAAC;MACzE;MACA,EAAE2B,MAAM,CAACC,QAAQ,IAAI;QACnB,IAAI,IAAI,CAACP,YAAY,CAAC,CAAC,EAAE;UACvB,GACE,MAAM,IAAI,CAACF,MAAM,CAAC,CAAC,CAAC,QACf,IAAI,CAACI,WAAW,CAAC,CAAC;UACzB,IAAI,CAACZ,MAAM,CAAC,CAAC;QACf;MACF;MACA;MACA;MACA;MACAkB,QAAQA,CAAC7B,CAAC,EAAEE,CAAC,EAAE;QACb,MAAMC,CAAC,GAAG,IAAI,CAACiB,YAAY,CAAC,CAAC;QAC7BjB,CAAC,IAAID,CAAC,CAACC,CAAC,EAAEH,CAAC,CAAC;QACZ,KAAK,MAAMc,CAAC,IAAI,IAAI,EAClBd,CAAC,IAAIA,CAAC,CAAC8B,OAAO,CAAChB,CAAC,CAAC,EAAE,IAAI,CAACe,QAAQ,CAAC7B,CAAC,EAAEE,CAAC,CAAC,EAAEF,CAAC,IAAIA,CAAC,CAACW,MAAM,CAAC,CAAC;MAC3D;MACAoB,QAAQA,CAAC/B,CAAC,EAAEE,CAAC,EAAE;QACb,IAAI,CAAC2B,QAAQ,CAAC7B,CAAC,EAAE,CAACG,CAAC,EAAEW,CAAC,KAAK;UACzBX,CAAC,CAAC6B,CAAC,IAAI,IAAI,IAAI9B,CAAC,CAACC,CAAC,CAAC6B,CAAC,EAAElB,CAAC,CAAC;QAC1B,CAAC,CAAC;MACJ;MACAmB,QAAQA,CAACjC,CAAC,EAAEE,CAAC,EAAE;QACb,IAAI,CAAC2B,QAAQ,CAAC7B,CAAC,EAAE,CAACG,CAAC,EAAEW,CAAC,KAAK;UACzBX,CAAC,CAAC+B,CAAC,IAAI,IAAI,IAAIhC,CAAC,CAACC,CAAC,CAAC+B,CAAC,EAAEpB,CAAC,CAAC;QAC1B,CAAC,CAAC;MACJ;IACF;IACAzF,CAAC,CAACwE,UAAU,GAAGmB,CAAC;IAChB,MAAMmB,CAAC,SAASrD,CAAC,CAAC;MAChBuB,WAAWA,CAACL,CAAC,GAAG,IAAI,EAAE;QACpB,KAAK,CAACA,CAAC,CAAC,EAAE,IAAI,CAACoC,cAAc,GAAG,EAAE,EAAE,IAAI,CAACC,GAAG,GAAGrC,CAAC;MAClD;MACAsC,YAAYA,CAAA,EAAG;QACbhH,CAAC,CAAC,IAAI,CAACgF,OAAO,CAACvB,MAAM,KAAK,IAAI,CAACwB,OAAO,CAACxB,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC2B,SAAS,KAAK,IAAI,KAAK,IAAI,CAAC2B,GAAG,GAAG,IAAI,CAAC3B,SAAS,GAAG,EAAE,CAAC;QAC/G,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACoC,cAAc,CAACrD,MAAM,EAAEiB,CAAC,EAAE,EAAE;UACnD,MAAME,CAAC,GAAG,IAAI,CAACkC,cAAc,CAACpC,CAAC,CAAC;UAChC,IAAIG,CAAC,GAAG,IAAI,CAACM,GAAG,GAAG,CAAC;UACpB,IAAIN,CAAC,GAAG,IAAI,CAACO,SAAS,CAAC3B,MAAM,IAAIxD,CAAC,CAAC,IAAI,CAACmF,SAAS,CAACP,CAAC,CAAC,CAAC,IAAIA,CAAC,EAAE,EAAE7E,CAAC,CAAC6E,CAAC,KAAK,IAAI,CAACO,SAAS,CAAC3B,MAAM,IAAI,CAACxD,CAAC,CAAC,IAAI,CAACmF,SAAS,CAACP,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,KAAK,IAAI,CAACO,SAAS,CAAC3B,MAAM,EAChJ,IAAI,CAAC2B,SAAS,CAACY,IAAI,CAACpB,CAAC,CAAC,EAAE,IAAI,CAACO,GAAG,GAAGN,CAAC,CAAC,KAClC,IAAI,IAAI,CAACO,SAAS,CAACP,CAAC,CAAC,KAAKD,CAAC,EAC9B,IAAI,CAACO,GAAG,GAAGN,CAAC,CAAC,KACV;YACH,IAAI,CAACvB,KAAK,CAACC,OAAO,CAAC,IAAI,CAAC6B,SAAS,CAACP,CAAC,CAAC,CAAC,EAAE;cACrC,MAAMW,CAAC,GAAG,IAAI,CAACJ,SAAS,CAAC6B,MAAM,CAACpC,CAAC,EAAE,IAAI,CAACO,SAAS,CAAC3B,MAAM,GAAGoB,CAAC,CAAC;cAC7D,IAAI,CAACO,SAAS,CAACY,IAAI,CAACR,CAAC,CAAC,EAAE,IAAI,CAACN,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,CAACA,KAAK,GAAGL,CAAC,CAAC;YAC7D;YACA,KAAK,IAAI,CAACI,OAAO,CAACe,IAAI,CAAC,IAAI,CAACb,GAAG,CAAC,EAAE,IAAI,CAACH,OAAO,CAACgB,IAAI,CAAC,IAAI,CAACZ,SAAS,CAAC,EAAE,IAAI,CAACF,KAAK,KAAK,CAAC,CAAC,KAAKlF,CAAC,CAACoD,CAAC,CAACwB,CAAC,EAAE,IAAI,CAACQ,SAAS,CAAC,IAAI,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEL,CAAC,GAAG,IAAI,CAACK,KAAK,GAAG,CAAC,EAAE,IAAI,CAACA,KAAK,GAAG,CAAC,CAAC,CAAC,EAAEL,CAAC,GAAG,IAAI,CAACO,SAAS,CAAC3B,MAAM,IAAIL,CAAC,CAACwB,CAAC,EAAE,IAAI,CAACQ,SAAS,CAACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAC9NA,CAAC,EAAE;YACL,IAAI,IAAI,CAACI,OAAO,CAACe,IAAI,CAACnB,CAAC,CAAC,EAAE,IAAI,CAACM,GAAG,GAAG,CAAC,EAAEN,CAAC,GAAG,IAAI,CAACO,SAAS,CAAC3B,MAAM,IAAI,IAAI,CAAC2B,SAAS,CAACP,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKD,CAAC,EAC7F,IAAI,CAACQ,SAAS,GAAG,IAAI,CAACA,SAAS,CAACP,CAAC,CAAC,CAAC,KAChC;cACH,MAAMW,CAAC,GAAG,CAACZ,CAAC,CAAC;cACb,IAAI,CAACQ,SAAS,CAAC6B,MAAM,CAACpC,CAAC,EAAE,CAAC,EAAEW,CAAC,CAAC,EAAE,IAAI,CAACJ,SAAS,GAAGI,CAAC;YACpD;UACF;QACF;QACA,IAAI,CAACsB,cAAc,CAACrD,MAAM,GAAG,CAAC;MAChC;MACAyD,KAAKA,CAAA,EAAG;QACN,IAAI,CAAChC,KAAK,GAAG,CAAC,CAAC;MACjB;MACA;MACA;MACA;MACAY,YAAYA,CAAA,EAAG;QACb,IAAI,CAACkB,YAAY,CAAC,CAAC;QACnB,MAAMtC,CAAC,GAAG,IAAI,CAACS,GAAG,GAAG,CAAC;QACtB,IAAIT,CAAC,GAAG,IAAI,CAACU,SAAS,CAAC3B,MAAM,IAAIxD,CAAC,CAAC,IAAI,CAACmF,SAAS,CAACV,CAAC,CAAC,CAAC,EACnD,OAAO,IAAI,CAACU,SAAS,CAACV,CAAC,CAAC;QAC1B;UACE,MAAME,CAAC,GAAG,CAAC,CAAC;UACZ,OAAO,IAAI,CAACQ,SAAS,CAAC6B,MAAM,CAACvC,CAAC,EAAE,CAAC,EAAEE,CAAC,CAAC,EAAEA,CAAC;QAC1C;MACF;MACAE,KAAKA,CAACJ,CAAC,EAAEE,CAAC,EAAE;QACV,MAAMC,CAAC,GAAG,IAAI,CAACiB,YAAY,CAAC,CAAC;QAC7B9F,CAAC,CAAC6E,CAAC,CAACH,CAAC,CAAC,IAAI,IAAI,IAAIG,CAAC,CAACH,CAAC,CAAC,KAAKE,CAAC,EAAE,+DAA+D,CAAC,EAAEC,CAAC,CAACH,CAAC,CAAC,GAAGE,CAAC;MAC1G;MACAe,GAAGA,CAAA,EAAG;QACJ,OAAO,IAAI,CAACoB,GAAG;MACjB;MACAP,OAAOA,CAAC9B,CAAC,EAAE;QACT,IAAI,CAAC3E,CAAC,CAACyE,eAAe,CAACE,CAAC,CAAC,EACvB,MAAMC,KAAK,CAAC,kBAAkB,CAAC;QACjC,IAAI,CAACmC,cAAc,CAACd,IAAI,CAACtB,CAAC,CAAC;MAC7B;MACAyC,WAAWA,CAACzC,CAAC,EAAE;QACb,OAAO,IAAI,CAACoC,cAAc,CAACd,IAAI,CAAC,GAAGtB,CAAC,CAAC,EAAE,IAAI;MAC7C;MACAW,MAAMA,CAAA,EAAG;QACP,IAAI,CAACyB,cAAc,CAACrD,MAAM,GAAG,IAAI,CAACqD,cAAc,CAACxB,GAAG,CAAC,CAAC,GAAG,KAAK,CAACD,MAAM,CAAC,CAAC;MACzE;MACA+B,SAASA,CAAC1C,CAAC,EAAEE,CAAC,GAAGvB,CAAC,EAAE;QAClB,IAAIqB,CAAC,KAAK,IAAI,EACZ;QACF,IAAI1E,CAAC,CAACsD,KAAK,CAACC,OAAO,CAACmB,CAAC,CAAC,CAAC,EAAEA,CAAC,KAAK,IAAI,CAACqC,GAAG,EACrC,MAAMpC,KAAK,CAAC,+BAA+B,CAAC;QAC9C,MAAME,CAAC,GAAG,IAAI,CAACK,KAAK;UAAEM,CAAC,GAAG,IAAI,CAACR,OAAO,CAACvB,MAAM;QAC7C,IAAI4D,CAAC,GAAG,CAAC;QACT,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAG5C,CAAC,CAACjB,MAAM,EAAE6D,EAAE,EAAE,EAAE;UACpC,MAAMC,CAAC,GAAG7C,CAAC,CAAC4C,EAAE,CAAC;UACf,OAAOC,CAAC,IAAI,QAAQ,IAAI,OAAOA,CAAC,IAAI,QAAQ,IAAIF,CAAC,EAAE,EAAE,IAAI,CAACb,OAAO,CAACe,CAAC,CAAC,IAAIjE,KAAK,CAACC,OAAO,CAACgE,CAAC,CAAC,GAAG,IAAI,CAACH,SAAS,CAACG,CAAC,EAAE3C,CAAC,CAAC,GAAG,OAAO2C,CAAC,IAAI,QAAQ,IAAI3C,CAAC,CAAC2C,CAAC,EAAE,IAAI,CAAC;QACtJ;QACA,OAAOF,CAAC,EAAE,GACR,IAAI,CAAChC,MAAM,CAAC,CAAC;QACf,IAAI,CAACH,KAAK,GAAG,IAAI,CAACF,OAAO,CAACvB,MAAM,KAAK+B,CAAC,GAAGX,CAAC,GAAG,CAAC,CAAC;MACjD;MACA7D,EAAEA,CAAC0D,CAAC,EAAEE,CAAC,EAAE;QACP,IAAI,CAACuC,WAAW,CAACzC,CAAC,CAAC,EAAEE,CAAC,CAAC,IAAI,CAAC;QAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,CAACjB,MAAM,EAAEoB,CAAC,EAAE,EAC/B,IAAI,CAACQ,MAAM,CAAC,CAAC;QACf,OAAO,IAAI;MACb;MACA;MACA;MACAmC,WAAWA,CAAC9C,CAAC,EAAEE,CAAC,EAAEC,CAAC,EAAE;QACnB,OAAO,IAAI,CAAC7D,EAAE,CAAC0D,CAAC,EAAE,MAAM,IAAI,CAACI,KAAK,CAACF,CAAC,EAAEC,CAAC,CAAC,CAAC,EAAE,IAAI,CAACqC,KAAK,CAAC,CAAC,EAAE,IAAI;MAC/D;MACAO,SAASA,CAAC/C,CAAC,EAAEE,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAE;QACrB,OAAO,IAAI,CAAC2C,WAAW,CAAC9C,CAAC,EAAE,GAAG,EAAEG,CAAC,CAAC,CAAC2C,WAAW,CAAC5C,CAAC,EAAE,GAAG,EAAEC,CAAC,CAAC;MAC3D;MACAU,OAAOA,CAAA,EAAG;QACR,MAAMb,CAAC,GAAG,KAAK,CAACa,OAAO,CAAC,CAAC;QACzB,OAAOb,CAAC,CAACsB,IAAI,CAAC,GAAG,IAAI,CAACc,cAAc,CAAC,EAAEpC,CAAC;MAC1C;IACF;IACA3E,CAAC,CAACuE,WAAW,GAAGuC,CAAC,EAAE9G,CAAC,CAACsE,WAAW,GAAG,MAAM,IAAIwC,CAAC,CAAC,CAAC,EAAE9G,CAAC,CAACqE,UAAU,GAAIK,CAAC,IAAK,IAAIiB,CAAC,CAACjB,CAAC,CAAC;IAChF,SAASiD,CAACA,CAACjD,CAAC,EAAEC,CAAC,EAAEE,CAAC,EAAE;MAClB,IAAIC,CAAC,EAAEW,CAAC;MACRA,CAAC,GAAGX,CAAC,GAAGJ,CAAC,GAAGA,CAAC,CAACsB,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC;MACjC,SAASsB,CAACA,CAACC,EAAE,EAAE;QACb,IAAIC,CAAC;QACL,OAAO/B,CAAC,GAAI;UACV,MAAMmC,EAAE,GAAGJ,CAAC,GAAG9C,CAAC,CAACoB,MAAM,CAAC,CAAC;UACzB,IAAIyB,EAAE,IAAI,IAAI,EAAE;YACd,IAAIM,EAAE,GAAG,CAAC,CAAC;YACX,IAAIlD,CAAC,IAAI,OAAOiD,EAAE,IAAI,QAAQ,KAAKJ,CAAC,GAAG7C,CAAC,CAACiD,EAAE,EAAElD,CAAC,CAACqB,YAAY,CAAC,CAAC,CAAC,EAAEyB,CAAC,GAAG,CAAC,KAAKA,CAAC,GAAG,CAACA,CAAC,EAAEK,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAExE,CAAC,CAACmE,CAAC,EAAED,EAAE,CAAC,EACnG,OAAO,IAAI;YACb,IAAIC,CAAC,KAAKD,EAAE,IAAI,CAACM,EAAE,EACjB,OAAOnD,CAAC;UACZ;UACAG,CAAC,IAAI,OAAO2C,CAAC,IAAI,QAAQ,IAAI3C,CAAC,CAAC2C,CAAC,EAAE9C,CAAC,CAACqB,YAAY,CAAC,CAAC,CAAC,EAAEN,CAAC,GAAGf,CAAC,CAACwB,WAAW,CAAC,CAAC;QAC1E;QACA,OAAO,IAAI;MACb;MACA,OAAOoB,CAAC,CAACQ,GAAG,GAAG,MAAM;QACnBhD,CAAC,IAAIJ,CAAC,CAACY,MAAM,CAAC,CAAC;MACjB,CAAC,EAAEgC,CAAC;IACN;IACAtH,CAAC,CAACoE,QAAQ,GAAGuD,CAAC;IACd,SAASI,CAACA,CAACrD,CAAC,EAAEC,CAAC,EAAEE,CAAC,EAAE;MAClB,IAAIC,CAAC,EAAEW,CAAC,EAAE6B,CAAC,EAAEC,EAAE;MACf,KAAKzC,CAAC,GAAGW,CAAC,GAAGf,CAAC,IAAIA,CAAC,CAACsB,YAAY,CAAC,CAAC,EAAEsB,CAAC,GAAGC,EAAE,GAAG5C,CAAC,IAAIA,CAAC,CAACqB,YAAY,CAAC,CAAC,EAAElB,CAAC,IAAIwC,CAAC,GAAI;QAC5E,IAAIE,CAAC,GAAG1C,CAAC,GAAGJ,CAAC,CAACoB,MAAM,CAAC,CAAC,GAAG,IAAI;UAAE8B,EAAE,GAAGN,CAAC,GAAG3C,CAAC,CAACmB,MAAM,CAAC,CAAC,GAAG,IAAI;QACzD0B,CAAC,KAAK,IAAI,IAAII,EAAE,KAAK,IAAI,KAAKvE,CAAC,CAACuE,EAAE,EAAEJ,CAAC,CAAC,GAAGI,EAAE,GAAG,IAAI,GAAGJ,CAAC,KAAKI,EAAE,KAAKJ,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE3C,CAAC,CAAC2C,CAAC,IAAI,IAAI,GAAGI,EAAE,GAAGJ,CAAC,EAAEA,CAAC,IAAI,IAAI,GAAG9C,CAAC,GAAG,IAAI,EAAEkD,EAAE,IAAI,IAAI,GAAGjD,CAAC,GAAG,IAAI,CAAC,EAAE6C,CAAC,IAAI,IAAI,IAAI1C,CAAC,KAAKA,CAAC,GAAGJ,CAAC,CAACwB,WAAW,CAAC,CAAC,CAAC,EAAE0B,EAAE,IAAI,IAAI,IAAIN,CAAC,KAAKA,CAAC,GAAG3C,CAAC,CAACuB,WAAW,CAAC,CAAC,CAAC;MACrO;MACAT,CAAC,IAAIf,CAAC,CAACY,MAAM,CAAC,CAAC,EAAEiC,EAAE,IAAI5C,CAAC,CAACW,MAAM,CAAC,CAAC;IACnC;IACAtF,CAAC,CAACmE,WAAW,GAAG4D,CAAC;EACnB,CAAC,CAAC/D,EAAE,CAAC,CAAC,EAAEA,EAAE;AACZ;AACA,IAAIgE,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE;AACf,SAASC,EAAEA,CAAA,EAAG;EACZ,OAAOD,EAAE,KAAKA,EAAE,GAAG,CAAC,EAAE,UAASjI,CAAC,EAAE;IAChCH,MAAM,CAACC,cAAc,CAACE,CAAC,EAAE,YAAY,EAAE;MAAEM,KAAK,EAAE,CAAC;IAAE,CAAC,CAAC,EAAEN,CAAC,CAACmI,YAAY,GAAG,KAAK,CAAC,EAAE,UAASlI,CAAC,EAAE;MAC1FA,CAAC,CAACA,CAAC,CAACmI,qBAAqB,GAAG,CAAC,CAAC,GAAG,uBAAuB,EAAEnI,CAAC,CAACA,CAAC,CAACoI,cAAc,GAAG,CAAC,CAAC,GAAG,gBAAgB,EAAEpI,CAAC,CAACA,CAAC,CAACqI,SAAS,GAAG,CAAC,CAAC,GAAG,WAAW;IACxI,CAAC,CAACtI,CAAC,CAACmI,YAAY,KAAKnI,CAAC,CAACmI,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5C,CAAC,CAACH,EAAE,CAAC,CAAC,EAAEA,EAAE;AACZ;AACA,IAAIO,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE;AACxB,SAASC,EAAEA,CAAA,EAAG;EACZ,OAAOD,EAAE,KAAKA,EAAE,GAAG,CAAC,EAAE5I,MAAM,CAACC,cAAc,CAAC0I,EAAE,EAAE,YAAY,EAAE;IAAElI,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC,EAAEkI,EAAE,CAACG,WAAW,GAAGH,EAAE,CAACI,WAAW,GAAG,KAAK,CAAC,EAAEJ,EAAE,CAACI,WAAW,GAAG,CAAC5I,CAAC,EAAEC,CAAC,GAAGD,CAAC,CAAC0D,MAAM,KAAK;IAC5J,IAAIxD,CAAC,GAAG,CAAC;MAAEmD,CAAC,GAAG,CAAC;IAChB,OAAOA,CAAC,GAAGpD,CAAC,EAAEoD,CAAC,EAAE,EAAE;MACjB,MAAMC,CAAC,GAAGtD,CAAC,CAAC6I,UAAU,CAACxF,CAAC,CAAC;MACzBC,CAAC,IAAI,KAAK,IAAIA,CAAC,IAAI,KAAK,KAAKpD,CAAC,EAAE,EAAEmD,CAAC,EAAE,CAAC;IACxC;IACA,IAAIA,CAAC,KAAKpD,CAAC,EACT,MAAM2E,KAAK,CAAC,uCAAuC,CAAC;IACtD,OAAOvB,CAAC,GAAGnD,CAAC;EACd,CAAC,EAAEsI,EAAE,CAACG,WAAW,GAAG,CAAC3I,CAAC,EAAEC,CAAC,KAAK;IAC5B,IAAIC,CAAC,GAAG,CAAC;IACT,OAAOD,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACjB,MAAMoD,CAAC,GAAGrD,CAAC,CAAC6I,UAAU,CAAC3I,CAAC,CAAC;MACzBA,CAAC,IAAImD,CAAC,IAAI,KAAK,IAAIA,CAAC,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC;IACvC;IACA,OAAOnD,CAAC;EACV,CAAC,CAAC,EAAEsI,EAAE;AACR;AACA,IAAIM,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE;AACf,SAASC,EAAEA,CAAA,EAAG;EACZ,OAAOD,EAAE,KAAKA,EAAE,GAAG,CAAC,EAAE,UAAS/I,CAAC,EAAE;IAChCH,MAAM,CAACC,cAAc,CAACE,CAAC,EAAE,YAAY,EAAE;MAAEM,KAAK,EAAE,CAAC;IAAE,CAAC,CAAC,EAAEN,CAAC,CAACiJ,QAAQ,GAAGjJ,CAAC,CAACkJ,IAAI,GAAGlJ,CAAC,CAACmJ,MAAM,GAAG,KAAK,CAAC;IAC9F,MAAMlJ,CAAC,GAAGyI,EAAE,CAAC,CAAC;MAAExI,CAAC,GAAIkJ,CAAC,IAAK;QACzB,IAAI,CAAC7F,KAAK,CAACC,OAAO,CAAC4F,CAAC,CAAC,EACnB,MAAMxE,KAAK,CAAC,mCAAmC,CAAC;QAClD,IAAIyE,CAAC,GAAG,IAAI;QACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,CAAC1F,MAAM,EAAE4F,CAAC,EAAE,EAAE;UACjC,MAAMC,CAAC,GAAGH,CAAC,CAACE,CAAC,CAAC;UACd,QAAQ,OAAOC,CAAC;YACd,KAAK,QAAQ;cACX,IAAI,OAAOA,CAAC,CAAC1C,CAAC,IAAI,QAAQ,IAAI,OAAO0C,CAAC,CAAC1C,CAAC,IAAI,QAAQ,EAClD,MAAMjC,KAAK,CAAC,iCAAiC,CAAC;cAChD,IAAI5E,CAAC,CAACkJ,IAAI,CAACK,CAAC,CAAC1C,CAAC,CAAC,IAAI,CAAC,EAClB,MAAMjC,KAAK,CAAC,2BAA2B,CAAC;cAC1C;YACF,KAAK,QAAQ;cACX,IAAI,EAAE2E,CAAC,CAAC7F,MAAM,GAAG,CAAC,CAAC,EACjB,MAAMkB,KAAK,CAAC,yBAAyB,CAAC;cACxC;YACF,KAAK,QAAQ;cACX,IAAI,EAAE2E,CAAC,GAAG,CAAC,CAAC,EACV,MAAM3E,KAAK,CAAC,4BAA4B,CAAC;cAC3C,IAAI,OAAOyE,CAAC,IAAI,QAAQ,EACtB,MAAMzE,KAAK,CAAC,6CAA6C,CAAC;cAC5D;UACJ;UACAyE,CAAC,GAAGE,CAAC;QACP;QACA,IAAI,OAAOF,CAAC,IAAI,QAAQ,EACtB,MAAMzE,KAAK,CAAC,wBAAwB,CAAC;MACzC,CAAC;IACD,SAASvB,CAACA,CAAC+F,CAAC,EAAEC,CAAC,EAAE;MACf,IAAIC,CAAC,GAAG,CAAC;QAAEC,CAAC,GAAG,CAAC;MAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,CAAC1F,MAAM,EAAE8F,CAAC,EAAE,EAAE;QACjC,MAAMC,CAAC,GAAGL,CAAC,CAACI,CAAC,CAAC;QACd,QAAQH,CAAC,CAACI,CAAC,EAAEH,CAAC,EAAEC,CAAC,CAAC,EAAE,OAAOE,CAAC;UAC1B,KAAK,QAAQ;YACXH,CAAC,IAAItJ,CAAC,CAACkJ,IAAI,CAACO,CAAC,CAAC5C,CAAC,CAAC;YAChB;UACF,KAAK,QAAQ;YACX0C,CAAC,IAAItJ,CAAC,CAAC2I,WAAW,CAACa,CAAC,CAAC;YACrB;UACF,KAAK,QAAQ;YACXH,CAAC,IAAIG,CAAC,EAAEF,CAAC,IAAIE,CAAC;YACd;QACJ;MACF;IACF;IACAzJ,CAAC,CAACmJ,MAAM,GAAG9F,CAAC;IACZ,SAASC,CAACA,CAAC8F,CAAC,EAAEC,CAAC,EAAE;MACf,MAAMC,CAAC,GAAG,EAAE;QAAEC,CAAC,GAAGzC,CAAC,CAACwC,CAAC,CAAC;MACtB,OAAOjG,CAAC,CAAC+F,CAAC,EAAE,CAACI,CAAC,EAAEC,CAAC,EAAEC,EAAE,KAAK;QACxBH,CAAC,CAACF,CAAC,CAACG,CAAC,EAAEC,CAAC,EAAEC,EAAE,CAAC,CAAC;MAChB,CAAC,CAAC,EAAE/E,CAAC,CAAC2E,CAAC,CAAC;IACV;IACA,MAAM7F,CAAC,GAAI2F,CAAC,IAAKA,CAAC;MAAEzD,CAAC,GAAIyD,CAAC,IAAK9F,CAAC,CAAC8F,CAAC,EAAE3F,CAAC,CAAC;IACtCzD,CAAC,CAACkJ,IAAI,GAAIE,CAAC,IAAK,OAAOA,CAAC,IAAI,QAAQ,GAAGA,CAAC,GAAGnJ,CAAC,CAAC2I,WAAW,CAACQ,CAAC,CAAC;IAC3D,MAAMtC,CAAC,GAAIsC,CAAC,IAAMC,CAAC,IAAK;QACtB,IAAI,EAAE,CAACA,CAAC,IAAIA,CAAC,CAACxC,CAAC,KAAK,CAAC,IAAIwC,CAAC,CAACxC,CAAC,KAAK,EAAE,CAAC,EAAE,IAAIuC,CAAC,CAAC1F,MAAM,KAAK,CAAC,EACtD0F,CAAC,CAACnD,IAAI,CAACoD,CAAC,CAAC,CAAC,KACP,IAAI,OAAOA,CAAC,IAAI,OAAOD,CAAC,CAACA,CAAC,CAAC1F,MAAM,GAAG,CAAC,CAAC;UACzC,IAAI,OAAO2F,CAAC,IAAI,QAAQ,EAAE;YACxB,MAAMC,CAAC,GAAGF,CAAC,CAACA,CAAC,CAAC1F,MAAM,GAAG,CAAC,CAAC;YACzB4F,CAAC,CAACzC,CAAC,GAAG,OAAOyC,CAAC,CAACzC,CAAC,IAAI,QAAQ,IAAI,OAAOwC,CAAC,CAACxC,CAAC,IAAI,QAAQ,GAAGyC,CAAC,CAACzC,CAAC,GAAGwC,CAAC,CAACxC,CAAC,GAAG7G,CAAC,CAACkJ,IAAI,CAACI,CAAC,CAACzC,CAAC,CAAC,GAAG7G,CAAC,CAACkJ,IAAI,CAACG,CAAC,CAACxC,CAAC,CAAC;UAChG,CAAC,MACCuC,CAAC,CAACA,CAAC,CAAC1F,MAAM,GAAG,CAAC,CAAC,IAAI2F,CAAC;QAAC,OAEvBD,CAAC,CAACnD,IAAI,CAACoD,CAAC,CAAC;MACb,CAAC;MAAE1B,CAAC,GAAIyB,CAAC,IAAK,OAAOA,CAAC,IAAI,QAAQ,GAAGA,CAAC,GAAG,OAAOA,CAAC,IAAI,QAAQ,GAAGnJ,CAAC,CAAC2I,WAAW,CAACQ,CAAC,CAAC,GAAG,OAAOA,CAAC,CAACvC,CAAC,IAAI,QAAQ,GAAGuC,CAAC,CAACvC,CAAC,GAAG5G,CAAC,CAAC2I,WAAW,CAACQ,CAAC,CAACvC,CAAC,CAAC;IACpI7G,CAAC,CAACiJ,QAAQ,GAAG,CAACG,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;MACxB,MAAMC,CAAC,GAAGtJ,CAAC,CAAC0I,WAAW,CAACS,CAAC,EAAEC,CAAC,CAAC;QAAEG,CAAC,GAAGF,CAAC,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,GAAGrJ,CAAC,CAAC0I,WAAW,CAACS,CAAC,EAAEE,CAAC,CAAC;MAC1E,OAAOF,CAAC,CAACvD,KAAK,CAAC0D,CAAC,EAAEC,CAAC,CAAC;IACtB,CAAC;IACD,MAAMzB,CAAC,GAAGA,CAACqB,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK,OAAOF,CAAC,IAAI,QAAQ,GAAGE,CAAC,IAAI,IAAI,GAAGF,CAAC,GAAGC,CAAC,GAAGM,IAAI,CAACC,GAAG,CAACR,CAAC,EAAEE,CAAC,CAAC,GAAGD,CAAC,GAAGrJ,CAAC,CAACiJ,QAAQ,CAACG,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;MAAE5E,CAAC,GAAI0E,CAAC,IAAK;QACrH,IAAIC,CAAC,GAAG,CAAC;UAAEC,CAAC,GAAG,CAAC;QAChB,OAAO;UAAEO,IAAI,EAAEA,CAACJ,CAAC,EAAEC,EAAE,KAAK;YACxB,IAAIL,CAAC,KAAKD,CAAC,CAAC1F,MAAM,EAChB,OAAO+F,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,GAAGA,CAAC;YAC5B,MAAMK,EAAE,GAAGV,CAAC,CAACC,CAAC,CAAC;YACf,IAAIU,EAAE;YACN,IAAI,OAAOD,EAAE,IAAI,QAAQ,EACvB,OAAOL,CAAC,KAAK,CAAC,CAAC,IAAIK,EAAE,GAAGR,CAAC,IAAIG,CAAC,IAAIM,EAAE,GAAGD,EAAE,GAAGR,CAAC,EAAE,EAAED,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAES,EAAE,KAAKT,CAAC,IAAIG,CAAC,EAAEA,CAAC,CAAC;YAC9E,IAAI,OAAOK,EAAE,IAAI,QAAQ,EAAE;cACzB,IAAIL,CAAC,KAAK,CAAC,CAAC,IAAIC,EAAE,KAAK,GAAG,IAAIzJ,CAAC,CAAC2I,WAAW,CAACkB,EAAE,CAACjE,KAAK,CAACyD,CAAC,CAAC,CAAC,IAAIG,CAAC,EAC3D,OAAOM,EAAE,GAAGD,EAAE,CAACjE,KAAK,CAACyD,CAAC,CAAC,EAAE,EAAED,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAES,EAAE;cACzC;gBACE,MAAMC,EAAE,GAAGV,CAAC,GAAGrJ,CAAC,CAAC0I,WAAW,CAACmB,EAAE,CAACjE,KAAK,CAACyD,CAAC,CAAC,EAAEG,CAAC,CAAC;gBAC5C,OAAOM,EAAE,GAAGD,EAAE,CAACjE,KAAK,CAACyD,CAAC,EAAEU,EAAE,CAAC,EAAEV,CAAC,GAAGU,EAAE,EAAED,EAAE;cACzC;YACF,CAAC,MAAM;cACL,IAAIN,CAAC,KAAK,CAAC,CAAC,IAAIC,EAAE,KAAK,GAAG,IAAI1J,CAAC,CAACkJ,IAAI,CAACY,EAAE,CAACjD,CAAC,CAAC,GAAGyC,CAAC,IAAIG,CAAC,EACjD,OAAOM,EAAE,GAAG;gBAAElD,CAAC,EAAEkB,CAAC,CAAC+B,EAAE,CAACjD,CAAC,EAAEyC,CAAC;cAAE,CAAC,EAAE,EAAED,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAES,EAAE;cAC/C;gBACE,IAAIC,EAAE,GAAGjC,CAAC,CAAC+B,EAAE,CAACjD,CAAC,EAAEyC,CAAC,EAAEA,CAAC,GAAGG,CAAC,CAAC;gBAC1B,OAAOH,CAAC,IAAIG,CAAC,EAAE;kBAAE5C,CAAC,EAAEmD;gBAAG,CAAC;cAC1B;YACF;UACF,CAAC;UAAEC,IAAI,EAAEA,CAAA,KAAMb,CAAC,CAACC,CAAC;QAAE,CAAC;MACvB,CAAC;MAAE1E,CAAC,GAAIyE,CAAC,KAAMA,CAAC,CAAC1F,MAAM,GAAG,CAAC,IAAI,OAAO0F,CAAC,CAACA,CAAC,CAAC1F,MAAM,GAAG,CAAC,CAAC,IAAI,QAAQ,IAAI0F,CAAC,CAAC7D,GAAG,CAAC,CAAC,EAAE6D,CAAC,CAAC;IAChF,SAASvE,CAACA,CAACuE,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;MAClB,IAAIA,CAAC,KAAK,MAAM,IAAIA,CAAC,KAAK,OAAO,EAC/B,MAAM1E,KAAK,CAAC,QAAQ,GAAG0E,CAAC,GAAG,6BAA6B,CAAC;MAC3DpJ,CAAC,CAACkJ,CAAC,CAAC,EAAElJ,CAAC,CAACmJ,CAAC,CAAC;MACV,MAAME,CAAC,GAAG,EAAE;QAAEC,CAAC,GAAG1C,CAAC,CAACyC,CAAC,CAAC;QAAE;UAAEM,IAAI,EAAEJ,CAAC;UAAEQ,IAAI,EAAEP;QAAG,CAAC,GAAGhF,CAAC,CAAC0E,CAAC,CAAC;MACpD,KAAK,IAAIW,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGV,CAAC,CAAC3F,MAAM,EAAEqG,EAAE,EAAE,EAAE;QACpC,MAAMC,EAAE,GAAGX,CAAC,CAACU,EAAE,CAAC;QAChB,IAAIG,EAAE,EAAEC,EAAE;QACV,QAAQ,OAAOH,EAAE;UACf,KAAK,QAAQ;YACX,KAAKE,EAAE,GAAGF,EAAE,EAAEE,EAAE,GAAG,CAAC,GAClBC,EAAE,GAAGV,CAAC,CAACS,EAAE,EAAE,GAAG,CAAC,EAAEV,CAAC,CAACW,EAAE,CAAC,EAAE,OAAOA,EAAE,IAAI,QAAQ,KAAKD,EAAE,IAAIvC,CAAC,CAACwC,EAAE,CAAC,CAAC;YAChE;UACF,KAAK,QAAQ;YACXb,CAAC,KAAK,MAAM,IAAI,OAAOI,EAAE,CAAC,CAAC,IAAI,QAAQ,IAAIF,CAAC,CAACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAED,CAAC,CAACvJ,CAAC,CAAC2I,WAAW,CAACoB,EAAE,CAAC,CAAC;YACzE;UACF,KAAK,QAAQ;YACX,KAAKE,EAAE,GAAGlK,CAAC,CAACkJ,IAAI,CAACc,EAAE,CAACnD,CAAC,CAAC,EAAEqD,EAAE,GAAG,CAAC,GAC5B,QAAQC,EAAE,GAAGV,CAAC,CAACS,EAAE,EAAE,GAAG,CAAC,EAAE,OAAOC,EAAE;cAChC,KAAK,QAAQ;gBACXD,EAAE,IAAIC,EAAE;gBACR;cACF,KAAK,QAAQ;gBACXX,CAAC,CAACW,EAAE,CAAC;gBACL;cACF,KAAK,QAAQ;gBACXD,EAAE,IAAIlK,CAAC,CAACkJ,IAAI,CAACiB,EAAE,CAACtD,CAAC,CAAC;YACtB;YACF;QACJ;MACF;MACA,IAAIiD,EAAE;MACN,OAAOA,EAAE,GAAGL,CAAC,CAAC,CAAC,CAAC,CAAC,GACfD,CAAC,CAACM,EAAE,CAAC;MACP,OAAOnF,CAAC,CAAC4E,CAAC,CAAC;IACb;IACA,SAASzE,CAACA,CAACsE,CAAC,EAAEC,CAAC,EAAE;MACfnJ,CAAC,CAACkJ,CAAC,CAAC,EAAElJ,CAAC,CAACmJ,CAAC,CAAC;MACV,MAAMC,CAAC,GAAG,EAAE;QAAEC,CAAC,GAAGzC,CAAC,CAACwC,CAAC,CAAC;QAAE;UAAEO,IAAI,EAAEL;QAAE,CAAC,GAAG9E,CAAC,CAAC0E,CAAC,CAAC;MAC1C,KAAK,IAAIM,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGL,CAAC,CAAC3F,MAAM,EAAEgG,EAAE,EAAE,EAAE;QACpC,MAAMI,EAAE,GAAGT,CAAC,CAACK,EAAE,CAAC;QAChB,IAAIK,EAAE,EAAEC,EAAE;QACV,QAAQ,OAAOF,EAAE;UACf,KAAK,QAAQ;YACX,KAAKC,EAAE,GAAGD,EAAE,EAAEC,EAAE,GAAG,CAAC,GAClBC,EAAE,GAAGR,CAAC,CAACO,EAAE,EAAE,GAAG,CAAC,EAAER,CAAC,CAACS,EAAE,CAAC,EAAE,OAAOA,EAAE,IAAI,QAAQ,KAAKD,EAAE,IAAIpC,CAAC,CAACqC,EAAE,CAAC,CAAC;YAChE;UACF,KAAK,QAAQ;YACXT,CAAC,CAACO,EAAE,CAAC;YACL;UACF,KAAK,QAAQ;YACXC,EAAE,GAAG/J,CAAC,CAACkJ,IAAI,CAACY,EAAE,CAACjD,CAAC,CAAC;YACjB,IAAIqD,EAAE,GAAG,CAAC;YACV,OAAOA,EAAE,GAAGH,EAAE,GACZ,QAAQC,EAAE,GAAGR,CAAC,CAACO,EAAE,GAAGG,EAAE,EAAE,GAAG,CAAC,EAAE,OAAOF,EAAE;cACrC,KAAK,QAAQ;gBACXT,CAAC,CAAC;kBAAE1C,CAAC,EAAEkB,CAAC,CAAC+B,EAAE,CAACjD,CAAC,EAAEqD,EAAE,EAAEA,EAAE,GAAGF,EAAE;gBAAE,CAAC,CAAC,EAAEE,EAAE,IAAIF,EAAE;gBACxC;cACF,KAAK,QAAQ;gBACXE,EAAE,IAAIjK,CAAC,CAAC2I,WAAW,CAACoB,EAAE,CAAC;gBACvB;cACF,KAAK,QAAQ;gBACXT,CAAC,CAACS,EAAE,CAAC;YACT;YACF;QACJ;MACF;MACA,IAAIP,CAAC;MACL,OAAOA,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,CAAC,GACdD,CAAC,CAACE,CAAC,CAAC;MACN,OAAO9E,CAAC,CAAC2E,CAAC,CAAC;IACb;IACA,MAAM7D,CAAC,GAAGA,CAAC2D,CAAC,EAAEC,CAAC,KAAK;QAClB,IAAIC,CAAC,GAAG,CAAC;QACT,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,CAAC3F,MAAM,IAAI0F,CAAC,GAAGE,CAAC,EAAEC,CAAC,EAAE,EAAE;UAC1C,MAAMC,CAAC,GAAGH,CAAC,CAACE,CAAC,CAAC;UACd,QAAQ,OAAOC,CAAC;YACd,KAAK,QAAQ;cAAE;gBACbF,CAAC,IAAIE,CAAC;gBACN;cACF;YACA,KAAK,QAAQ;cACX,MAAMC,CAAC,GAAGxJ,CAAC,CAAC2I,WAAW,CAACY,CAAC,CAAC;cAC1BF,CAAC,IAAIG,CAAC,EAAEL,CAAC,IAAIK,CAAC;cACd;YACF,KAAK,QAAQ;cACXL,CAAC,IAAIO,IAAI,CAACC,GAAG,CAAC5J,CAAC,CAACkJ,IAAI,CAACM,CAAC,CAAC3C,CAAC,CAAC,EAAEuC,CAAC,GAAGE,CAAC,CAAC;cACjC;UACJ;QACF;QACA,OAAOF,CAAC;MACV,CAAC;MAAE9B,CAAC,GAAGA,CAAC8B,CAAC,EAAEC,CAAC,KAAK,OAAOD,CAAC,IAAI,QAAQ,GAAG3D,CAAC,CAAC2D,CAAC,EAAEC,CAAC,CAAC,GAAGD,CAAC,CAACrF,GAAG,CAAEuF,CAAC,IAAK7D,CAAC,CAAC6D,CAAC,EAAED,CAAC,CAAC,CAAC;IACvE,SAAS9B,EAAEA,CAAC6B,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;MACnB,OAAOhG,CAAC,CAAC8F,CAAC,EAAE,CAACG,CAAC,EAAEC,CAAC,KAAK,OAAOD,CAAC,IAAI,QAAQ,IAAI,OAAOA,CAAC,CAAC1C,CAAC,IAAI,QAAQ,GAAG;QAAEA,CAAC,EAAEyC,CAAC,CAACzD,KAAK,CAACwD,CAAC,EAAEG,CAAC,EAAEA,CAAC,GAAGD,CAAC,CAAC1C,CAAC;MAAE,CAAC,GAAG0C,CAAC,CAAC;IAC3G;IACA,SAAS/B,CAACA,CAAC4B,CAAC,EAAE;MACZ,OAAO9F,CAAC,CAAC8F,CAAC,EAAGC,CAAC,IAAK;QACjB,QAAQ,OAAOA,CAAC;UACd,KAAK,QAAQ;YACX,IAAI,OAAOA,CAAC,CAACxC,CAAC,IAAI,QAAQ,EACxB,MAAMjC,KAAK,CAAC,wGAAwG,CAAC;YACvH,OAAOyE,CAAC,CAACxC,CAAC;UACZ;UACA,KAAK,QAAQ;YACX,OAAO;cAAEA,CAAC,EAAEwC;YAAE,CAAC;UACjB;UACA,KAAK,QAAQ;YACX,OAAOA,CAAC;QACZ;MACF,CAAC,CAAC;IACJ;IACA,SAASzB,EAAEA,CAACwB,CAAC,EAAE;MACb,OAAO9F,CAAC,CAAC8F,CAAC,EAAGC,CAAC,IAAK,OAAOA,CAAC,IAAI,QAAQ,IAAI,OAAOA,CAAC,CAACxC,CAAC,IAAI,QAAQ,GAAG;QAAEA,CAAC,EAAE5G,CAAC,CAAC2I,WAAW,CAACS,CAAC,CAACxC,CAAC;MAAE,CAAC,GAAGwC,CAAC,CAAC;IACpG;IACA,SAASxB,EAAEA,CAACuB,CAAC,EAAE;MACb,IAAIC,CAAC,GAAG,CAAC,CAAC;MACV,OAAOhG,CAAC,CAAC+F,CAAC,EAAGE,CAAC,IAAK;QACjB,OAAOA,CAAC,IAAI,QAAQ,IAAI,OAAOA,CAAC,CAACzC,CAAC,IAAI,QAAQ,KAAKwC,CAAC,GAAG,CAAC,CAAC,CAAC;MAC5D,CAAC,CAAC,EAAEA,CAAC;IACP;IACA,SAASe,EAAEA,CAAChB,CAAC,EAAE;MACb,OAAO;QACLiB,IAAI,EAAE,cAAc;QACpBC,GAAG,EAAE,uCAAuC;QAC5CC,IAAI,EAAE5F,CAAC;QACP6F,SAAS,EAAE7E,CAAC;QACZ8E,OAAO,EAAEvK,CAAC;QACV;AACR;AACA;AACA;AACA;QACQwK,MAAMA,CAACrB,CAAC,GAAG,EAAE,EAAE;UACb,IAAI,OAAOA,CAAC,IAAI,QAAQ,EACtB,MAAMzE,KAAK,CAAC,+BAA+B,CAAC;UAC9C,OAAOwE,CAAC,CAACsB,MAAM,CAACrB,CAAC,CAAC;QACpB,CAAC;QACD;AACR;QACQsB,KAAKA,CAACtB,CAAC,EAAEC,CAAC,EAAE;UACVpJ,CAAC,CAACoJ,CAAC,CAAC;UACJ,MAAMC,CAAC,GAAGH,CAAC,CAACwB,OAAO,CAACvB,CAAC,CAAC;UACtB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,CAAC5F,MAAM,EAAE8F,CAAC,EAAE,EAAE;YACjC,MAAMC,CAAC,GAAGH,CAAC,CAACE,CAAC,CAAC;YACd,QAAQ,OAAOC,CAAC;cACd,KAAK,QAAQ;gBACXF,CAAC,CAACsB,IAAI,CAACpB,CAAC,CAAC;gBACT;cACF,KAAK,QAAQ;gBACXF,CAAC,CAACuB,MAAM,CAACrB,CAAC,CAAC;gBACX;cACF,KAAK,QAAQ;gBACXF,CAAC,CAACwB,GAAG,CAAC/K,CAAC,CAACkJ,IAAI,CAACO,CAAC,CAAC5C,CAAC,CAAC,CAAC;gBAClB;YACJ;UACF;UACA,OAAO0C,CAAC,CAACyB,KAAK,CAAC,CAAC;QAClB,CAAC;QACDC,SAAS,EAAEpG,CAAC;QACZqG,OAAO,EAAEpG,CAAC;QACVqG,iBAAiB,EAAE1F,CAAC;QACpB2F,kBAAkB,EAAE9D,CAAC;QACrB+D,YAAY,EAAExD,EAAE;QAChByD,cAAcA,CAACjC,CAAC,EAAEC,CAAC,EAAE;UACnB,OAAO/B,EAAE,CAAC8B,CAAC,EAAEC,CAAC,EAAEF,CAAC,CAAC;QACpB,CAAC;QACDmC,eAAe,EAAE3D,EAAE;QACnB4D,MAAM,EAAEhE,CAAC;QACTiE,aAAaA,CAACpC,CAAC,EAAEC,CAAC,EAAE;UAClB,OAAO9B,CAAC,CAACD,EAAE,CAAC8B,CAAC,EAAEC,CAAC,EAAEF,CAAC,CAAC,CAAC;QACvB,CAAC;QACDsC,MAAM,EAAGrC,CAAC,IAAKA,CAAC,CAAC3F,MAAM,KAAK;MAC9B,CAAC;IACH;IACA1D,CAAC,CAAC2D,OAAO,GAAGyG,EAAE;EAChB,CAAC,CAACtB,EAAE,CAAC,CAAC,EAAEA,EAAE;AACZ;AACA,IAAI6C,EAAE,GAAG,CAAC,CAAC;EAAEC,EAAE;AACf,SAASC,EAAEA,CAAA,EAAG;EACZ,IAAID,EAAE,EAAE,OAAOD,EAAE;EACjBC,EAAE,GAAG,CAAC,EAAE/L,MAAM,CAACC,cAAc,CAAC6L,EAAE,EAAE,YAAY,EAAE;IAAErL,KAAK,EAAE,CAAC;EAAE,CAAC,CAAC;EAC9D,MAAMN,CAAC,GAAGgJ,EAAE,CAAC,CAAC;IAAE/I,CAAC,GAAGyI,EAAE,CAAC,CAAC;EACxB,SAASxI,CAACA,CAACmD,CAAC,EAAEC,CAAC,EAAE;IACf,OAAO;MACL;MACAsC,GAAG,EAAEvC,CAAC;MACN;MACAyI,SAASA,CAAA,EAAG;QACV,OAAOzI,CAAC,CAAC,CAAC,CAACK,MAAM;MACnB,CAAC;MACD;MACAqI,MAAMA,CAACtI,CAAC,EAAEkC,CAAC,EAAEmB,CAAC,EAAE;QACd,MAAMa,CAAC,GAAG1H,CAAC,CAAC2I,WAAW,CAACvF,CAAC,CAAC,CAAC,EAAEI,CAAC,CAAC;QAC/B,OAAOH,CAAC,CAAC,CAACqE,CAAC,EAAEhC,CAAC,CAAC,EAAEmB,CAAC,CAAC;MACrB,CAAC;MACDkF,MAAMA,CAACvI,CAAC,EAAEkC,CAAC,EAAEmB,CAAC,EAAE;QACd,MAAMa,CAAC,GAAG1H,CAAC,CAAC2I,WAAW,CAACvF,CAAC,CAAC,CAAC,EAAEI,CAAC,CAAC;QAC/B,OAAOH,CAAC,CAAC,CAACqE,CAAC,EAAE;UAAEd,CAAC,EAAElB;QAAE,CAAC,CAAC,EAAEmB,CAAC,CAAC;MAC5B,CAAC;MACD;MACA;MACA;MACA;MACAmF,KAAKA,CAACxI,CAAC,EAAE;QACPzD,CAAC,CAACmJ,MAAM,CAAC1F,CAAC,EAAE,CAACkC,CAAC,EAAEmB,CAAC,EAAEa,CAAC,KAAK;UACvB,QAAQ,OAAOhC,CAAC;YACd,KAAK,QAAQ;cACX,IAAI,CAACuG,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACvE,CAAC,EAAEhC,CAAC,CAAC;cACpC;YACF,KAAK,QAAQ;cACX,MAAMoC,CAAC,GAAG/H,CAAC,CAACkJ,IAAI,CAACvD,CAAC,CAACkB,CAAC,CAAC;cACrB,IAAI,CAACsF,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACxE,CAAC,EAAEI,CAAC,CAAC;UACxC;QACF,CAAC,CAAC;MACJ,CAAC;MACDmE,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE;IACZ,CAAC;EACH;EACA,OAAOR,EAAE,CAAChI,OAAO,GAAGzD,CAAC,EAAEA,CAAC,CAACkM,QAAQ,GAAG;IAAEC,IAAI,EAAE,CAAC;EAAE,CAAC,EAAEV,EAAE;AACtD;AACA,IAAIW,EAAE;AACN,SAASC,EAAEA,CAAA,EAAG;EACZ,OAAOD,EAAE,KAAKA,EAAE,GAAG,CAAC,EAAE,UAAStM,CAAC,EAAE;IAChC,IAAIC,CAAC,GAAGsI,EAAE,IAAIA,EAAE,CAACiE,eAAe,KAAK3M,MAAM,CAAC6K,MAAM,GAAG,UAAS7F,CAAC,EAAEC,CAAC,EAAEW,CAAC,EAAE6B,CAAC,EAAE;QACxEA,CAAC,KAAK,KAAK,CAAC,KAAKA,CAAC,GAAG7B,CAAC,CAAC,EAAE5F,MAAM,CAACC,cAAc,CAAC+E,CAAC,EAAEyC,CAAC,EAAE;UAAEnH,UAAU,EAAE,CAAC,CAAC;UAAEyF,GAAG,EAAE,SAAAA,CAAA,EAAW;YACrF,OAAOd,CAAC,CAACW,CAAC,CAAC;UACb;QAAE,CAAC,CAAC;MACN,CAAC,GAAG,UAASZ,CAAC,EAAEC,CAAC,EAAEW,CAAC,EAAE6B,CAAC,EAAE;QACvBA,CAAC,KAAK,KAAK,CAAC,KAAKA,CAAC,GAAG7B,CAAC,CAAC,EAAEZ,CAAC,CAACyC,CAAC,CAAC,GAAGxC,CAAC,CAACW,CAAC,CAAC;MACtC,CAAC,CAAC;MAAEvF,CAAC,GAAGqI,EAAE,IAAIA,EAAE,CAACkE,kBAAkB,KAAK5M,MAAM,CAAC6K,MAAM,GAAG,UAAS7F,CAAC,EAAEC,CAAC,EAAE;QACrEjF,MAAM,CAACC,cAAc,CAAC+E,CAAC,EAAE,SAAS,EAAE;UAAE1E,UAAU,EAAE,CAAC,CAAC;UAAEG,KAAK,EAAEwE;QAAE,CAAC,CAAC;MACnE,CAAC,GAAG,UAASD,CAAC,EAAEC,CAAC,EAAE;QACjBD,CAAC,CAAClB,OAAO,GAAGmB,CAAC;MACf,CAAC,CAAC;MAAEzB,CAAC,GAAGkF,EAAE,IAAIA,EAAE,CAACmE,YAAY,IAAI,UAAS7H,CAAC,EAAE;QAC3C,IAAIA,CAAC,IAAIA,CAAC,CAAC8H,UAAU,EAAE,OAAO9H,CAAC;QAC/B,IAAIC,CAAC,GAAG,CAAC,CAAC;QACV,IAAID,CAAC,IAAI,IAAI,EAAE,KAAK,IAAIY,CAAC,IAAIZ,CAAC,EAAEhF,MAAM,CAAC+M,cAAc,CAACC,IAAI,CAAChI,CAAC,EAAEY,CAAC,CAAC,IAAIxF,CAAC,CAAC6E,CAAC,EAAED,CAAC,EAAEY,CAAC,CAAC;QAC9E,OAAOvF,CAAC,CAAC4E,CAAC,EAAED,CAAC,CAAC,EAAEC,CAAC;MACnB,CAAC;MAAExB,CAAC,GAAGiF,EAAE,IAAIA,EAAE,CAACuE,eAAe,IAAI,UAASjI,CAAC,EAAE;QAC7C,OAAOA,CAAC,IAAIA,CAAC,CAAC8H,UAAU,GAAG9H,CAAC,GAAG;UAAElB,OAAO,EAAEkB;QAAE,CAAC;MAC/C,CAAC;IACDhF,MAAM,CAACC,cAAc,CAACE,CAAC,EAAE,YAAY,EAAE;MAAEM,KAAK,EAAE,CAAC;IAAE,CAAC,CAAC,EAAEN,CAAC,CAAC+M,IAAI,GAAG/M,CAAC,CAACgM,MAAM,GAAGhM,CAAC,CAAC+L,MAAM,GAAG,KAAK,CAAC;IAC5F,MAAMtI,CAAC,GAAGiF,EAAE,CAAC,CAAC;MAAE/C,CAAC,GAAGtC,CAAC,CAAC2F,EAAE,CAAC,CAAC,CAAC;MAAElC,CAAC,GAAGxD,CAAC,CAACuI,EAAE,CAAC,CAAC,CAAC;MAAElE,CAAC,GAAG;QAC5C+C,MAAMA,CAAC7F,CAAC,EAAE;UACR,OAAOA,CAAC;QACV,CAAC;QACDmI,QAAQA,CAACnI,CAAC,EAAE;UACV,OAAOA,CAAC;QACV,CAAC;QACD+F,OAAOA,CAAC/F,CAAC,EAAE;UACT,IAAI,OAAOA,CAAC,IAAI,QAAQ,EACtB,MAAMD,KAAK,CAAC,6BAA6B,GAAGC,CAAC,CAAC;UAChD,MAAMC,CAAC,GAAG,EAAE;UACZ,OAAO;YACL+F,IAAIA,CAACpF,CAAC,EAAE;cACN,IAAI6B,CAAC,GAAG7D,CAAC,CAACkF,WAAW,CAAC9D,CAAC,EAAEY,CAAC,CAAC;cAC3B,IAAI6B,CAAC,GAAGzC,CAAC,CAACnB,MAAM,EACd,MAAMkB,KAAK,CAAC,sCAAsC,CAAC;cACrDE,CAAC,CAACmB,IAAI,CAACpB,CAAC,CAACgB,KAAK,CAAC,CAAC,EAAEyB,CAAC,CAAC,CAAC,EAAEzC,CAAC,GAAGA,CAAC,CAACgB,KAAK,CAACyB,CAAC,CAAC;YACvC,CAAC;YACDwD,MAAMA,CAACrF,CAAC,EAAE;cACRX,CAAC,CAACmB,IAAI,CAACR,CAAC,CAAC;YACX,CAAC;YACDsF,GAAGA,CAACtF,CAAC,EAAE;cACLZ,CAAC,GAAGA,CAAC,CAACgB,KAAK,CAACpC,CAAC,CAACkF,WAAW,CAAC9D,CAAC,EAAEY,CAAC,CAAC,CAAC;YAClC,CAAC;YACDuF,KAAKA,CAAA,EAAG;cACN,OAAOlG,CAAC,CAACmI,IAAI,CAAC,EAAE,CAAC,GAAGpI,CAAC;YACvB;UACF,CAAC;QACH,CAAC;QACDgB,KAAK,EAAEF,CAAC,CAACsD;MACX,CAAC;MAAElB,CAAC,GAAGpC,CAAC,CAAChC,OAAO,CAACgE,CAAC,CAAC;MAAEjD,CAAC,GAAG7E,MAAM,CAACqN,MAAM,CAACrN,MAAM,CAACqN,MAAM,CAAC,CAAC,CAAC,EAAEnF,CAAC,CAAC,EAAE;QAAEoF,GAAG,EAAErG,CAAC,CAACnD;MAAQ,CAAC,CAAC;IAChF3D,CAAC,CAAC+M,IAAI,GAAGrI,CAAC,EAAE1E,CAAC,CAAC+L,MAAM,GAAG,CAAClH,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACpB,MAAM,KAAK,CAAC,GAAG,EAAE,GAAGmB,CAAC,KAAK,CAAC,GAAG,CAACC,CAAC,CAAC,GAAG,CAACD,CAAC,EAAEC,CAAC,CAAC,EAAE9E,CAAC,CAACgM,MAAM,GAAG,CAACnH,CAAC,EAAEC,CAAC,KAAKa,CAAC,CAACuD,IAAI,CAACpE,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE,GAAGD,CAAC,KAAK,CAAC,GAAG,CAAC;MAAEgC,CAAC,EAAE/B;IAAE,CAAC,CAAC,GAAG,CAACD,CAAC,EAAE;MAAEgC,CAAC,EAAE/B;IAAE,CAAC,CAAC;IAC/J,IAAIH,CAAC,GAAGqE,EAAE,CAAC,CAAC;IACZnJ,MAAM,CAACC,cAAc,CAACE,CAAC,EAAE,UAAU,EAAE;MAAEG,UAAU,EAAE,CAAC,CAAC;MAAEyF,GAAG,EAAE,SAAAA,CAAA,EAAW;QACrE,OAAOjB,CAAC,CAAChB,OAAO;MAClB;IAAE,CAAC,CAAC;EACN,CAAC,CAAC4E,EAAE,CAAC,CAAC,EAAEA,EAAE;AACZ;AACA,IAAI6E,EAAE;AACN,SAASC,EAAEA,CAAA,EAAG;EACZ,OAAOD,EAAE,KAAKA,EAAE,GAAG,CAAC,EAAE,UAASpN,CAAC,EAAE;IAChC,IAAIC,CAAC,GAAGgD,EAAE,IAAIA,EAAE,CAAC6J,eAAe,IAAI,UAASQ,CAAC,EAAE;MAC9C,OAAOA,CAAC,IAAIA,CAAC,CAACX,UAAU,GAAGW,CAAC,GAAG;QAC7B3J,OAAO,EAAE2J;MACX,CAAC;IACH,CAAC;IACDzN,MAAM,CAACC,cAAc,CAACE,CAAC,EAAE,YAAY,EAAE;MACrCM,KAAK,EAAE,CAAC;IACV,CAAC,CAAC,EAAEN,CAAC,CAACuN,MAAM,GAAGvN,CAAC,CAACwN,SAAS,GAAGxN,CAAC,CAACyN,QAAQ,GAAGzN,CAAC,CAAC0N,MAAM,GAAG1N,CAAC,CAAC2N,QAAQ,GAAG3N,CAAC,CAAC+M,IAAI,GAAG,KAAK,CAAC;IACjF,MAAM7M,CAAC,GAAGD,CAAC,CAACmD,EAAE,CAAC,CAAC,CAAC;MAAEC,CAAC,GAAGpD,CAAC,CAAC6D,EAAE,CAAC,CAAC,CAAC;MAAER,CAAC,GAAGY,EAAE,CAAC,CAAC;MAAET,CAAC,GAAGyE,EAAE,CAAC,CAAC;IAClD,SAASvC,CAACA,CAAC2H,CAAC,EAAEzG,CAAC,EAAE;MACf,IAAI,CAACyG,CAAC,EAAE,MAAM,IAAI1I,KAAK,CAACiC,CAAC,CAAC;IAC5B;IACA7G,CAAC,CAAC+M,IAAI,GAAG;MACP1C,IAAI,EAAE,OAAO;MACbC,GAAG,EAAE,iCAAiC;MACtCjG,UAAU,EAAEf,CAAC,CAACe,UAAU;MACxBC,WAAW,EAAEhB,CAAC,CAACgB,WAAW;MAC1BoG,MAAM,EAAG4C,CAAC,IAAKA,CAAC;MAChB5B,MAAM,EAAG4B,CAAC,IAAKA,CAAC,IAAI,IAAI;MACxBM,QAAQA,CAACN,CAAC,EAAE,CACZ,CAAC;MACDO,eAAe,EAAErG,CAAC;MAClBsG,YAAY,EAAEtE,CAAC;MACfgB,SAAS,EAAEf,CAAC;MACZkB,KAAK,EAAEjB,EAAE;MACTyB,iBAAiB,EAAErB,EAAE;MACrBoB,OAAO,EAAEnB,EAAE;MACXgE,YAAY,EAAEC,EAAE;MAChB/C,SAAS,EAAEgD,EAAE;MACb3C,cAAc,EAAEnB,EAAE;MAClBqB,MAAM,EAAExB,EAAE;MACVyB,aAAa,EAAEyC,EAAE;MACjB9F,qBAAqB,EAAE3E,CAAC,CAAC0E,YAAY,CAACC,qBAAqB;MAC3DC,cAAc,EAAE5E,CAAC,CAAC0E,YAAY,CAACE,cAAc;MAC7CC,SAAS,EAAE7E,CAAC,CAAC0E,YAAY,CAACG,SAAS;MACnC6F,mBAAmB,EAAEA,CAACb,CAAC,EAAEzG,CAAC,EAAEuH,CAAC,KAAKC,EAAE,CAAC,MAAM,CAAC,CAAC,EAAEf,CAAC,EAAEzG,CAAC,EAAEuH,CAAC,CAAC;MACvDE,yBAAyB,EAAGhB,CAAC,IAAKzN,MAAM,CAACqN,MAAM,CAACrN,MAAM,CAACqN,MAAM,CAAC,CAAC,CAAC,EAAElN,CAAC,CAAC+M,IAAI,CAAC,EAAE;QACzE9B,SAAS,EAAEA,CAACpE,CAAC,EAAEuH,CAAC,EAAEG,CAAC,KAAKF,EAAE,CAACf,CAAC,EAAEzG,CAAC,EAAEuH,CAAC,EAAEG,CAAC;MACvC,CAAC;IACH,CAAC;IACD,MAAMzH,CAAC,GAAIwG,CAAC,IAAKA,CAAC,GAAGA,CAAC,CAACvH,YAAY,CAAC,CAAC,GAAG,IAAI;IAC5C,SAAS4B,CAACA,CAAC2F,CAAC,EAAE;MACZ,OAAOA,CAAC,IAAI,OAAOA,CAAC,IAAI,QAAQ,IAAI,CAAC/J,KAAK,CAACC,OAAO,CAAC8J,CAAC,CAAC;IACvD;IACA,MAAMvF,CAAC,GAAIuF,CAAC,IAAK/J,KAAK,CAACC,OAAO,CAAC8J,CAAC,CAAC,GAAGA,CAAC,CAACzH,KAAK,CAAC,CAAC,GAAGyH,CAAC,KAAK,IAAI,IAAI,OAAOA,CAAC,IAAI,QAAQ,GAAGzN,MAAM,CAACqN,MAAM,CAAC,CAAC,CAAC,EAAEI,CAAC,CAAC,GAAGA,CAAC;MAAE5I,CAAC,GAAI4I,CAAC,IAAKA,CAAC,KAAKA,CAAC,CAAC3G,CAAC,IAAI,IAAI,IAAI2G,CAAC,CAAChK,CAAC,KAAK,KAAK,CAAC,CAAC;MAAEqB,CAAC,GAAI2I,CAAC,IAAKA,CAAC,KAAKA,CAAC,CAACzG,CAAC,IAAI,IAAI,IAAIyG,CAAC,CAACA,CAAC,KAAK,KAAK,CAAC,CAAC;IAC/M,SAASzI,CAACA,CAACyI,CAAC,EAAEzG,CAAC,EAAE;MACf,OAAOlB,CAAC,CAAC2H,CAAC,IAAI,IAAI,CAAC,EAAE,OAAOzG,CAAC,IAAI,QAAQ,IAAIlB,CAAC,CAACpC,KAAK,CAACC,OAAO,CAAC8J,CAAC,CAAC,EAAE,qCAAqC,CAAC,EAAE,CAACA,CAAC,GAAGA,CAAC,CAACzH,KAAK,CAAC,CAAC,EAAEqB,MAAM,CAACL,CAAC,EAAE,CAAC,CAAC,KAAKlB,CAAC,CAACgC,CAAC,CAAC2F,CAAC,CAAC,EAAE,sCAAsC,CAAC,EAAE,OAAO,CAACA,CAAC,GAAGzN,MAAM,CAACqN,MAAM,CAAC,CAAC,CAAC,EAAEI,CAAC,CAAC,EAAEzG,CAAC,CAAC,CAAC,EAAEyG,CAAC;IACrO;IACA,SAASxI,CAACA,CAACwI,CAAC,EAAEzG,CAAC,EAAEuH,CAAC,EAAE;MAClB,OAAO,OAAOvH,CAAC,IAAI,QAAQ,IAAIlB,CAAC,CAAC2H,CAAC,IAAI,IAAI,EAAE,8BAA8B,CAAC,EAAE3H,CAAC,CAACpC,KAAK,CAACC,OAAO,CAAC8J,CAAC,CAAC,EAAE,+CAA+C,CAAC,EAAE3H,CAAC,CAAC2H,CAAC,CAAC5J,MAAM,IAAImD,CAAC,EAAE,wCAAwC,CAAC,EAAEyG,CAAC,CAACpG,MAAM,CAACL,CAAC,EAAE,CAAC,EAAEuH,CAAC,CAAC,KAAKzI,CAAC,CAACgC,CAAC,CAAC2F,CAAC,CAAC,EAAE,iCAAiC,CAAC,EAAE3H,CAAC,CAAC2H,CAAC,CAACzG,CAAC,CAAC,KAAK,KAAK,CAAC,EAAE,oEAAoE,CAAC,EAAEyG,CAAC,CAACzG,CAAC,CAAC,GAAGuH,CAAC,CAAC,EAAEA,CAAC;IACzX;IACApO,CAAC,CAAC2N,QAAQ,GAAG,CAACL,CAAC,EAAEzG,CAAC,GAAG,CAAC,CAAC,KAAKvD,CAAC,CAACgB,WAAW,CAAC,CAAC,CAACmD,WAAW,CAAC6F,CAAC,EAAE,GAAG,EAAEzG,CAAC,CAAC,CAACjB,GAAG,CAAC,CAAC,EAAE5F,CAAC,CAAC0N,MAAM,GAAG,CAACJ,CAAC,EAAEzG,CAAC,KAAKvD,CAAC,CAACgB,WAAW,CAAC,CAAC,CAACoD,SAAS,CAAC4F,CAAC,EAAEzG,CAAC,CAAC,CAACjB,GAAG,CAAC,CAAC,EAAE5F,CAAC,CAACyN,QAAQ,GAAG,CAACH,CAAC,EAAEzG,CAAC,KAAKvD,CAAC,CAACgB,WAAW,CAAC,CAAC,CAACmD,WAAW,CAAC6F,CAAC,EAAE,GAAG,EAAEzG,CAAC,CAAC,CAACjB,GAAG,CAAC,CAAC,EAAE5F,CAAC,CAACwN,SAAS,GAAG,CAACF,CAAC,EAAEzG,CAAC,EAAEuH,CAAC,KAAK9K,CAAC,CAACgB,WAAW,CAAC,CAAC,CAACrD,EAAE,CAACqM,CAAC,EAAGiB,CAAC,IAAK;MACjQA,CAAC,CAACxJ,KAAK,CAAC,GAAG,EAAE8B,CAAC,CAAC,EAAE0H,CAAC,CAACxJ,KAAK,CAAC,GAAG,EAAEqJ,CAAC,CAAC;IAClC,CAAC,CAAC,CAACxI,GAAG,CAAC,CAAC,EAAE5F,CAAC,CAACuN,MAAM,GAAG,CAACD,CAAC,EAAEzG,CAAC,EAAEuH,CAAC,EAAEG,CAAC,GAAG,CAAC,CAAC,KAAKjL,CAAC,CAACgB,WAAW,CAAC,CAAC,CAACrD,EAAE,CAACqM,CAAC,EAAGkB,CAAC,IAAKnF,CAAC,CAACmF,CAAC,EAAE3H,CAAC,EAAEuH,CAAC,EAAEG,CAAC,CAAC,CAAC,CAAC3I,GAAG,CAAC,CAAC;IAC3F,MAAMH,CAAC,GAAGA,CAAC6H,CAAC,EAAEzG,CAAC,KAAKyG,CAAC,IAAI,IAAI,KAAK,OAAOzG,CAAC,IAAI,QAAQ,GAAGtD,KAAK,CAACC,OAAO,CAAC8J,CAAC,CAAC,GAAG,OAAOA,CAAC,IAAI,QAAQ,CAAC;MAAEhG,CAAC,GAAGA,CAACgG,CAAC,EAAEzG,CAAC,KAAKpB,CAAC,CAAC6H,CAAC,EAAEzG,CAAC,CAAC,GAAGyG,CAAC,CAACzG,CAAC,CAAC,GAAG,KAAK,CAAC;MAAEU,EAAE,GAAG,CAAC,CAAC;IACjJ,SAASC,CAACA,CAAC8F,CAAC,EAAE;MACZ,IAAIzG,CAAC,GAAGyG,CAAC,CAACP,IAAI,GAAGO,CAAC,CAACP,IAAI,GAAGO,CAAC;MAC3BzG,CAAC,CAACwD,IAAI,KAAK9C,EAAE,CAACV,CAAC,CAACwD,IAAI,CAAC,GAAGxD,CAAC,CAAC,EAAEA,CAAC,CAACyD,GAAG,KAAK/C,EAAE,CAACV,CAAC,CAACyD,GAAG,CAAC,GAAGzD,CAAC,CAAC;IACtD;IACA,MAAMe,EAAE,GAAI0F,CAAC,IAAK;MAChB,MAAMzG,CAAC,GAAGU,EAAE,CAAC+F,CAAC,CAAC;MACf,IAAIzG,CAAC,EAAE,OAAOA,CAAC;MACf,MAAMjC,KAAK,CAAC,gBAAgB,GAAG0I,CAAC,CAAC;IACnC,CAAC;IACD9F,CAAC,CAAC+E,EAAE,CAAC,CAAC,CAAC;IACP,MAAM1E,EAAE,GAAGA,CAACyF,CAAC,EAAEzG,CAAC,KAAKyG,CAAC,GAAGzG,CAAC;IAC1BW,CAAC,CAAC;MACA6C,IAAI,EAAE,QAAQ;MACdM,KAAK,EAAE9C,EAAE;MACTqD,OAAO,EAAErD,EAAE;MACX2D,MAAM,EAAG8B,CAAC,IAAK,CAACA,CAAC;MACjBrC,SAAS,EAAGqC,CAAC,IAAKA;IACpB,CAAC,CAAC;IACF,MAAMlD,EAAE,GAAIkD,CAAC,IAAKA,CAAC,IAAI,IAAI,GAAG,IAAI,GAAGA,CAAC,CAACnM,EAAE,GAAGyG,EAAE,CAAC0F,CAAC,CAACnM,EAAE,CAAC,GAAGmM,CAAC,CAACmB,EAAE,GAAGlH,EAAE,CAAC,cAAc,CAAC,GAAG+F,CAAC,CAACoB,GAAG,IAAI,IAAI,GAAGnH,EAAE,CAACoH,MAAM,GAAG,IAAI;MAAEvF,CAAC,GAAIkE,CAAC,IAAKA,CAAC,CAACmB,EAAE,GAAGnB,CAAC,CAACmB,EAAE,GAAGnB,CAAC,CAACoB,GAAG,IAAI,IAAI,GAAGpB,CAAC,CAACoB,GAAG,GAAGpB,CAAC,CAACrN,CAAC;MAAEoJ,CAAC,GAAGA,CAACiE,CAAC,EAAEzG,CAAC,EAAEuH,CAAC,EAAEG,CAAC,GAAG,CAAC,CAAC,KAAK;QAClM,MAAM,CAACC,CAAC,EAAEI,CAAC,CAAC,GAAG,OAAO/H,CAAC,IAAI,QAAQ,GAAG,CAACe,EAAE,CAACf,CAAC,CAAC,EAAEA,CAAC,CAAC,GAAG,CAACA,CAAC,EAAEA,CAAC,CAACwD,IAAI,CAAC;QAC9D,CAACkE,CAAC,IAAIC,CAAC,CAAC9C,MAAM,IAAI8C,CAAC,CAAC9C,MAAM,CAAC0C,CAAC,CAAC,KAAKQ,CAAC,KAAK,QAAQ,GAAGtB,CAAC,CAACvI,KAAK,CAAC,KAAK,EAAEqJ,CAAC,CAAC,GAAGQ,CAAC,KAAK,cAAc,GAAGtB,CAAC,CAACvI,KAAK,CAAC,IAAI,EAAEqJ,CAAC,CAAC,IAAId,CAAC,CAACvI,KAAK,CAAC,IAAI,EAAE6J,CAAC,CAAC,EAAEtB,CAAC,CAACvI,KAAK,CAAC,GAAG,EAAEqJ,CAAC,CAAC,CAAC,CAAC;MACvJ,CAAC;IACD,SAAS9E,CAACA,CAACgE,CAAC,EAAE;MACZ3H,CAAC,CAAC,OAAO2H,CAAC,IAAI,QAAQ,CAAC,EAAE3H,CAAC,CAAC2H,CAAC,IAAI,CAAC,CAAC,EAAE3H,CAAC,CAAC2H,CAAC,MAAM,CAAC,GAAGA,CAAC,CAAC,CAAC;IACtD;IACA,SAAS/D,CAACA,CAAC+D,CAAC,EAAE;MACZ,OAAOA,CAAC,IAAI,QAAQ,GAAGhE,CAAC,CAACgE,CAAC,CAAC,GAAG3H,CAAC,CAAC,OAAO2H,CAAC,IAAI,QAAQ,CAAC;IACvD;IACA,SAAS9D,CAACA,CAAC8D,CAAC,EAAE;MACZ,IAAIA,CAAC,KAAK,IAAI,EAAE;MAChB,MAAMzG,CAAC,GAAG,eAAgB,IAAIgI,GAAG,CAAC,CAAC;QAAET,CAAC,GAAG,eAAgB,IAAIS,GAAG,CAAC,CAAC;QAAEN,CAAC,GAAIK,CAAC,IAAK;UAC7E,IAAIE,CAAC,GAAG,CAAC,CAAC;YAAEC,CAAC,GAAG,CAAC,CAAC;UAClB,KAAK,IAAIpI,CAAC,IAAIiI,CAAC,EAAE;YACf,MAAMI,CAAC,GAAGJ,CAAC,CAACjI,CAAC,CAAC;YACd,IAAImI,CAAC,GAAG,CAAC,CAAC,EAAEnJ,CAAC,CAACgB,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,IAAI,EAAE,0BAA0B,GAAGA,CAAC,GAAG,GAAG,CAAC,EAAEA,CAAC,KAAK,GAAG,EAAE2C,CAAC,CAAC0F,CAAC,CAAC,EAAErJ,CAAC,CAAC,CAACkB,CAAC,CAACoI,GAAG,CAACD,CAAC,CAAC,CAAC,EAAEnI,CAAC,CAACqI,GAAG,CAACF,CAAC,CAAC,EAAErJ,CAAC,CAACiJ,CAAC,CAACtL,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,KACrN,IAAIqD,CAAC,KAAK,GAAG,EAAE2C,CAAC,CAAC0F,CAAC,CAAC,EAAErJ,CAAC,CAAC,CAACyI,CAAC,CAACa,GAAG,CAACD,CAAC,CAAC,CAAC,EAAEZ,CAAC,CAACc,GAAG,CAACF,CAAC,CAAC,EAAErJ,CAAC,CAACiJ,CAAC,CAACtB,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,KAC/D,IAAI3G,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,EAAE;cAC/ChB,CAAC,CAAC,CAACoJ,CAAC,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;cACb,MAAMI,CAAC,GAAG/E,EAAE,CAACwE,CAAC,CAAC;cACfjJ,CAAC,CAACwJ,CAAC,EAAE,sBAAsB,CAAC,EAAEA,CAAC,CAACrB,YAAY,IAAIqB,CAAC,CAACrB,YAAY,CAAC1E,CAAC,CAACwF,CAAC,CAAC,CAAC;YACtE;UACF;UACAjJ,CAAC,CAAC,CAACmJ,CAAC,CAAC;QACP,CAAC;QAAEN,CAAC,GAAGA,CAACI,CAAC,EAAEE,CAAC,EAAEC,CAAC,KAAK;UAClB,IAAI,CAACxL,KAAK,CAACC,OAAO,CAACoL,CAAC,CAAC,EAAE,MAAMhK,KAAK,CAAC,2BAA2B,CAAC;UAC/D,IAAIgK,CAAC,CAAClL,MAAM,KAAK,CAAC,EAAE,MAAMkB,KAAK,CAAC,eAAe,CAAC;UAChDkK,CAAC,IAAIvF,CAAC,CAACqF,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,IAAIjI,CAAC,GAAG,CAAC;YAAEqI,CAAC,GAAG,CAAC;YAAEG,CAAC,GAAG,CAAC;UACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,CAAC,CAAClL,MAAM,EAAE0L,CAAC,EAAE,EAAE;YACjC,MAAMC,CAAC,GAAGT,CAAC,CAACQ,CAAC,CAAC;YACd,IAAIzJ,CAAC,CAAC0J,CAAC,IAAI,IAAI,CAAC,EAAE9L,KAAK,CAACC,OAAO,CAAC6L,CAAC,CAAC,EAAE;cAClC,MAAMC,CAAC,GAAGd,CAAC,CAACa,CAAC,EAAE,CAAC,CAAC,CAAC;cAClB,IAAIL,CAAC,EAAE;gBACL,MAAMO,CAAC,GAAG,OAAOJ,CAAC;kBAAEK,CAAC,GAAG,OAAOF,CAAC;gBAChCC,CAAC,KAAKC,CAAC,GAAG7J,CAAC,CAACwJ,CAAC,GAAGG,CAAC,EAAE,+BAA+B,CAAC,GAAG3J,CAAC,CAAC4J,CAAC,KAAK,QAAQ,IAAIC,CAAC,KAAK,QAAQ,CAAC;cAC3F;cACAL,CAAC,GAAGG,CAAC,EAAEN,CAAC,EAAE,EAAErI,CAAC,GAAG,CAAC;YACnB,CAAC,MAAM,OAAO0I,CAAC,IAAI,QAAQ,IAAI1J,CAAC,CAACgB,CAAC,KAAK,CAAC,EAAG,6BAA4BA,CAAE,EAAC,CAAC,EAAE4H,CAAC,CAACc,CAAC,CAAC,EAAE1I,CAAC,GAAG,CAAC,KAAKhB,CAAC,CAACgB,CAAC,KAAK,CAAC,CAAC,EAAE4C,CAAC,CAAC8F,CAAC,CAAC,EAAE1J,CAAC,CAACrC,CAAC,CAACmB,eAAe,CAAC4K,CAAC,CAAC,EAAE,kBAAkB,CAAC,EAAE1I,CAAC,GAAG,CAAC,CAAC;UACpK;UACA,OAAOhB,CAAC,CAACqJ,CAAC,KAAK,CAAC,EAAE,mDAAmD,CAAC,EAAErJ,CAAC,CAACgB,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAK,CAAC,CAAC,EAAEiI,CAAC,CAAC,CAAC,CAAC;QACrG,CAAC;MACDJ,CAAC,CAAClB,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE3H,CAAC,CAACkB,CAAC,CAAC4I,IAAI,KAAKrB,CAAC,CAACqB,IAAI,EAAE,kCAAkC,CAAC;MAClE,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/H,CAAC,CAAC4I,IAAI,EAAEb,CAAC,EAAE,EAAEjJ,CAAC,CAACkB,CAAC,CAACoI,GAAG,CAACL,CAAC,CAAC,CAAC,EAAEjJ,CAAC,CAACyI,CAAC,CAACa,GAAG,CAACL,CAAC,CAAC,CAAC;IAC3D;IACA,SAASnF,CAACA,CAAC6D,CAAC,EAAE;MACZ,IAAIzG,CAAC,GAAG,CAAC;QAAEuH,CAAC,GAAG,EAAE;MACjB,MAAMG,CAAC,GAAGjL,CAAC,CAACgB,WAAW,CAAC,CAAC;MACzB,OAAOiK,CAAC,CAAClH,SAAS,CAACiG,CAAC,EAAE,CAACkB,CAAC,EAAEI,CAAC,KAAK;QAC9B,MAAME,CAAC,GAAG1E,EAAE,CAACoE,CAAC,CAAC;QACf,IAAIM,CAAC,EAAE;UACL,MAAMnI,CAAC,GAAGyC,CAAC,CAACoF,CAAC,CAAC;UACdnF,CAAC,CAACuF,CAAC,EAAEE,CAAC,EAAEA,CAAC,CAACtE,SAAS,GAAGsE,CAAC,CAACtE,SAAS,CAAC7D,CAAC,CAAC,GAAGA,CAAC,CAAC;QAC3C;QACA,KAAK,MAAMA,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,IAAI6H,CAAC,CAAC7H,CAAC,CAAC,KAAK,KAAK,CAAC,EAAE;UACzD,MAAMqI,CAAC,GAAGrI,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,IAAIoI,CAAC,GAAGP,CAAC,CAAC7H,CAAC,CAAC,EAAEyH,CAAC,CAACW,CAAC,CAAC,IAAI,IAAI,KAAKX,CAAC,CAACW,CAAC,CAAC,GAAGlI,CAAC,EAAE,CAAC,EAAEuH,CAAC,CAACW,CAAC,CAAC,IAAIP,CAAC,CAAC7H,CAAC,CAAC;UACxFiI,CAAC,CAAC7J,KAAK,CAAC4B,CAAC,EAAEqI,CAAC,CAAC;QACf;QACA,IAAID,CAAC;MACP,CAAC,CAAC,EAAER,CAAC,CAAC3I,GAAG,CAAC,CAAC;IACb;IACA,SAAS8D,EAAEA,CAAC4D,CAAC,EAAEzG,CAAC,EAAE;MAChB,IAAI2C,CAAC,CAAC3C,CAAC,CAAC,EAAEA,CAAC,KAAK,IAAI,EAAE,OAAOyG,CAAC;MAC9B,MAAMc,CAAC,GAAG,EAAE;MACZ,OAAO,SAASG,CAACA,CAACC,CAAC,EAAEI,CAAC,EAAE;QACtB,IAAIE,CAAC,GAAGN,CAAC;UAAEO,CAAC,GAAG,CAAC;UAAEpI,CAAC,GAAG;YACpB+I,IAAI,EAAElB;UACR,CAAC;UAAEQ,CAAC,GAAG,CAAC;UAAEG,CAAC,GAAGxI,CAAC;UAAEyI,CAAC,GAAG,MAAM;QAC3B,SAASC,CAACA,CAAA,EAAG;UACX,OAAOL,CAAC,GAAGD,CAAC,EAAEC,CAAC,EAAE,EAAE;YACjB,IAAIM,CAAC,GAAGV,CAAC,CAACI,CAAC,CAAC;YACZ,OAAOM,CAAC,IAAI,QAAQ,KAAK3J,CAAC,CAACF,CAAC,CAAC0J,CAAC,EAAEC,CAAC,CAAC,CAAC,EAAED,CAAC,GAAGA,CAAC,CAACC,CAAC,CAAC,GAAGrH,CAAC,CAACoH,CAAC,CAACC,CAAC,CAAC,CAAC,EAAEA,CAAC,GAAGE,CAAC,CAAC;UACjE;QACF;QACA,OAAOP,CAAC,GAAGH,CAAC,CAAClL,MAAM,EAAEqL,CAAC,EAAE,EAAE;UACxB,MAAMO,CAAC,GAAGV,CAAC,CAACG,CAAC,CAAC;UACd,IAAIxL,KAAK,CAACC,OAAO,CAAC8L,CAAC,CAAC,EAAE;YACpB,MAAMC,CAAC,GAAGhB,CAAC,CAACO,CAAC,EAAEQ,CAAC,CAAC;YACjBC,CAAC,KAAKT,CAAC,IAAIS,CAAC,KAAK,KAAK,CAAC,KAAKF,CAAC,CAAC,CAAC,EAAEP,CAAC,GAAGK,CAAC,CAACC,CAAC,CAAC,GAAGG,CAAC,CAAC;UAChD,CAAC,MAAM,IAAI,OAAOD,CAAC,IAAI,QAAQ,EAAE;YAC/BA,CAAC,CAACzI,CAAC,IAAI,IAAI,IAAIwI,CAAC,CAAC,CAAC,EAAEP,CAAC,GAAGhK,CAAC,CAACqK,CAAC,EAAEC,CAAC,EAAEhB,CAAC,CAACkB,CAAC,CAACzI,CAAC,CAAC,CAAC,IAAIyI,CAAC,CAAChC,CAAC,KAAK,KAAK,CAAC,KAAK+B,CAAC,CAAC,CAAC,EAAEP,CAAC,GAAGhK,CAAC,CAACqK,CAAC,EAAEC,CAAC,EAAEE,CAAC,CAAChC,CAAC,CAAC,CAAC;YACpF,MAAMiC,CAAC,GAAGnF,EAAE,CAACkF,CAAC,CAAC;YACf,IAAIC,CAAC,EAAEF,CAAC,CAAC,CAAC,EAAEP,CAAC,GAAGK,CAAC,CAACC,CAAC,CAAC,GAAGG,CAAC,CAAC5E,KAAK,CAACmE,CAAC,EAAE1F,CAAC,CAACkG,CAAC,CAAC,CAAC,CAAC,KACnC,IAAIA,CAAC,CAACrP,CAAC,KAAK,KAAK,CAAC,EAAE,MAAM2E,KAAK,CAAC,UAAU,GAAG0K,CAAC,CAACnO,EAAE,GAAG,YAAY,CAAC;UACxE,CAAC,MAAM2N,CAAC,GAAGxH,CAAC,CAACwH,CAAC,EAAEQ,CAAC,CAAC;QACpB;QACA,OAAO3I,CAAC,CAAC+I,IAAI;MACf,CAAC,CAACpC,CAAC,GAAG,SAASiB,CAACA,CAACC,CAAC,EAAEI,CAAC,EAAE;QACrB,MAAME,CAAC,GAAG,EAAE;QACZ,IAAIC,CAAC,GAAG,CAAC;QACT,OAAOA,CAAC,GAAGH,CAAC,CAAClL,MAAM,EAAEqL,CAAC,EAAE,EAAE;UACxB,MAAMK,CAAC,GAAGR,CAAC,CAACG,CAAC,CAAC;UACd,IAAIxL,KAAK,CAACC,OAAO,CAAC4L,CAAC,CAAC,EAAE;UACtB,OAAOA,CAAC,IAAI,QAAQ,KAAKN,CAAC,CAAC7I,IAAI,CAACuI,CAAC,CAAC,EAAEA,CAAC,GAAGlH,CAAC,CAACkH,CAAC,EAAEY,CAAC,CAAC,CAAC;QAClD;QACA,KAAK,IAAIA,CAAC,GAAGR,CAAC,CAAClL,MAAM,GAAG,CAAC,EAAE0L,CAAC,IAAIL,CAAC,EAAEK,CAAC,EAAE,EAAEZ,CAAC,GAAGD,CAAC,CAACC,CAAC,EAAEI,CAAC,CAACQ,CAAC,CAAC,CAAC;QACtD,KAAK,EAAEL,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UACrB,MAAMK,CAAC,GAAGR,CAAC,CAACG,CAAC,CAAC;UACd,IAAI,OAAOK,CAAC,IAAI,QAAQ,EAAE;YACxB,MAAMC,CAAC,GAAGP,CAAC,CAACvJ,GAAG,CAAC,CAAC;YACjBiJ,CAAC,GAAGA,CAAC,KAAKlH,CAAC,CAAC+H,CAAC,EAAED,CAAC,CAAC,GAAGC,CAAC,GAAGb,CAAC,KAAK,KAAK,CAAC,GAAG3J,CAAC,CAACwK,CAAC,EAAED,CAAC,CAAC,IAAIJ,CAAC,GAAGI,CAAC,EAAED,CAAC,GAAGX,CAAC,EAAE,CAAC7H,CAAC,GAAGoB,CAAC,CAACpB,CAAC,GAAG0I,CAAC,CAAC,EAAEL,CAAC,CAAC,GAAGG,CAAC,EAAExI,CAAC,CAAC;UAC3F,CAAC,MAAMjC,CAAC,CAAC0K,CAAC,CAAC,KAAKzJ,CAAC,CAAC6I,CAAC,KAAK,KAAK,CAAC,EAAE,oCAAoC,CAAC,EAAEY,CAAC,CAACzI,CAAC,IAAI,IAAI,KAAKyH,CAAC,CAACgB,CAAC,CAACzI,CAAC,CAAC,GAAG6H,CAAC,CAAC,EAAEA,CAAC,GAAG,KAAK,CAAC,CAAC;QACjH;QACA,IAAI7H,CAAC,EAAEqI,CAAC,EAAEG,CAAC;QACX,OAAOX,CAAC;MACV,CAAC,CAAClB,CAAC,EAAEzG,CAAC,CAAC,EAAEA,CAAC,CAAC;IACb;IACA,SAASiD,EAAEA,CAACwD,CAAC,EAAEzG,CAAC,EAAE;MAChByG,CAAC,GAAGA,CAAC,CAACzH,KAAK,CAAC,CAAC,EAAE2D,CAAC,CAAC3C,CAAC,CAAC;MACnB,MAAMuH,CAAC,GAAG9K,CAAC,CAACe,UAAU,CAACwC,CAAC,CAAC;MACzB,IAAI0H,CAAC;QAAEC,CAAC;QAAEI,CAAC,GAAG,CAAC,CAAC;MAChB,MAAME,CAAC,GAAG,EAAE;MACZ,KAAK,IAAInI,CAAC,GAAG,CAAC,GAAIA,CAAC,EAAE,EAAE;QACrB,MAAMqI,CAAC,GAAG1B,CAAC,CAAC3G,CAAC,CAAC;UAAEwI,CAAC,GAAGf,CAAC,CAACrI,YAAY,CAAC,CAAC;QACpC,IAAIoJ,CAAC,KAAKA,CAAC,CAAC7L,CAAC,KAAK,KAAK,CAAC,GAAGsL,CAAC,GAAG,CAAC,CAAC,GAAGO,CAAC,CAACxI,CAAC,IAAI,IAAI,KAAKiI,CAAC,GAAG,CAAC,CAAC,EAAEL,CAAC,GAAGY,CAAC,CAACxI,CAAC,EAAE6H,CAAC,GAAG7H,CAAC,CAAC,CAAC,EAAEA,CAAC,IAAI2G,CAAC,CAAC5J,MAAM,EAAE;QAC7F,IAAI0L,CAAC,GAAG,CAAC;QACT,MAAMC,CAAC,GAAG/L,CAAC,CAACc,QAAQ,CAACgK,CAAC,EAAE,KAAK,CAAC,EAAE,CAACmB,CAAC,EAAEC,CAAC,KAAK;UACxC9K,CAAC,CAAC8K,CAAC,CAAC,IAAIJ,CAAC,EAAE;QACb,CAAC,CAAC;QACFN,CAAC,CAACpJ,OAAO,CAAC2J,CAAC,CAAC;QACZ,MAAMC,CAAC,GAAGD,CAAC,CAACL,CAAC,CAAC;QACd,IAAI,OAAOA,CAAC,IAAI,QAAQ,KAAK1B,CAAC,CAAC3G,CAAC,CAAC,IAAIyI,CAAC,CAAC,EAAE,CAACE,CAAC,EAAE;MAC/C;MACA,IAAIR,CAAC,CAACa,OAAO,CAAEhJ,CAAC,IAAKA,CAAC,CAACmB,GAAG,CAAC,CAAC,CAAC,EAAE8G,CAAC,EAAE,OAAO,IAAI;MAC7C,MAAMG,CAAC,GAAGA,CAAA,KAAM;QACd,IAAIpI,CAAC,GAAG,CAAC;QACT,IAAI4H,CAAC,IAAI,IAAI,EAAE;UACb,MAAMS,CAAC,GAAGZ,CAAC,CAAC5I,OAAO,CAAC,CAAC;UACrBmB,CAAC,GAAGqI,CAAC,CAACtL,MAAM,EAAE4J,CAAC,GAAG0B,CAAC,CAACY,MAAM,CAACtC,CAAC,CAACzH,KAAK,CAAC2I,CAAC,CAAC,CAAC;QACxC;QACA,OAAO7H,CAAC,GAAG2G,CAAC,CAAC5J,MAAM,EAAEiD,CAAC,EAAE,EAAE;UACxB,MAAMqI,CAAC,GAAG1B,CAAC,CAAC3G,CAAC,CAAC;YAAEwI,CAAC,GAAGrI,CAAC,CAACsH,CAAC,CAAC;YAAEgB,CAAC,GAAGhF,EAAE,CAAC+E,CAAC,CAAC;UACnC,IAAIC,CAAC,EAAE;YACL,MAAMG,CAAC,GAAGnG,CAAC,CAAC+F,CAAC,CAAC;YACdC,CAAC,CAACjE,iBAAiB,KAAKmC,CAAC,CAAC3G,CAAC,CAAC,GAAGyI,CAAC,CAACjE,iBAAiB,CAACmC,CAAC,CAAC3G,CAAC,CAAC,EAAE4I,CAAC,CAAC,CAAC;YAC5D;UACF;UACA,IAAIF,CAAC,GAAG,CAAC;UACT,MAAMC,CAAC,GAAGhM,CAAC,CAACc,QAAQ,CAACgK,CAAC,EAAE,CAACmB,CAAC,EAAEC,CAAC,KAAK7K,CAAC,CAAC6K,CAAC,CAAC,GAAG,EAAED,CAAC,GAAGF,CAAC,CAAC,GAAGE,CAAC,GAAGF,CAAC,EAAE,CAACE,CAAC,EAAEC,CAAC,KAAK;YACnE7K,CAAC,CAAC6K,CAAC,CAAC,IAAIH,CAAC,EAAE;UACb,CAAC,CAAC,CAACL,CAAC,CAAC;UACL,IAAI,OAAOA,CAAC,IAAI,QAAQ,KAAK1B,CAAC,CAAC3G,CAAC,CAAC,IAAI0I,CAAC,CAAC,EAAE,CAACC,CAAC,EAAE;QAC/C;MACF,CAAC;MACD,OAAOf,CAAC,IAAI,IAAI,GAAGH,CAAC,CAACxH,QAAQ,CAAC,IAAI,EAAGD,CAAC,IAAK;QACzCA,CAAC,KAAK4H,CAAC,IAAIQ,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,EAAEzB,CAAC;IACb;IACA,SAASvD,EAAEA,CAACuD,CAAC,EAAEzG,CAAC,EAAE;MAChB,IAAI2C,CAAC,CAAC8D,CAAC,CAAC,EAAE9D,CAAC,CAAC3C,CAAC,CAAC,EAAEyG,CAAC,IAAI,IAAI,EAAE,OAAOzG,CAAC;MACnC,IAAIA,CAAC,IAAI,IAAI,EAAE,OAAOyG,CAAC;MACvB,IAAIc,CAAC,GAAG,CAAC;MACT,MAAMG,CAAC,GAAGjL,CAAC,CAACe,UAAU,CAACiJ,CAAC,CAAC;QAAEkB,CAAC,GAAGlL,CAAC,CAACe,UAAU,CAACwC,CAAC,CAAC;QAAE+H,CAAC,GAAGtL,CAAC,CAACgB,WAAW,CAAC,CAAC;QAAEwK,CAAC,GAAG,EAAE;QAAEC,CAAC,GAAG,EAAE;QAAEpI,CAAC,GAAG,EAAE;QAAEqI,CAAC,GAAG,EAAE;QAAEG,CAAC,GAAG,EAAE;QAAEC,CAAC,GAAG,EAAE;QAAEC,CAAC,GAAG,eAAgB,IAAIR,GAAG,CAAC,CAAC;MAClJN,CAAC,CAAC/H,QAAQ,CAAC,IAAI,EAAG+I,CAAC,IAAK;QACtBA,CAAC,CAAC5I,CAAC,IAAI,IAAI,KAAKA,CAAC,CAAC4I,CAAC,CAAC5I,CAAC,CAAC,GAAG4H,CAAC,CAAClI,KAAK,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC,EAAEmI,CAAC,CAAChI,QAAQ,CAAC,IAAI,EAAG+I,CAAC,IAAK;QAC1BA,CAAC,CAAC1I,CAAC,IAAI,IAAI,KAAKmI,CAAC,CAACO,CAAC,CAAC1I,CAAC,CAAC,GAAG2H,CAAC,CAACnI,KAAK,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC;MACF,MAAMiJ,CAAC,GAAGhM,CAAC,CAACgB,WAAW,CAAC,CAAC;MACzB,OAAO,SAASiL,CAACA,CAACC,CAAC,EAAEK,EAAE,EAAEC,EAAE,EAAEC,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;QAC9CxK,CAAC,CAACkK,EAAE,IAAIC,EAAE,CAAC;QACX,MAAMM,EAAE,GAAGtJ,CAAC,CAAC+I,EAAE,CAAC;UAAEQ,EAAE,GAAGvJ,CAAC,CAACgJ,EAAE,CAAC;UAAEQ,EAAE,GAAG,CAAC,CAACD,EAAE,IAAIA,EAAE,CAAC/M,CAAC,KAAK,KAAK,CAAC;UAAEiN,EAAE,GAAG,CAAC,CAACH,EAAE,IAAIA,EAAE,CAAC9C,CAAC,KAAK,KAAK,CAAC;UAAEkD,EAAE,GAAGJ,EAAE,GAAGA,EAAE,CAACvJ,CAAC,GAAG,IAAI;UAAE4J,EAAE,GAAGJ,EAAE,GAAGA,EAAE,CAAC1J,CAAC,GAAG,IAAI;UAAE+J,EAAE,GAAG,CAACT,EAAE,IAAIK,EAAE,KAAKG,EAAE,IAAI,IAAI;QACrK,IAAIA,EAAE,IAAI,IAAI,EAAEV,CAAC,GAAGf,CAAC,CAACyB,EAAE,CAAC,EAAEP,EAAE,GAAGnB,CAAC,CAAC0B,EAAE,CAAC,GAAG,IAAInN,CAAC,CAACiB,WAAW,CAAC,CAAC,CAAC,KACvD,IAAI8L,EAAE,IAAIA,EAAE,CAAC/M,CAAC,KAAK,KAAK,CAAC,EAAEyM,CAAC,GAAG,IAAI,CAAC,KACpC;UACH,MAAMY,CAAC,GAAG7J,CAAC,CAACiJ,CAAC,CAAC;UACdY,CAAC,IAAIA,CAAC,CAAC9J,CAAC,IAAI,IAAI,KAAKkJ,CAAC,GAAG,IAAI,CAAC;QAChC;QACA,MAAMa,CAAC,GAAG9J,CAAC,CAACiJ,CAAC,CAAC;QACd,IAAIS,EAAE,IAAI,IAAI;UAAE,IAAIhB,CAAC,GAAG7I,CAAC,CAAC6J,EAAE,CAAC,EAAEL,EAAE,GAAGrB,CAAC,CAAC0B,EAAE,CAAC,GAAG,IAAIlN,CAAC,CAACiB,WAAW,CAAC,CAAC,EAAEmM,EAAE,EAAET,EAAE,IAAI,CAACK,EAAE,IAAIH,EAAE,CAACpL,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,KAC/F;YACH,MAAM4L,CAAC,GAAGxB,CAAC,CAACqB,EAAE,CAAC,GAAGpC,CAAC,EAAE;YACrB8B,EAAE,CAACnL,KAAK,CAAC,GAAG,EAAE4L,CAAC,CAAC;UAClB;QAAC,OACI,IAAIP,EAAE,IAAIA,EAAE,CAAC9C,CAAC,KAAK,KAAK,CAAC,EAAEkC,CAAC,GAAG,IAAI,CAAC,KACpC;UACH,MAAMmB,CAAC,GAAG7J,CAAC,CAAC0I,CAAC,CAAC;UACdmB,CAAC,IAAIA,CAAC,CAAChK,CAAC,IAAI,IAAI,KAAK6I,CAAC,GAAG,IAAI,CAAC;QAChC;QACA,IAAIqB,CAAC;QACLN,EAAE,IAAI5K,CAAC,CAACqK,EAAE,KAAK,KAAK,CAAC,CAAC,EAAEa,CAAC,GAAGT,EAAE,CAAC9C,CAAC,IAAIuD,CAAC,GAAGb,EAAE;QAC1C,MAAMc,CAAC,GAAG,CAACL,EAAE,IAAI,IAAI,GAAG,CAACF,EAAE,IAAIN,EAAE,IAAIK,EAAE,GAAGO,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI,GAAGX,EAAE,CAACnK,YAAY,CAAC,CAAC;QAClF,IAAI0K,EAAE,IAAI,IAAI,EAAE;UACd,IAAI,EAAET,EAAE,KAAK,KAAK,CAAC,IAAIO,EAAE,CAAC,EAAE;YAC1B,MAAMI,CAAC,GAAGH,EAAE,IAAI,IAAI,GAAGrB,CAAC,CAACqB,EAAE,CAAC,GAAGpC,CAAC,EAAE;YAClCgB,CAAC,CAACqB,EAAE,CAAC,GAAGE,CAAC,EAAER,EAAE,CAACpL,KAAK,CAAC,GAAG,EAAE4L,CAAC,CAAC;UAC7B;QACF,CAAC,MAAML,EAAE,KAAKC,EAAE,IAAIP,EAAE,KAAK,KAAK,CAAC,KAAKK,EAAE,CAAC/M,CAAC,EAAE6M,EAAE,CAACpL,KAAK,CAAC,GAAG,EAAEsL,EAAE,CAAC/M,CAAC,CAAC,CAAC,CAAC;QACjE,MAAMyN,CAAC,GAAGL,EAAE,GAAG,IAAI,GAAGtG,EAAE,CAACgG,EAAE,CAAC;UAAEY,CAAC,GAAG5G,EAAE,CAACwG,CAAC,CAAC;QACvC,IAAI,CAACG,CAAC,IAAIC,CAAC,MAAMD,CAAC,IAAIA,CAAC,CAAC1G,IAAI,EAAE2G,CAAC,IAAIA,CAAC,CAAC3G,IAAI,CAAC,EAAE0G,CAAC,IAAIC,CAAC,EAAE;UAClDrL,CAAC,CAACoL,CAAC,KAAKC,CAAC,CAAC;UACV,MAAML,CAAC,GAAGvH,CAAC,CAACgH,EAAE,CAAC;YAAEa,CAAC,GAAG7H,CAAC,CAACwH,CAAC,CAAC;YAAEM,EAAE,GAAGH,CAAC,CAAC7F,OAAO,CAACyF,CAAC,EAAEM,CAAC,CAAC;UAC/C5H,CAAC,CAAC6G,EAAE,EAAEa,CAAC,EAAEG,EAAE,CAAC,EAAE7B,CAAC,CAACH,GAAG,CAAC0B,CAAC,CAAC;QACxB,CAAC,MAAMG,CAAC,GAAG1H,CAAC,CAAC6G,EAAE,EAAEa,CAAC,EAAE3H,CAAC,CAACgH,EAAE,CAAC,CAAC,GAAGY,CAAC,KAAK3H,CAAC,CAAC6G,EAAE,EAAEc,CAAC,EAAE5H,CAAC,CAACwH,CAAC,CAAC,CAAC,EAAEvB,CAAC,CAACH,GAAG,CAAC0B,CAAC,CAAC,CAAC;QAC5D,MAAMO,CAAC,GAAG,OAAON,CAAC,IAAI,QAAQ,IAAIA,CAAC,IAAI,IAAI;QAC3C,IAAIO,CAAC,GAAG,CAAC,CAAC;UAAEC,CAAC,GAAG,CAAC;UAAEC,EAAE,GAAG,CAAC;UAAEC,EAAE,GAAG,CAAC;UAAEC,EAAE,GAAG,CAAC;UAAEC,EAAE,GAAG,CAAC;QACjD,MAAMC,EAAE,GAAGpO,CAAC,CAACc,QAAQ,CAAC2L,CAAC,EAAE,CAACY,CAAC,EAAEM,CAAC,KAAKtM,CAAC,CAACsM,CAAC,CAAC,GAAGO,EAAE,GAAGb,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAGa,EAAE,EAAE,CAACb,CAAC,EAAEM,CAAC,KAAK;YACvEtM,CAAC,CAACsM,CAAC,CAAC,IAAIO,EAAE,EAAE;UACd,CAAC,CAAC;UAAEG,CAAC,GAAGrO,CAAC,CAACc,QAAQ,CAACoL,CAAC,EAAE,CAACmB,CAAC,EAAEM,CAAC,KAAKvM,CAAC,CAACuM,CAAC,CAAC,GAAGI,CAAC,GAAGV,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAGU,CAAC,EAAE,CAACV,CAAC,EAAEM,CAAC,KAAK;YAClEvM,CAAC,CAACuM,CAAC,CAAC,IAAII,CAAC,EAAE;UACb,CAAC,CAAC;QACF,IAAI/N,CAAC,CAACa,WAAW,CAAC0L,EAAE,EAAEC,EAAE,EAAE,CAACa,CAAC,EAAEM,CAAC,EAAEC,EAAE,KAAK;UACtC,IAAIU,EAAE;YAAEC,EAAE;YAAEC,EAAE,GAAGnB,CAAC;YAAEoB,EAAE,GAAGpB,CAAC;YAAEqB,EAAE,GAAGrB,CAAC;UAClC,IAAI,OAAOA,CAAC,IAAI,QAAQ,EAAE;YACxB,IAAIsB,EAAE,GAAGtB,CAAC,GAAGY,EAAE;YACfM,EAAE,GAAGH,EAAE,CAACO,EAAE,CAAC,EAAEF,EAAE,GAAGE,EAAE,GAAGT,EAAE;YACzB,IAAIU,EAAE,GAAGvB,CAAC,GAAGW,EAAE;YACfM,EAAE,GAAGD,CAAC,CAACO,EAAE,CAAC,EAAEvN,CAAC,CAACmC,CAAC,CAAC+K,EAAE,CAAC,CAAC,KAAKD,EAAE,GAAG,IAAI,CAAC,EAAEE,EAAE,GAAGI,EAAE,GAAGb,CAAC,EAAEW,EAAE,GAAGrB,CAAC,GAAGc,EAAE,EAAE9L,CAAC,CAACmM,EAAE,IAAI,CAAC,EAAE,uBAAuB,CAAC,EAAEnM,CAAC,CAACoM,EAAE,IAAI,CAAC,EAAE,uBAAuB,CAAC;YACvI,MAAMI,EAAE,GAAGxN,CAAC,CAACmC,CAAC,CAACmK,CAAC,CAAC,CAAC;cAAEmB,EAAE,GAAG1N,CAAC,CAACoC,CAAC,CAACoK,EAAE,CAAC,CAAC;YACjC,CAACiB,EAAE,IAAIC,EAAE,IAAI,CAAC1B,EAAE,KAAKe,EAAE,EAAE,EAAEU,EAAE,IAAIb,EAAE,EAAE,EAAEc,EAAE,IAAIb,EAAE,EAAE;UACnD,CAAC,MAAMK,EAAE,GAAGD,CAAC,CAAChB,CAAC,CAAC,EAAEkB,EAAE,GAAGH,EAAE,CAACf,CAAC,CAAC;UAC5BR,EAAE,CAAC1J,OAAO,CAACqL,EAAE,CAAC,EAAE5B,EAAE,CAACzJ,OAAO,CAACsL,EAAE,CAAC;UAC9B,MAAMM,EAAE,GAAGlB,CAAC,IAAI,CAACxM,CAAC,CAACmC,CAAC,CAACmK,CAAC,CAAC,CAAC,GAAGJ,CAAC,CAACmB,EAAE,CAAC,GAAG,KAAK,CAAC;YAAEM,EAAE,GAAG/C,CAAC,CAACqC,EAAE,EAAEX,CAAC,EAAEC,EAAE,EAAEW,EAAE,EAAEQ,EAAE,EAAE3B,EAAE,EAAER,EAAE,EAAEC,EAAE,CAAC;UAChF,IAAIoC,EAAE,EAAEC,CAAC,EAAEC,EAAE;UACbtB,CAAC,IAAI,CAACT,EAAE,GAAG2B,EAAE,KAAKC,EAAE,KAAKlB,CAAC,KAAKP,CAAC,GAAGtN,KAAK,CAACC,OAAO,CAACqN,CAAC,CAAC,GAAGA,CAAC,CAAChL,KAAK,CAAC,CAAC,GAAGhG,MAAM,CAACqN,MAAM,CAAC,CAAC,CAAC,EAAE2D,CAAC,CAAC,EAAEO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEmB,EAAE,GAAG1B,CAAC,EAAE4B,EAAE,GAAGH,EAAE,EAAE,QAAQE,CAAC,GAAGR,EAAE,CAAC,IAAI,QAAQ,IAAIrM,CAAC,CAACpC,KAAK,CAACC,OAAO,CAAC+O,EAAE,CAAC,CAAC,EAAE5M,CAAC,CAAC6M,CAAC,GAAGD,EAAE,CAAC7O,MAAM,CAAC,KAAKiC,CAAC,CAAC,CAACpC,KAAK,CAACC,OAAO,CAAC+O,EAAE,CAAC,CAAC,EAAE5M,CAAC,CAAC4M,EAAE,CAACC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,EAAEC,EAAE,KAAK,KAAK,CAAC,GAAG,OAAOD,CAAC,IAAI,QAAQ,GAAGD,EAAE,CAACrL,MAAM,CAACsL,CAAC,EAAE,CAAC,CAAC,GAAG,OAAOD,EAAE,CAACC,CAAC,CAAC,GAAGD,EAAE,CAACC,CAAC,CAAC,GAAGC,EAAE,CAAC,GAAG9M,CAAC,CAAC2M,EAAE,KAAK,KAAK,CAAC,CAAC,EAAEpC,EAAE,CAAC5K,MAAM,CAAC,CAAC,EAAE6K,EAAE,CAAC7K,MAAM,CAAC,CAAC;QAC3W,CAAC,CAAC,EAAEqM,CAAC,CAAC7J,GAAG,CAAC,CAAC,EAAE4J,EAAE,CAAC5J,GAAG,CAAC,CAAC,EAAEgJ,CAAC,IAAI,IAAI,EAAEA,CAAC,CAACxD,CAAC,GAAGuD,CAAC,CAAC,KACrC,IAAI,CAACZ,EAAE,IAAI,CAACK,EAAE,IAAIG,EAAE,IAAI,IAAI,EAAE,OAAOI,CAAC;MAC7C,CAAC,CAACtC,CAAC,EAAEA,CAAC,CAAClI,KAAK,CAAC,CAAC,EAAEmI,CAAC,EAAEA,CAAC,CAACnI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAEuI,CAAC,EAAEU,CAAC,CAAC,EAAEV,CAAC,CAACzH,KAAK,CAAC,CAAC,EAAEyH,CAAC,CAACvH,SAAS,CAACiI,CAAC,CAAC1J,GAAG,CAAC,CAAC,CAAC,EAAEgJ,CAAC,CAACzH,KAAK,CAAC,CAAC,EAAEyH,CAAC,CAAChJ,GAAG,CAAC,CAAC,EAAEkJ,CAAC,CAAC/K,GAAG,CAAEwL,CAAC,IAAKA,CAAC,CAAC3J,GAAG,CAAC,CAAC,CAAC,EAAEmJ,CAAC,CAAChL,GAAG,CAAEwL,CAAC,IAAKA,CAAC,CAAC3J,GAAG,CAAC,CAAC,CAAC,EAAE2I,CAAC,CAAC/H,QAAQ,CAACoI,CAAC,EAAE,CAACW,CAAC,EAAEC,CAAC,KAAK;QAC1K,MAAMK,EAAE,GAAGN,CAAC,CAAC5I,CAAC;QACd,IAAIkJ,EAAE,IAAI,IAAI,EAAE;UACd,MAAMC,EAAE,GAAGX,CAAC,CAACU,EAAE,CAAC;UAChBC,EAAE,IAAI,IAAI,IAAIN,CAAC,CAACzK,KAAK,CAAC,GAAG,EAAE+K,EAAE,CAAC;UAC9B,MAAMC,CAAC,GAAGjB,CAAC,CAACe,EAAE,CAAC;UACfE,CAAC,IAAIA,CAAC,CAACnK,GAAG,CAAC,CAAC,EAAEmK,CAAC,IAAIP,CAAC,CAACnI,SAAS,CAAC0I,CAAC,CAACnK,GAAG,CAAC,CAAC,CAAC;QACzC,CAAC,MAAM2J,CAAC,CAACjM,CAAC,KAAK,KAAK,CAAC,IAAIkM,CAAC,CAACzK,KAAK,CAAC,GAAG,EAAEwK,CAAC,CAACjM,CAAC,CAAC;MAC5C,CAAC,CAAC,EAAEsL,CAAC,CAACzH,KAAK,CAAC,CAAC,EAAEyH,CAAC,CAAChJ,GAAG,CAAC,CAAC,EAAE4I,CAAC,CAAChI,QAAQ,CAACoI,CAAC,EAAE,CAACW,CAAC,EAAEC,CAAC,KAAK;QAC9C,MAAMK,EAAE,GAAGN,CAAC,CAAC1I,CAAC;QACd,IAAIgJ,EAAE,IAAI,IAAI,EAAE;UACd,MAAME,CAAC,GAAGX,CAAC,CAACS,EAAE,CAAC;UACfE,CAAC,IAAI,IAAI,IAAIP,CAAC,CAACzK,KAAK,CAAC,GAAG,EAAEgL,CAAC,CAAC;UAC5B,MAAMC,EAAE,GAAGjB,CAAC,CAACc,EAAE,CAAC;UAChBG,EAAE,IAAIR,CAAC,CAACnI,SAAS,CAAC2I,EAAE,CAACpK,GAAG,CAAC,CAAC,CAAC;QAC7B,CAAC,MAAM2J,CAAC,CAACjC,CAAC,KAAK,KAAK,CAAC,IAAIkC,CAAC,CAACzK,KAAK,CAAC,GAAG,EAAEwK,CAAC,CAACjC,CAAC,CAAC;QAC1C,MAAMwC,EAAE,GAAG1F,EAAE,CAACmF,CAAC,CAAC;QAChBO,EAAE,IAAI,CAACT,CAAC,CAACJ,GAAG,CAACM,CAAC,CAAC,IAAIlG,CAAC,CAACmG,CAAC,EAAEM,EAAE,EAAE1G,CAAC,CAACmG,CAAC,CAAC,CAAC;MACnC,CAAC,CAAC,EAAEX,CAAC,CAAChJ,GAAG,CAAC,CAAC;IACb;IACA,SAASoE,EAAEA,CAACsD,CAAC,EAAE;MACb,IAAIA,CAAC,IAAI,IAAI,EAAE,OAAO,IAAI;MAC1B,MAAMzG,CAAC,GAAG,IAAIvD,CAAC,CAACkB,UAAU,CAAC8I,CAAC,CAAC;QAAEc,CAAC,GAAG,IAAI9K,CAAC,CAACiB,WAAW,CAAC,CAAC;MACtD,IAAIgK,CAAC;MACL,MAAMC,CAAC,GAAG,EAAE;QAAEI,CAAC,GAAG,EAAE;MACpB,OAAO,SAASE,CAACA,CAACC,CAAC,EAAEpI,CAAC,EAAEqI,CAAC,EAAE;QACzB,MAAMG,CAAC,GAAGJ,CAAC,CAAChJ,YAAY,CAAC,CAAC;QAC1B,IAAIqJ,CAAC;UAAEC,CAAC,GAAG,CAAC,CAAC;QACb,IAAIF,CAAC,EAAE;UACLA,CAAC,CAACxI,CAAC,IAAI,IAAI,KAAKA,CAAC,CAAC5B,KAAK,CAAC,GAAG,EAAEoK,CAAC,CAACxI,CAAC,CAAC,EAAE6H,CAAC,CAACW,CAAC,CAACxI,CAAC,CAAC,GAAGoI,CAAC,CAAC1I,KAAK,CAAC,CAAC,CAAC,EAAE8I,CAAC,CAAC7L,CAAC,KAAK,KAAK,CAAC,IAAIqD,CAAC,CAAC5B,KAAK,CAAC,GAAG,EAAEoK,CAAC,CAAC7L,CAAC,CAAC,EAAE6L,CAAC,CAACtI,CAAC,IAAI,IAAI,KAAKF,CAAC,CAAC5B,KAAK,CAAC,GAAG,EAAEoK,CAAC,CAACtI,CAAC,CAAC,EAAEmI,CAAC,GAAG,KAAK,CAAC,CAAC,EAAEG,CAAC,CAAC7B,CAAC,KAAK,KAAK,CAAC,KAAK0B,CAAC,GAAGI,CAAC,GAAGD,CAAC,CAAC7B,CAAC,CAAC;UAC5K,MAAMiC,CAAC,GAAGnF,EAAE,CAAC+E,CAAC,CAAC;UACfI,CAAC,KAAKP,CAAC,KAAK,KAAK,CAAC,IAAIT,CAAC,KAAKA,CAAC,GAAG,eAAgB,IAAIM,GAAG,CAAC,CAAC,CAAC,EAAEN,CAAC,CAACW,GAAG,CAACC,CAAC,CAAC,KAAK/F,CAAC,CAAC+F,CAAC,CAAC,EAAEH,CAAC,GAAGO,CAAC,CAAC5E,KAAK,CAACqE,CAAC,EAAE5F,CAAC,CAAC+F,CAAC,CAAC,CAAC,EAAEE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC/G;QACA,IAAIC,CAAC,GAAG,CAAC;QACT,KAAK,MAAMC,CAAC,IAAIR,CAAC,EAAE;UACjBpI,CAAC,CAACF,OAAO,CAAC8I,CAAC,CAAC;UACZ,MAAMC,CAAC,GAAG,OAAOD,CAAC,IAAI,QAAQ,GAAGA,CAAC,GAAGD,CAAC,GAAGC,CAAC;YAAEM,EAAE,GAAGvI,CAAC,CAAC0H,CAAC,EAAEQ,CAAC,CAAC;UACxD7K,CAAC,CAACoK,CAAC,CAAChJ,YAAY,CAAC,CAAC,CAAC,IAAIuJ,CAAC,EAAE;UAC1B,MAAMQ,EAAE,GAAGhB,CAAC,CAACC,CAAC,EAAEpI,CAAC,EAAEkJ,EAAE,CAAC;UACtB,IAAIb,CAAC,KAAK,KAAK,CAAC,IAAIc,EAAE,KAAK,KAAK,CAAC,EAAE;YACjC,IAAIT,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,EAAEL,CAAC,GAAGjH,CAAC,CAACiH,CAAC,CAAC,CAAC,EAAE,CAACvJ,CAAC,CAACuJ,CAAC,EAAEQ,CAAC,CAAC,EAAE,MAAM5K,KAAK,CAAC,yCAAyC,CAAC;YAC7FoK,CAAC,CAACQ,CAAC,CAAC,GAAGM,EAAE;UACX;UACAnJ,CAAC,CAACrB,MAAM,CAAC,CAAC;QACZ;QACA,IAAI8J,CAAC,KAAK,KAAK,CAAC,EAAE,OAAOC,CAAC,GAAGL,CAAC,GAAG,KAAK,CAAC;QACvCrI,CAAC,CAAC5B,KAAK,CAAC,GAAG,EAAEiK,CAAC,CAAC;MACjB,CAAC,CAACnI,CAAC,EAAEuH,CAAC,EAAE,KAAK,CAAC,CAAC,EAAEG,CAAC,KAAKH,CAAC,CAACjH,KAAK,CAAC,CAAC,EAAE,SAAS2H,CAACA,CAACC,CAAC,EAAEpI,CAAC,EAAEqI,CAAC,EAAE;QACpD,MAAMG,CAAC,GAAGxI,CAAC,CAACZ,YAAY,CAAC,CAAC;QAC1B,IAAIoJ,CAAC,EAAE;UACL,MAAMI,CAAC,GAAGJ,CAAC,CAACtI,CAAC;UACb,IAAI0I,CAAC,IAAI,IAAI,KAAKR,CAAC,GAAGP,CAAC,CAACe,CAAC,CAAC,EAAEP,CAAC,GAAGJ,CAAC,CAACW,CAAC,CAAC,GAAGjM,CAAC,CAACgB,WAAW,CAAC,CAAC,CAAC,EAAEiK,CAAC,CAACU,GAAG,CAACE,CAAC,CAAC,EAAE;YACjE,MAAMK,CAAC,GAAGpF,EAAE,CAAC+E,CAAC,CAAC;YACf,IAAI,CAACK,CAAC,CAAChE,MAAM,EAAE,MAAM5G,KAAK,CAAE,yBAAwB4K,CAAC,CAACnF,IAAK,EAAC,CAAC;YAC7DhB,CAAC,CAAC2F,CAAC,EAAEQ,CAAC,EAAEA,CAAC,CAAChE,MAAM,CAACpC,CAAC,CAAC+F,CAAC,CAAC,CAAC,CAAC;UACzB;QACF;QACA,IAAIC,CAAC,GAAG,CAAC;UAAEC,CAAC,GAAG,CAAC;QAChB,MAAMC,CAAC,GAAGhM,CAAC,CAACc,QAAQ,CAAC2K,CAAC,EAAE,CAACQ,CAAC,EAAEC,CAAC,KAAK9K,CAAC,CAAC8K,CAAC,CAAC,GAAGJ,CAAC,GAAGG,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAGH,CAAC,EAAE,CAACG,CAAC,EAAEC,CAAC,KAAK;UACpE9K,CAAC,CAAC8K,CAAC,CAAC,IAAIJ,CAAC,EAAE;QACb,CAAC,CAAC;QACF,KAAK,MAAMG,CAAC,IAAI5I,CAAC,EAAE,IAAI,OAAO4I,CAAC,IAAI,QAAQ,EAAE;UAC3C,MAAMC,CAAC,GAAGD,CAAC,GAAGF,CAAC;YAAEQ,EAAE,GAAGP,CAAC,CAACE,CAAC,CAAC;YAAEM,EAAE,GAAGN,CAAC,GAAGJ,CAAC;UACtCJ,CAAC,CAACvI,OAAO,CAACqJ,EAAE,CAAC,EAAEhB,CAAC,CAACe,EAAE,EAAElJ,CAAC,EAAEqI,CAAC,CAAC,EAAErK,CAAC,CAACgC,CAAC,CAACZ,YAAY,CAAC,CAAC,CAAC,IAAIsJ,CAAC,EAAE,EAAEL,CAAC,CAAC1J,MAAM,CAAC,CAAC;QACpE,CAAC,MAAM0J,CAAC,CAACvI,OAAO,CAAC8I,CAAC,CAAC,EAAET,CAAC,CAACQ,CAAC,CAACC,CAAC,CAAC,EAAE5I,CAAC,EAAEqI,CAAC,CAAC,EAAEA,CAAC,CAAC1J,MAAM,CAAC,CAAC;QAC9CgK,CAAC,CAACxH,GAAG,CAAC,CAAC;MACT,CAAC,CAACjB,CAAC,CAACR,KAAK,CAAC,CAAC,EAAEQ,CAAC,EAAEuH,CAAC,CAAC,EAAEQ,CAAC,CAAClL,MAAM,KAAK0K,CAAC,CAACjH,KAAK,CAAC,CAAC,EAAEN,CAAC,CAACL,QAAQ,CAAC4H,CAAC,EAAE,CAACU,CAAC,EAAEC,CAAC,KAAK;QAClE,MAAMpI,CAAC,GAAGmI,CAAC,CAACnI,CAAC;QACb,IAAIA,CAAC,IAAI,IAAI,EAAE;UACb,MAAMqI,CAAC,GAAGJ,CAAC,CAACjI,CAAC,CAAC;UACdqI,CAAC,IAAIA,CAAC,CAACpJ,GAAG,CAAC,CAAC,EAAEoJ,CAAC,IAAID,CAAC,CAAC1H,SAAS,CAAC2H,CAAC,CAACpJ,GAAG,CAAC,CAAC,CAAC;QACzC;MACF,CAAC,CAAC,CAAC,CAAC,EAAEwI,CAAC,CAACxI,GAAG,CAAC,CAAC;IACf;IACA,MAAMsE,EAAE,GAAGA,CAACoD,CAAC,EAAEzG,CAAC,KAAKyG,CAAC,CAACoF,IAAI,CAAEtE,CAAC,IAAK,OAAOA,CAAC,IAAI,QAAQ,KAAK7K,KAAK,CAACC,OAAO,CAAC4K,CAAC,CAAC,GAAGlE,EAAE,CAACkE,CAAC,EAAEvH,CAAC,CAAC,GAAGA,CAAC,CAACuH,CAAC,CAAC,CAAC,CAAC;IAChG,SAASjE,EAAEA,CAACmD,CAAC,EAAEzG,CAAC,EAAE;MAChB,IAAIyG,CAAC,IAAI,IAAI,IAAI,CAACpD,EAAE,CAACoD,CAAC,EAAG3G,CAAC,IAAK;QAC7B,IAAIqI,CAAC;QACL,OAAOrI,CAAC,CAACrD,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC0L,CAAC,GAAG5E,EAAE,CAACzD,CAAC,CAAC,MAAM,IAAI,IAAIqI,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAAC1D,cAAc,KAAK,IAAI;MACrG,CAAC,CAAC,EAAE,OAAOgC,CAAC;MACZ,MAAMc,CAAC,GAAG,IAAI9K,CAAC,CAACkB,UAAU,CAAC8I,CAAC,CAAC;QAAEiB,CAAC,GAAG,IAAIjL,CAAC,CAACiB,WAAW,CAAC,CAAC;MACtD,IAAIiK,CAAC,GAAG,CAAC,CAAC;MACV,MAAMI,CAAC,GAAG,EAAE;QAAEE,CAAC,GAAG,EAAE;QAAEC,CAAC,GAAGA,CAACpI,CAAC,EAAEqI,CAAC,EAAEG,CAAC,KAAK;UACrC,MAAMC,CAAC,GAAGzI,CAAC,CAACZ,YAAY,CAAC,CAAC;UAC1B,IAAIsJ,CAAC,GAAG,CAAC,CAAC;UACV,IAAID,CAAC,EAAE;YACLA,CAAC,CAACvI,CAAC,IAAI,IAAI,IAAImI,CAAC,CAACjK,KAAK,CAAC,GAAG,EAAEqK,CAAC,CAACvI,CAAC,CAAC,EAAEuI,CAAC,CAAC9B,CAAC,KAAK,KAAK,CAAC,IAAI0B,CAAC,CAACjK,KAAK,CAAC,GAAG,EAAEqK,CAAC,CAAC9B,CAAC,CAAC;YACrE,MAAMiC,CAAC,GAAGH,CAAC,CAACzI,CAAC;YACb,IAAI4I,CAAC,IAAI,IAAI,KAAKX,CAAC,CAACW,CAAC,CAAC,GAAG5I,CAAC,CAACN,KAAK,CAAC,CAAC,EAAEV,CAAC,CAACwJ,CAAC,KAAK,KAAK,CAAC,EAAE,sCAAsC,CAAC,EAAEL,CAAC,CAACS,CAAC,CAAC,GAAGJ,CAAC,EAAEH,CAAC,CAACjK,KAAK,CAAC,GAAG,EAAEqK,CAAC,CAACzI,CAAC,CAAC,CAAC,EAAEyI,CAAC,CAAC9L,CAAC,KAAK,KAAK,CAAC,IAAI6L,CAAC,KAAK,KAAK,CAAC,EAAE,MAAMvK,KAAK,CAAC,mEAAmE,CAAC;YAC3O,MAAM4K,CAAC,GAAGpF,EAAE,CAACgF,CAAC,CAAC;YACfI,CAAC,KAAKA,CAAC,CAAClE,cAAc,GAAGkD,CAAC,GAAG,CAAC,CAAC,GAAGnF,CAAC,CAAC2F,CAAC,EAAEQ,CAAC,EAAEpG,CAAC,CAACgG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACtD;UACA,IAAIE,CAAC,GAAG,CAAC;UACT,KAAK,MAAMC,CAAC,IAAI5I,CAAC,EAAE;YACjBqI,CAAC,CAACvI,OAAO,CAAC8I,CAAC,CAAC;YACZ,MAAMC,CAAC,GAAG,OAAOD,CAAC,IAAI,QAAQ,GAAGA,CAAC,GAAGD,CAAC,GAAGC,CAAC;cAAEM,EAAE,GAAGvI,CAAC,CAAC6H,CAAC,EAAEK,CAAC,CAAC;cAAEM,EAAE,GAAGf,CAAC,CAACpI,CAAC,EAAEqI,CAAC,EAAEa,EAAE,CAAC;YAC1EA,EAAE,KAAKC,EAAE,KAAKT,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,EAAEF,CAAC,GAAGpH,CAAC,CAACoH,CAAC,CAAC,CAAC,EAAEW,EAAE,KAAK,KAAK,CAAC,IAAIX,CAAC,GAAGtK,CAAC,CAACsK,CAAC,EAAEK,CAAC,CAAC,EAAE,OAAOD,CAAC,IAAI,QAAQ,IAAID,CAAC,EAAE,IAAIH,CAAC,CAACK,CAAC,CAAC,GAAGM,EAAE,CAAC,EAAEd,CAAC,CAAC1J,MAAM,CAAC,CAAC;UAC5H;UACA,OAAO8J,CAAC,KAAKA,CAAC,CAAC9L,CAAC,KAAK,KAAK,CAAC,IAAI0L,CAAC,CAACjK,KAAK,CAAC,GAAG,EAAE1B,CAAC,CAACM,OAAO,CAACwL,CAAC,CAAC,CAAC,EAAEA,CAAC,GAAG,KAAK,CAAC,IAAIC,CAAC,CAACzI,CAAC,IAAI,IAAI,KAAKwI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAEA,CAAC;QAC1G,CAAC;MACD,OAAOJ,CAAC,CAACX,CAAC,EAAEG,CAAC,EAAE1H,CAAC,CAAC,EAAE0H,CAAC,CAAC3I,GAAG,CAAC,CAAC,EAAE4I,CAAC,KAAKD,CAAC,CAACpH,KAAK,CAAC,CAAC,EAAE,SAASR,CAACA,CAACqI,CAAC,EAAEG,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;QACrE,MAAMC,CAAC,GAAGJ,CAAC,CAACpJ,YAAY,CAAC,CAAC;QAC1B,IAAIwJ,CAAC,EAAE;UACLA,CAAC,CAACjC,CAAC,KAAK,KAAK,CAAC,IAAI+B,CAAC,GAAGE,CAAC,CAACjC,CAAC,EAAEgC,CAAC,GAAG,CAAC,CAAC,IAAIC,CAAC,CAAC1I,CAAC,IAAI,IAAI,KAAKwI,CAAC,GAAGP,CAAC,CAACS,CAAC,CAAC1I,CAAC,CAAC,EAAEmI,CAAC,GAAGJ,CAAC,CAACW,CAAC,CAAC1I,CAAC,CAAC,EAAEyI,CAAC,GAAG,CAAC,CAAC,EAAEC,CAAC,CAAC1I,CAAC,CAAC;UACzF,IAAIkJ,CAAC,GAAG3F,EAAE,CAACmF,CAAC,CAAC;UACb,IAAIQ,CAAC,IAAIA,CAAC,CAACzE,cAAc,EAAE;YACzB,MAAM0E,EAAE,GAAG5G,CAAC,CAACmG,CAAC,CAAC;YACflG,CAAC,CAAC+F,CAAC,EAAEW,CAAC,EAAEA,CAAC,CAACzE,cAAc,CAAC0E,EAAE,EAAEX,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;UACtC;QACF;QACA,IAAIG,CAAC,GAAG,CAAC;UAAEK,EAAE,GAAG,CAAC;QACjB,MAAMC,EAAE,GAAGxM,CAAC,CAACc,QAAQ,CAAC4K,CAAC,EAAE,CAACe,CAAC,EAAEC,EAAE,KAAKtL,CAAC,CAACsL,EAAE,CAAC,GAAGR,CAAC,GAAGO,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAGP,CAAC,EAAE,CAACO,CAAC,EAAEC,EAAE,KAAK;UACxEtL,CAAC,CAACsL,EAAE,CAAC,IAAIR,CAAC,EAAE;QACd,CAAC,CAAC;QACF,KAAK,MAAMO,CAAC,IAAIZ,CAAC,EAAE,IAAI,OAAOY,CAAC,IAAI,QAAQ,EAAE;UAC3C,MAAMC,EAAE,GAAGD,CAAC,GAAGF,EAAE;YAAEI,EAAE,GAAGH,EAAE,CAACE,EAAE,CAAC;YAAEE,EAAE,GAAGF,EAAE,GAAGR,CAAC;YAAEW,EAAE,GAAG7I,CAAC,CAAC+H,CAAC,EAAEC,CAAC,GAAGU,EAAE,GAAGE,EAAE,CAAC;UACnEd,CAAC,CAAC3I,OAAO,CAACsJ,CAAC,CAAC,EAAEpJ,CAAC,CAACsJ,EAAE,EAAEd,CAAC,EAAEC,CAAC,EAAEe,EAAE,EAAEb,CAAC,CAAC,EAAE3K,CAAC,CAACwK,CAAC,CAACpJ,YAAY,CAAC,CAAC,CAAC,IAAI8J,EAAE,EAAE,EAAET,CAAC,CAAC9J,MAAM,CAAC,CAAC;QAC3E,CAAC,MAAM;UACL,MAAM0K,EAAE,GAAG1I,CAAC,CAAC+H,CAAC,EAAEU,CAAC,CAAC;UAClBX,CAAC,CAAC3I,OAAO,CAACsJ,CAAC,CAAC,EAAEpJ,CAAC,CAACmJ,EAAE,CAACC,CAAC,CAAC,EAAEZ,CAAC,EAAEC,CAAC,EAAEY,EAAE,EAAEV,CAAC,CAAC,EAAEF,CAAC,CAAC9J,MAAM,CAAC,CAAC;QACjD;QACAwK,EAAE,CAAChI,GAAG,CAAC,CAAC;MACV,CAAC,CAACsG,CAAC,CAAC/H,KAAK,CAAC,CAAC,EAAE+H,CAAC,EAAEG,CAAC,EAAE1H,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE0H,CAAC,CAAC3I,GAAG,CAAC,CAAC;IACrC;IACA,SAASsI,EAAEA,CAACZ,CAAC,EAAEzG,CAAC,EAAE;MAChB,OAAOmD,EAAE,CAACG,EAAE,CAACmD,CAAC,EAAEzG,CAAC,CAAC,CAAC;IACrB;IACA,MAAM8L,EAAE,GAAIrF,CAAC,IAAK;MAChB,IAAIA,CAAC,IAAI,IAAI,EAAE,OAAO,IAAI;MAC1B,MAAMzG,CAAC,GAAGyG,CAAC,CAACzH,KAAK,CAAC,CAAC;MACnB,KAAK,IAAIuI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,CAAC,CAAC5J,MAAM,EAAE0K,CAAC,EAAE,EAAE;QACjC,MAAMG,CAAC,GAAG1H,CAAC,CAACuH,CAAC,CAAC;QACd7K,KAAK,CAACC,OAAO,CAAC+K,CAAC,CAAC,KAAK1H,CAAC,CAACuH,CAAC,CAAC,GAAGuE,EAAE,CAACpE,CAAC,CAAC,CAAC;MACpC;MACA,OAAO1H,CAAC;IACV,CAAC;IACD,SAASmH,EAAEA,CAACV,CAAC,EAAEzG,CAAC,EAAEuH,CAAC,EAAE;MACnBzI,CAAC,CAACyI,CAAC,KAAK,MAAM,IAAIA,CAAC,KAAK,OAAO,EAAE,iCAAiC,CAAC;MACnE,MAAMG,CAAC,GAAGH,CAAC,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC;MAC9B,IAAIvH,CAAC,IAAI,IAAI,EAAE,OAAO;QACpB+L,EAAE,EAAE,CAAC,CAAC;QACNC,MAAM,EAAEvF;MACV,CAAC;MACD9D,CAAC,CAAC8D,CAAC,CAAC,EAAE9D,CAAC,CAAC3C,CAAC,CAAC;MACV,IAAI2H,CAAC,GAAG,IAAI;MACZ,MAAMI,CAAC,GAAG,EAAE;QAAEE,CAAC,GAAG,EAAE;QAAEC,CAAC,GAAG,EAAE;QAAEpI,CAAC,GAAG,EAAE;QAAEqI,CAAC,GAAG,EAAE;QAAEG,CAAC,GAAG,EAAE;QAAEC,CAAC,GAAG,EAAE;QAAEC,CAAC,GAAG,EAAE;QAAEC,CAAC,GAAG,EAAE;QAAEC,CAAC,GAAG,EAAE;QAAEC,CAAC,GAAG,EAAE;QAAEK,EAAE,GAAG,EAAE;QAAEC,EAAE,GAAG,EAAE;QAAEC,CAAC,GAAG,EAAE;QAAEC,EAAE,GAAG,EAAE;MAC/H,IAAIC,EAAE,GAAG,CAAC;MACV,MAAMC,EAAE,GAAG5M,CAAC,CAACe,UAAU,CAACiJ,CAAC,CAAC;QAAE6C,EAAE,GAAG7M,CAAC,CAACe,UAAU,CAACwC,CAAC,CAAC;QAAEuJ,EAAE,GAAG9M,CAAC,CAACgB,WAAW,CAAC,CAAC;MACtE,IAAI,SAASsM,CAACA,CAACC,CAAC,EAAEC,CAAC,GAAG,IAAI,EAAEC,CAAC,EAAE;QAC7B,MAAMC,CAAC,GAAGlK,CAAC,CAACgK,CAAC,CAAC;QACdE,CAAC,KAAKA,CAAC,CAAC1N,CAAC,KAAK,KAAK,CAAC,GAAGyN,CAAC,GAAGD,CAAC,CAACzK,KAAK,CAAC,CAAC,GAAG2K,CAAC,CAACrK,CAAC,IAAI,IAAI,KAAKoK,CAAC,GAAG,IAAI,EAAE5B,CAAC,CAAC6B,CAAC,CAACrK,CAAC,CAAC,GAAGkK,CAAC,CAACxK,KAAK,CAAC,CAAC,CAAC,CAAC;QACrF,MAAM8K,CAAC,GAAGN,CAAC,CAAC9K,YAAY,CAAC,CAAC;QAC1B,IAAIqL,CAAC;QACLD,CAAC,IAAI,CAACC,CAAC,GAAGD,CAAC,CAACxK,CAAC,KAAK,IAAI,KAAKqI,CAAC,CAACoC,CAAC,CAAC,GAAGN,CAAC,GAAGA,CAAC,CAACzK,KAAK,CAAC,CAAC,GAAG,IAAI,EAAE0I,CAAC,CAACqC,CAAC,CAAC,GAAGP,CAAC,CAACxK,KAAK,CAAC,CAAC,EAAE0K,CAAC,KAAKxB,CAAC,CAAC6B,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE9B,CAAC,CAAC8B,CAAC,CAAC,GAAGL,CAAC,CAAC,EAAEC,CAAC,IAAIA,CAAC,CAACrK,CAAC,IAAI,IAAI,KAAKoJ,CAAC,CAACqB,CAAC,CAAC,GAAGJ,CAAC,CAACrK,CAAC,CAAC,CAAC;QACvI,MAAM0K,CAAC,GAAG/N,CAAC,CAACc,QAAQ,CAAC0M,CAAC,CAAC;QACvB,KAAK,MAAMQ,EAAE,IAAIT,CAAC,EAAED,CAAC,CAACC,CAAC,EAAEQ,CAAC,CAACC,EAAE,CAAC,EAAEP,CAAC,CAAC;QAClCM,CAAC,CAACvJ,GAAG,CAAC,CAAC;MACT,CAAC,CAACqI,EAAE,EAAED,EAAE,EAAE,IAAI,CAAC,EAAE,SAASU,CAACA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEG,CAAC,EAAE;QACzC,MAAMC,CAAC,GAAGL,CAAC,CAAChL,YAAY,CAAC,CAAC;QAC1B,IAAIsL,CAAC;UAAEC,EAAE,GAAG,CAAC,CAAC;QACdF,CAAC,KAAK,CAACC,CAAC,GAAGD,CAAC,CAACvK,CAAC,KAAK,IAAI,IAAIF,CAAC,CAAC0K,CAAC,CAAC,GAAGN,CAAC,CAAC1K,KAAK,CAAC,CAAC,EAAE2K,CAAC,IAAI,IAAI,KAAKhB,EAAE,CAACgB,CAAC,CAAC,IAAI,IAAI,KAAKhB,EAAE,CAACgB,CAAC,CAAC,GAAG,EAAE,CAAC,EAAEhB,EAAE,CAACgB,CAAC,CAAC,CAAC/K,IAAI,CAACoL,CAAC,CAAC,CAAC,EAAE9B,CAAC,CAAC8B,CAAC,CAAC,EAAER,CAAC,GAAG7B,CAAC,CAACqC,CAAC,CAAC,IAAI,IAAI,EAAEP,CAAC,GAAG/B,CAAC,CAACsC,CAAC,CAAC,IAAI,IAAI,EAAE9B,CAAC,CAAC8B,CAAC,CAAC,IAAIF,CAAC,KAAK3B,CAAC,CAAC6B,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEF,CAAC,GAAG7B,CAAC,CAAC+B,CAAC,CAAC,IAAI,IAAI,IAAI,CAACF,CAAC,IAAI5C,CAAC,KAAK,CAAC,IAAIwB,CAAC,CAACsB,CAAC,CAAC,IAAI,IAAI,IAAI7C,CAAC,IAAI,IAAI,KAAKA,CAAC,GAAG;UACnPzB,IAAI,EAAEtJ,CAAC,CAAC0E,YAAY,CAACC,qBAAqB;UAC1C0K,GAAG,EAAE9S,CAAC,CAAC2N,QAAQ,CAACwD,CAAC,CAAC3L,OAAO,CAAC,CAAC,CAAC;UAC5BuN,GAAG,EAAE/S,CAAC,CAAC0N,MAAM,CAACoD,CAAC,CAACtL,OAAO,CAAC,CAAC,EAAEuL,CAAC,CAACvL,OAAO,CAAC,CAAC;QACxC,CAAC,CAAC,EAAE8L,EAAE,GAAG,CAAC,CAAC,IAAIF,CAAC,CAAC9D,CAAC,KAAK,KAAK,CAAC,KAAKuD,CAAC,GAAGC,CAAC,GAAG,IAAI,EAAEQ,EAAE,GAAG,CAAC,CAAC,EAAEH,CAAC,IAAI3C,CAAC,IAAI,IAAI,KAAKA,CAAC,GAAG;UAC9EzB,IAAI,EAAEtJ,CAAC,CAAC0E,YAAY,CAACC,qBAAqB;UAC1C0K,GAAG,EAAE9S,CAAC,CAAC2N,QAAQ,CAACwD,CAAC,CAAC3L,OAAO,CAAC,CAAC,CAAC;UAC5BuN,GAAG,EAAE/S,CAAC,CAACyN,QAAQ,CAACsD,CAAC,CAACvL,OAAO,CAAC,CAAC,EAAE4L,CAAC,CAAC9D,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC;QACJ,MAAMiE,EAAE,GAAGzK,CAAC,CAAC+J,CAAC,CAAC;QACfU,EAAE,KAAKA,EAAE,CAACjO,CAAC,KAAK,KAAK,CAAC,GAAG6N,CAAC,GAAGN,CAAC,CAACxK,KAAK,CAAC,CAAC,GAAGkL,EAAE,CAAC5K,CAAC,IAAI,IAAI,KAAK4K,EAAE,CAAC5K,CAAC,EAAEqK,CAAC,GAAGO,EAAE,CAAC5K,CAAC,EAAEwK,CAAC,GAAG,IAAI,CAAC,CAAC;QACpF,MAAMK,EAAE,GAAGpH,EAAE,CAACgH,CAAC,CAAC;QAChBI,EAAE,IAAIL,CAAC,IAAI3C,CAAC,IAAI,IAAI,KAAKA,CAAC,GAAG;UAC3BzB,IAAI,EAAEtJ,CAAC,CAAC0E,YAAY,CAACC,qBAAqB;UAC1C0K,GAAG,EAAE9S,CAAC,CAAC2N,QAAQ,CAACwD,CAAC,CAAC3L,OAAO,CAAC,CAAC,CAAC;UAC5BuN,GAAG,EAAE/S,CAAC,CAACuN,MAAM,CAACwD,CAAC,CAACvL,OAAO,CAAC,CAAC,EAAEgM,EAAE,EAAEpI,CAAC,CAACgI,CAAC,CAAC,EAAE,CAAC,CAAC;QACzC,CAAC,CAAC;QACF,IAAIK,EAAE,GAAG,CAAC;UAAEC,EAAE,GAAG,CAAC;QAClB,MAAMC,CAAC,GAAGrO,CAAC,CAACc,QAAQ,CAAC0M,CAAC,EAAE,CAACG,CAAC,EAAEC,EAAE,KAAKxM,CAAC,CAACwM,EAAE,CAAC,GAAGO,EAAE,GAAGR,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAGQ,EAAE,EAAE,CAACR,CAAC,EAAEC,EAAE,KAAK;YACzExM,CAAC,CAACwM,EAAE,CAAC,IAAIO,EAAE,EAAE;UACf,CAAC,CAAC;UAAEd,CAAC,GAAGrN,CAAC,CAACc,QAAQ,CAACyM,CAAC,CAAC;QACrB,KAAK,MAAMI,CAAC,IAAIF,CAAC,EAAE,IAAI,OAAOE,CAAC,IAAI,QAAQ,EAAE;UAC3C,MAAMC,EAAE,GAAGD,CAAC,GAAGS,EAAE;YAAEE,EAAE,GAAGD,CAAC,CAACT,EAAE,CAAC;UAC7BQ,EAAE,IAAI,CAACd,CAAC,CAACD,CAAC,CAACO,EAAE,GAAGO,EAAE,CAAC,EAAEG,EAAE,EAAEb,CAAC,EAAEC,CAAC,EAAEG,CAAC,CAAC;QACnC,CAAC,MAAM;UACL,MAAMD,EAAE,GAAGS,CAAC,CAACV,CAAC,CAAC;UACfL,CAAC,CAACD,CAAC,CAACM,CAAC,CAAC,EAAEC,EAAE,EAAEH,CAAC,EAAEC,CAAC,EAAEG,CAAC,CAAC;QACtB;QACA,OAAOQ,CAAC,CAAC7J,GAAG,CAAC,CAAC,EAAE6I,CAAC,CAAC7I,GAAG,CAAC,CAAC,EAAEwJ,EAAE;MAC7B,CAAC,CAACpB,EAAE,EAAEC,EAAE,EAAEA,EAAE,CAAC9J,KAAK,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAEM,CAAC,CAAC5C,GAAG,CAAE6M,CAAC,IAAKA,CAAC,IAAIA,CAAC,CAAChL,GAAG,CAAC,CAAC,CAAC,EAAE4I,CAAC,EAAE,OAAO;QACvEoE,EAAE,EAAE,CAAC,CAAC;QACNI,QAAQ,EAAExE;MACZ,CAAC;MACDgB,CAAC,CAACzL,GAAG,CAAE6M,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC;MACjB,MAAMP,EAAE,GAAG,EAAE;MACb,IAAIC,EAAE,GAAG,IAAI;MACb,CAAC,SAASM,CAACA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEG,CAAC,EAAE;QACzB,IAAIC,CAAC,GAAG,CAAC,CAAC;QACV,MAAMC,CAAC,GAAGvK,CAAC,CAACgK,CAAC,CAAC;QACd,IAAIpM,CAAC,CAAC2M,CAAC,CAAC,EAAE;UACR,MAAMM,CAAC,GAAGN,CAAC,CAAC1K,CAAC;UACbgL,CAAC,IAAI,IAAI,IAAIZ,CAAC,GAAGpK,CAAC,CAACgL,CAAC,CAAC,EAAEX,CAAC,GAAGnB,EAAE,CAAC8B,CAAC,CAAC,GAAGrO,CAAC,CAACgB,WAAW,CAAC,CAAC,EAAE8M,CAAC,GAAG,CAAC,CAAC,EAAED,CAAC,GAAG,IAAI,KAAKJ,CAAC,GAAG,IAAI,EAAEI,CAAC,GAAGL,CAAC,CAACzK,KAAK,CAAC,CAAC,CAAC;QACnG,CAAC,MAAM1B,CAAC,CAACmC,CAAC,CAACiK,CAAC,CAAC,CAAC,KAAKA,CAAC,GAAG,IAAI,CAAC;QAC5B,MAAMO,EAAE,GAAGT,CAAC,CAAC9K,YAAY,CAAC,CAAC;QAC3B,IAAIuL,EAAE,EAAE;UACN,MAAMK,CAAC,GAAGL,EAAE,CAAC3K,CAAC;UACdgL,CAAC,IAAI,IAAI,IAAIR,CAAC,KAAK9B,CAAC,CAACsC,CAAC,CAAC,GAAGR,CAAC,CAAC,EAAEd,EAAE,CAACsB,CAAC,CAAC,GAAGR,CAAC,IAAI5C,CAAC,KAAK,CAAC,IAAI6C,CAAC,GAAG,IAAI,GAAGJ,CAAC,CAACjL,YAAY,CAAC,CAAC,EAAE6I,CAAC,CAAC+C,CAAC,CAAC,GAAGd,CAAC,CAACxK,KAAK,CAAC,CAAC,EAAE0K,CAAC,KAAK3B,CAAC,CAACuC,CAAC,CAAC,GAAGZ,CAAC,CAAC1K,KAAK,CAAC,CAAC,CAAC,IAAIiL,EAAE,CAAChO,CAAC,KAAK,KAAK,CAAC,KAAK6N,CAAC,IAAIH,CAAC,CAACjM,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAACoM,CAAC,IAAIC,CAAC,MAAMd,EAAE,IAAI,IAAI,KAAKA,EAAE,GAAG,eAAgB,IAAIzB,GAAG,CAAC,CAAC,CAAC,EAAEyB,EAAE,CAACpB,GAAG,CAACoC,EAAE,CAAC,CAAC,CAAC;QACtP;QACA,IAAIC,EAAE,GAAG,CAAC;UAAEC,EAAE,GAAG,CAAC;QAClB,MAAMC,EAAE,GAAGnO,CAAC,CAACc,QAAQ,CAAC0M,CAAC,EAAE,KAAK,CAAC,EAAE,CAACa,CAAC,EAAEhB,CAAC,KAAK;YACzCjM,CAAC,CAACiM,CAAC,CAAC,IAAIY,EAAE,EAAE;UACd,CAAC,CAAC;UAAEG,EAAE,GAAGpO,CAAC,CAACc,QAAQ,CAAC2M,CAAC,EAAE,CAACY,CAAC,EAAEhB,CAAC,KAAKhM,CAAC,CAACgM,CAAC,CAAC,GAAG,EAAEgB,CAAC,GAAGH,EAAE,CAAC,GAAGG,CAAC,GAAGH,EAAE,EAAE,CAACG,CAAC,EAAEhB,CAAC,KAAK;YACpEhM,CAAC,CAACgM,CAAC,CAAC,IAAIa,EAAE,EAAE;UACd,CAAC,CAAC;QACF,IAAIX,CAAC,EAAE,KAAK,MAAMc,CAAC,IAAId,CAAC,EAAE,IAAI,OAAOc,CAAC,IAAI,QAAQ,EAAE;UAClD,MAAMhB,CAAC,GAAGc,EAAE,CAACE,CAAC,CAAC;YAAEV,CAAC,GAAGS,EAAE,CAACC,CAAC,CAAC;UAC1BX,CAAC,CAACvK,OAAO,CAACkL,CAAC,CAAC,EAAEf,CAAC,CAACC,CAAC,EAAEF,CAAC,EAAEM,CAAC,EAAED,CAAC,EAAEG,CAAC,CAAC,EAAEH,CAAC,CAAC1L,MAAM,CAAC,CAAC;QAC5C,CAAC,MAAM;UACL,MAAMqL,CAAC,GAAGc,EAAE,CAACE,CAAC,CAAC;YAAEV,CAAC,GAAGU,CAAC,GAAGJ,EAAE;YAAEL,EAAE,GAAGxM,CAAC,CAACoC,CAAC,CAAC6J,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGe,EAAE,CAACT,CAAC,CAAC;YAAEW,EAAE,GAAGX,CAAC,GAAGO,EAAE;UACrE7L,CAAC,CAACiM,EAAE,IAAI,CAAC,CAAC,EAAEZ,CAAC,CAACvK,OAAO,CAACmL,EAAE,CAAC,EAAEhB,CAAC,CAACC,CAAC,EAAEF,CAAC,EAAEO,EAAE,EAAEF,CAAC,EAAEG,CAAC,CAAC,EAAEH,CAAC,CAAC1L,MAAM,CAAC,CAAC;QAC1D;QACAmM,EAAE,CAAC3J,GAAG,CAAC,CAAC,EAAE4J,EAAE,CAAC5J,GAAG,CAAC,CAAC;MACpB,CAAC,EAAEoI,EAAE,EAAEC,EAAE,EAAEA,EAAE,CAAC9J,KAAK,CAAC,CAAC,EAAE+J,EAAE,EAAE,IAAI,CAAC,EAAEA,EAAE,CAACjJ,KAAK,CAAC,CAAC;MAC5C,IAAIoJ,EAAE,GAAG,EAAE;MACX,IAAI,SAASK,CAACA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEG,CAAC,EAAEC,CAAC,EAAE;QAC/BzL,CAAC,CAACmL,CAAC,CAAC;QACJ,MAAMO,CAAC,GAAGP,CAAC,CAAC/K,YAAY,CAAC,CAAC;QAC1B,IAAIuL,EAAE,GAAGxK,CAAC,CAACkK,CAAC,CAAC;UAAEO,EAAE,GAAG,CAAC,CAAC;QACtB,MAAMC,EAAE,GAAGA,CAACgB,CAAC,EAAEC,EAAE,EAAER,EAAE,KAAKO,CAAC,GAAGxS,CAAC,CAAC0N,MAAM,CAAC8E,CAAC,CAAChN,OAAO,CAAC,CAAC,EAAEiN,EAAE,CAACjN,OAAO,CAAC,CAAC,CAAC,GAAGxF,CAAC,CAACyN,QAAQ,CAACgF,EAAE,CAACjN,OAAO,CAAC,CAAC,EAAEyM,EAAE,CAAC3E,CAAC,CAAC;QAClG,IAAI3I,CAAC,CAAC0M,CAAC,CAAC,EAAE;UACR,MAAMmB,CAAC,GAAGnB,CAAC,CAACxK,CAAC;UACb2L,CAAC,IAAI,IAAI,KAAK1D,CAAC,CAAC0D,CAAC,CAAC,GAAG1B,CAAC,CAACzK,KAAK,CAAC,CAAC,CAAC;UAC/B,MAAMoM,EAAE,GAAGD,CAAC,IAAI,IAAI,GAAGnC,EAAE,CAACmC,CAAC,CAAC,GAAG,IAAI;UACnC,IAAIP,EAAE,GAAG,CAAC,CAAC;UACX,IAAIZ,CAAC,CAAC/D,CAAC,KAAK,KAAK,CAAC,IAAIkF,CAAC,IAAI,IAAI,IAAIC,EAAE,EAAE;YACrC,IAAIP,EAAE;YACNZ,EAAE,KAAKA,EAAE,CAAChE,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC4E,EAAE,GAAGZ,EAAE,CAACzK,CAAC,KAAK,IAAI,IAAI,CAAC0I,CAAC,CAAC2C,EAAE,CAAC,CAAC,KAAKD,EAAE,GAAGC,EAAE,IAAI,IAAI,GAAGM,CAAC,IAAI,IAAI,IAAIA,CAAC,KAAKzC,CAAC,CAACmC,EAAE,CAAC,GAAGhS,CAAC,CAACyD,OAAO,CAAC2N,EAAE,CAAChE,CAAC,EAAE+D,CAAC,CAAC/D,CAAC,CAAC,EAAE2E,EAAE,IAAIC,EAAE,IAAI,IAAI,IAAI3D,CAAC,KAAK,CAAC,IAAIwB,CAAC,CAACmC,EAAE,CAAC,IAAI,IAAI,IAAI1D,CAAC,IAAI,IAAI,KAAKA,CAAC,GAAG;cAChMzB,IAAI,EAAEtJ,CAAC,CAAC0E,YAAY,CAACE,cAAc;cACnCyK,GAAG,EAAEtB,EAAE,CAACgB,CAAC,IAAI,IAAI,GAAG5D,CAAC,CAAC4D,CAAC,CAAC,GAAG,IAAI,EAAE1B,CAAC,EAAEO,CAAC,CAAC;cACtC0B,GAAG,EAAEvB,EAAE,CAACU,EAAE,IAAI,IAAI,GAAGnD,CAAC,CAACmD,EAAE,CAAC,GAAG,IAAI,EAAElB,CAAC,EAAEM,EAAE;YAC1C,CAAC,CAAC,CAAC,EAAEW,EAAE,KAAKb,CAAC,GAAG5C,CAAC,IAAI,IAAI,KAAKA,CAAC,GAAG;cAChCzB,IAAI,EAAEtJ,CAAC,CAAC0E,YAAY,CAACC,qBAAqB;cAC1C0K,GAAG,EAAEtB,EAAE,CAACgB,CAAC,IAAI,IAAI,GAAG5D,CAAC,CAAC4D,CAAC,CAAC,GAAG,IAAI,EAAE1B,CAAC,EAAEO,CAAC,CAAC;cACtC0B,GAAG,EAAE/S,CAAC,CAAC2N,QAAQ,CAACyD,CAAC,CAAC5L,OAAO,CAAC,CAAC;YAC7B,CAAC,CAAC,IAAIgN,CAAC,IAAI,IAAI,IAAIjC,EAAE,CAACN,EAAE,CAAC,GAAGuC,CAAC,EAAErB,CAAC,CAACpM,KAAK,CAAC,GAAG,EAAE0N,EAAE,CAAC9L,CAAC,GAAGsJ,EAAE,EAAE,CAAC,IAAIkB,CAAC,CAACpM,KAAK,CAAC,GAAG,EAAE1B,CAAC,CAACM,OAAO,CAAC0N,CAAC,CAAC/D,CAAC,CAAC,CAAC,EAAEiE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;UACrG,CAAC,MAAM,IAAIiB,CAAC,IAAI,IAAI,IAAI,CAACC,EAAE,EAAE;YAC3B,MAAMP,EAAE,GAAG7C,CAAC,CAACmD,CAAC,CAAC;YACfN,EAAE,KAAKd,CAAC,GAAGc,EAAE,CAAC7L,KAAK,CAAC,CAAC,CAAC;UACxB;UACAmM,CAAC,IAAI,IAAI,IAAI3B,CAAC,GAAGjC,CAAC,CAAC4D,CAAC,CAAC,EAAEzB,CAAC,GAAG5B,CAAC,CAACqD,CAAC,CAAC,EAAExB,CAAC,GAAG5B,CAAC,CAACoD,CAAC,CAAC,IAAInB,CAAC,CAAC/D,CAAC,KAAK,KAAK,CAAC,KAAKuD,CAAC,GAAGE,CAAC,GAAG,IAAI,EAAEkB,EAAE,KAAKjB,CAAC,GAAG,IAAI,CAAC,CAAC;QACjG,CAAC,MAAMtM,CAAC,CAACoC,CAAC,CAAC+J,CAAC,CAAC,CAAC,KAAKA,CAAC,GAAGE,CAAC,GAAGC,CAAC,GAAG,IAAI,CAAC;QACpC,MAAMS,EAAE,GAAG3K,CAAC,CAAC+J,CAAC,CAAC;UAAEa,EAAE,GAAG5K,CAAC,CAACiK,CAAC,CAAC;QAC1B,IAAIrM,CAAC,CAACgN,EAAE,CAAC,EAAE;UACT,MAAMc,CAAC,GAAGd,EAAE,CAAC/K,CAAC;UACd+K,EAAE,CAACpO,CAAC,KAAK,KAAK,CAAC,KAAK,CAACmO,EAAE,IAAIA,EAAE,CAACnO,CAAC,KAAK,KAAK,CAAC,CAAC,IAAIiM,CAAC,CAACiD,CAAC,CAAC,IAAIxB,CAAC,GAAG,IAAI,EAAEI,CAAC,GAAGL,CAAC,CAAC1K,KAAK,CAAC,CAAC,IAAImM,CAAC,IAAI,IAAI,KAAKxB,CAAC,GAAGrK,CAAC,CAAC6L,CAAC,CAAC,EAAEjE,CAAC,KAAK,CAAC,IAAIwB,CAAC,CAACyC,CAAC,CAAC,IAAI,IAAI,KAAK,CAACrB,CAAC,GAAGrB,EAAE,CAAC0C,CAAC,CAAC,MAAMrB,CAAC,GAAGrB,EAAE,CAAC0C,CAAC,CAAC,GAAGlP,CAAC,CAACgB,WAAW,CAAC,CAAC,CAAC,EAAE6M,CAAC,CAAChK,KAAK,CAAC,CAAC,EAAEiK,CAAC,GAAG,IAAI,CAAC,CAAC;QAC3M,CAAC,MAAM,CAACzM,CAAC,CAAC0M,CAAC,CAAC,IAAI1M,CAAC,CAAC2M,EAAE,CAAC,KAAKN,CAAC,GAAG,IAAI,CAAC;QACnCM,EAAE,GAAGN,CAAC,IAAI,IAAI,GAAGA,CAAC,CAACjL,YAAY,CAAC,CAAC,GAAG,IAAI;QACxC,MAAM4L,CAAC,GAAGvH,EAAE,CAACiH,CAAC,CAAC;QACf,IAAIM,CAAC,EAAE;UACL,MAAMa,CAAC,GAAGpJ,CAAC,CAACiI,CAAC,CAAC;UACd,IAAID,CAAC,EAAE5C,CAAC,IAAI,IAAI,KAAKA,CAAC,GAAG;YACvBzB,IAAI,EAAEtJ,CAAC,CAAC0E,YAAY,CAACC,qBAAqB;YAC1C0K,GAAG,EAAE9S,CAAC,CAACuN,MAAM,CAACuD,CAAC,CAACtL,OAAO,CAAC,CAAC,EAAEmM,CAAC,EAAEa,CAAC,EAAE,CAAC,CAAC,CAAC;YACpCO,GAAG,EAAE/S,CAAC,CAAC2N,QAAQ,CAACyD,CAAC,CAAC5L,OAAO,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,KACE;YACH,MAAMiN,EAAE,GAAGrI,EAAE,CAACkH,EAAE,CAAC;YACjB,IAAIW,EAAE;YACN,IAAIQ,EAAE,EAAE;cACN,IAAId,CAAC,KAAKc,EAAE,EAAE,MAAM7N,KAAK,CAAC,iCAAiC,CAAC;cAC5D,MAAMsN,EAAE,GAAG9I,CAAC,CAACkI,EAAE,CAAC;cAChBW,EAAE,GAAGN,CAAC,CAAC1G,SAAS,CAACuH,CAAC,EAAEN,EAAE,EAAE9D,CAAC,CAAC;YAC5B,CAAC,MAAM6D,EAAE,GAAG5O,CAAC,CAACM,OAAO,CAAC6O,CAAC,CAAC;YACxBnJ,CAAC,CAAC8H,CAAC,EAAEQ,CAAC,EAAEM,EAAE,CAAC;UACb;QACF;QACA,IAAItB,CAAC,GAAG,CAAC;UAAEM,CAAC,GAAG,CAAC;UAAEC,EAAE,GAAG,CAAC;UAAEU,EAAE,GAAG,CAAC;UAAEC,EAAE,GAAG,CAAC;UAAEC,EAAE,GAAG,CAAC;UAAEC,EAAE,GAAGlB,CAAC,IAAI,IAAI,IAAIA,CAAC,CAAC7K,YAAY,CAAC,CAAC;UAAEgM,EAAE,GAAGD,EAAE;QAC7F,MAAMM,EAAE,GAAG/O,CAAC,CAACc,QAAQ,CAAC2M,CAAC,EAAE,KAAK,CAAC,EAAE,CAACyB,CAAC,EAAEC,EAAE,KAAK;UAC1C/N,CAAC,CAAC+N,EAAE,CAAC,IAAIvB,EAAE,EAAE;QACf,CAAC,CAAC;QACF,IAAIoB,EAAE,GAAGtB,CAAC,IAAI,IAAI,IAAIA,CAAC,CAAChL,YAAY,CAAC,CAAC;UAAEuM,EAAE,GAAGD,EAAE;QAC/C,KAAK,MAAME,CAAC,IAAI1B,CAAC,EAAE,IAAI,OAAO0B,CAAC,IAAI,QAAQ,EAAE;UAC3C,IAAIC,EAAE;UACN,MAAMR,EAAE,GAAGtN,CAAC,CAACmM,CAAC,CAAC/K,YAAY,CAAC,CAAC,CAAC;YAAEmM,EAAE,GAAGM,CAAC,GAAGvB,CAAC;UAC1C;YACE,IAAIgC,EAAE;YACN,OAAOlB,EAAE,IAAI,QAAQkB,EAAE,GAAGpC,CAAC,CAAC/K,MAAM,CAAC,CAAC,CAAC,IAAI,QAAQ,GAAI;cACnDmN,EAAE,IAAItC,CAAC;cACP,MAAMuC,EAAE,GAAGrC,CAAC,CAAC9K,YAAY,CAAC,CAAC;gBAAEoN,EAAE,GAAGzO,CAAC,CAACwO,EAAE,CAAC;cACvC,IAAID,EAAE,GAAGf,EAAE,IAAIe,EAAE,KAAKf,EAAE,KAAK,CAACiB,EAAE,IAAI5E,CAAC,KAAK,CAAC,IAAI0D,EAAE,CAAC,EAAE;cACpD,IAAIkB,EAAE,EAAE;gBACNxC,CAAC,EAAE;gBACH,MAAMyC,EAAE,GAAGF,EAAE,CAACvM,CAAC;gBACfoJ,CAAC,CAACsD,QAAQ,CAACD,EAAE,CAAC,EAAEF,EAAE,CAACrM,CAAC,EAAEC,CAAC,CAACgJ,EAAE,CAACoD,EAAE,CAACrM,CAAC,CAAC,CAAC,EAAEnC,CAAC,CAACoC,CAAC,CAACgJ,EAAE,CAACoD,EAAE,CAACrM,CAAC,CAAC,CAAC,CAAC,EAAE,CAACqM,EAAE,CAAC5P,CAAC,KAAK,KAAK,CAAC,IAAIgN,EAAE,IAAIA,EAAE,CAACrB,GAAG,CAACiE,EAAE,CAAC,MAAME,EAAE,IAAI,IAAI,IAAI,CAAC/C,EAAE,CAAC+C,EAAE,CAAC,IAAI7E,CAAC,KAAK,CAAC,IAAIwB,CAAC,CAACsD,QAAQ,CAACD,EAAE,CAAC,CAAC,IAAIvB,EAAE,EAAE;cAC1J;cACAE,EAAE,GAAGlB,CAAC,CAAC3K,WAAW,CAAC,CAAC;YACtB;YACAuM,EAAE,GAAGV,EAAE,IAAIkB,EAAE,KAAKf,EAAE,GAAGrB,CAAC,GAAG,IAAI;UACjC;UACA,MAAMsB,EAAE,GAAGD,EAAE,GAAGvB,CAAC;UACjB,IAAIyB,EAAE,GAAGC,EAAE,CAACF,EAAE,CAAC;UACf,MAAMmB,EAAE,GAAGnB,EAAE,GAAGjB,EAAE;UAClB,IAAIqC,EAAE,GAAG,IAAI;UACb;YACE,IAAIN,EAAE,EAAEC,EAAE;YACV,OAAOZ,EAAE,IAAI,QAAQW,EAAE,GAAGjC,CAAC,CAAClL,MAAM,CAAC,CAAC,CAAC,IAAI,QAAQ,GAAI;cACnDoN,EAAE,GAAGD,EAAE,GAAGrB,EAAE;cACZ,MAAMuB,EAAE,GAAGnC,CAAC,CAACjL,YAAY,CAAC,CAAC;gBAAEqN,EAAE,GAAGzO,CAAC,CAACwO,EAAE,CAAC;cACvC,IAAID,EAAE,GAAGI,EAAE,EAAE;cACb,IAAIJ,EAAE,KAAKI,EAAE,EAAE;gBACb,IAAI,CAACF,EAAE,EAAE;kBACPG,EAAE,GAAGvC,CAAC;kBACN;gBACF;gBACA;kBACE,IAAIzC,CAAC,KAAK,CAAC,IAAI0D,EAAE,EAAE;oBACjBsB,EAAE,GAAGvC,CAAC;oBACN;kBACF;kBACA,MAAMwC,EAAE,GAAGpB,EAAE,IAAI1N,CAAC,CAAC0N,EAAE,CAACrM,YAAY,CAAC,CAAC,CAAC;kBACrC,IAAIwI,CAAC,KAAK,CAAC,IAAIiF,EAAE,EAAE;gBACrB;cACF;cACA,IAAIJ,EAAE,EAAE;gBACN,MAAMI,EAAE,GAAGL,EAAE,CAACtM,CAAC;gBACf0I,CAAC,CAACiE,EAAE,CAAC,EAAEzD,CAAC,CAACyD,EAAE,CAAC,EAAEL,EAAE,CAAC7F,CAAC,KAAK,KAAK,CAAC,KAAKiC,CAAC,CAACiE,EAAE,CAAC,IAAIzD,CAAC,CAACyD,EAAE,CAAC,IAAI,IAAI,IAAIjF,CAAC,KAAK,CAAC,CAAC,GAAG,CAACgB,CAAC,CAACiE,EAAE,CAAC,IAAIzD,CAAC,CAACyD,EAAE,CAAC,IAAI,IAAI,IAAIjF,CAAC,KAAK,CAAC,MAAMqD,EAAE,EAAE,EAAEE,EAAE,EAAE,CAAC,GAAGF,EAAE,EAAE;cACnI;cACAU,EAAE,GAAGtB,CAAC,CAAC9K,WAAW,CAAC,CAAC;YACtB;UACF;UACA,MAAMuN,EAAE,GAAGH,EAAE,GAAG1B,EAAE,GAAGC,EAAE,GAAGC,EAAE;UAC5BnM,CAAC,CAAC8N,EAAE,IAAI,CAAC,EAAE,uCAAuC,CAAC,EAAEtC,CAAC,CAAC1K,OAAO,CAACgN,EAAE,CAAC,EAAExB,EAAE,KAAKQ,EAAE,GAAGL,EAAE,GAAGmB,EAAE,GAAG,IAAI,EAAEtC,CAAC,EAAE,CAAC,EAAEL,CAAC,CAAC6B,EAAE,EAAE3B,CAAC,EAAEsB,EAAE,EAAEmB,EAAE,EAAEpC,CAAC,EAAEC,CAAC,CAAC,IAAIU,EAAE,EAAE,EAAEX,CAAC,CAAC7L,MAAM,CAAC,CAAC;QAClJ,CAAC,MAAM;UACL,IAAImN,EAAE;UACN,OAAOV,EAAE,KAAKU,EAAE,GAAG5B,CAAC,CAAC/K,MAAM,CAAC,CAAC,EAAE,OAAO2M,EAAE,IAAI,QAAQ,IAAI,EAAEA,EAAE,GAAGD,CAAC,IAAIC,EAAE,KAAKD,CAAC,CAAC,CAAC,GAAIT,EAAE,GAAGlB,CAAC,CAAC3K,WAAW,CAAC,CAAC;UACtG,MAAM+L,EAAE,GAAGF,EAAE,IAAIU,EAAE,KAAKD,CAAC,GAAG3B,CAAC,GAAG,IAAI;YAAEqB,EAAE,GAAGG,EAAE,CAACG,CAAC,CAAC;UAChD,IAAIL,EAAE;UACN,OAAOG,EAAE,KAAKH,EAAE,GAAGnB,CAAC,CAAClL,MAAM,CAAC,CAAC,EAAE,OAAOqM,EAAE,IAAI,QAAQ,IAAI,EAAEA,EAAE,GAAGK,CAAC,IAAIL,EAAE,KAAKK,CAAC,CAAC,CAAC,GAAIF,EAAE,GAAGtB,CAAC,CAAC9K,WAAW,CAAC,CAAC;UACtG,MAAMkM,EAAE,GAAGE,EAAE,IAAIH,EAAE,KAAKK,CAAC,GAAGxB,CAAC,GAAG,IAAI;UACpCG,CAAC,CAAC1K,OAAO,CAAC+L,CAAC,CAAC,EAAE5B,CAAC,CAACqB,EAAE,EAAEnB,CAAC,EAAEoB,EAAE,EAAEE,EAAE,EAAEjB,CAAC,EAAEC,CAAC,CAAC,EAAED,CAAC,CAAC7L,MAAM,CAAC,CAAC;QAClD;QACA,OAAO+M,EAAE,CAACvK,GAAG,CAAC,CAAC,EAAEkK,EAAE,IAAInB,CAAC,CAACvL,MAAM,CAAC,CAAC,EAAEiN,EAAE,IAAIvB,CAAC,CAAC1L,MAAM,CAAC,CAAC,EAAEiM,EAAE;MACzD,CAAC,CAACrB,EAAE,EAAEA,EAAE,CAAC7J,KAAK,CAAC,CAAC,EAAE8J,EAAE,EAAEA,EAAE,CAAC9J,KAAK,CAAC,CAAC,EAAE+J,EAAE,EAAE,IAAI,CAAC,EAAE5B,CAAC,EAAE,OAAO;QACrDoE,EAAE,EAAE,CAAC,CAAC;QACNI,QAAQ,EAAExE;MACZ,CAAC;MACD4B,EAAE,CAACjJ,KAAK,CAAC,CAAC;MACV,MAAMqJ,EAAE,GAAGA,CAACI,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAKF,CAAC,CAACpK,QAAQ,CAACqK,CAAC,EAAE,CAACE,CAAC,EAAEC,CAAC,KAAK;QAC9CD,CAAC,CAAClK,CAAC,IAAI,IAAI,IAAIiK,CAAC,CAACC,CAAC,CAAClK,CAAC,EAAE+J,CAAC,EAAEI,CAAC,CAAC;MAC7B,CAAC,CAAC;MACF,CAACzB,CAAC,CAAC7L,MAAM,IAAImM,EAAE,CAACnM,MAAM,MAAM8M,EAAE,CAACL,EAAE,EAAEC,EAAE,EAAE,CAACQ,CAAC,EAAEC,CAAC,EAAEC,CAAC,KAAK;QAClDvB,CAAC,CAACqB,CAAC,CAAC,IAAI,CAACpB,CAAC,CAACoB,CAAC,CAAC,IAAIE,CAAC,CAAC/L,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE8K,EAAE,CAACe,CAAC,CAAC,IAAIE,CAAC,CAACzJ,SAAS,CAACwI,EAAE,CAACe,CAAC,CAAC,CAAChL,GAAG,CAAC,CAAC,CAAC;MACtE,CAAC,CAAC,EAAEwK,EAAE,CAACjJ,KAAK,CAAC,CAAC,CAAC;MACf,MAAMsJ,EAAE,GAAG,EAAE;QAAEC,EAAE,GAAG,EAAE;MACtB,IAAI,CAACZ,EAAE,CAACpM,MAAM,IAAI6L,CAAC,CAAC7L,MAAM,KAAK,CAAC8K,CAAC,EAAE;QACjC,MAAMoC,CAAC,GAAGtN,CAAC,CAACe,UAAU,CAACsO,EAAE,CAACvC,EAAE,CAACxK,GAAG,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI4K,EAAE,CAACI,CAAC,EAAE,IAAI,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAK;UACxBL,EAAE,CAACI,CAAC,CAAC,GAAGC,CAAC,CAACzK,KAAK,CAAC,CAAC;QACnB,CAAC,CAAC,EAAEyJ,EAAE,CAACH,OAAO,CAAEkB,CAAC,IAAK;UACpBA,CAAC,IAAIL,EAAE,CAAClN,CAAC,CAACe,UAAU,CAACwM,CAAC,CAACjL,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAACkL,CAAC,EAAEC,CAAC,KAAK;YAC7CN,EAAE,CAACK,CAAC,CAAC,GAAGC,CAAC,CAAC1K,KAAK,CAAC,CAAC;UACnB,CAAC,CAAC;QACJ,CAAC,CAAC,EAAE,SAASwK,CAACA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEG,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;UAC/B,MAAMC,EAAE,GAAGxK,CAAC,CAACiK,CAAC,CAAC;UACf,IAAIO,EAAE,IAAI5M,CAAC,CAAC4M,EAAE,CAAC;YAAE,IAAIA,EAAE,CAAC3K,CAAC,IAAI,IAAI,EAAE;cACjC,MAAMgK,CAAC,GAAGW,EAAE,CAAC3K,CAAC;cACd8J,EAAE,CAACE,CAAC,CAAC,CAACnL,OAAO,CAAC,CAAC,EAAEwL,CAAC,GAAGP,EAAE,CAACE,CAAC,CAAC,EAAEQ,CAAC,GAAGT,EAAE,CAACC,CAAC,CAAC,GAAGrN,CAAC,CAACgB,WAAW,CAAC,CAAC;YACzD,CAAC,MAAMgN,EAAE,CAAChO,CAAC,KAAK,KAAK,CAAC,KAAK0N,CAAC,GAAG,IAAI,CAAC;UAAC,OAChCrM,CAAC,CAACmC,CAAC,CAACkK,CAAC,CAAC,CAAC,KAAKA,CAAC,GAAG,IAAI,CAAC;UAC1B,MAAMO,EAAE,GAAGT,CAAC,CAAC/K,YAAY,CAAC,CAAC;UAC3B,IAAIwL,EAAE,EAAE;YACN,IAAIZ,CAAC;YACL,IAAI,CAACA,CAAC,GAAGY,EAAE,CAAC1K,CAAC,KAAK,IAAI,EAAE;cACtB,MAAMoK,CAAC,GAAGnB,EAAE,CAACa,CAAC,CAAC;cACfM,CAAC,KAAKA,CAAC,CAACrL,GAAG,CAAC,CAAC,EAAEuL,CAAC,CAAC9J,SAAS,CAAC4J,CAAC,CAACrL,GAAG,CAAC,CAAC,CAAC,EAAEoL,CAAC,GAAG1N,CAAC,CAACe,UAAU,CAAC4M,CAAC,CAACrL,GAAG,CAAC,CAAC,CAAC,CAAC;YACjE;UACF;UACA,IAAI4L,EAAE,GAAG,CAAC;YAAEC,EAAE,GAAG,CAAC;UAClB,MAAMC,EAAE,GAAGpO,CAAC,CAACc,QAAQ,CAAC2M,CAAC,EAAE,KAAK,CAAC,EAAE,CAACJ,CAAC,EAAEM,CAAC,KAAK;cACzCvM,CAAC,CAACuM,CAAC,CAAC,IAAIO,EAAE,EAAE;YACd,CAAC,CAAC;YAAEG,CAAC,GAAGrO,CAAC,CAACc,QAAQ,CAAC4M,CAAC,EAAE,CAACL,CAAC,EAAEM,CAAC,KAAKtM,CAAC,CAACsM,CAAC,CAAC,GAAG,EAAEN,CAAC,GAAGc,EAAE,CAAC,GAAG,CAAC,GAAGd,CAAC,GAAGc,EAAE,EAAE,CAACd,CAAC,EAAEM,CAAC,KAAK;cACvEtM,CAAC,CAACsM,CAAC,CAAC,IAAIQ,EAAE,EAAE;YACd,CAAC,CAAC;UACF,KAAK,MAAMd,CAAC,IAAIG,CAAC,EAAE,IAAI,OAAOH,CAAC,IAAI,QAAQ,EAAE;YAC3C,MAAMM,CAAC,GAAGS,EAAE,CAACf,CAAC,CAAC;cAAEO,EAAE,GAAGP,CAAC,GAAGa,EAAE;cAAEI,EAAE,GAAGD,CAAC,CAACT,EAAE,CAAC;cAAEW,EAAE,GAAGX,EAAE,GAAGO,EAAE;YACtDN,CAAC,CAAC1K,OAAO,CAACoL,EAAE,CAAC,EAAEhB,CAAC,CAACC,CAAC,EAAEG,CAAC,EAAEW,EAAE,EAAET,CAAC,CAAC,EAAEA,CAAC,CAAC7L,MAAM,CAAC,CAAC;UAC3C,CAAC,MAAM6L,CAAC,CAAC1K,OAAO,CAACkK,CAAC,CAAC,EAAEE,CAAC,CAACC,CAAC,EAAEY,EAAE,CAACf,CAAC,CAAC,EAAEgB,CAAC,CAAChB,CAAC,CAAC,EAAEQ,CAAC,CAAC,EAAEA,CAAC,CAAC7L,MAAM,CAAC,CAAC;UACrDoM,EAAE,CAAC5J,GAAG,CAAC,CAAC,EAAE6J,CAAC,CAAC7J,GAAG,CAAC,CAAC;QACnB,CAAC,CAACqI,EAAE,EAAES,CAAC,EAAEA,CAAC,CAACvK,KAAK,CAAC,CAAC,EAAE+J,EAAE,CAAC,EAAEA,EAAE,CAACjJ,KAAK,CAAC,CAAC,EAAEqH,CAAC,EAAE,OAAO;UAC7CoE,EAAE,EAAE,CAAC,CAAC;UACNI,QAAQ,EAAExE;QACZ,CAAC;QACD,IAAI4B,EAAE,CAACxK,GAAG,CAAC,CAAC,EAAE8K,EAAE,CAAChN,MAAM,EAAE;UACvB,MAAMmN,CAAC,GAAGH,EAAE,CAAC3M,GAAG,CAAEgN,CAAC,IAAKA,CAAC,GAAGA,CAAC,CAACnL,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;YAAEkL,CAAC,GAAGxN,CAAC,CAACe,UAAU,CAACsO,EAAE,CAACvC,EAAE,CAACxK,GAAG,CAAC,CAAC,CAAC,CAAC;UAC3E,IAAI4K,EAAE,CAACM,CAAC,EAAEV,EAAE,EAAE,CAACW,CAAC,EAAEC,CAAC,EAAEG,CAAC,KAAK;YACzB,MAAMC,CAAC,GAAGP,CAAC,CAACE,CAAC,CAAC;YACdK,CAAC,KAAKD,CAAC,CAAC9J,SAAS,CAAC+J,CAAC,CAAC,EAAEP,CAAC,CAACE,CAAC,CAAC,GAAG,IAAI,CAAC;UACpC,CAAC,CAAC,EAAEF,CAAC,CAAC6C,IAAI,CAAE3C,CAAC,IAAKA,CAAC,CAAC,EAAE;YACpB,MAAMA,CAAC,GAAGzN,CAAC,CAACgB,WAAW,CAAC,CAAC;cAAE0M,CAAC,GAAG1N,CAAC,CAACgB,WAAW,CAAC,CAAC;YAC9C,IAAI6M,CAAC,GAAG,CAAC;cAAEC,CAAC,GAAG,CAAC;YAChBP,CAAC,CAAClB,OAAO,CAAE0B,CAAC,IAAK;cACfA,CAAC,IAAI,IAAI,IAAIb,EAAE,CAAClN,CAAC,CAACe,UAAU,CAACgN,CAAC,CAAC,EAAE,IAAI,EAAGC,EAAE,IAAK;gBAC7C,MAAMC,EAAE,GAAGhB,EAAE,CAACe,EAAE,CAAC;gBACjBP,CAAC,CAACrJ,SAAS,CAACkH,CAAC,CAAC2C,EAAE,CAAC,CAAC/L,OAAO,CAAC,CAAC,EAAEsJ,CAAC,CAACyC,EAAE,CAAC,CAAC/L,OAAO,CAAC,CAAC,EAAE2L,CAAC,EAAE,CAAC;gBAClD,MAAMK,EAAE,GAAGxB,EAAE,CAACuB,EAAE,CAAC;gBACjBC,EAAE,IAAIA,EAAE,CAAC7B,OAAO,CAAE8B,EAAE,IAAK;kBACvBlC,CAAC,CAACkC,EAAE,CAAC,IAAIlD,CAAC,KAAK,CAAC,IAAIwB,CAAC,CAAC0B,EAAE,CAAC,IAAI,IAAI,IAAIT,CAAC,CAACtJ,SAAS,CAACqH,CAAC,CAAC0C,EAAE,CAAC,CAACjM,OAAO,CAAC,CAAC,EAAEmB,CAAC,CAAC8K,EAAE,CAAC,CAACjM,OAAO,CAAC,CAAC,EAAE4L,CAAC,EAAE,CAAC;gBACzF,CAAC,CAAC;cACJ,CAAC,CAAC;YACJ,CAAC,CAAC,EAAE5C,CAAC,GAAG;cACNzB,IAAI,EAAEtJ,CAAC,CAAC0E,YAAY,CAACG,SAAS;cAC9BwK,GAAG,EAAE/B,CAAC,CAACnL,GAAG,CAAC,CAAC;cACZmN,GAAG,EAAE/B,CAAC,CAACpL,GAAG,CAAC;YACb,CAAC;UACH;QACF;MACF;MACA,OAAO4I,CAAC,GAAG;QACToE,EAAE,EAAE,CAAC,CAAC;QACNI,QAAQ,EAAExE;MACZ,CAAC,GAAG;QACFoE,EAAE,EAAE,CAAC,CAAC;QACNC,MAAM,EAAEzC,EAAE,CAACxK,GAAG,CAAC;MACjB,CAAC;IACH;IACA,MAAM+N,EAAE,GAAIrG,CAAC,IAAK;MAChB,MAAMzG,CAAC,GAAG,IAAIjC,KAAK,CAAC,mCAAmC,CAAC;MACxD,MAAMiC,CAAC,CAACmM,QAAQ,GAAG1F,CAAC,EAAEzG,CAAC,CAACkG,IAAI,GAAGlG,CAAC,CAACwD,IAAI,GAAG,eAAe,EAAExD,CAAC;IAC5D,CAAC;IACD,SAASoH,EAAEA,CAACX,CAAC,EAAEzG,CAAC,EAAEuH,CAAC,EAAE;MACnB,MAAMG,CAAC,GAAGP,EAAE,CAACV,CAAC,EAAEzG,CAAC,EAAEuH,CAAC,CAAC;MACrB,IAAIG,CAAC,CAACqE,EAAE,EAAE,OAAOrE,CAAC,CAACsE,MAAM;MACzBc,EAAE,CAACpF,CAAC,CAACyE,QAAQ,CAAC;IAChB;IACA,MAAMY,EAAE,GAAItG,CAAC,IAAK;QAChB,MAAMzG,CAAC,GAAGvD,CAAC,CAACgB,WAAW,CAAC,CAAC;QACzB,OAAOhB,CAAC,CAACe,UAAU,CAACiJ,CAAC,CAAC,CAAC9G,QAAQ,CAACK,CAAC,EAAE,CAACuH,CAAC,EAAEG,CAAC,KAAK;UAC3C,CAAC5J,CAAC,CAACyJ,CAAC,CAAC,IAAIhE,EAAE,CAACgE,CAAC,CAAC,KAAKG,CAAC,CAACxJ,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,EAAE8B,CAAC,CAACjB,GAAG,CAAC,CAAC;MACb,CAAC;MAAEiO,EAAE,GAAGA,CAACvG,CAAC,EAAEzG,CAAC,KAAK;QAChB,MAAM;UAAEkG,IAAI,EAAEqB,CAAC;UAAE0E,GAAG,EAAEvE,CAAC;UAAEwE,GAAG,EAAEvE;QAAE,CAAC,GAAGlB,CAAC;QACrC,QAAQc,CAAC;UACP,KAAK3K,CAAC,CAAC0E,YAAY,CAACE,cAAc;YAChC,OAAOxB,CAAC,KAAK,MAAM,GAAG,CAAC,IAAI,EAAE+M,EAAE,CAACpF,CAAC,CAAC,CAAC,GAAG,CAACoF,EAAE,CAACrF,CAAC,CAAC,EAAE,IAAI,CAAC;UACrD,KAAK9K,CAAC,CAAC0E,YAAY,CAACC,qBAAqB;YACvC,IAAIwG,CAAC,GAAG,CAAC,CAAC;YACV,OAAOtL,CAAC,CAACe,UAAU,CAACkK,CAAC,CAAC,CAAC/H,QAAQ,CAAC,IAAI,EAAGsI,CAAC,IAAK;cAC3CA,CAAC,CAACxL,CAAC,KAAK,KAAK,CAAC,KAAKsL,CAAC,GAAG,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAI,EAAEgF,EAAE,CAACpF,CAAC,CAAC,CAAC,GAAG,CAACoF,EAAE,CAACrF,CAAC,CAAC,EAAE,IAAI,CAAC;UACvC,KAAK9K,CAAC,CAAC0E,YAAY,CAACG,SAAS;YAC3B,OAAO,CAACsL,EAAE,CAACrF,CAAC,CAAC,EAAEqF,EAAE,CAACpF,CAAC,CAAC,CAAC;UACvB;YACE,MAAM5J,KAAK,CAAC,yBAAyB,GAAGwJ,CAAC,CAAC;QAC9C;MACF,CAAC;IACD,SAASC,EAAEA,CAACf,CAAC,EAAEzG,CAAC,EAAEuH,CAAC,EAAEG,CAAC,EAAE;MACtB,IAAIC,CAAC,GAAG,IAAI;MACZ,SAAW;QACT,MAAMI,CAAC,GAAGZ,EAAE,CAACnH,CAAC,EAAEuH,CAAC,EAAEG,CAAC,CAAC;QACrB,IAAIK,CAAC,CAACgE,EAAE,EAAE,OAAO7I,EAAE,CAACyE,CAAC,EAAEI,CAAC,CAACiE,MAAM,CAAC;QAChC;UACE,MAAM;YAAEG,QAAQ,EAAElE;UAAE,CAAC,GAAGF,CAAC;UACzBtB,CAAC,CAACwB,CAAC,CAAC,IAAI6E,EAAE,CAAC7E,CAAC,CAAC;UACb,MAAM,CAACC,CAAC,EAAEpI,CAAC,CAAC,GAAGkN,EAAE,CAAC/E,CAAC,EAAEP,CAAC,CAAC;UACvB1H,CAAC,GAAGkD,EAAE,CAACN,CAAC,CAAC5C,CAAC,CAAC,EAAEkI,CAAC,CAAC,EAAEX,CAAC,GAAGrE,EAAE,CAACN,CAAC,CAAC2E,CAAC,CAAC,EAAEzH,CAAC,CAAC,EAAE6H,CAAC,GAAGzE,EAAE,CAACyE,CAAC,EAAE7H,CAAC,CAAC;QAChD;MACF;IACF;EACF,CAAC,CAAC1D,EAAE,CAAC,CAAC,EAAEA,EAAE;AACZ;AACA,IAAI6Q,EAAE;AACN,SAASC,EAAEA,CAAA,EAAG;EACZ,OAAOD,EAAE,KAAKA,EAAE,GAAG,CAAC,EAAE,UAAS9T,CAAC,EAAE;IAChC,IAAIC,CAAC,GAAG+C,EAAE,IAAIA,EAAE,CAACwJ,eAAe,KAAK3M,MAAM,CAAC6K,MAAM,GAAG,UAASjH,CAAC,EAAEkC,CAAC,EAAEmB,CAAC,EAAEa,CAAC,EAAE;QACxEA,CAAC,KAAK,KAAK,CAAC,KAAKA,CAAC,GAAGb,CAAC,CAAC,EAAEjH,MAAM,CAACC,cAAc,CAAC2D,CAAC,EAAEkE,CAAC,EAAE;UAAExH,UAAU,EAAE,CAAC,CAAC;UAAEyF,GAAG,EAAE,SAAAA,CAAA,EAAW;YACrF,OAAOD,CAAC,CAACmB,CAAC,CAAC;UACb;QAAE,CAAC,CAAC;MACN,CAAC,GAAG,UAASrD,CAAC,EAAEkC,CAAC,EAAEmB,CAAC,EAAEa,CAAC,EAAE;QACvBA,CAAC,KAAK,KAAK,CAAC,KAAKA,CAAC,GAAGb,CAAC,CAAC,EAAErD,CAAC,CAACkE,CAAC,CAAC,GAAGhC,CAAC,CAACmB,CAAC,CAAC;MACtC,CAAC,CAAC;MAAE5G,CAAC,GAAG8C,EAAE,IAAIA,EAAE,CAACgR,YAAY,IAAI,UAASvQ,CAAC,EAAEkC,CAAC,EAAE;QAC9C,KAAK,IAAImB,CAAC,IAAIrD,CAAC,EAAEqD,CAAC,KAAK,SAAS,IAAI,CAACnB,CAAC,CAACiH,cAAc,CAAC9F,CAAC,CAAC,IAAI7G,CAAC,CAAC0F,CAAC,EAAElC,CAAC,EAAEqD,CAAC,CAAC;MACxE,CAAC;IACDjH,MAAM,CAACC,cAAc,CAACE,CAAC,EAAE,YAAY,EAAE;MAAEM,KAAK,EAAE,CAAC;IAAE,CAAC,CAAC,EAAEJ,CAAC,CAACmN,EAAE,CAAC,CAAC,EAAErN,CAAC,CAAC;IACjE,IAAIqD,CAAC,GAAGa,EAAE,CAAC,CAAC;IACZrE,MAAM,CAACC,cAAc,CAACE,CAAC,EAAE,YAAY,EAAE;MAAEG,UAAU,EAAE,CAAC,CAAC;MAAEyF,GAAG,EAAE,SAAAA,CAAA,EAAW;QACvE,OAAOvC,CAAC,CAACmB,UAAU;MACrB;IAAE,CAAC,CAAC,EAAE3E,MAAM,CAACC,cAAc,CAACE,CAAC,EAAE,aAAa,EAAE;MAAEG,UAAU,EAAE,CAAC,CAAC;MAAEyF,GAAG,EAAE,SAAAA,CAAA,EAAW;QAC9E,OAAOvC,CAAC,CAACkB,WAAW;MACtB;IAAE,CAAC,CAAC;IACJ,IAAIjB,CAAC,GAAG4E,EAAE,CAAC,CAAC;IACZrI,MAAM,CAACC,cAAc,CAACE,CAAC,EAAE,cAAc,EAAE;MAAEG,UAAU,EAAE,CAAC,CAAC;MAAEyF,GAAG,EAAE,SAAAA,CAAA,EAAW;QACzE,OAAOtC,CAAC,CAAC6E,YAAY;MACvB;IAAE,CAAC,CAAC;EACN,CAAC,CAACnF,EAAE,CAAC,CAAC,EAAEA,EAAE;AACZ;AACA,IAAIiR,CAAC,GAAGF,EAAE,CAAC,CAAC;AACZ,MAAMG,EAAE,CAAC;EACPlP,WAAWA,CAAA,EAAG;IACZzE,CAAC,CAAC,IAAI,EAAE,oBAAoB,EAAE,CAAC,CAAC,CAAC;IACjCA,CAAC,CAAC,IAAI,EAAE,wBAAwB,EAAE,CAAC,CAAC,CAAC;IACrCA,CAAC,CAAC,IAAI,EAAE,gBAAgB,EAAE,EAAE,CAAC;IAC7BA,CAAC,CAAC,IAAI,EAAE,UAAU,EAAE,IAAImC,EAAE,CAAC,CAAC,CAAC;IAC7BnC,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC4T,QAAQ,CAACC,YAAY,CAAC,CAAC,CAAC;IAChD7T,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,IAAImC,EAAE,CAAC,CAAC,CAAC;IAC1BnC,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC8T,KAAK,CAACD,YAAY,CAAC,CAAC,CAAC;IAC1C7T,CAAC,CAAC,IAAI,EAAE,UAAU,EAAE,IAAImC,EAAE,CAAC,CAAC,CAAC;IAC7BnC,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC+T,QAAQ,CAACF,YAAY,CAAC,CAAC,CAAC;IAChD7T,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,IAAImC,EAAE,CAAC,CAAC,CAAC;IAC5BnC,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAACgU,OAAO,CAACH,YAAY,CAAC,CAAC,CAAC;IAC9C7T,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,IAAImC,EAAE,CAAC,CAAC,CAAC;IAC5BnC,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAACiU,OAAO,CAACJ,YAAY,CAAC,CAAC,CAAC;IAC9C7T,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,IAAImC,EAAE,CAAC,CAAC,CAAC;IAC9BnC,CAAC,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAACkU,SAAS,CAACL,YAAY,CAAC,CAAC,CAAC;IAClD7T,CAAC,CAAC,IAAI,EAAE,oBAAoB,EAAE,IAAImC,EAAE,CAAC,CAAC,CAAC;IACvCnC,CAAC,CAAC,IAAI,EAAE,mBAAmB,EAAE,IAAI,CAACmU,kBAAkB,CAACN,YAAY,CAAC,CAAC,CAAC;IACpE7T,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,IAAImC,EAAE,CAAC,CAAC,CAAC;IAC9BnC,CAAC,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,CAACoU,SAAS,CAACP,YAAY,CAAC,CAAC,CAAC;IAClD;IACA;IACA7T,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,IAAImC,EAAE,CAAC,CAAC,CAAC;IAC5BnC,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAACqU,OAAO,CAACR,YAAY,CAAC,CAAC,CAAC;IAC9C7T,CAAC,CAAC,IAAI,EAAE,uBAAuB,EAAE,IAAImC,EAAE,CAAC,CAAC,CAAC;IAC1CnC,CAAC,CAAC,IAAI,EAAE,sBAAsB,EAAE,IAAI,CAACsU,qBAAqB,CAACT,YAAY,CAAC,CAAC,CAAC;IAC1E7T,CAAC,CAAC,IAAI,EAAE,oBAAoB,EAAE,IAAImC,EAAE,CAAC,CAAC,CAAC;IACvCnC,CAAC,CAAC,IAAI,EAAE,mBAAmB,EAAE,IAAI,CAACuU,kBAAkB,CAACV,YAAY,CAAC,CAAC,CAAC;IACpE7T,CAAC,CAAC,IAAI,EAAE,uBAAuB,EAAE,IAAImC,EAAE,CAAC,CAAC,CAAC;IAC1CnC,CAAC,CAAC,IAAI,EAAE,sBAAsB,EAAE,IAAI,CAACwU,qBAAqB,CAACX,YAAY,CAAC,CAAC,CAAC;IAC1E7T,CAAC,CAAC,IAAI,EAAE,4BAA4B,EAAE,IAAImC,EAAE,CAAC,CAAC,CAAC;IAC/CnC,CAAC,CAAC,IAAI,EAAE,2BAA2B,EAAE,IAAI,CAACyU,0BAA0B,CAACZ,YAAY,CAAC,CAAC,CAAC;IACpF7T,CAAC,CAAC,IAAI,EAAE,4BAA4B,EAAE,IAAImC,EAAE,CAAC,CAAC,CAAC;IAC/CnC,CAAC,CAAC,IAAI,EAAE,2BAA2B,EAAE,IAAI,CAAC0U,0BAA0B,CAACb,YAAY,CAAC,CAAC,CAAC;IACpF7T,CAAC,CAAC,IAAI,EAAE,8BAA8B,EAAE,IAAImC,EAAE,CAAC,CAAC,CAAC;IACjDnC,CAAC,CAAC,IAAI,EAAE,6BAA6B,EAAE,IAAI,CAAC2U,4BAA4B,CAACd,YAAY,CAAC,CAAC,CAAC;IACxF7T,CAAC,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;IACvBA,CAAC,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC;EAC1B;EACA4U,OAAOA,CAAA,EAAG;IACR,IAAI,CAAChB,QAAQ,CAACiB,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACf,KAAK,CAACe,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACd,QAAQ,CAACc,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACb,OAAO,CAACa,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACR,OAAO,CAACQ,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACP,qBAAqB,CAACO,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACN,kBAAkB,CAACM,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACL,qBAAqB,CAACK,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACJ,0BAA0B,CAACI,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACC,kBAAkB,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,sBAAsB,GAAG,CAAC,CAAC;EAC3V;EACAC,mBAAmBA,CAACtV,CAAC,EAAE;IACrB,IAAI,CAAC0U,SAAS,CAACa,IAAI,CAACvV,CAAC,CAAC;EACxB;EACAwV,gBAAgBA,CAACxV,CAAC,EAAE;IAClBA,CAAC,CAAC0P,OAAO,CAAEzP,CAAC,IAAK;MACf,MAAMmD,CAAC,GAAG,IAAI,CAACqS,mBAAmB,CAACxV,CAAC,CAAC;MACrCmD,CAAC,IAAI,IAAI,KAAKA,CAAC,CAAC4H,SAAS,GAAG/K,CAAC,CAAC+K,SAAS,EAAE5H,CAAC,CAACsS,UAAU,GAAGzV,CAAC,CAACyV,UAAU,EAAEtS,CAAC,CAACuS,gBAAgB,GAAG1V,CAAC,CAAC0V,gBAAgB,CAAC;IAChH,CAAC,CAAC,EAAE,IAAI,CAACC,4BAA4B,CAAC5V,CAAC,CAAC;EAC1C;EACA6V,qBAAqBA,CAAC7V,CAAC,EAAE;IACvB,OAAO,IAAI,CAACoV,kBAAkB,CAACpV,CAAC,CAAC,IAAI,CAAC,CAAC;EACzC;EACA8V,wBAAwBA,CAAC9V,CAAC,EAAE;IAC1B,MAAMC,CAAC,GAAG,IAAI,CAACmV,kBAAkB,CAACpV,CAAC,CAAC;IACpC,IAAIC,CAAC,IAAI,IAAI,EACX;IACF,OAAO,IAAI,CAACmV,kBAAkB,CAACpV,CAAC,CAAC;IACjC,MAAMoD,CAAC,GAAG,EAAE;IACZxD,MAAM,CAACmW,IAAI,CAAC9V,CAAC,CAAC,CAACyP,OAAO,CAAErM,CAAC,IAAK;MAC5B,MAAMG,CAAC,GAAGvD,CAAC,CAACoD,CAAC,CAAC;MACd,CAACG,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACwS,IAAI,KAAK,IAAI,IAAIpW,MAAM,CAACmW,IAAI,CAACvS,CAAC,CAACwS,IAAI,CAAC,CAACtG,OAAO,CAAEhK,CAAC,IAAK;QAC1EtC,CAAC,CAAC4C,IAAI,CAAC;UAAEiQ,MAAM,EAAEjW,CAAC;UAAEkW,SAAS,EAAE7S,CAAC;UAAE8S,SAAS,EAAEzQ;QAAE,CAAC,CAAC;MACnD,CAAC,CAAC;IACJ,CAAC,CAAC,EAAEtC,CAAC,CAACK,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC2S,kBAAkB,CAAChT,CAAC,CAAC;EAChD;EACAiT,mBAAmBA,CAACrW,CAAC,EAAEC,CAAC,EAAE;IACxB,IAAI,CAACmV,kBAAkB,CAACpV,CAAC,CAAC,GAAGC,CAAC;EAChC;EACAqW,sBAAsBA,CAACtW,CAAC,EAAE;IACxB,MAAMC,CAAC,GAAG,EAAE;MAAEmD,CAAC,GAAG,IAAI,CAACgS,kBAAkB,CAACpV,CAAC,CAAC;IAC5CoD,CAAC,IAAI,IAAI,KAAKxD,MAAM,CAACmW,IAAI,CAAC3S,CAAC,CAAC,CAACsM,OAAO,CAAErM,CAAC,IAAK;MAC1C,IAAI,CAACkT,oBAAoB,CAACvW,CAAC,EAAEqD,CAAC,CAAC;MAC/B,MAAMG,CAAC,GAAGJ,CAAC,CAACC,CAAC,CAAC;MACdzD,MAAM,CAACmW,IAAI,CAACvS,CAAC,CAACwS,IAAI,CAAC,CAACtG,OAAO,CAAEhK,CAAC,IAAK;QACjC,MAAMmB,CAAC,GAAGrD,CAAC,CAACwS,IAAI,CAACtQ,CAAC,CAAC;QACnBmB,CAAC,CAACoP,MAAM,GAAGjW,CAAC,EAAE6G,CAAC,CAACqP,SAAS,GAAG7S,CAAC,EAAEpD,CAAC,CAAC+F,IAAI,CAACa,CAAC,CAAC;MAC1C,CAAC,CAAC;IACJ,CAAC,CAAC,EAAE5G,CAAC,CAACwD,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC+S,eAAe,CAACvW,CAAC,CAAC,CAAC;EAC9C;EACAwW,cAAcA,CAACzW,CAAC,EAAEC,CAAC,EAAE;IACnB,OAAO,IAAI,CAACyW,eAAe,CAAC1W,CAAC,EAAEC,CAAC,CAAC;EACnC;EACA;EACA0W,cAAcA,CAAC3W,CAAC,EAAEC,CAAC,EAAEmD,CAAC,EAAE;IACtB,IAAI,CAACgS,kBAAkB,CAACpV,CAAC,CAAC,CAACC,CAAC,CAAC,CAAC+V,IAAI,GAAG5S,CAAC;EACxC;EACAwT,aAAaA,CAAC5W,CAAC,EAAE;IACf,MAAMC,CAAC,GAAG,EAAE;MAAEmD,CAAC,GAAG,EAAE;MAAEC,CAAC,GAAG,EAAE;IAC5BrD,CAAC,CAAC0P,OAAO,CAAE5H,CAAC,IAAK;MACf,MAAM;QAAE+O,EAAE,EAAEpS,CAAC;QAAEqS,QAAQ,EAAEpS;MAAE,CAAC,GAAG,IAAI,CAACqS,WAAW,CAACjP,CAAC,CAAC;MAClD7H,CAAC,CAAC+F,IAAI,CAAC;QAAEiQ,MAAM,EAAEnO,CAAC,CAACmO,MAAM;QAAEC,SAAS,EAAEpO,CAAC,CAACoO,SAAS;QAAEC,SAAS,EAAErO,CAAC,CAACqO;MAAU,CAAC,CAAC,EAAE/S,CAAC,CAAC4C,IAAI,CAACvB,CAAC,CAAC,EAAEpB,CAAC,CAAC2C,IAAI,CAACtB,CAAC,CAAC;IACpG,CAAC,CAAC;IACF,MAAMlB,CAAC,GAAGJ,CAAC,CAAC4T,MAAM,CAAChD,CAAC,CAAClH,IAAI,CAAC7B,OAAO,EAAE,IAAI,CAAC;MAAEvF,CAAC,GAAGrC,CAAC,CAAC2T,MAAM,CAAChD,CAAC,CAAClH,IAAI,CAAC7B,OAAO,EAAE,IAAI,CAAC;MAAE;QAAEgL,MAAM,EAAEpP,CAAC;QAAEqP,SAAS,EAAExO;MAAE,CAAC,GAAG1H,CAAC,CAAC,CAAC,CAAC;IAChH,OAAO;MAAEiX,IAAI,EAAEvR,CAAC;MAAEwR,IAAI,EAAE1T,CAAC;MAAEyS,MAAM,EAAEpP,CAAC;MAAEqP,SAAS,EAAExO,CAAC;MAAEyP,OAAO,EAAElX;IAAE,CAAC;EAClE;EACAmX,gBAAgBA,CAACpX,CAAC,EAAE;IAClB,MAAMC,CAAC,GAAG,EAAE;MAAEmD,CAAC,GAAG,EAAE;IACpBpD,CAAC,CAAC0P,OAAO,CAAEhI,CAAC,IAAK;MACf,MAAM;QAAEmP,EAAE,EAAE/O,CAAC;QAAEgP,QAAQ,EAAErS;MAAE,CAAC,GAAG,IAAI,CAAC4S,cAAc,CAAC3P,CAAC,CAAC;MACrDzH,CAAC,CAACwF,OAAO,CAACqC,CAAC,CAAC,EAAE1E,CAAC,CAAC4C,IAAI,CAACvB,CAAC,CAAC;IACzB,CAAC,CAAC;IACF,MAAMpB,CAAC,GAAGpD,CAAC,CAAC+W,MAAM,CAAChD,CAAC,CAAClH,IAAI,CAAC7B,OAAO,EAAE,IAAI,CAAC;MAAEzH,CAAC,GAAGJ,CAAC,CAAC4T,MAAM,CAAChD,CAAC,CAAClH,IAAI,CAAC7B,OAAO,EAAE,IAAI,CAAC;MAAE;QAAEgL,MAAM,EAAEvQ,CAAC;QAAEwQ,SAAS,EAAErP;MAAE,CAAC,GAAG7G,CAAC,CAAC,CAAC,CAAC;IAChH,OAAO;MAAEiX,IAAI,EAAEzT,CAAC;MAAE0T,IAAI,EAAE7T,CAAC;MAAE4S,MAAM,EAAEvQ,CAAC;MAAEwQ,SAAS,EAAErP,CAAC;MAAEsQ,OAAO,EAAEnX;IAAE,CAAC;EAClE;EACAsX,gBAAgBA,CAACtX,CAAC,EAAE;IAClB,MAAMC,CAAC,GAAG,EAAE;MAAEmD,CAAC,GAAG,EAAE;MAAEC,CAAC,GAAG,EAAE;IAC5BrD,CAAC,CAAC0P,OAAO,CAAE5H,CAAC,IAAK;MACf,MAAM;QAAE+O,EAAE,EAAEpS,CAAC;QAAEqS,QAAQ,EAAEpS;MAAE,CAAC,GAAG,IAAI,CAAC6S,cAAc,CAACzP,CAAC,CAAC;MACrD7H,CAAC,CAAC+F,IAAI,CAAC;QAAEiQ,MAAM,EAAEnO,CAAC,CAACmO,MAAM;QAAEC,SAAS,EAAEpO,CAAC,CAACoO,SAAS;QAAEC,SAAS,EAAErO,CAAC,CAACqO;MAAU,CAAC,CAAC,EAAE/S,CAAC,CAAC4C,IAAI,CAACvB,CAAC,CAAC,EAAEpB,CAAC,CAAC2C,IAAI,CAACtB,CAAC,CAAC;IACpG,CAAC,CAAC;IACF,MAAMlB,CAAC,GAAGJ,CAAC,CAAC4T,MAAM,CAAChD,CAAC,CAAClH,IAAI,CAAC7B,OAAO,EAAE,IAAI,CAAC;MAAEvF,CAAC,GAAGrC,CAAC,CAAC2T,MAAM,CAAChD,CAAC,CAAClH,IAAI,CAAC7B,OAAO,EAAE,IAAI,CAAC;MAAE;QAAEgL,MAAM,EAAEpP,CAAC;QAAEqP,SAAS,EAAExO;MAAE,CAAC,GAAG1H,CAAC,CAAC,CAAC,CAAC;IAChH,OAAO;MAAEiX,IAAI,EAAEvR,CAAC;MAAEwR,IAAI,EAAE1T,CAAC;MAAEyS,MAAM,EAAEpP,CAAC;MAAEqP,SAAS,EAAExO,CAAC;MAAEyP,OAAO,EAAElX;IAAE,CAAC;EAClE;EACAmW,kBAAkBA,CAACpW,CAAC,EAAE;IACpB,IAAI,CAACkU,QAAQ,CAACqB,IAAI,CAACvV,CAAC,CAAC;EACvB;EACAwW,eAAeA,CAACxW,CAAC,EAAE;IACjB,IAAI,CAACoU,KAAK,CAACmB,IAAI,CAACvV,CAAC,CAAC;EACpB;EACAwX,kBAAkBA,CAACxX,CAAC,EAAE;IACpB,IAAI,CAACqU,QAAQ,CAACkB,IAAI,CAACvV,CAAC,CAAC;EACvB;EACAyX,iBAAiBA,CAACzX,CAAC,EAAE;IACnB,IAAI,CAACsU,OAAO,CAACiB,IAAI,CAACvV,CAAC,CAAC;EACtB;EACA0X,uBAAuBA,CAAC1X,CAAC,EAAE;IACzB,IAAI,CAACuU,OAAO,CAACgB,IAAI,CAACvV,CAAC,CAAC;EACtB;EACA2X,yBAAyBA,CAAC3X,CAAC,EAAE;IAC3B,IAAI,CAACwU,SAAS,CAACe,IAAI,CAACvV,CAAC,CAAC;EACxB;EACA4V,4BAA4BA,CAAC5V,CAAC,EAAE;IAC9B,IAAI,CAACyU,kBAAkB,CAACc,IAAI,CAACvV,CAAC,CAAC;EACjC;EACA4X,iBAAiBA,CAAC5X,CAAC,EAAE;IACnB,MAAMC,CAAC,GAAG,EAAE;MAAE;QAAEgW,MAAM,EAAE7S,CAAC;QAAE8S,SAAS,EAAE7S;MAAE,CAAC,GAAGrD,CAAC,CAAC,CAAC,CAAC,CAAC6X,MAAM;IACvD7X,CAAC,CAAC0P,OAAO,CAAE7I,CAAC,IAAK;MACf5G,CAAC,CAAC+F,IAAI,CAAC,IAAI,CAAC8R,kBAAkB,CAACjR,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC;IACF,MAAMrD,CAAC,GAAGvD,CAAC,CAAC+W,MAAM,CAAChD,CAAC,CAAClH,IAAI,CAAC7B,OAAO,EAAE,IAAI,CAAC;IACxC,OAAO;MAAEgM,IAAI,EAAEjD,CAAC,CAAClH,IAAI,CAACtB,aAAa,CAAChI,CAAC,EAAE,IAAI,CAAC4R,kBAAkB,CAAC;MAAE8B,IAAI,EAAE1T,CAAC;MAAEyS,MAAM,EAAE7S,CAAC;MAAE8S,SAAS,EAAE7S,CAAC;MAAE8T,OAAO,EAAEnX;IAAE,CAAC;EACjH;EACA+X,mBAAmBA,CAAC/X,CAAC,EAAE;IACrB,MAAMC,CAAC,GAAG,EAAE;MAAE;QAAEgW,MAAM,EAAE7S,CAAC;QAAE8S,SAAS,EAAE7S;MAAE,CAAC,GAAGrD,CAAC,CAAC,CAAC,CAAC,CAAC6X,MAAM;IACvD7X,CAAC,CAAC0P,OAAO,CAAE7I,CAAC,IAAK;MACf5G,CAAC,CAAC+F,IAAI,CAAC,IAAI,CAACgS,oBAAoB,CAACnR,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC;IACF,MAAMrD,CAAC,GAAGvD,CAAC,CAAC+W,MAAM,CAAChD,CAAC,CAAClH,IAAI,CAAC7B,OAAO,EAAE,IAAI,CAAC;IACxC,OAAO;MAAEgM,IAAI,EAAEjD,CAAC,CAAClH,IAAI,CAACtB,aAAa,CAAChI,CAAC,EAAE,IAAI,CAAC4R,kBAAkB,CAAC;MAAE8B,IAAI,EAAE1T,CAAC;MAAEyS,MAAM,EAAE7S,CAAC;MAAE8S,SAAS,EAAE7S,CAAC;MAAE8T,OAAO,EAAEnX;IAAE,CAAC;EACjH;EACAiY,kBAAkBA,CAACjY,CAAC,EAAE;IACpB,MAAM;MAAEiW,MAAM,EAAEhW,CAAC;MAAEiW,SAAS,EAAE9S,CAAC;MAAE+S,SAAS,EAAE9S;IAAE,CAAC,GAAGrD,CAAC;IACnD,IAAI,IAAI,CAACkY,iBAAiB,CAAC;MAAEjC,MAAM,EAAEhW,CAAC;MAAEiW,SAAS,EAAE9S,CAAC;MAAE+S,SAAS,EAAE9S;IAAE,CAAC,CAAC,IAAI,IAAI,EAC3E,OAAO,EAAE;IACX,MAAMqC,CAAC,GAAG,IAAI,CAACgR,eAAe,CAACzW,CAAC,EAAEmD,CAAC,CAAC;MAAEyD,CAAC,GAAG,EAAE;IAC5C,OAAOjH,MAAM,CAACmW,IAAI,CAACrQ,CAAC,CAAC,CAACgK,OAAO,CAAEhI,CAAC,IAAK;MACnC,MAAMI,CAAC,GAAGpC,CAAC,CAACgC,CAAC,CAAC;MACdI,CAAC,CAACqQ,OAAO,KAAK9U,CAAC,IAAIwD,CAAC,CAACb,IAAI,CAAC8B,CAAC,CAAC;IAC9B,CAAC,CAAC,EAAEjB,CAAC;EACP;EACAiR,kBAAkBA,CAAC9X,CAAC,EAAE;IACpB,MAAM;QAAE6X,MAAM,EAAE5X,CAAC;QAAEmY,QAAQ,EAAEhV;MAAE,CAAC,GAAGpD,CAAC;MAAE;QAAEiW,MAAM,EAAE5S,CAAC;QAAE6S,SAAS,EAAE1S,CAAC;QAAE2S,SAAS,EAAEzQ;MAAE,CAAC,GAAGzF,CAAC;MAAE4G,CAAC,GAAG,EAAE;IAC3FA,CAAC,CAACb,IAAI,CACJgO,CAAC,CAACxG,QAAQ,CAAC,CAACnK,CAAC,EAAEG,CAAC,EAAE,MAAM,EAAEkC,CAAC,CAAC,EAAEzF,CAAC,CACjC,CAAC;IACD,IAAIyH,CAAC,GAAG2Q,MAAM,CAACC,iBAAiB;IAChC,OAAOlV,CAAC,CAACsM,OAAO,CAAE5H,CAAC,IAAK;MACtB,MAAM;UAAEmO,MAAM,EAAExR,CAAC;UAAEyR,SAAS,EAAExR,CAAC;UAAEyR,SAAS,EAAEvR;QAAE,CAAC,GAAGkD,CAAC;QAAEjD,CAAC,GAAG,IAAI,CAAC0T,gBAAgB,CAAC;UAAEtC,MAAM,EAAExR,CAAC;UAAEyR,SAAS,EAAExR,CAAC;UAAEyR,SAAS,EAAEvR;QAAE,CAAC,CAAC;MACzH8C,CAAC,GAAGgC,IAAI,CAAC8O,GAAG,CAAC9Q,CAAC,EAAE7C,CAAC,CAAC,EAAEgC,CAAC,CAACb,IAAI,CACxB,GAAG,IAAI,CAACyS,wBAAwB,CAAC3Q,CAAC,EAAE,IAAI,CAACoQ,iBAAiB,CAAC;QAAEjC,MAAM,EAAExR,CAAC;QAAEyR,SAAS,EAAExR,CAAC;QAAEyR,SAAS,EAAEvR;MAAE,CAAC,CAAC,CACvG,CAAC;IACH,CAAC,CAAC,EAAE8C,CAAC,KAAK2Q,MAAM,CAACC,iBAAiB,KAAK5Q,CAAC,GAAG,IAAI,CAACgR,gBAAgB,CAACrV,CAAC,EAAEG,CAAC,CAAC,CAACC,MAAM,CAAC,EAAEoD,CAAC,CAACb,IAAI,CACpFgO,CAAC,CAACxG,QAAQ,CAAC,CAACnK,CAAC,EAAEG,CAAC,EAAE,OAAO,EAAEkE,CAAC,CAAC,EAAEhC,CAAC,CAClC,CAAC,EAAEmB,CAAC,CAACmQ,MAAM,CAAChD,CAAC,CAAClH,IAAI,CAAC7B,OAAO,EAAE,IAAI,CAAC;EACnC;EACA+M,oBAAoBA,CAAChY,CAAC,EAAE;IACtB,MAAM;QAAE6X,MAAM,EAAE5X,CAAC;QAAEmY,QAAQ,EAAEhV;MAAE,CAAC,GAAGpD,CAAC;MAAE;QAAEiW,MAAM,EAAE5S,CAAC;QAAE6S,SAAS,EAAE1S,CAAC;QAAE2S,SAAS,EAAEzQ;MAAE,CAAC,GAAGzF,CAAC;MAAE4G,CAAC,GAAG,EAAE;IAC3F,OAAOzD,CAAC,CAACsM,OAAO,CAAEhI,CAAC,IAAK;MACtB,MAAM;QAAEuO,MAAM,EAAEnO,CAAC;QAAEoO,SAAS,EAAEzR,CAAC;QAAE0R,SAAS,EAAEzR;MAAE,CAAC,GAAGgD,CAAC;MACnDb,CAAC,CAACb,IAAI,CACJ,GAAG,IAAI,CAACyS,wBAAwB,CAAC/Q,CAAC,EAAE,IAAI,CAACwQ,iBAAiB,CAAC;QAAEjC,MAAM,EAAEnO,CAAC;QAAEoO,SAAS,EAAEzR,CAAC;QAAE0R,SAAS,EAAEzR;MAAE,CAAC,CAAC,CACvG,CAAC;IACH,CAAC,CAAC,EAAEmC,CAAC,CAACb,IAAI,CACRgO,CAAC,CAACtG,QAAQ,CAAC,CAACrK,CAAC,EAAEG,CAAC,EAAE,MAAM,EAAEkC,CAAC,CAAC,EAAE,CAAC,CAAC,CAClC,CAAC,EAAEmB,CAAC,CAACb,IAAI,CACPgO,CAAC,CAACtG,QAAQ,CAAC,CAACrK,CAAC,EAAEG,CAAC,EAAE,OAAO,EAAE,IAAI,CAACkV,gBAAgB,CAACrV,CAAC,EAAEG,CAAC,CAAC,CAACmV,OAAO,CAACjT,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CACxE,CAAC,EAAEmB,CAAC,CAACmQ,MAAM,CAAChD,CAAC,CAAClH,IAAI,CAAC7B,OAAO,EAAE,IAAI,CAAC;EACnC;EACA2N,UAAUA,CAAC5Y,CAAC,EAAEC,CAAC,EAAEmD,CAAC,EAAE;IAClB,IAAI,CAACmT,oBAAoB,CAACvW,CAAC,EAAEC,CAAC,CAAC,EAAE,IAAI,CAACoV,sBAAsB,GAAG;MAAE,GAAG,IAAI,CAACD;IAAmB,CAAC,EAAE,IAAI,CAACA,kBAAkB,GAAGpB,CAAC,CAAClH,IAAI,CAACpC,KAAK,CAAC,IAAI,CAAC0K,kBAAkB,EAAEhS,CAAC,CAAC;EACnK;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAyV,+BAA+BA,CAAC7Y,CAAC,EAAE;IACjC,IAAI,CAAC4U,qBAAqB,CAACW,IAAI,CAACvV,CAAC,CAAC;EACpC;EACA8Y,oCAAoCA,CAAC9Y,CAAC,EAAE;IACtC,IAAI,CAAC+U,0BAA0B,CAACQ,IAAI,CAACvV,CAAC,CAAC;EACzC;EACA+Y,4BAA4BA,CAAC/Y,CAAC,EAAE;IAC9B,IAAI,CAAC6U,kBAAkB,CAACU,IAAI,CAACvV,CAAC,CAAC;EACjC;EACAgZ,+BAA+BA,CAAChZ,CAAC,EAAE;IACjC,IAAI,CAAC8U,qBAAqB,CAACS,IAAI,CAACvV,CAAC,CAAC;EACpC;EACAiZ,oCAAoCA,CAACjZ,CAAC,EAAE;IACtC,IAAI,CAACgV,0BAA0B,CAACO,IAAI,CAACvV,CAAC,CAAC;EACzC;EACAkZ,sCAAsCA,CAAClZ,CAAC,EAAE;IACxC,IAAI,CAACiV,4BAA4B,CAACM,IAAI,CAACvV,CAAC,CAAC;EAC3C;EACAkY,iBAAiBA,CAAClY,CAAC,EAAE;IACnB,OAAO,IAAI,CAACyV,mBAAmB,CAACzV,CAAC,CAAC;EACpC;EACAmZ,oBAAoBA,CAACnZ,CAAC,EAAE;IACtB,OAAO,IAAI,CAACoZ,eAAe,CAACpZ,CAAC,CAAC;EAChC;EACAqZ,cAAcA,CAACrZ,CAAC,EAAE;IAChB,MAAM,CAACC,CAAC,EAAEmD,CAAC,EAAEC,CAAC,CAAC,GAAGrD,CAAC,CAACsZ,KAAK,CAAC,KAAK,CAAC;IAChC,OAAO,IAAI,CAAC7D,mBAAmB,CAAC;MAAEQ,MAAM,EAAEhW,CAAC;MAAEiW,SAAS,EAAE9S,CAAC;MAAE+S,SAAS,EAAE9S;IAAE,CAAC,CAAC;EAC5E;EACAkW,YAAYA,CAACvZ,CAAC,EAAE;IACd,IAAIA,CAAC,IAAI,IAAI,IAAIA,CAAC,CAACyD,MAAM,KAAK,CAAC,EAAE;MAC/B,IAAI,CAAC+V,cAAc,GAAG,EAAE,EAAE,IAAI,CAAC7E,OAAO,CAACY,IAAI,CAAC,EAAE,CAAC;MAC/C;IACF;IACA,MAAMtV,CAAC,GAAG,EAAE;IACZD,CAAC,CAAC0P,OAAO,CAAEtM,CAAC,IAAK;MACf,IAAIsE,CAAC;MACL,MAAM;UAAEuO,MAAM,EAAE5S,CAAC;UAAE6S,SAAS,EAAE1S,CAAC;UAAE2S,SAAS,EAAEzQ;QAAE,CAAC,GAAGtC,CAAC;QAAEyD,CAAC,GAAG,CAACa,CAAC,GAAG,IAAI,CAACgP,eAAe,CAACrT,CAAC,EAAEG,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGkE,CAAC,CAAChC,CAAC,CAAC;MACjHmB,CAAC,IAAI,IAAI,IAAI5G,CAAC,CAAC+F,IAAI,CAACa,CAAC,CAAC;IACxB,CAAC,CAAC,EAAE5G,CAAC,CAACwD,MAAM,GAAG,CAAC,KAAK,IAAI,CAAC+V,cAAc,GAAGvZ,CAAC,EAAE,IAAI,CAAC0U,OAAO,CAACY,IAAI,CAACtV,CAAC,CAAC,CAAC;EACrE;EACAwZ,gBAAgBA,CAAA,EAAG;IACjB,MAAMzZ,CAAC,GAAG,EAAE;IACZ,OAAO,IAAI,CAACwZ,cAAc,CAAC9J,OAAO,CAAEzP,CAAC,IAAK;MACxC,IAAI4G,CAAC;MACL,MAAM;UAAEoP,MAAM,EAAE7S,CAAC;UAAE8S,SAAS,EAAE7S,CAAC;UAAE8S,SAAS,EAAE3S;QAAE,CAAC,GAAGvD,CAAC;QAAEyF,CAAC,GAAG,CAACmB,CAAC,GAAG,IAAI,CAAC6P,eAAe,CAACtT,CAAC,EAAEC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGwD,CAAC,CAACrD,CAAC,CAAC;MACjHkC,CAAC,IAAI,IAAI,IAAI1F,CAAC,CAACgG,IAAI,CAACN,CAAC,CAAC;IACxB,CAAC,CAAC,EAAE1F,CAAC;EACP;EACA0Z,eAAeA,CAAC1Z,CAAC,EAAEC,CAAC,EAAE;IACpB,OAAO,IAAI,CAACyY,gBAAgB,CAAC1Y,CAAC,EAAEC,CAAC,CAAC;EACpC;EACA;EACA0Z,eAAeA,CAAC3Z,CAAC,EAAEC,CAAC,EAAEmD,CAAC,EAAE;IACvB,IAAI,CAACgS,kBAAkB,CAACpV,CAAC,CAAC,CAACC,CAAC,CAAC,CAAC2Z,KAAK,GAAGxW,CAAC;EACzC;EACAyW,uBAAuBA,CAAC7Z,CAAC,EAAE;IACzB,IAAI,CAACsU,OAAO,CAACiB,IAAI,CAACvV,CAAC,CAAC;EACtB;EACA8Z,oBAAoBA,CAAC9Z,CAAC,EAAE;IACtB,MAAM;QAAEiW,MAAM,EAAEhW,CAAC;QAAEiW,SAAS,EAAE9S,CAAC;QAAE2W,UAAU,EAAE1W;MAAE,CAAC,GAAGrD,CAAC;MAAEwD,CAAC,GAAG,EAAE;MAAEkC,CAAC,GAAG,IAAI,CAACgU,eAAe,CAACzZ,CAAC,EAAEmD,CAAC,CAAC;MAAEyD,CAAC,GAAG,CAAC,GAAGxD,CAAC,CAAC;IACxGA,CAAC,CAACqM,OAAO,CAAEjL,CAAC,IAAK;MACf,MAAMC,CAAC,GAAG,IAAI,CAAC6T,gBAAgB,CAAC;QAAEtC,MAAM,EAAEhW,CAAC;QAAEiW,SAAS,EAAE9S,CAAC;QAAE+S,SAAS,EAAE1R;MAAE,CAAC,CAAC;MAC1E,IAAIC,CAAC,KAAK,CAAC,CAAC,IAAIA,CAAC,KAAKgB,CAAC,CAACjC,MAAM,GAAG,CAAC,EAChC;MACF,MAAMmB,CAAC,GAAGoP,CAAC,CAACvG,MAAM,CAAC,CAACxN,CAAC,EAAEmD,CAAC,EAAE,OAAO,EAAEsB,CAAC,CAAC,EAAE,CAACzE,CAAC,EAAEmD,CAAC,EAAE,OAAO,EAAEsB,CAAC,GAAG,CAAC,CAAC,CAAC;MAC9DlB,CAAC,CAACwC,IAAI,CAACpB,CAAC,CAAC,EAAEiC,CAAC,CAACuM,QAAQ,CAAC1N,CAAC,CAAChB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAImC,CAAC,CAACb,IAAI,CAACN,CAAC,CAAChB,CAAC,GAAG,CAAC,CAAC,CAAC;IACrD,CAAC,CAAC;IACF,MAAMgD,CAAC,GAAGlE,CAAC,CAACwT,MAAM,CAAChD,CAAC,CAAClH,IAAI,CAAC7B,OAAO,EAAE,IAAI,CAAC;IACxC,OAAO;MAAEgM,IAAI,EAAEjD,CAAC,CAAClH,IAAI,CAACtB,aAAa,CAAC9D,CAAC,EAAE,IAAI,CAAC0N,kBAAkB,CAAC;MAAE8B,IAAI,EAAExP,CAAC;MAAEuO,MAAM,EAAEhW,CAAC;MAAEiW,SAAS,EAAE9S,CAAC;MAAE+T,OAAO,EAAE;QAAE,GAAGnX,CAAC;QAAE+Z,UAAU,EAAElT;MAAE;IAAE,CAAC;EACvI;EACAmT,oBAAoBA,CAACha,CAAC,EAAE;IACtB,MAAM;QAAEiW,MAAM,EAAEhW,CAAC;QAAEiW,SAAS,EAAE9S,CAAC;QAAE2W,UAAU,EAAE1W;MAAE,CAAC,GAAGrD,CAAC;MAAEwD,CAAC,GAAG,EAAE;MAAEkC,CAAC,GAAG,IAAI,CAACgU,eAAe,CAACzZ,CAAC,EAAEmD,CAAC,CAAC;MAAEyD,CAAC,GAAG,CAAC,GAAGxD,CAAC,CAAC;IACxGA,CAAC,CAACqM,OAAO,CAAEjL,CAAC,IAAK;MACf,MAAMC,CAAC,GAAG,IAAI,CAAC6T,gBAAgB,CAAC;QAAEtC,MAAM,EAAEhW,CAAC;QAAEiW,SAAS,EAAE9S,CAAC;QAAE+S,SAAS,EAAE1R;MAAE,CAAC,CAAC;MAC1E,IAAIC,CAAC,KAAK,CAAC,CAAC,IAAIA,CAAC,KAAK,CAAC,EACrB;MACF,MAAME,CAAC,GAAGoP,CAAC,CAACvG,MAAM,CAAC,CAACxN,CAAC,EAAEmD,CAAC,EAAE,OAAO,EAAEsB,CAAC,CAAC,EAAE,CAACzE,CAAC,EAAEmD,CAAC,EAAE,OAAO,EAAEsB,CAAC,GAAG,CAAC,CAAC,CAAC;MAC9DlB,CAAC,CAACwC,IAAI,CAACpB,CAAC,CAAC,EAAEiC,CAAC,CAACuM,QAAQ,CAAC1N,CAAC,CAAChB,CAAC,GAAG,CAAC,CAAC,CAAC,IAAImC,CAAC,CAACb,IAAI,CAACN,CAAC,CAAChB,CAAC,GAAG,CAAC,CAAC,CAAC;IACrD,CAAC,CAAC;IACF,MAAMgD,CAAC,GAAGlE,CAAC,CAACwT,MAAM,CAAChD,CAAC,CAAClH,IAAI,CAAC7B,OAAO,EAAE,IAAI,CAAC;IACxC,OAAO;MAAEgM,IAAI,EAAEjD,CAAC,CAAClH,IAAI,CAACtB,aAAa,CAAC9D,CAAC,EAAE,IAAI,CAAC0N,kBAAkB,CAAC;MAAE8B,IAAI,EAAExP,CAAC;MAAEuO,MAAM,EAAEhW,CAAC;MAAEiW,SAAS,EAAE9S,CAAC;MAAE+T,OAAO,EAAE;QAAE,GAAGnX,CAAC;QAAE+Z,UAAU,EAAElT;MAAE;IAAE,CAAC;EACvI;EACAoT,kBAAkBA,CAACja,CAAC,EAAE;IACpB,MAAM;QAAEiW,MAAM,EAAEhW,CAAC;QAAEiW,SAAS,EAAE9S,CAAC;QAAE2W,UAAU,EAAE1W;MAAE,CAAC,GAAGrD,CAAC;MAAEwD,CAAC,GAAG,IAAI,CAAC0W,yBAAyB,CAACja,CAAC,EAAEmD,CAAC,EAAEC,CAAC,CAAC;MAAEqC,CAAC,GAAG,CAAC,GAAGrC,CAAC,CAAC;MAAEwD,CAAC,GAAG,IAAI,CAAC6S,eAAe,CAACzZ,CAAC,EAAEmD,CAAC,CAAC;MAAEsE,CAAC,GAAG,EAAE;IACrJlE,CAAC,CAACkM,OAAO,CAAEhL,CAAC,IAAK;MACf,MAAM;UAAEyR,SAAS,EAAEvR;QAAE,CAAC,GAAGF,CAAC;QAAEG,CAAC,GAAG,IAAI,CAACsV,gBAAgB,CAACla,CAAC,EAAEmD,CAAC,CAAC,GAAG,CAAC;QAAEoC,CAAC,GAAGwO,CAAC,CAACvG,MAAM,CAAC,CAACxN,CAAC,EAAEmD,CAAC,EAAE,OAAO,EAAE,IAAI,CAACsV,gBAAgB,CAACzY,CAAC,EAAEmD,CAAC,CAAC,CAACuV,OAAO,CAAC/T,CAAC,CAAC,CAAC,EAAE,CAAC3E,CAAC,EAAEmD,CAAC,EAAE,OAAO,EAAEyB,CAAC,CAAC,CAAC;MAC1J6C,CAAC,CAAC1B,IAAI,CAACR,CAAC,CAAC,EAAEE,CAAC,CAAC0N,QAAQ,CAACvM,CAAC,CAAChC,CAAC,CAAC,CAAC,IAAIa,CAAC,CAACM,IAAI,CAACa,CAAC,CAAChC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;IACF,MAAMiD,CAAC,GAAGJ,CAAC,CAACsP,MAAM,CAAChD,CAAC,CAAClH,IAAI,CAAC7B,OAAO,EAAE,IAAI,CAAC;IACxC,OAAO;MAAEgM,IAAI,EAAEjD,CAAC,CAAClH,IAAI,CAACtB,aAAa,CAAC1D,CAAC,EAAE,IAAI,CAACsN,kBAAkB,CAAC;MAAE8B,IAAI,EAAEpP,CAAC;MAAEmO,MAAM,EAAEhW,CAAC;MAAEiW,SAAS,EAAE9S,CAAC;MAAE+T,OAAO,EAAE;QAAE,GAAGnX,CAAC;QAAE+Z,UAAU,EAAErU;MAAE;IAAE,CAAC;EACvI;EACA0U,iBAAiBA,CAACpa,CAAC,EAAE;IACnB,MAAM;QAAEiW,MAAM,EAAEhW,CAAC;QAAEiW,SAAS,EAAE9S,CAAC;QAAE2W,UAAU,EAAE1W;MAAE,CAAC,GAAGrD,CAAC;MAAEwD,CAAC,GAAG,IAAI,CAAC0W,yBAAyB,CAACja,CAAC,EAAEmD,CAAC,EAAEC,CAAC,EAAE,CAAC,CAAC,CAAC;MAAEqC,CAAC,GAAG,CAAC,GAAGrC,CAAC,CAAC;MAAEwD,CAAC,GAAG,IAAI,CAAC6S,eAAe,CAACzZ,CAAC,EAAEmD,CAAC,CAAC;MAAEsE,CAAC,GAAG,EAAE;IACzJlE,CAAC,CAACkM,OAAO,CAAEhL,CAAC,IAAK;MACf,MAAM;UAAEyR,SAAS,EAAEvR;QAAE,CAAC,GAAGF,CAAC;QAAEG,CAAC,GAAGmP,CAAC,CAACvG,MAAM,CAAC,CAACxN,CAAC,EAAEmD,CAAC,EAAE,OAAO,EAAE,IAAI,CAACsV,gBAAgB,CAACzY,CAAC,EAAEmD,CAAC,CAAC,CAACuV,OAAO,CAAC/T,CAAC,CAAC,CAAC,EAAE,CAAC3E,CAAC,EAAEmD,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;MACrHsE,CAAC,CAAC1B,IAAI,CAACnB,CAAC,CAAC,EAAEa,CAAC,CAAC0N,QAAQ,CAACvM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAInB,CAAC,CAACM,IAAI,CAACa,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7C,CAAC,CAAC;IACF,MAAMiB,CAAC,GAAGJ,CAAC,CAACsP,MAAM,CAAChD,CAAC,CAAClH,IAAI,CAAC7B,OAAO,EAAE,IAAI,CAAC;IACxC,OAAO;MAAEgM,IAAI,EAAEjD,CAAC,CAAClH,IAAI,CAACtB,aAAa,CAAC1D,CAAC,EAAE,IAAI,CAACsN,kBAAkB,CAAC;MAAE8B,IAAI,EAAEpP,CAAC;MAAEmO,MAAM,EAAEhW,CAAC;MAAEiW,SAAS,EAAE9S,CAAC;MAAE+T,OAAO,EAAE;QAAE,GAAGnX,CAAC;QAAE+Z,UAAU,EAAErU;MAAE;IAAE,CAAC;EACvI;EACAyU,gBAAgBA,CAACna,CAAC,EAAEC,CAAC,EAAE;IACrB,OAAO,IAAI,CAACyZ,eAAe,CAAC1Z,CAAC,EAAEC,CAAC,CAAC,CAACwD,MAAM,IAAI,CAAC;EAC/C;EACAyW,yBAAyBA,CAACla,CAAC,EAAEC,CAAC,EAAEmD,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,EAAE;IACzC,OAAOD,CAAC,CAACU,GAAG,CAAEN,CAAC,IAAK;MAClB,MAAMkC,CAAC,GAAG,IAAI,CAAC6S,gBAAgB,CAAC;QAAEtC,MAAM,EAAEjW,CAAC;QAAEkW,SAAS,EAAEjW,CAAC;QAAEkW,SAAS,EAAE3S;MAAE,CAAC,CAAC;MAC1E,OAAO;QAAE2S,SAAS,EAAE3S,CAAC;QAAE6W,MAAM,EAAE3U;MAAE,CAAC;IACpC,CAAC,CAAC,CAAC4U,IAAI,CAACjX,CAAC,KAAK,CAAC,CAAC,GAAG7C,EAAE,GAAGE,EAAE,CAAC;EAC7B;EACA6X,gBAAgBA,CAACvY,CAAC,EAAE;IAClB,IAAIA,CAAC,IAAI,IAAI,EACX,OAAO,CAAC,CAAC;IACX,MAAM;MAAEiW,MAAM,EAAEhW,CAAC;MAAEiW,SAAS,EAAE9S,CAAC;MAAE+S,SAAS,EAAE9S;IAAE,CAAC,GAAGrD,CAAC;IACnD,OAAO,IAAI,CAACuW,oBAAoB,CAACtW,CAAC,EAAEmD,CAAC,CAAC,EAAE,IAAI,CAACsV,gBAAgB,CAACzY,CAAC,EAAEmD,CAAC,CAAC,CAACuV,OAAO,CAACtV,CAAC,CAAC;EAChF;EACAoS,mBAAmBA,CAACzV,CAAC,EAAE;IACrB,IAAIwD,CAAC,EAAEkC,CAAC,EAAEmB,CAAC;IACX,IAAI7G,CAAC,IAAI,IAAI,EACX;IACF,MAAM;MAAEiW,MAAM,EAAEhW,CAAC;MAAEiW,SAAS,EAAE9S,CAAC;MAAE+S,SAAS,EAAE9S;IAAE,CAAC,GAAGrD,CAAC;IACnD,OAAO,CAAC6G,CAAC,GAAG,CAACnB,CAAC,GAAG,CAAClC,CAAC,GAAG,IAAI,CAAC4R,kBAAkB,CAACnV,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGuD,CAAC,CAACJ,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGsC,CAAC,CAACsQ,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGnP,CAAC,CAACxD,CAAC,CAAC;EAC/H;EACA+V,eAAeA,CAACpZ,CAAC,EAAE;IACjB,IAAIwD,CAAC,EAAEkC,CAAC,EAAEmB,CAAC;IACX,IAAI7G,CAAC,IAAI,IAAI,EACX;IACF,MAAM;MAAEiW,MAAM,EAAEhW,CAAC;MAAEiW,SAAS,EAAE9S,CAAC;MAAE+S,SAAS,EAAE9S;IAAE,CAAC,GAAGrD,CAAC;IACnD,OAAO,CAAC6G,CAAC,GAAG,CAACnB,CAAC,GAAG,CAAClC,CAAC,GAAG,IAAI,CAAC6R,sBAAsB,CAACpV,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGuD,CAAC,CAACJ,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGsC,CAAC,CAACsQ,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGnP,CAAC,CAACxD,CAAC,CAAC;EACnI;EACAkT,oBAAoBA,CAACvW,CAAC,EAAEC,CAAC,EAAEmD,CAAC,EAAE;IAC5B,IAAIC,CAAC;IACL,OAAO,IAAI,CAAC+R,kBAAkB,CAACpV,CAAC,CAAC,KAAK,IAAI,CAACoV,kBAAkB,CAACpV,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAACoV,kBAAkB,CAACpV,CAAC,CAAC,CAACC,CAAC,CAAC,KAAK,IAAI,CAACmV,kBAAkB,CAACpV,CAAC,CAAC,CAACC,CAAC,CAAC,GAAG;MACxI+V,IAAI,EAAE,CAAC,CAAC;MACR4D,KAAK,EAAE;IACT,CAAC,CAAC,EAAExW,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,CAACC,CAAC,GAAG,IAAI,CAAC+R,kBAAkB,CAACpV,CAAC,CAAC,CAACC,CAAC,CAAC,CAAC+V,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG3S,CAAC,CAACD,CAAC,CAAC;EACzF;EACA2T,WAAWA,CAAC/W,CAAC,EAAE;IACb,MAAM;MAAEiW,MAAM,EAAEhW,CAAC;MAAEiW,SAAS,EAAE9S,CAAC;MAAE+S,SAAS,EAAE9S;IAAE,CAAC,GAAGrD,CAAC;IACnD,IAAI,CAACuW,oBAAoB,CAACtW,CAAC,EAAEmD,CAAC,EAAEC,CAAC,CAAC;IAClC,MAAMG,CAAC,GAAGwQ,CAAC,CAACxG,QAAQ,CAAC,CAACvN,CAAC,EAAEmD,CAAC,EAAE,MAAM,EAAEC,CAAC,CAAC,EAAErD,CAAC,CAAC;MAAE0F,CAAC,GAAGsO,CAAC,CAACxG,QAAQ,CAAC,CAACvN,CAAC,EAAEmD,CAAC,EAAE,OAAO,EAAE,IAAI,CAACsV,gBAAgB,CAACzY,CAAC,EAAEmD,CAAC,CAAC,CAACK,MAAM,CAAC,EAAEJ,CAAC,CAAC;MAAEwD,CAAC,GAAG,CAACrD,CAAC,EAAEkC,CAAC,CAAC,CAACsR,MAAM,CAAChD,CAAC,CAAClH,IAAI,CAAC7B,OAAO,EAAE,IAAI,CAAC;MAAEvD,CAAC,GAAGsM,CAAC,CAAClH,IAAI,CAACtB,aAAa,CAAC3E,CAAC,EAAE,IAAI,CAACuO,kBAAkB,CAAC;IACjN,OAAO;MAAEyB,EAAE,EAAEhQ,CAAC;MAAEiQ,QAAQ,EAAEpP;IAAE,CAAC;EAC/B;EACA2P,cAAcA,CAACrX,CAAC,EAAE;IAChB,IAAIA,CAAC,IAAI,IAAI,EACX,OAAO;MAAE6W,EAAE,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAG,CAAC;IACjC,MAAM;MAAEb,MAAM,EAAEhW,CAAC;MAAEiW,SAAS,EAAE9S,CAAC;MAAE+S,SAAS,EAAE9S;IAAE,CAAC,GAAGrD,CAAC;IACnD,IAAI,IAAI,CAACuW,oBAAoB,CAACtW,CAAC,EAAEmD,CAAC,EAAEC,CAAC,CAAC,IAAI,IAAI,EAC5C,OAAO;MAAEwT,EAAE,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAG,CAAC;IACjC,MAAMpR,CAAC,GAAGsO,CAAC,CAACtG,QAAQ,CAAC,CAACzN,CAAC,EAAEmD,CAAC,EAAE,MAAM,EAAEC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAAEwD,CAAC,GAAGmN,CAAC,CAACtG,QAAQ,CAAC,CAACzN,CAAC,EAAEmD,CAAC,EAAE,OAAO,EAAE,IAAI,CAACsV,gBAAgB,CAACzY,CAAC,EAAEmD,CAAC,CAAC,CAACuV,OAAO,CAACtV,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAAEqE,CAAC,GAAG,CAAChC,CAAC,EAAEmB,CAAC,CAAC,CAACmQ,MAAM,CAAChD,CAAC,CAAClH,IAAI,CAAC7B,OAAO,EAAE,IAAI,CAAC;MAAEnD,CAAC,GAAGkM,CAAC,CAAClH,IAAI,CAACtB,aAAa,CAAC9D,CAAC,EAAE,IAAI,CAAC0N,kBAAkB,CAAC;IACvN,OAAO;MAAEyB,EAAE,EAAEnP,CAAC;MAAEoP,QAAQ,EAAEhP;IAAE,CAAC;EAC/B;EACAyP,cAAcA,CAACvX,CAAC,EAAE;IAChB,MAAM;QAAEiW,MAAM,EAAEhW,CAAC;QAAEiW,SAAS,EAAE9S,CAAC;QAAE+S,SAAS,EAAE9S;MAAE,CAAC,GAAGrD,CAAC;MAAEwD,CAAC,GAAG,IAAI,CAAC+S,oBAAoB,CAACtW,CAAC,EAAEmD,CAAC,EAAEC,CAAC,CAAC;IAC3F,IAAIG,CAAC,IAAI,IAAI,EACX,OAAO;MAAEqT,EAAE,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAG,CAAC;IACjC,MAAMjQ,CAAC,GAAG,IAAI,CAAC4R,wBAAwB,CAACzY,CAAC,EAAEwD,CAAC,CAAC,CAACwT,MAAM,CAAChD,CAAC,CAAClH,IAAI,CAAC7B,OAAO,EAAE,IAAI,CAAC;MAAEvD,CAAC,GAAGsM,CAAC,CAAClH,IAAI,CAACtB,aAAa,CAAC3E,CAAC,EAAE,IAAI,CAACuO,kBAAkB,CAAC;IAChI,OAAO;MAAEyB,EAAE,EAAEhQ,CAAC;MAAEiQ,QAAQ,EAAEpP;IAAE,CAAC;EAC/B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA+Q,wBAAwBA,CAACzY,CAAC,EAAEC,CAAC,EAAE;IAC7B,MAAM;QAAEgW,MAAM,EAAE7S,CAAC;QAAE8S,SAAS,EAAE7S,CAAC;QAAE8S,SAAS,EAAE3S;MAAE,CAAC,GAAGxD,CAAC;MAAE0F,CAAC,GAAG,EAAE;IAC3D,OAAO9F,MAAM,CAACmW,IAAI,CAAC/V,CAAC,CAAC,CAAC0P,OAAO,CAAE7I,CAAC,IAAK;MACnC,MAAMa,CAAC,GAAG1H,CAAC,CAAC6G,CAAC,CAAC;QAAEiB,CAAC,GAAG7H,CAAC,CAAC4G,CAAC,CAAC;MACxBiB,CAAC,KAAKJ,CAAC,IAAIhC,CAAC,CAACM,IAAI,CACfgO,CAAC,CAACzG,SAAS,CAAC,CAACnK,CAAC,EAAEC,CAAC,EAAE,MAAM,EAAEG,CAAC,EAAEqD,CAAC,CAAC,EAAEiB,CAAC,EAAEJ,CAAC,CACxC,CAAC;IACH,CAAC,CAAC,EAAEhC,CAAC;EACP;EACAgR,eAAeA,CAAC1W,CAAC,EAAEC,CAAC,EAAE;IACpB,IAAImD,CAAC,EAAEC,CAAC;IACR,OAAO,CAAC,CAACA,CAAC,GAAG,CAACD,CAAC,GAAG,IAAI,CAACgS,kBAAkB,CAACpV,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoD,CAAC,CAACnD,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoD,CAAC,CAAC2S,IAAI,KAAK,CAAC,CAAC;EACzG;EACA0C,gBAAgBA,CAAC1Y,CAAC,EAAEC,CAAC,EAAE;IACrB,IAAImD,CAAC,EAAEC,CAAC;IACR,OAAO,CAAC,CAACA,CAAC,GAAG,CAACD,CAAC,GAAG,IAAI,CAACgS,kBAAkB,CAACpV,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoD,CAAC,CAACnD,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoD,CAAC,CAACuW,KAAK,KAAK,EAAE;EAC1G;EACAW,iBAAiBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,QAAQ;EACtB;EACAC,kBAAkBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACC,SAAS;EACvB;EACAC,iBAAiBA,CAAC3a,CAAC,EAAE;IACnB,IAAI,CAACwa,QAAQ,GAAGxa,CAAC;EACnB;EACA4a,kBAAkBA,CAAC5a,CAAC,EAAE;IACpB,IAAI,CAAC0a,SAAS,GAAG1a,CAAC;EACpB;AACF;AACA,MAAM6a,EAAE,SAAS5G,EAAE,CAAC;AAEpB,SAAS6G,EAAEA,CAAC;EAAE7E,MAAM,EAAElW,CAAC;EAAEmW,SAAS,EAAElW,CAAC;EAAEmW,SAAS,EAAElW;AAAE,CAAC,EAAEmD,CAAC,EAAE;EACxD,OAAO,OAAOA,CAAC,IAAI,QAAQ,GAAI,GAAErD,CAAE,MAAKC,CAAE,MAAKC,CAAE,MAAKmD,CAAE,EAAC,GAAI,GAAErD,CAAE,MAAKC,CAAE,MAAKC,CAAE,EAAC;AAClF;AACA,MAAM8a,EAAE,GAAG,MAAOhb,CAAC,IAAK,IAAIib,OAAO,CAAC,CAAChb,CAAC,EAAEC,CAAC,KAAK;IAC5C,MAAMmD,CAAC,GAAG,IAAI6X,KAAK,CAAC,CAAC;IACrB7X,CAAC,CAAC8X,GAAG,GAAGnb,CAAC,EAAEqD,CAAC,CAAC+X,MAAM,GAAG,MAAM;MAC1Bnb,CAAC,CAAC;QACAob,KAAK,EAAEhY,CAAC,CAACgY,KAAK;QACdC,MAAM,EAAEjY,CAAC,CAACiY,MAAM;QAChBC,KAAK,EAAElY;MACT,CAAC,CAAC;IACJ,CAAC,EAAEA,CAAC,CAACmY,OAAO,GAAIlY,CAAC,IAAK;MACpBpD,CAAC,CAACoD,CAAC,CAAC;IACN,CAAC;EACH,CAAC,CAAC;EAAEmY,EAAE,GAAG5a,EAAE,CAAC,gCAAgC,CAAC;EAAE6a,EAAE,GAAG;IAClDC,EAAE,EAAE,wCAAwC;IAC5C5O,IAAI,EAAEhM,EAAE,CAAC6a,SAAS;IAClBC,OAAO,EAAEA,CAAC7b,CAAC,EAAEC,CAAC,KAAK;MACjB,MAAMC,CAAC,GAAGF,CAAC,CAAC4F,GAAG,CAAC6V,EAAE,CAAC;MACnB,OAAOxb,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,IAAIC,CAAC,CAACsZ,YAAY,CAACvZ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACjD;EACF,CAAC;EAAE6b,EAAE,GAAG,gBAAgB;EAAEC,EAAE,GAAG,CAAC,CAAC;AACjC,MAAMC,EAAE,CAAC;EACPhX,WAAWA,CAAA,EAAG;IACZzE,CAAC,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IACxBA,CAAC,CAAC,IAAI,EAAE,UAAU,EAAE,IAAImC,EAAE,CAAC,CAAC,CAAC;IAC7BnC,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,CAAC0b,QAAQ,CAAC;IACjC1b,CAAC,CAAC,IAAI,EAAE,mBAAmB,EAAE,eAAgB,IAAI2b,GAAG,CAAC,CAAC,CAAC;EACzD;EACAC,YAAYA,CAAClc,CAAC,EAAE;IACd,IAAI,CAACmc,UAAU,GAAGnc,CAAC,EAAE,IAAI,CAACgc,QAAQ,CAACzG,IAAI,CAACvV,CAAC,CAAC;EAC5C;EACAoc,mBAAmBA,CAACpc,CAAC,EAAEC,CAAC,EAAE;IACxB,IAAIA,CAAC,KAAKe,EAAE,CAACqb,MAAM,EAAE;MACnB,MAAMjZ,CAAC,GAAG,IAAI6X,KAAK,CAAC,CAAC;MACrB,OAAO7X,CAAC,CAAC8X,GAAG,GAAGlb,CAAC,EAAEoD,CAAC;IACrB;IACA,OAAO,IAAI,CAACkZ,iBAAiB,CAAC3W,GAAG,CAAC3F,CAAC,CAAC;EACtC;EACAuc,mBAAmBA,CAACvc,CAAC,EAAEC,CAAC,EAAEmD,CAAC,EAAE;IAC3BnD,CAAC,KAAKe,EAAE,CAACqb,MAAM,IAAIjZ,CAAC,IAAI,IAAI,IAAI,IAAI,CAACkZ,iBAAiB,CAACE,GAAG,CAACxc,CAAC,EAAEoD,CAAC,CAAC;EAClE;EACA,MAAMqZ,QAAQA,CAACzc,CAAC,EAAE;IAChB,OAAOgb,OAAO,CAAC0B,OAAO,CAAC1c,CAAC,CAAC;EAC3B;EACA,MAAM2c,SAASA,CAAC3c,CAAC,EAAE;IACjB,OAAO,IAAIgb,OAAO,CAAC,CAAC/a,CAAC,EAAEmD,CAAC,KAAK;MAC3B,IAAI,CAACN,EAAE,CAACsQ,QAAQ,CAACpT,CAAC,CAAC8M,IAAI,CAAC,EAAE;QACxB1J,CAAC,CAAC,IAAIuB,KAAK,CAACzD,EAAE,CAAC0b,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAACC,gBAAgB,CAAC,CAAC;QAC1D;MACF;MACA,IAAI7c,CAAC,CAACwP,IAAI,GAAG3M,EAAE,EAAE;QACfO,CAAC,CAAC,IAAIuB,KAAK,CAACzD,EAAE,CAAC4b,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAACD,gBAAgB,CAAC,CAAC;QAC3D;MACF;MACA,MAAMxZ,CAAC,GAAG,IAAI0Z,UAAU,CAAC,CAAC;MAC1B1Z,CAAC,CAAC2Z,aAAa,CAAChd,CAAC,CAAC,EAAEqD,CAAC,CAAC8X,MAAM,GAAI3X,CAAC,IAAK;QACpC,IAAIkE,CAAC;QACL,MAAMhC,CAAC,GAAG,CAACgC,CAAC,GAAGlE,CAAC,CAACyZ,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGvV,CAAC,CAACkL,MAAM;QACpD,IAAIlN,CAAC,IAAI,IAAI,EAAE;UACbtC,CAAC,CAAC,IAAIuB,KAAK,CAACzD,EAAE,CAACgc,WAAW,CAAC,CAAC,EAAE,IAAI,CAACL,gBAAgB,CAAC,CAAC;UACrD;QACF;QACA,MAAMhW,CAAC,GAAGzF,EAAE,CAAC+b,gBAAgB,CAAC,CAAC,CAAC;QAChCld,CAAC,CAAC;UACAmd,OAAO,EAAEvW,CAAC;UACVwW,eAAe,EAAErc,EAAE,CAACqb,MAAM;UAC1BiB,MAAM,EAAE5X,CAAC;UACT6X,WAAW,EAAE7X,CAAC;UACd8X,MAAM,EAAEtc,EAAE,CAACuc;QACb,CAAC,CAAC,EAAE,IAAI,CAACZ,gBAAgB,CAAC,CAAC;MAC7B,CAAC;IACH,CAAC,CAAC;EACJ;EACAA,gBAAgBA,CAAA,EAAG;IACjB,IAAI,CAACV,UAAU,IAAI,CAAC,EAAE,IAAI,CAACH,QAAQ,CAACzG,IAAI,CAAC,IAAI,CAAC4G,UAAU,CAAC;EAC3D;AACF;AACA,IAAIuB,EAAE,GAAG9d,MAAM,CAAC+d,wBAAwB;EAAEC,EAAE,GAAGA,CAAC7d,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEmD,CAAC,KAAK;IAC7D,KAAK,IAAIC,CAAC,GAAGD,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,GAAGsa,EAAE,CAAC1d,CAAC,EAAEC,CAAC,CAAC,GAAGD,CAAC,EAAEwD,CAAC,GAAGzD,CAAC,CAAC0D,MAAM,GAAG,CAAC,EAAEiC,CAAC,EAAElC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAC9E,CAACkC,CAAC,GAAG3F,CAAC,CAACyD,CAAC,CAAC,MAAMH,CAAC,GAAGqC,CAAC,CAACrC,CAAC,CAAC,IAAIA,CAAC,CAAC;IAC/B,OAAOA,CAAC;EACV,CAAC;EAAEwa,EAAE,GAAGA,CAAC9d,CAAC,EAAEC,CAAC,KAAK,CAACC,CAAC,EAAEmD,CAAC,KAAKpD,CAAC,CAACC,CAAC,EAAEmD,CAAC,EAAErD,CAAC,CAAC;AACtC,MAAM+d,EAAE,GAAG,uBAAuB;AAClC,IAAIC,EAAE;AACN,IAAIC,EAAE,IAAID,EAAE,GAAG,cAAcjc,EAAE,CAAC;EAC9BiD,WAAWA,CAAChF,CAAC,GAAG+b,EAAE,EAAE9b,CAAC,EAAEC,CAAC,EAAEmD,CAAC,EAAE;IAC3B,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC6a,OAAO,GAAGle,CAAC,EAAE,IAAI,CAACme,SAAS,GAAGle,CAAC,EAAE,IAAI,CAACme,cAAc,GAAGle,CAAC,EAAE,IAAI,CAACme,eAAe,GAAGhb,CAAC;IAChG,MAAM;MAAE,GAAGC;IAAE,CAAC,GAAGrB,EAAE,CACjB,CAAC,CAAC,EACF8Z,EAAE,EACF,IAAI,CAACmC,OACP,CAAC;IACD,IAAI,CAACE,cAAc,CAACE,SAAS,CAACxC,EAAE,EAAExY,CAAC,CAAC;EACtC;EACAib,UAAUA,CAAA,EAAG;IACX,IAAI,CAACC,aAAa,CAAC,CAAC,EAAE,IAAI,CAACC,iBAAiB,CAAC,CAAC;EAChD;EACAA,iBAAiBA,CAAA,EAAG;IAClB,IAAIve,CAAC;IACLiC,EAAE,CAAC,CACD,CAACE,EAAE,EAAE;MAAEqc,QAAQ,EAAE1C;IAAG,CAAC,CAAC,EACtB,CAACP,EAAE,EAAE;MAAEiD,QAAQ,EAAE5D;IAAG,CAAC,CAAC,CACvB,EAAE,CAAC5a,CAAC,GAAG,IAAI,CAACge,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGhe,CAAC,CAACye,QAAQ,CAAC,CAAChP,OAAO,CAAEtM,CAAC,IAAK,IAAI,CAAC8a,SAAS,CAACjP,GAAG,CAAC7L,CAAC,CAAC,CAAC;EAC5F;EACAmb,aAAaA,CAAA,EAAG;IACd,CACE9C,EAAE,CACH,CAAC/L,OAAO,CAAE3P,CAAC,IAAK,IAAI,CAAC4e,aAAa,CAAC,IAAI,CAACP,eAAe,CAACQ,eAAe,CAAC7e,CAAC,CAAC,CAAC,CAAC;EAC/E;AACF,CAAC,EAAEO,CAAC,CAACyd,EAAE,EAAE,YAAY,EAAED,EAAE,CAAC,EAAEC,EAAE,CAAC;AAC/BC,EAAE,GAAGJ,EAAE,CAAC,CACNC,EAAE,CAAC,CAAC,EAAEvc,EAAE,CAACE,EAAE,CAAC,CAAC,EACbqc,EAAE,CAAC,CAAC,EAAEnc,EAAE,CAAC,EACTmc,EAAE,CAAC,CAAC,EAAEjc,EAAE,CAAC,CACV,EAAEoc,EAAE,CAAC;AACN,SACElb,EAAE,IAAI+b,8BAA8B,EACpChc,EAAE,IAAIic,wBAAwB,EAC9Blc,EAAE,IAAImc,yBAAyB,EAC/Bpc,EAAE,IAAIqc,0BAA0B,EAChCtc,EAAE,IAAIuc,yBAAyB,EAC/BpE,EAAE,IAAIqE,qBAAqB,EAC3B1D,EAAE,IAAI2D,sBAAsB,EAC5B9c,EAAE,IAAIF,eAAe,EACrB4Z,EAAE,IAAIqD,cAAc,EACpB9c,EAAE,IAAIvB,eAAe,EACrBwB,EAAE,IAAItB,qBAAqB,EAC3Bwa,EAAE,IAAI4D,2BAA2B,EACjCpL,EAAE,IAAIqL,kBAAkB,EACxBtB,EAAE,IAAIuB,mBAAmB,EACzBzE,EAAE,IAAI0E,iCAAiC,EACvCzE,EAAE,IAAI0E,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}