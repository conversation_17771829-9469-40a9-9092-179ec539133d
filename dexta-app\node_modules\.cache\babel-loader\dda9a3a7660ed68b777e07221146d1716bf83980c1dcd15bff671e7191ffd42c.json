{"ast": null, "code": "import * as React from 'react';\n\n/**\n * @ignore - internal component.\n */\nconst FormControlUnstyledContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  FormControlUnstyledContext.displayName = 'FormControlUnstyledContext';\n}\nexport default FormControlUnstyledContext;", "map": {"version": 3, "names": ["React", "FormControlUnstyledContext", "createContext", "undefined", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/FormControlUnstyled/FormControlUnstyledContext.js"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * @ignore - internal component.\n */\nconst FormControlUnstyledContext = /*#__PURE__*/React.createContext(undefined);\n\nif (process.env.NODE_ENV !== 'production') {\n  FormControlUnstyledContext.displayName = 'FormControlUnstyledContext';\n}\n\nexport default FormControlUnstyledContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA,MAAMC,0BAA0B,GAAG,aAAaD,KAAK,CAACE,aAAa,CAACC,SAAS,CAAC;AAE9E,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCL,0BAA0B,CAACM,WAAW,GAAG,4BAA4B;AACvE;AAEA,eAAeN,0BAA0B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}