{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { innerFrom } from '../observable/innerFrom';\nexport function distinct(keySelector, flushes) {\n  return operate(function (source, subscriber) {\n    var distinctKeys = new Set();\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      var key = keySelector ? keySelector(value) : value;\n      if (!distinctKeys.has(key)) {\n        distinctKeys.add(key);\n        subscriber.next(value);\n      }\n    }));\n    flushes && innerFrom(flushes).subscribe(createOperatorSubscriber(subscriber, function () {\n      return distinctKeys.clear();\n    }, noop));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "noop", "innerFrom", "distinct", "keySelector", "flushes", "source", "subscriber", "distinctKeys", "Set", "subscribe", "value", "key", "has", "add", "next", "clear"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\distinct.ts"], "sourcesContent": ["import { MonoTypeOperatorFunction, ObservableInput } from '../types';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { innerFrom } from '../observable/innerFrom';\n\n/**\n * Returns an Observable that emits all items emitted by the source Observable that are distinct by comparison from previous items.\n *\n * If a `keySelector` function is provided, then it will project each value from the source observable into a new value that it will\n * check for equality with previously projected values. If the `keySelector` function is not provided, it will use each value from the\n * source observable directly with an equality check against previous values.\n *\n * In JavaScript runtimes that support `Set`, this operator will use a `Set` to improve performance of the distinct value checking.\n *\n * In other runtimes, this operator will use a minimal implementation of `Set` that relies on an `Array` and `indexOf` under the\n * hood, so performance will degrade as more values are checked for distinction. Even in newer browsers, a long-running `distinct`\n * use might result in memory leaks. To help alleviate this in some scenarios, an optional `flushes` parameter is also provided so\n * that the internal `Set` can be \"flushed\", basically clearing it of values.\n *\n * ## Examples\n *\n * A simple example with numbers\n *\n * ```ts\n * import { of, distinct } from 'rxjs';\n *\n * of(1, 1, 2, 2, 2, 1, 2, 3, 4, 3, 2, 1)\n *   .pipe(distinct())\n *   .subscribe(x => console.log(x));\n *\n * // Outputs\n * // 1\n * // 2\n * // 3\n * // 4\n * ```\n *\n * An example using the `keySelector` function\n *\n * ```ts\n * import { of, distinct } from 'rxjs';\n *\n * of(\n *   { age: 4, name: 'Foo'},\n *   { age: 7, name: 'Bar'},\n *   { age: 5, name: 'Foo'}\n * )\n * .pipe(distinct(({ name }) => name))\n * .subscribe(x => console.log(x));\n *\n * // Outputs\n * // { age: 4, name: 'Foo' }\n * // { age: 7, name: 'Bar' }\n * ```\n * @see {@link distinctUntilChanged}\n * @see {@link distinctUntilKeyChanged}\n *\n * @param keySelector Optional `function` to select which value you want to check as distinct.\n * @param flushes Optional `ObservableInput` for flushing the internal HashSet of the operator.\n * @return A function that returns an Observable that emits items from the\n * source Observable with distinct values.\n */\nexport function distinct<T, K>(keySelector?: (value: T) => K, flushes?: ObservableInput<any>): MonoTypeOperatorFunction<T> {\n  return operate((source, subscriber) => {\n    const distinctKeys = new Set();\n    source.subscribe(\n      createOperatorSubscriber(subscriber, (value) => {\n        const key = keySelector ? keySelector(value) : value;\n        if (!distinctKeys.has(key)) {\n          distinctKeys.add(key);\n          subscriber.next(value);\n        }\n      })\n    );\n\n    flushes && innerFrom(flushes).subscribe(createOperatorSubscriber(subscriber, () => distinctKeys.clear(), noop));\n  });\n}\n"], "mappings": "AACA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,SAAS,QAAQ,yBAAyB;AA2DnD,OAAM,SAAUC,QAAQA,CAAOC,WAA6B,EAAEC,OAA8B;EAC1F,OAAON,OAAO,CAAC,UAACO,MAAM,EAAEC,UAAU;IAChC,IAAMC,YAAY,GAAG,IAAIC,GAAG,EAAE;IAC9BH,MAAM,CAACI,SAAS,CACdV,wBAAwB,CAACO,UAAU,EAAE,UAACI,KAAK;MACzC,IAAMC,GAAG,GAAGR,WAAW,GAAGA,WAAW,CAACO,KAAK,CAAC,GAAGA,KAAK;MACpD,IAAI,CAACH,YAAY,CAACK,GAAG,CAACD,GAAG,CAAC,EAAE;QAC1BJ,YAAY,CAACM,GAAG,CAACF,GAAG,CAAC;QACrBL,UAAU,CAACQ,IAAI,CAACJ,KAAK,CAAC;;IAE1B,CAAC,CAAC,CACH;IAEDN,OAAO,IAAIH,SAAS,CAACG,OAAO,CAAC,CAACK,SAAS,CAACV,wBAAwB,CAACO,UAAU,EAAE;MAAM,OAAAC,YAAY,CAACQ,KAAK,EAAE;IAApB,CAAoB,EAAEf,IAAI,CAAC,CAAC;EACjH,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}