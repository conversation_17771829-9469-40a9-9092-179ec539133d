{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function sample(notifier) {\n  return operate(function (source, subscriber) {\n    var hasValue = false;\n    var lastValue = null;\n    source.subscribe(createOperatorSubscriber(subscriber, function (value) {\n      hasValue = true;\n      lastValue = value;\n    }));\n    innerFrom(notifier).subscribe(createOperatorSubscriber(subscriber, function () {\n      if (hasValue) {\n        hasValue = false;\n        var value = lastValue;\n        lastValue = null;\n        subscriber.next(value);\n      }\n    }, noop));\n  });\n}", "map": {"version": 3, "names": ["innerFrom", "operate", "noop", "createOperatorSubscriber", "sample", "notifier", "source", "subscriber", "hasValue", "lastValue", "subscribe", "value", "next"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\sample.ts"], "sourcesContent": ["import { innerFrom } from '../observable/innerFrom';\nimport { MonoTypeOperatorFunction, ObservableInput } from '../types';\nimport { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\n\n/**\n * Emits the most recently emitted value from the source Observable whenever\n * another Observable, the `notifier`, emits.\n *\n * <span class=\"informal\">It's like {@link sampleTime}, but samples whenever\n * the `notifier` `ObservableInput` emits something.</span>\n *\n * ![](sample.png)\n *\n * Whenever the `notifier` `ObservableInput` emits a value, `sample`\n * looks at the source Observable and emits whichever value it has most recently\n * emitted since the previous sampling, unless the source has not emitted\n * anything since the previous sampling. The `notifier` is subscribed to as soon\n * as the output Observable is subscribed.\n *\n * ## Example\n *\n * On every click, sample the most recent `seconds` timer\n *\n * ```ts\n * import { fromEvent, interval, sample } from 'rxjs';\n *\n * const seconds = interval(1000);\n * const clicks = fromEvent(document, 'click');\n * const result = seconds.pipe(sample(clicks));\n *\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link audit}\n * @see {@link debounce}\n * @see {@link sampleTime}\n * @see {@link throttle}\n *\n * @param notifier The `ObservableInput` to use for sampling the\n * source Observable.\n * @return A function that returns an Observable that emits the results of\n * sampling the values emitted by the source Observable whenever the notifier\n * Observable emits value or completes.\n */\nexport function sample<T>(notifier: ObservableInput<any>): MonoTypeOperatorFunction<T> {\n  return operate((source, subscriber) => {\n    let hasValue = false;\n    let lastValue: T | null = null;\n    source.subscribe(\n      createOperatorSubscriber(subscriber, (value) => {\n        hasValue = true;\n        lastValue = value;\n      })\n    );\n    innerFrom(notifier).subscribe(\n      createOperatorSubscriber(\n        subscriber,\n        () => {\n          if (hasValue) {\n            hasValue = false;\n            const value = lastValue!;\n            lastValue = null;\n            subscriber.next(value);\n          }\n        },\n        noop\n      )\n    );\n  });\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,yBAAyB;AAEnD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,wBAAwB,QAAQ,sBAAsB;AA0C/D,OAAM,SAAUC,MAAMA,CAAIC,QAA8B;EACtD,OAAOJ,OAAO,CAAC,UAACK,MAAM,EAAEC,UAAU;IAChC,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIC,SAAS,GAAa,IAAI;IAC9BH,MAAM,CAACI,SAAS,CACdP,wBAAwB,CAACI,UAAU,EAAE,UAACI,KAAK;MACzCH,QAAQ,GAAG,IAAI;MACfC,SAAS,GAAGE,KAAK;IACnB,CAAC,CAAC,CACH;IACDX,SAAS,CAACK,QAAQ,CAAC,CAACK,SAAS,CAC3BP,wBAAwB,CACtBI,UAAU,EACV;MACE,IAAIC,QAAQ,EAAE;QACZA,QAAQ,GAAG,KAAK;QAChB,IAAMG,KAAK,GAAGF,SAAU;QACxBA,SAAS,GAAG,IAAI;QAChBF,UAAU,CAACK,IAAI,CAACD,KAAK,CAAC;;IAE1B,CAAC,EACDT,IAAI,CACL,CACF;EACH,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}