{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { defaultListboxReducer, useListbox, ActionTypes } from '../ListboxUnstyled';\nfunction stateReducer(state, action) {\n  if (action.type === ActionTypes.blur || action.type === ActionTypes.optionHover || action.type === ActionTypes.setValue) {\n    return state;\n  }\n  const newState = defaultListboxReducer(state, action);\n  if (action.type !== ActionTypes.setHighlight && newState.highlightedValue === null && action.props.options.length > 0) {\n    return _extends({}, newState, {\n      highlightedValue: action.props.options[0]\n    });\n  }\n  return newState;\n}\nexport default function useMenu(parameters = {}) {\n  const {\n    listboxRef: listboxRefProp,\n    open = false,\n    onClose,\n    listboxId\n  } = parameters;\n  const [menuItems, setMenuItems] = React.useState({});\n  const listboxRef = React.useRef(null);\n  const handleRef = useForkRef(listboxRef, listboxRefProp);\n  const registerItem = React.useCallback((id, metadata) => {\n    setMenuItems(previousState => {\n      const newState = _extends({}, previousState);\n      newState[id] = metadata;\n      return newState;\n    });\n  }, []);\n  const unregisterItem = React.useCallback(id => {\n    setMenuItems(previousState => {\n      const newState = _extends({}, previousState);\n      delete newState[id];\n      return newState;\n    });\n  }, []);\n  const {\n    getOptionState,\n    getOptionProps,\n    getRootProps,\n    highlightedOption,\n    setHighlightedValue: setListboxHighlight\n  } = useListbox({\n    options: Object.keys(menuItems),\n    optionStringifier: id => {\n      var _menuItems$id$ref$cur;\n      return menuItems[id].label || ((_menuItems$id$ref$cur = menuItems[id].ref.current) == null ? void 0 : _menuItems$id$ref$cur.innerText);\n    },\n    isOptionDisabled: id => {\n      var _menuItems$id;\n      return (menuItems == null ? void 0 : (_menuItems$id = menuItems[id]) == null ? void 0 : _menuItems$id.disabled) || false;\n    },\n    listboxRef: handleRef,\n    focusManagement: 'DOM',\n    id: listboxId,\n    stateReducer,\n    disabledItemsFocusable: true\n  });\n  const highlightFirstItem = React.useCallback(() => {\n    if (Object.keys(menuItems).length > 0) {\n      setListboxHighlight(menuItems[Object.keys(menuItems)[0]].id);\n    }\n  }, [menuItems, setListboxHighlight]);\n  const highlightLastItem = React.useCallback(() => {\n    if (Object.keys(menuItems).length > 0) {\n      setListboxHighlight(menuItems[Object.keys(menuItems)[Object.keys(menuItems).length - 1]].id);\n    }\n  }, [menuItems, setListboxHighlight]);\n  React.useEffect(() => {\n    if (!open) {\n      highlightFirstItem();\n    }\n  }, [open, highlightFirstItem]);\n  const createHandleKeyDown = otherHandlers => e => {\n    var _otherHandlers$onKeyD;\n    (_otherHandlers$onKeyD = otherHandlers.onKeyDown) == null ? void 0 : _otherHandlers$onKeyD.call(otherHandlers, e);\n    if (e.defaultPrevented) {\n      return;\n    }\n    if (e.key === 'Escape' && open) {\n      onClose == null ? void 0 : onClose();\n    }\n  };\n  const createHandleBlur = otherHandlers => e => {\n    var _otherHandlers$onBlur, _listboxRef$current;\n    (_otherHandlers$onBlur = otherHandlers.onBlur) == null ? void 0 : _otherHandlers$onBlur.call(otherHandlers, e);\n    if (!((_listboxRef$current = listboxRef.current) != null && _listboxRef$current.contains(e.relatedTarget))) {\n      onClose == null ? void 0 : onClose();\n    }\n  };\n  React.useEffect(() => {\n    var _listboxRef$current2;\n\n    // set focus to the highlighted item (but prevent stealing focus from other elements on the page)\n    if ((_listboxRef$current2 = listboxRef.current) != null && _listboxRef$current2.contains(document.activeElement) && highlightedOption !== null) {\n      var _menuItems$highlighte, _menuItems$highlighte2;\n      menuItems == null ? void 0 : (_menuItems$highlighte = menuItems[highlightedOption]) == null ? void 0 : (_menuItems$highlighte2 = _menuItems$highlighte.ref.current) == null ? void 0 : _menuItems$highlighte2.focus();\n    }\n  }, [highlightedOption, menuItems]);\n  const getListboxProps = (otherHandlers = {}) => {\n    const rootProps = getRootProps(_extends({}, otherHandlers, {\n      onBlur: createHandleBlur(otherHandlers),\n      onKeyDown: createHandleKeyDown(otherHandlers)\n    }));\n    return _extends({}, otherHandlers, rootProps, {\n      role: 'menu'\n    });\n  };\n  const getItemState = id => {\n    const {\n      disabled,\n      highlighted\n    } = getOptionState(id);\n    return {\n      disabled,\n      highlighted\n    };\n  };\n  React.useDebugValue({\n    menuItems,\n    highlightedOption\n  });\n  return {\n    registerItem,\n    unregisterItem,\n    menuItems,\n    getListboxProps,\n    getItemState,\n    getItemProps: getOptionProps,\n    highlightedOption,\n    highlightFirstItem,\n    highlightLastItem\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_useForkRef", "useForkRef", "defaultListboxReducer", "useListbox", "ActionTypes", "stateReducer", "state", "action", "type", "blur", "optionHover", "setValue", "newState", "<PERSON><PERSON><PERSON><PERSON>", "highlightedValue", "props", "options", "length", "useMenu", "parameters", "listboxRef", "listboxRefProp", "open", "onClose", "listboxId", "menuItems", "setMenuItems", "useState", "useRef", "handleRef", "registerItem", "useCallback", "id", "metadata", "previousState", "unregisterItem", "getOptionState", "getOptionProps", "getRootProps", "highlightedOption", "setHighlightedValue", "setListboxHighlight", "Object", "keys", "optionStringifier", "_menuItems$id$ref$cur", "label", "ref", "current", "innerText", "isOptionDisabled", "_menuItems$id", "disabled", "focusManagement", "disabledItemsFocusable", "highlightFirstItem", "highlightLastItem", "useEffect", "createHandleKeyDown", "otherHandlers", "e", "_otherHandlers$onKeyD", "onKeyDown", "call", "defaultPrevented", "key", "createHandleBlur", "_otherHandlers$onBlur", "_listboxRef$current", "onBlur", "contains", "relatedTarget", "_listboxRef$current2", "document", "activeElement", "_menuItems$highlighte", "_menuItems$highlighte2", "focus", "getListboxProps", "rootProps", "role", "getItemState", "highlighted", "useDebugValue", "getItemProps"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/MenuUnstyled/useMenu.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { defaultListboxReducer, useListbox, ActionTypes } from '../ListboxUnstyled';\n\nfunction stateReducer(state, action) {\n  if (action.type === ActionTypes.blur || action.type === ActionTypes.optionHover || action.type === ActionTypes.setValue) {\n    return state;\n  }\n\n  const newState = defaultListboxReducer(state, action);\n\n  if (action.type !== ActionTypes.setHighlight && newState.highlightedValue === null && action.props.options.length > 0) {\n    return _extends({}, newState, {\n      highlightedValue: action.props.options[0]\n    });\n  }\n\n  return newState;\n}\n\nexport default function useMenu(parameters = {}) {\n  const {\n    listboxRef: listboxRefProp,\n    open = false,\n    onClose,\n    listboxId\n  } = parameters;\n  const [menuItems, setMenuItems] = React.useState({});\n  const listboxRef = React.useRef(null);\n  const handleRef = useForkRef(listboxRef, listboxRefProp);\n  const registerItem = React.useCallback((id, metadata) => {\n    setMenuItems(previousState => {\n      const newState = _extends({}, previousState);\n\n      newState[id] = metadata;\n      return newState;\n    });\n  }, []);\n  const unregisterItem = React.useCallback(id => {\n    setMenuItems(previousState => {\n      const newState = _extends({}, previousState);\n\n      delete newState[id];\n      return newState;\n    });\n  }, []);\n  const {\n    getOptionState,\n    getOptionProps,\n    getRootProps,\n    highlightedOption,\n    setHighlightedValue: setListboxHighlight\n  } = useListbox({\n    options: Object.keys(menuItems),\n    optionStringifier: id => {\n      var _menuItems$id$ref$cur;\n\n      return menuItems[id].label || ((_menuItems$id$ref$cur = menuItems[id].ref.current) == null ? void 0 : _menuItems$id$ref$cur.innerText);\n    },\n    isOptionDisabled: id => {\n      var _menuItems$id;\n\n      return (menuItems == null ? void 0 : (_menuItems$id = menuItems[id]) == null ? void 0 : _menuItems$id.disabled) || false;\n    },\n    listboxRef: handleRef,\n    focusManagement: 'DOM',\n    id: listboxId,\n    stateReducer,\n    disabledItemsFocusable: true\n  });\n  const highlightFirstItem = React.useCallback(() => {\n    if (Object.keys(menuItems).length > 0) {\n      setListboxHighlight(menuItems[Object.keys(menuItems)[0]].id);\n    }\n  }, [menuItems, setListboxHighlight]);\n  const highlightLastItem = React.useCallback(() => {\n    if (Object.keys(menuItems).length > 0) {\n      setListboxHighlight(menuItems[Object.keys(menuItems)[Object.keys(menuItems).length - 1]].id);\n    }\n  }, [menuItems, setListboxHighlight]);\n  React.useEffect(() => {\n    if (!open) {\n      highlightFirstItem();\n    }\n  }, [open, highlightFirstItem]);\n\n  const createHandleKeyDown = otherHandlers => e => {\n    var _otherHandlers$onKeyD;\n\n    (_otherHandlers$onKeyD = otherHandlers.onKeyDown) == null ? void 0 : _otherHandlers$onKeyD.call(otherHandlers, e);\n\n    if (e.defaultPrevented) {\n      return;\n    }\n\n    if (e.key === 'Escape' && open) {\n      onClose == null ? void 0 : onClose();\n    }\n  };\n\n  const createHandleBlur = otherHandlers => e => {\n    var _otherHandlers$onBlur, _listboxRef$current;\n\n    (_otherHandlers$onBlur = otherHandlers.onBlur) == null ? void 0 : _otherHandlers$onBlur.call(otherHandlers, e);\n\n    if (!((_listboxRef$current = listboxRef.current) != null && _listboxRef$current.contains(e.relatedTarget))) {\n      onClose == null ? void 0 : onClose();\n    }\n  };\n\n  React.useEffect(() => {\n    var _listboxRef$current2;\n\n    // set focus to the highlighted item (but prevent stealing focus from other elements on the page)\n    if ((_listboxRef$current2 = listboxRef.current) != null && _listboxRef$current2.contains(document.activeElement) && highlightedOption !== null) {\n      var _menuItems$highlighte, _menuItems$highlighte2;\n\n      menuItems == null ? void 0 : (_menuItems$highlighte = menuItems[highlightedOption]) == null ? void 0 : (_menuItems$highlighte2 = _menuItems$highlighte.ref.current) == null ? void 0 : _menuItems$highlighte2.focus();\n    }\n  }, [highlightedOption, menuItems]);\n\n  const getListboxProps = (otherHandlers = {}) => {\n    const rootProps = getRootProps(_extends({}, otherHandlers, {\n      onBlur: createHandleBlur(otherHandlers),\n      onKeyDown: createHandleKeyDown(otherHandlers)\n    }));\n    return _extends({}, otherHandlers, rootProps, {\n      role: 'menu'\n    });\n  };\n\n  const getItemState = id => {\n    const {\n      disabled,\n      highlighted\n    } = getOptionState(id);\n    return {\n      disabled,\n      highlighted\n    };\n  };\n\n  React.useDebugValue({\n    menuItems,\n    highlightedOption\n  });\n  return {\n    registerItem,\n    unregisterItem,\n    menuItems,\n    getListboxProps,\n    getItemState,\n    getItemProps: getOptionProps,\n    highlightedOption,\n    highlightFirstItem,\n    highlightLastItem\n  };\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAC9D,SAASC,qBAAqB,EAAEC,UAAU,EAAEC,WAAW,QAAQ,oBAAoB;AAEnF,SAASC,YAAYA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACnC,IAAIA,MAAM,CAACC,IAAI,KAAKJ,WAAW,CAACK,IAAI,IAAIF,MAAM,CAACC,IAAI,KAAKJ,WAAW,CAACM,WAAW,IAAIH,MAAM,CAACC,IAAI,KAAKJ,WAAW,CAACO,QAAQ,EAAE;IACvH,OAAOL,KAAK;EACd;EAEA,MAAMM,QAAQ,GAAGV,qBAAqB,CAACI,KAAK,EAAEC,MAAM,CAAC;EAErD,IAAIA,MAAM,CAACC,IAAI,KAAKJ,WAAW,CAACS,YAAY,IAAID,QAAQ,CAACE,gBAAgB,KAAK,IAAI,IAAIP,MAAM,CAACQ,KAAK,CAACC,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;IACrH,OAAOnB,QAAQ,CAAC,CAAC,CAAC,EAAEc,QAAQ,EAAE;MAC5BE,gBAAgB,EAAEP,MAAM,CAACQ,KAAK,CAACC,OAAO,CAAC,CAAC;IAC1C,CAAC,CAAC;EACJ;EAEA,OAAOJ,QAAQ;AACjB;AAEA,eAAe,SAASM,OAAOA,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE;EAC/C,MAAM;IACJC,UAAU,EAAEC,cAAc;IAC1BC,IAAI,GAAG,KAAK;IACZC,OAAO;IACPC;EACF,CAAC,GAAGL,UAAU;EACd,MAAM,CAACM,SAAS,EAAEC,YAAY,CAAC,GAAG3B,KAAK,CAAC4B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAMP,UAAU,GAAGrB,KAAK,CAAC6B,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMC,SAAS,GAAG5B,UAAU,CAACmB,UAAU,EAAEC,cAAc,CAAC;EACxD,MAAMS,YAAY,GAAG/B,KAAK,CAACgC,WAAW,CAAC,CAACC,EAAE,EAAEC,QAAQ,KAAK;IACvDP,YAAY,CAACQ,aAAa,IAAI;MAC5B,MAAMtB,QAAQ,GAAGd,QAAQ,CAAC,CAAC,CAAC,EAAEoC,aAAa,CAAC;MAE5CtB,QAAQ,CAACoB,EAAE,CAAC,GAAGC,QAAQ;MACvB,OAAOrB,QAAQ;IACjB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,MAAMuB,cAAc,GAAGpC,KAAK,CAACgC,WAAW,CAACC,EAAE,IAAI;IAC7CN,YAAY,CAACQ,aAAa,IAAI;MAC5B,MAAMtB,QAAQ,GAAGd,QAAQ,CAAC,CAAC,CAAC,EAAEoC,aAAa,CAAC;MAE5C,OAAOtB,QAAQ,CAACoB,EAAE,CAAC;MACnB,OAAOpB,QAAQ;IACjB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,MAAM;IACJwB,cAAc;IACdC,cAAc;IACdC,YAAY;IACZC,iBAAiB;IACjBC,mBAAmB,EAAEC;EACvB,CAAC,GAAGtC,UAAU,CAAC;IACba,OAAO,EAAE0B,MAAM,CAACC,IAAI,CAAClB,SAAS,CAAC;IAC/BmB,iBAAiB,EAAEZ,EAAE,IAAI;MACvB,IAAIa,qBAAqB;MAEzB,OAAOpB,SAAS,CAACO,EAAE,CAAC,CAACc,KAAK,KAAK,CAACD,qBAAqB,GAAGpB,SAAS,CAACO,EAAE,CAAC,CAACe,GAAG,CAACC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,qBAAqB,CAACI,SAAS,CAAC;IACxI,CAAC;IACDC,gBAAgB,EAAElB,EAAE,IAAI;MACtB,IAAImB,aAAa;MAEjB,OAAO,CAAC1B,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC0B,aAAa,GAAG1B,SAAS,CAACO,EAAE,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmB,aAAa,CAACC,QAAQ,KAAK,KAAK;IAC1H,CAAC;IACDhC,UAAU,EAAES,SAAS;IACrBwB,eAAe,EAAE,KAAK;IACtBrB,EAAE,EAAER,SAAS;IACbnB,YAAY;IACZiD,sBAAsB,EAAE;EAC1B,CAAC,CAAC;EACF,MAAMC,kBAAkB,GAAGxD,KAAK,CAACgC,WAAW,CAAC,MAAM;IACjD,IAAIW,MAAM,CAACC,IAAI,CAAClB,SAAS,CAAC,CAACR,MAAM,GAAG,CAAC,EAAE;MACrCwB,mBAAmB,CAAChB,SAAS,CAACiB,MAAM,CAACC,IAAI,CAAClB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAACO,EAAE,CAAC;IAC9D;EACF,CAAC,EAAE,CAACP,SAAS,EAAEgB,mBAAmB,CAAC,CAAC;EACpC,MAAMe,iBAAiB,GAAGzD,KAAK,CAACgC,WAAW,CAAC,MAAM;IAChD,IAAIW,MAAM,CAACC,IAAI,CAAClB,SAAS,CAAC,CAACR,MAAM,GAAG,CAAC,EAAE;MACrCwB,mBAAmB,CAAChB,SAAS,CAACiB,MAAM,CAACC,IAAI,CAAClB,SAAS,CAAC,CAACiB,MAAM,CAACC,IAAI,CAAClB,SAAS,CAAC,CAACR,MAAM,GAAG,CAAC,CAAC,CAAC,CAACe,EAAE,CAAC;IAC9F;EACF,CAAC,EAAE,CAACP,SAAS,EAAEgB,mBAAmB,CAAC,CAAC;EACpC1C,KAAK,CAAC0D,SAAS,CAAC,MAAM;IACpB,IAAI,CAACnC,IAAI,EAAE;MACTiC,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACjC,IAAI,EAAEiC,kBAAkB,CAAC,CAAC;EAE9B,MAAMG,mBAAmB,GAAGC,aAAa,IAAIC,CAAC,IAAI;IAChD,IAAIC,qBAAqB;IAEzB,CAACA,qBAAqB,GAAGF,aAAa,CAACG,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,qBAAqB,CAACE,IAAI,CAACJ,aAAa,EAAEC,CAAC,CAAC;IAEjH,IAAIA,CAAC,CAACI,gBAAgB,EAAE;MACtB;IACF;IAEA,IAAIJ,CAAC,CAACK,GAAG,KAAK,QAAQ,IAAI3C,IAAI,EAAE;MAC9BC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,CAAC;IACtC;EACF,CAAC;EAED,MAAM2C,gBAAgB,GAAGP,aAAa,IAAIC,CAAC,IAAI;IAC7C,IAAIO,qBAAqB,EAAEC,mBAAmB;IAE9C,CAACD,qBAAqB,GAAGR,aAAa,CAACU,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,qBAAqB,CAACJ,IAAI,CAACJ,aAAa,EAAEC,CAAC,CAAC;IAE9G,IAAI,EAAE,CAACQ,mBAAmB,GAAGhD,UAAU,CAAC4B,OAAO,KAAK,IAAI,IAAIoB,mBAAmB,CAACE,QAAQ,CAACV,CAAC,CAACW,aAAa,CAAC,CAAC,EAAE;MAC1GhD,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,CAAC;IACtC;EACF,CAAC;EAEDxB,KAAK,CAAC0D,SAAS,CAAC,MAAM;IACpB,IAAIe,oBAAoB;;IAExB;IACA,IAAI,CAACA,oBAAoB,GAAGpD,UAAU,CAAC4B,OAAO,KAAK,IAAI,IAAIwB,oBAAoB,CAACF,QAAQ,CAACG,QAAQ,CAACC,aAAa,CAAC,IAAInC,iBAAiB,KAAK,IAAI,EAAE;MAC9I,IAAIoC,qBAAqB,EAAEC,sBAAsB;MAEjDnD,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAACkD,qBAAqB,GAAGlD,SAAS,CAACc,iBAAiB,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,CAACqC,sBAAsB,GAAGD,qBAAqB,CAAC5B,GAAG,CAACC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG4B,sBAAsB,CAACC,KAAK,CAAC,CAAC;IACvN;EACF,CAAC,EAAE,CAACtC,iBAAiB,EAAEd,SAAS,CAAC,CAAC;EAElC,MAAMqD,eAAe,GAAGA,CAACnB,aAAa,GAAG,CAAC,CAAC,KAAK;IAC9C,MAAMoB,SAAS,GAAGzC,YAAY,CAACxC,QAAQ,CAAC,CAAC,CAAC,EAAE6D,aAAa,EAAE;MACzDU,MAAM,EAAEH,gBAAgB,CAACP,aAAa,CAAC;MACvCG,SAAS,EAAEJ,mBAAmB,CAACC,aAAa;IAC9C,CAAC,CAAC,CAAC;IACH,OAAO7D,QAAQ,CAAC,CAAC,CAAC,EAAE6D,aAAa,EAAEoB,SAAS,EAAE;MAC5CC,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAGjD,EAAE,IAAI;IACzB,MAAM;MACJoB,QAAQ;MACR8B;IACF,CAAC,GAAG9C,cAAc,CAACJ,EAAE,CAAC;IACtB,OAAO;MACLoB,QAAQ;MACR8B;IACF,CAAC;EACH,CAAC;EAEDnF,KAAK,CAACoF,aAAa,CAAC;IAClB1D,SAAS;IACTc;EACF,CAAC,CAAC;EACF,OAAO;IACLT,YAAY;IACZK,cAAc;IACdV,SAAS;IACTqD,eAAe;IACfG,YAAY;IACZG,YAAY,EAAE/C,cAAc;IAC5BE,iBAAiB;IACjBgB,kBAAkB;IAClBC;EACF,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}