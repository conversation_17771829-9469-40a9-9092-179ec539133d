{"ast": null, "code": "import { mergeMap } from './mergeMap';\nexport var flatMap = mergeMap;", "map": {"version": 3, "names": ["mergeMap", "flatMap"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\flatMap.ts"], "sourcesContent": ["import { mergeMap } from './mergeMap';\n\n/**\n * @deprecated Renamed to {@link mergeMap}. Will be removed in v8.\n */\nexport const flatMap = mergeMap;\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,YAAY;AAKrC,OAAO,IAAMC,OAAO,GAAGD,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}