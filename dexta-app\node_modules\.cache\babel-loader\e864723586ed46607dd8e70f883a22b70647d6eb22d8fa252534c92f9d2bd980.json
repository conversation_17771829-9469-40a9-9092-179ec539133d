{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = htmlAttributesToReact;\nvar _BooleanAttributes = require('../dom/attributes/BooleanAttributes');\nvar _BooleanAttributes2 = _interopRequireDefault(_BooleanAttributes);\nvar _ReactAttributes = require('../dom/attributes/ReactAttributes');\nvar _ReactAttributes2 = _interopRequireDefault(_ReactAttributes);\nvar _isValidTagOrAttributeName = require('./isValidTagOrAttributeName');\nvar _isValidTagOrAttributeName2 = _interopRequireDefault(_isValidTagOrAttributeName);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\n\n/**\n * Returns the parsed attribute value taking into account things like boolean attributes\n *\n * @param {String} attribute The name of the attribute\n * @param {*} value The value of the attribute from the HTML\n * @returns {*} The parsed attribute value\n */\nvar getParsedAttributeValue = function getParsedAttributeValue(attribute, value) {\n  // if the attribute if a boolean then it's value should be the same as it's name\n  // e.g. disabled=\"disabled\"\n  var lowerBooleanAttributes = _BooleanAttributes2.default.map(function (attr) {\n    return attr.toLowerCase();\n  });\n  if (lowerBooleanAttributes.indexOf(attribute.toLowerCase()) >= 0) {\n    value = attribute;\n  }\n  return value;\n};\n\n/**\n * Takes an object of standard HTML property names and converts them to their React counterpart. If the react\n * version does not exist for an attribute then just use it as it is\n *\n * @param {Object} attributes The HTML attributes to convert\n * @returns {Object} The React attributes\n */\nfunction htmlAttributesToReact(attributes) {\n  return Object.keys(attributes).filter(function (attr) {\n    return (0, _isValidTagOrAttributeName2.default)(attr);\n  }).reduce(function (mappedAttributes, attribute) {\n    // lowercase the attribute name and find it in the react attribute map\n    var lowerCaseAttribute = attribute.toLowerCase();\n\n    // format the attribute name\n    var name = _ReactAttributes2.default[lowerCaseAttribute] || lowerCaseAttribute;\n\n    // add the parsed attribute value to the mapped attributes\n    mappedAttributes[name] = getParsedAttributeValue(name, attributes[attribute]);\n    return mappedAttributes;\n  }, {});\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "htmlAttributesToReact", "_BooleanAttributes", "require", "_BooleanAttributes2", "_interopRequireDefault", "_ReactAttributes", "_ReactAttributes2", "_isValidTagOrAttributeName", "_isValidTagOrAttributeName2", "obj", "__esModule", "getParsedAttributeValue", "attribute", "lowerBooleanAttributes", "map", "attr", "toLowerCase", "indexOf", "attributes", "keys", "filter", "reduce", "mappedAttributes", "lowerCaseAttribute", "name"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/react-html-parser/lib/utils/htmlAttributesToReact.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = htmlAttributesToReact;\n\nvar _BooleanAttributes = require('../dom/attributes/BooleanAttributes');\n\nvar _BooleanAttributes2 = _interopRequireDefault(_BooleanAttributes);\n\nvar _ReactAttributes = require('../dom/attributes/ReactAttributes');\n\nvar _ReactAttributes2 = _interopRequireDefault(_ReactAttributes);\n\nvar _isValidTagOrAttributeName = require('./isValidTagOrAttributeName');\n\nvar _isValidTagOrAttributeName2 = _interopRequireDefault(_isValidTagOrAttributeName);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\n/**\n * Returns the parsed attribute value taking into account things like boolean attributes\n *\n * @param {String} attribute The name of the attribute\n * @param {*} value The value of the attribute from the HTML\n * @returns {*} The parsed attribute value\n */\nvar getParsedAttributeValue = function getParsedAttributeValue(attribute, value) {\n\n  // if the attribute if a boolean then it's value should be the same as it's name\n  // e.g. disabled=\"disabled\"\n  var lowerBooleanAttributes = _BooleanAttributes2.default.map(function (attr) {\n    return attr.toLowerCase();\n  });\n  if (lowerBooleanAttributes.indexOf(attribute.toLowerCase()) >= 0) {\n    value = attribute;\n  }\n\n  return value;\n};\n\n/**\n * Takes an object of standard HTML property names and converts them to their React counterpart. If the react\n * version does not exist for an attribute then just use it as it is\n *\n * @param {Object} attributes The HTML attributes to convert\n * @returns {Object} The React attributes\n */\nfunction htmlAttributesToReact(attributes) {\n\n  return Object.keys(attributes).filter(function (attr) {\n    return (0, _isValidTagOrAttributeName2.default)(attr);\n  }).reduce(function (mappedAttributes, attribute) {\n\n    // lowercase the attribute name and find it in the react attribute map\n    var lowerCaseAttribute = attribute.toLowerCase();\n\n    // format the attribute name\n    var name = _ReactAttributes2.default[lowerCaseAttribute] || lowerCaseAttribute;\n\n    // add the parsed attribute value to the mapped attributes\n    mappedAttributes[name] = getParsedAttributeValue(name, attributes[attribute]);\n\n    return mappedAttributes;\n  }, {});\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,qBAAqB;AAEvC,IAAIC,kBAAkB,GAAGC,OAAO,CAAC,qCAAqC,CAAC;AAEvE,IAAIC,mBAAmB,GAAGC,sBAAsB,CAACH,kBAAkB,CAAC;AAEpE,IAAII,gBAAgB,GAAGH,OAAO,CAAC,mCAAmC,CAAC;AAEnE,IAAII,iBAAiB,GAAGF,sBAAsB,CAACC,gBAAgB,CAAC;AAEhE,IAAIE,0BAA0B,GAAGL,OAAO,CAAC,6BAA6B,CAAC;AAEvE,IAAIM,2BAA2B,GAAGJ,sBAAsB,CAACG,0BAA0B,CAAC;AAEpF,SAASH,sBAAsBA,CAACK,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEV,OAAO,EAAEU;EAAI,CAAC;AAAE;;AAE9F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,uBAAuB,GAAG,SAASA,uBAAuBA,CAACC,SAAS,EAAEd,KAAK,EAAE;EAE/E;EACA;EACA,IAAIe,sBAAsB,GAAGV,mBAAmB,CAACJ,OAAO,CAACe,GAAG,CAAC,UAAUC,IAAI,EAAE;IAC3E,OAAOA,IAAI,CAACC,WAAW,CAAC,CAAC;EAC3B,CAAC,CAAC;EACF,IAAIH,sBAAsB,CAACI,OAAO,CAACL,SAAS,CAACI,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;IAChElB,KAAK,GAAGc,SAAS;EACnB;EAEA,OAAOd,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,qBAAqBA,CAACkB,UAAU,EAAE;EAEzC,OAAOvB,MAAM,CAACwB,IAAI,CAACD,UAAU,CAAC,CAACE,MAAM,CAAC,UAAUL,IAAI,EAAE;IACpD,OAAO,CAAC,CAAC,EAAEP,2BAA2B,CAACT,OAAO,EAAEgB,IAAI,CAAC;EACvD,CAAC,CAAC,CAACM,MAAM,CAAC,UAAUC,gBAAgB,EAAEV,SAAS,EAAE;IAE/C;IACA,IAAIW,kBAAkB,GAAGX,SAAS,CAACI,WAAW,CAAC,CAAC;;IAEhD;IACA,IAAIQ,IAAI,GAAGlB,iBAAiB,CAACP,OAAO,CAACwB,kBAAkB,CAAC,IAAIA,kBAAkB;;IAE9E;IACAD,gBAAgB,CAACE,IAAI,CAAC,GAAGb,uBAAuB,CAACa,IAAI,EAAEN,UAAU,CAACN,SAAS,CAAC,CAAC;IAE7E,OAAOU,gBAAgB;EACzB,CAAC,EAAE,CAAC,CAAC,CAAC;AACR"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}