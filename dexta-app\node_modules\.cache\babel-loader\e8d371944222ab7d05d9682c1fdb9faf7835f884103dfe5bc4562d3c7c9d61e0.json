{"ast": null, "code": "/*! ieee754. BSD-3-Clause License. Feross Abouk<PERSON>i<PERSON> <https://feross.org/opensource> */\nexports.read = function (buffer, offset, isLE, mLen, nBytes) {\n  var e, m;\n  var eLen = nBytes * 8 - mLen - 1;\n  var eMax = (1 << eLen) - 1;\n  var eBias = eMax >> 1;\n  var nBits = -7;\n  var i = isLE ? nBytes - 1 : 0;\n  var d = isLE ? -1 : 1;\n  var s = buffer[offset + i];\n  i += d;\n  e = s & (1 << -nBits) - 1;\n  s >>= -nBits;\n  nBits += eLen;\n  for (; nBits > 0; e = e * 256 + buffer[offset + i], i += d, nBits -= 8) {}\n  m = e & (1 << -nBits) - 1;\n  e >>= -nBits;\n  nBits += mLen;\n  for (; nBits > 0; m = m * 256 + buffer[offset + i], i += d, nBits -= 8) {}\n  if (e === 0) {\n    e = 1 - eBias;\n  } else if (e === eMax) {\n    return m ? NaN : (s ? -1 : 1) * Infinity;\n  } else {\n    m = m + Math.pow(2, mLen);\n    e = e - eBias;\n  }\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen);\n};\nexports.write = function (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c;\n  var eLen = nBytes * 8 - mLen - 1;\n  var eMax = (1 << eLen) - 1;\n  var eBias = eMax >> 1;\n  var rt = mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0;\n  var i = isLE ? 0 : nBytes - 1;\n  var d = isLE ? 1 : -1;\n  var s = value < 0 || value === 0 && 1 / value < 0 ? 1 : 0;\n  value = Math.abs(value);\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0;\n    e = eMax;\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2);\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--;\n      c *= 2;\n    }\n    if (e + eBias >= 1) {\n      value += rt / c;\n    } else {\n      value += rt * Math.pow(2, 1 - eBias);\n    }\n    if (value * c >= 2) {\n      e++;\n      c /= 2;\n    }\n    if (e + eBias >= eMax) {\n      m = 0;\n      e = eMax;\n    } else if (e + eBias >= 1) {\n      m = (value * c - 1) * Math.pow(2, mLen);\n      e = e + eBias;\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen);\n      e = 0;\n    }\n  }\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n  e = e << mLen | m;\n  eLen += mLen;\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n  buffer[offset + i - d] |= s * 128;\n};", "map": {"version": 3, "names": ["exports", "read", "buffer", "offset", "isLE", "mLen", "nBytes", "e", "m", "eLen", "eMax", "eBias", "nBits", "i", "d", "s", "NaN", "Infinity", "Math", "pow", "write", "value", "c", "rt", "abs", "isNaN", "floor", "log", "LN2"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/ieee754/index.js"], "sourcesContent": ["/*! ieee754. BSD-3-Clause License. Feross A<PERSON> <https://feross.org/opensource> */\nexports.read = function (buffer, offset, isLE, mLen, nBytes) {\n  var e, m\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var nBits = -7\n  var i = isLE ? (nBytes - 1) : 0\n  var d = isLE ? -1 : 1\n  var s = buffer[offset + i]\n\n  i += d\n\n  e = s & ((1 << (-nBits)) - 1)\n  s >>= (-nBits)\n  nBits += eLen\n  for (; nBits > 0; e = (e * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & ((1 << (-nBits)) - 1)\n  e >>= (-nBits)\n  nBits += mLen\n  for (; nBits > 0; m = (m * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias\n  } else if (e === eMax) {\n    return m ? NaN : ((s ? -1 : 1) * Infinity)\n  } else {\n    m = m + Math.pow(2, mLen)\n    e = e - eBias\n  }\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen)\n}\n\nexports.write = function (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0)\n  var i = isLE ? 0 : (nBytes - 1)\n  var d = isLE ? 1 : -1\n  var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0\n\n  value = Math.abs(value)\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0\n    e = eMax\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2)\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--\n      c *= 2\n    }\n    if (e + eBias >= 1) {\n      value += rt / c\n    } else {\n      value += rt * Math.pow(2, 1 - eBias)\n    }\n    if (value * c >= 2) {\n      e++\n      c /= 2\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0\n      e = eMax\n    } else if (e + eBias >= 1) {\n      m = ((value * c) - 1) * Math.pow(2, mLen)\n      e = e + eBias\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen)\n      e = 0\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = (e << mLen) | m\n  eLen += mLen\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128\n}\n"], "mappings": "AAAA;AACAA,OAAO,CAACC,IAAI,GAAG,UAAUC,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAE;EAC3D,IAAIC,CAAC,EAAEC,CAAC;EACR,IAAIC,IAAI,GAAIH,MAAM,GAAG,CAAC,GAAID,IAAI,GAAG,CAAC;EAClC,IAAIK,IAAI,GAAG,CAAC,CAAC,IAAID,IAAI,IAAI,CAAC;EAC1B,IAAIE,KAAK,GAAGD,IAAI,IAAI,CAAC;EACrB,IAAIE,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,CAAC,GAAGT,IAAI,GAAIE,MAAM,GAAG,CAAC,GAAI,CAAC;EAC/B,IAAIQ,CAAC,GAAGV,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;EACrB,IAAIW,CAAC,GAAGb,MAAM,CAACC,MAAM,GAAGU,CAAC,CAAC;EAE1BA,CAAC,IAAIC,CAAC;EAENP,CAAC,GAAGQ,CAAC,GAAI,CAAC,CAAC,IAAK,CAACH,KAAM,IAAI,CAAE;EAC7BG,CAAC,KAAM,CAACH,KAAM;EACdA,KAAK,IAAIH,IAAI;EACb,OAAOG,KAAK,GAAG,CAAC,EAAEL,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAIL,MAAM,CAACC,MAAM,GAAGU,CAAC,CAAC,EAAEA,CAAC,IAAIC,CAAC,EAAEF,KAAK,IAAI,CAAC,EAAE,CAAC;EAE3EJ,CAAC,GAAGD,CAAC,GAAI,CAAC,CAAC,IAAK,CAACK,KAAM,IAAI,CAAE;EAC7BL,CAAC,KAAM,CAACK,KAAM;EACdA,KAAK,IAAIP,IAAI;EACb,OAAOO,KAAK,GAAG,CAAC,EAAEJ,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAIN,MAAM,CAACC,MAAM,GAAGU,CAAC,CAAC,EAAEA,CAAC,IAAIC,CAAC,EAAEF,KAAK,IAAI,CAAC,EAAE,CAAC;EAE3E,IAAIL,CAAC,KAAK,CAAC,EAAE;IACXA,CAAC,GAAG,CAAC,GAAGI,KAAK;EACf,CAAC,MAAM,IAAIJ,CAAC,KAAKG,IAAI,EAAE;IACrB,OAAOF,CAAC,GAAGQ,GAAG,GAAI,CAACD,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIE,QAAS;EAC5C,CAAC,MAAM;IACLT,CAAC,GAAGA,CAAC,GAAGU,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEd,IAAI,CAAC;IACzBE,CAAC,GAAGA,CAAC,GAAGI,KAAK;EACf;EACA,OAAO,CAACI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAIP,CAAC,GAAGU,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEZ,CAAC,GAAGF,IAAI,CAAC;AACjD,CAAC;AAEDL,OAAO,CAACoB,KAAK,GAAG,UAAUlB,MAAM,EAAEmB,KAAK,EAAElB,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAE;EACnE,IAAIC,CAAC,EAAEC,CAAC,EAAEc,CAAC;EACX,IAAIb,IAAI,GAAIH,MAAM,GAAG,CAAC,GAAID,IAAI,GAAG,CAAC;EAClC,IAAIK,IAAI,GAAG,CAAC,CAAC,IAAID,IAAI,IAAI,CAAC;EAC1B,IAAIE,KAAK,GAAGD,IAAI,IAAI,CAAC;EACrB,IAAIa,EAAE,GAAIlB,IAAI,KAAK,EAAE,GAAGa,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAE;EAChE,IAAIN,CAAC,GAAGT,IAAI,GAAG,CAAC,GAAIE,MAAM,GAAG,CAAE;EAC/B,IAAIQ,CAAC,GAAGV,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;EACrB,IAAIW,CAAC,GAAGM,KAAK,GAAG,CAAC,IAAKA,KAAK,KAAK,CAAC,IAAI,CAAC,GAAGA,KAAK,GAAG,CAAE,GAAG,CAAC,GAAG,CAAC;EAE3DA,KAAK,GAAGH,IAAI,CAACM,GAAG,CAACH,KAAK,CAAC;EAEvB,IAAII,KAAK,CAACJ,KAAK,CAAC,IAAIA,KAAK,KAAKJ,QAAQ,EAAE;IACtCT,CAAC,GAAGiB,KAAK,CAACJ,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;IACxBd,CAAC,GAAGG,IAAI;EACV,CAAC,MAAM;IACLH,CAAC,GAAGW,IAAI,CAACQ,KAAK,CAACR,IAAI,CAACS,GAAG,CAACN,KAAK,CAAC,GAAGH,IAAI,CAACU,GAAG,CAAC;IAC1C,IAAIP,KAAK,IAAIC,CAAC,GAAGJ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAACZ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;MACrCA,CAAC,EAAE;MACHe,CAAC,IAAI,CAAC;IACR;IACA,IAAIf,CAAC,GAAGI,KAAK,IAAI,CAAC,EAAE;MAClBU,KAAK,IAAIE,EAAE,GAAGD,CAAC;IACjB,CAAC,MAAM;MACLD,KAAK,IAAIE,EAAE,GAAGL,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGR,KAAK,CAAC;IACtC;IACA,IAAIU,KAAK,GAAGC,CAAC,IAAI,CAAC,EAAE;MAClBf,CAAC,EAAE;MACHe,CAAC,IAAI,CAAC;IACR;IAEA,IAAIf,CAAC,GAAGI,KAAK,IAAID,IAAI,EAAE;MACrBF,CAAC,GAAG,CAAC;MACLD,CAAC,GAAGG,IAAI;IACV,CAAC,MAAM,IAAIH,CAAC,GAAGI,KAAK,IAAI,CAAC,EAAE;MACzBH,CAAC,GAAG,CAAEa,KAAK,GAAGC,CAAC,GAAI,CAAC,IAAIJ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEd,IAAI,CAAC;MACzCE,CAAC,GAAGA,CAAC,GAAGI,KAAK;IACf,CAAC,MAAM;MACLH,CAAC,GAAGa,KAAK,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,KAAK,GAAG,CAAC,CAAC,GAAGO,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEd,IAAI,CAAC;MACtDE,CAAC,GAAG,CAAC;IACP;EACF;EAEA,OAAOF,IAAI,IAAI,CAAC,EAAEH,MAAM,CAACC,MAAM,GAAGU,CAAC,CAAC,GAAGL,CAAC,GAAG,IAAI,EAAEK,CAAC,IAAIC,CAAC,EAAEN,CAAC,IAAI,GAAG,EAAEH,IAAI,IAAI,CAAC,EAAE,CAAC;EAE/EE,CAAC,GAAIA,CAAC,IAAIF,IAAI,GAAIG,CAAC;EACnBC,IAAI,IAAIJ,IAAI;EACZ,OAAOI,IAAI,GAAG,CAAC,EAAEP,MAAM,CAACC,MAAM,GAAGU,CAAC,CAAC,GAAGN,CAAC,GAAG,IAAI,EAAEM,CAAC,IAAIC,CAAC,EAAEP,CAAC,IAAI,GAAG,EAAEE,IAAI,IAAI,CAAC,EAAE,CAAC;EAE9EP,MAAM,CAACC,MAAM,GAAGU,CAAC,GAAGC,CAAC,CAAC,IAAIC,CAAC,GAAG,GAAG;AACnC,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}