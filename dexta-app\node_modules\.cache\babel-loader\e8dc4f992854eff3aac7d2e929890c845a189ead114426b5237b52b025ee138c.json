{"ast": null, "code": "function _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _s, _e;\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nimport _extractCountryCallingCode from './helpers/extractCountryCallingCode.js';\nimport extractCountryCallingCodeFromInternationalNumberWithoutPlusSign from './helpers/extractCountryCallingCodeFromInternationalNumberWithoutPlusSign.js';\nimport extractNationalNumberFromPossiblyIncompleteNumber from './helpers/extractNationalNumberFromPossiblyIncompleteNumber.js';\nimport stripIddPrefix from './helpers/stripIddPrefix.js';\nimport parseDigits from './helpers/parseDigits.js';\nimport { VALID_DIGITS, VALID_PUNCTUATION, PLUS_CHARS } from './constants.js';\nvar VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART = '[' + VALID_PUNCTUATION + VALID_DIGITS + ']+';\nvar VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART_PATTERN = new RegExp('^' + VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART + '$', 'i');\nvar VALID_FORMATTED_PHONE_NUMBER_PART = '(?:' + '[' + PLUS_CHARS + ']' + '[' + VALID_PUNCTUATION + VALID_DIGITS + ']*' + '|' + '[' + VALID_PUNCTUATION + VALID_DIGITS + ']+' + ')';\nvar AFTER_PHONE_NUMBER_DIGITS_END_PATTERN = new RegExp('[^' + VALID_PUNCTUATION + VALID_DIGITS + ']+' + '.*' + '$'); // Tests whether `national_prefix_for_parsing` could match\n// different national prefixes.\n// Matches anything that's not a digit or a square bracket.\n\nvar COMPLEX_NATIONAL_PREFIX = /[^\\d\\[\\]]/;\nvar AsYouTypeParser = /*#__PURE__*/function () {\n  function AsYouTypeParser(_ref) {\n    var defaultCountry = _ref.defaultCountry,\n      defaultCallingCode = _ref.defaultCallingCode,\n      metadata = _ref.metadata,\n      onNationalSignificantNumberChange = _ref.onNationalSignificantNumberChange;\n    _classCallCheck(this, AsYouTypeParser);\n    this.defaultCountry = defaultCountry;\n    this.defaultCallingCode = defaultCallingCode;\n    this.metadata = metadata;\n    this.onNationalSignificantNumberChange = onNationalSignificantNumberChange;\n  }\n  _createClass(AsYouTypeParser, [{\n    key: \"input\",\n    value: function input(text, state) {\n      var _extractFormattedDigi = extractFormattedDigitsAndPlus(text),\n        _extractFormattedDigi2 = _slicedToArray(_extractFormattedDigi, 2),\n        formattedDigits = _extractFormattedDigi2[0],\n        hasPlus = _extractFormattedDigi2[1];\n      var digits = parseDigits(formattedDigits); // Checks for a special case: just a leading `+` has been entered.\n\n      var justLeadingPlus;\n      if (hasPlus) {\n        if (!state.digits) {\n          state.startInternationalNumber();\n          if (!digits) {\n            justLeadingPlus = true;\n          }\n        }\n      }\n      if (digits) {\n        this.inputDigits(digits, state);\n      }\n      return {\n        digits: digits,\n        justLeadingPlus: justLeadingPlus\n      };\n    }\n    /**\r\n     * Inputs \"next\" phone number digits.\r\n     * @param  {string} digits\r\n     * @return {string} [formattedNumber] Formatted national phone number (if it can be formatted at this stage). Returning `undefined` means \"don't format the national phone number at this stage\".\r\n     */\n  }, {\n    key: \"inputDigits\",\n    value: function inputDigits(nextDigits, state) {\n      var digits = state.digits;\n      var hasReceivedThreeLeadingDigits = digits.length < 3 && digits.length + nextDigits.length >= 3; // Append phone number digits.\n\n      state.appendDigits(nextDigits); // Attempt to extract IDD prefix:\n      // Some users input their phone number in international format,\n      // but in an \"out-of-country\" dialing format instead of using the leading `+`.\n      // https://github.com/catamphetamine/libphonenumber-js/issues/185\n      // Detect such numbers as soon as there're at least 3 digits.\n      // Google's library attempts to extract IDD prefix at 3 digits,\n      // so this library just copies that behavior.\n      // I guess that's because the most commot IDD prefixes are\n      // `00` (Europe) and `011` (US).\n      // There exist really long IDD prefixes too:\n      // for example, in Australia the default IDD prefix is `0011`,\n      // and it could even be as long as `14880011`.\n      // An IDD prefix is extracted here, and then every time when\n      // there's a new digit and the number couldn't be formatted.\n\n      if (hasReceivedThreeLeadingDigits) {\n        this.extractIddPrefix(state);\n      }\n      if (this.isWaitingForCountryCallingCode(state)) {\n        if (!this.extractCountryCallingCode(state)) {\n          return;\n        }\n      } else {\n        state.appendNationalSignificantNumberDigits(nextDigits);\n      } // If a phone number is being input in international format,\n      // then it's not valid for it to have a national prefix.\n      // Still, some people incorrectly input such numbers with a national prefix.\n      // In such cases, only attempt to strip a national prefix if the number becomes too long.\n      // (but that is done later, not here)\n\n      if (!state.international) {\n        if (!this.hasExtractedNationalSignificantNumber) {\n          this.extractNationalSignificantNumber(state.getNationalDigits(), function (stateUpdate) {\n            return state.update(stateUpdate);\n          });\n        }\n      }\n    }\n  }, {\n    key: \"isWaitingForCountryCallingCode\",\n    value: function isWaitingForCountryCallingCode(_ref2) {\n      var international = _ref2.international,\n        callingCode = _ref2.callingCode;\n      return international && !callingCode;\n    } // Extracts a country calling code from a number\n    // being entered in internatonal format.\n  }, {\n    key: \"extractCountryCallingCode\",\n    value: function extractCountryCallingCode(state) {\n      var _extractCountryCallin = _extractCountryCallingCode('+' + state.getDigitsWithoutInternationalPrefix(), this.defaultCountry, this.defaultCallingCode, this.metadata.metadata),\n        countryCallingCode = _extractCountryCallin.countryCallingCode,\n        number = _extractCountryCallin.number;\n      if (countryCallingCode) {\n        state.setCallingCode(countryCallingCode);\n        state.update({\n          nationalSignificantNumber: number\n        });\n        return true;\n      }\n    }\n  }, {\n    key: \"reset\",\n    value: function reset(numberingPlan) {\n      if (numberingPlan) {\n        this.hasSelectedNumberingPlan = true;\n        var nationalPrefixForParsing = numberingPlan._nationalPrefixForParsing();\n        this.couldPossiblyExtractAnotherNationalSignificantNumber = nationalPrefixForParsing && COMPLEX_NATIONAL_PREFIX.test(nationalPrefixForParsing);\n      } else {\n        this.hasSelectedNumberingPlan = undefined;\n        this.couldPossiblyExtractAnotherNationalSignificantNumber = undefined;\n      }\n    }\n    /**\r\n     * Extracts a national (significant) number from user input.\r\n     * Google's library is different in that it only applies `national_prefix_for_parsing`\r\n     * and doesn't apply `national_prefix_transform_rule` after that.\r\n     * https://github.com/google/libphonenumber/blob/a3d70b0487875475e6ad659af404943211d26456/java/libphonenumber/src/com/google/i18n/phonenumbers/AsYouTypeFormatter.java#L539\r\n     * @return {boolean} [extracted]\r\n     */\n  }, {\n    key: \"extractNationalSignificantNumber\",\n    value: function extractNationalSignificantNumber(nationalDigits, setState) {\n      if (!this.hasSelectedNumberingPlan) {\n        return;\n      }\n      var _extractNationalNumbe = extractNationalNumberFromPossiblyIncompleteNumber(nationalDigits, this.metadata),\n        nationalPrefix = _extractNationalNumbe.nationalPrefix,\n        nationalNumber = _extractNationalNumbe.nationalNumber,\n        carrierCode = _extractNationalNumbe.carrierCode;\n      if (nationalNumber === nationalDigits) {\n        return;\n      }\n      this.onExtractedNationalNumber(nationalPrefix, carrierCode, nationalNumber, nationalDigits, setState);\n      return true;\n    }\n    /**\r\n     * In Google's code this function is called \"attempt to extract longer NDD\".\r\n     * \"Some national prefixes are a substring of others\", they say.\r\n     * @return {boolean} [result] — Returns `true` if extracting a national prefix produced different results from what they were.\r\n     */\n  }, {\n    key: \"extractAnotherNationalSignificantNumber\",\n    value: function extractAnotherNationalSignificantNumber(nationalDigits, prevNationalSignificantNumber, setState) {\n      if (!this.hasExtractedNationalSignificantNumber) {\n        return this.extractNationalSignificantNumber(nationalDigits, setState);\n      }\n      if (!this.couldPossiblyExtractAnotherNationalSignificantNumber) {\n        return;\n      }\n      var _extractNationalNumbe2 = extractNationalNumberFromPossiblyIncompleteNumber(nationalDigits, this.metadata),\n        nationalPrefix = _extractNationalNumbe2.nationalPrefix,\n        nationalNumber = _extractNationalNumbe2.nationalNumber,\n        carrierCode = _extractNationalNumbe2.carrierCode; // If a national prefix has been extracted previously,\n      // then it's always extracted as additional digits are added.\n      // That's assuming `extractNationalNumberFromPossiblyIncompleteNumber()`\n      // doesn't do anything different from what it currently does.\n      // So, just in case, here's this check, though it doesn't occur.\n\n      /* istanbul ignore if */\n\n      if (nationalNumber === prevNationalSignificantNumber) {\n        return;\n      }\n      this.onExtractedNationalNumber(nationalPrefix, carrierCode, nationalNumber, nationalDigits, setState);\n      return true;\n    }\n  }, {\n    key: \"onExtractedNationalNumber\",\n    value: function onExtractedNationalNumber(nationalPrefix, carrierCode, nationalSignificantNumber, nationalDigits, setState) {\n      var complexPrefixBeforeNationalSignificantNumber;\n      var nationalSignificantNumberMatchesInput; // This check also works with empty `this.nationalSignificantNumber`.\n\n      var nationalSignificantNumberIndex = nationalDigits.lastIndexOf(nationalSignificantNumber); // If the extracted national (significant) number is the\n      // last substring of the `digits`, then it means that it hasn't been altered:\n      // no digits have been removed from the national (significant) number\n      // while applying `national_prefix_transform_rule`.\n      // https://gitlab.com/catamphetamine/libphonenumber-js/-/blob/master/METADATA.md#national_prefix_for_parsing--national_prefix_transform_rule\n\n      if (nationalSignificantNumberIndex >= 0 && nationalSignificantNumberIndex === nationalDigits.length - nationalSignificantNumber.length) {\n        nationalSignificantNumberMatchesInput = true; // If a prefix of a national (significant) number is not as simple\n        // as just a basic national prefix, then such prefix is stored in\n        // `this.complexPrefixBeforeNationalSignificantNumber` property and will be\n        // prepended \"as is\" to the national (significant) number to produce\n        // a formatted result.\n\n        var prefixBeforeNationalNumber = nationalDigits.slice(0, nationalSignificantNumberIndex); // `prefixBeforeNationalNumber` is always non-empty,\n        // because `onExtractedNationalNumber()` isn't called\n        // when a national (significant) number hasn't been actually \"extracted\":\n        // when a national (significant) number is equal to the national part of `digits`,\n        // then `onExtractedNationalNumber()` doesn't get called.\n\n        if (prefixBeforeNationalNumber !== nationalPrefix) {\n          complexPrefixBeforeNationalSignificantNumber = prefixBeforeNationalNumber;\n        }\n      }\n      setState({\n        nationalPrefix: nationalPrefix,\n        carrierCode: carrierCode,\n        nationalSignificantNumber: nationalSignificantNumber,\n        nationalSignificantNumberMatchesInput: nationalSignificantNumberMatchesInput,\n        complexPrefixBeforeNationalSignificantNumber: complexPrefixBeforeNationalSignificantNumber\n      }); // `onExtractedNationalNumber()` is only called when\n      // the national (significant) number actually did change.\n\n      this.hasExtractedNationalSignificantNumber = true;\n      this.onNationalSignificantNumberChange();\n    }\n  }, {\n    key: \"reExtractNationalSignificantNumber\",\n    value: function reExtractNationalSignificantNumber(state) {\n      // Attempt to extract a national prefix.\n      //\n      // Some people incorrectly input national prefix\n      // in an international phone number.\n      // For example, some people write British phone numbers as `+44(0)...`.\n      //\n      // Also, in some rare cases, it is valid for a national prefix\n      // to be a part of an international phone number.\n      // For example, mobile phone numbers in Mexico are supposed to be\n      // dialled internationally using a `1` national prefix,\n      // so the national prefix will be part of an international number.\n      //\n      // Quote from:\n      // https://www.mexperience.com/dialing-cell-phones-in-mexico/\n      //\n      // \"Dialing a Mexican cell phone from abroad\n      // When you are calling a cell phone number in Mexico from outside Mexico,\n      // it’s necessary to dial an additional “1” after Mexico’s country code\n      // (which is “52”) and before the area code.\n      // You also ignore the 045, and simply dial the area code and the\n      // cell phone’s number.\n      //\n      // If you don’t add the “1”, you’ll receive a recorded announcement\n      // asking you to redial using it.\n      //\n      // For example, if you are calling from the USA to a cell phone\n      // in Mexico City, you would dial +52 – 1 – 55 – 1234 5678.\n      // (Note that this is different to calling a land line in Mexico City\n      // from abroad, where the number dialed would be +52 – 55 – 1234 5678)\".\n      //\n      // Google's demo output:\n      // https://libphonenumber.appspot.com/phonenumberparser?number=%2b5215512345678&country=MX\n      //\n      if (this.extractAnotherNationalSignificantNumber(state.getNationalDigits(), state.nationalSignificantNumber, function (stateUpdate) {\n        return state.update(stateUpdate);\n      })) {\n        return true;\n      } // If no format matches the phone number, then it could be\n      // \"a really long IDD\" (quote from a comment in Google's library).\n      // An IDD prefix is first extracted when the user has entered at least 3 digits,\n      // and then here — every time when there's a new digit and the number\n      // couldn't be formatted.\n      // For example, in Australia the default IDD prefix is `0011`,\n      // and it could even be as long as `14880011`.\n      //\n      // Could also check `!hasReceivedThreeLeadingDigits` here\n      // to filter out the case when this check duplicates the one\n      // already performed when there're 3 leading digits,\n      // but it's not a big deal, and in most cases there\n      // will be a suitable `format` when there're 3 leading digits.\n      //\n\n      if (this.extractIddPrefix(state)) {\n        this.extractCallingCodeAndNationalSignificantNumber(state);\n        return true;\n      } // Google's AsYouType formatter supports sort of an \"autocorrection\" feature\n      // when it \"autocorrects\" numbers that have been input for a country\n      // with that country's calling code.\n      // Such \"autocorrection\" feature looks weird, but different people have been requesting it:\n      // https://github.com/catamphetamine/libphonenumber-js/issues/376\n      // https://github.com/catamphetamine/libphonenumber-js/issues/375\n      // https://github.com/catamphetamine/libphonenumber-js/issues/316\n\n      if (this.fixMissingPlus(state)) {\n        this.extractCallingCodeAndNationalSignificantNumber(state);\n        return true;\n      }\n    }\n  }, {\n    key: \"extractIddPrefix\",\n    value: function extractIddPrefix(state) {\n      // An IDD prefix can't be present in a number written with a `+`.\n      // Also, don't re-extract an IDD prefix if has already been extracted.\n      var international = state.international,\n        IDDPrefix = state.IDDPrefix,\n        digits = state.digits,\n        nationalSignificantNumber = state.nationalSignificantNumber;\n      if (international || IDDPrefix) {\n        return;\n      } // Some users input their phone number in \"out-of-country\"\n      // dialing format instead of using the leading `+`.\n      // https://github.com/catamphetamine/libphonenumber-js/issues/185\n      // Detect such numbers.\n\n      var numberWithoutIDD = stripIddPrefix(digits, this.defaultCountry, this.defaultCallingCode, this.metadata.metadata);\n      if (numberWithoutIDD !== undefined && numberWithoutIDD !== digits) {\n        // If an IDD prefix was stripped then convert the IDD-prefixed number\n        // to international number for subsequent parsing.\n        state.update({\n          IDDPrefix: digits.slice(0, digits.length - numberWithoutIDD.length)\n        });\n        this.startInternationalNumber(state, {\n          country: undefined,\n          callingCode: undefined\n        });\n        return true;\n      }\n    }\n  }, {\n    key: \"fixMissingPlus\",\n    value: function fixMissingPlus(state) {\n      if (!state.international) {\n        var _extractCountryCallin2 = extractCountryCallingCodeFromInternationalNumberWithoutPlusSign(state.digits, this.defaultCountry, this.defaultCallingCode, this.metadata.metadata),\n          newCallingCode = _extractCountryCallin2.countryCallingCode,\n          number = _extractCountryCallin2.number;\n        if (newCallingCode) {\n          state.update({\n            missingPlus: true\n          });\n          this.startInternationalNumber(state, {\n            country: state.country,\n            callingCode: newCallingCode\n          });\n          return true;\n        }\n      }\n    }\n  }, {\n    key: \"startInternationalNumber\",\n    value: function startInternationalNumber(state, _ref3) {\n      var country = _ref3.country,\n        callingCode = _ref3.callingCode;\n      state.startInternationalNumber(country, callingCode); // If a national (significant) number has been extracted before, reset it.\n\n      if (state.nationalSignificantNumber) {\n        state.resetNationalSignificantNumber();\n        this.onNationalSignificantNumberChange();\n        this.hasExtractedNationalSignificantNumber = undefined;\n      }\n    }\n  }, {\n    key: \"extractCallingCodeAndNationalSignificantNumber\",\n    value: function extractCallingCodeAndNationalSignificantNumber(state) {\n      if (this.extractCountryCallingCode(state)) {\n        // `this.extractCallingCode()` is currently called when the number\n        // couldn't be formatted during the standard procedure.\n        // Normally, the national prefix would be re-extracted\n        // for an international number if such number couldn't be formatted,\n        // but since it's already not able to be formatted,\n        // there won't be yet another retry, so also extract national prefix here.\n        this.extractNationalSignificantNumber(state.getNationalDigits(), function (stateUpdate) {\n          return state.update(stateUpdate);\n        });\n      }\n    }\n  }]);\n  return AsYouTypeParser;\n}();\n/**\r\n * Extracts formatted phone number from text (if there's any).\r\n * @param  {string} text\r\n * @return {string} [formattedPhoneNumber]\r\n */\n\nexport { AsYouTypeParser as default };\nfunction extractFormattedPhoneNumber(text) {\n  // Attempt to extract a possible number from the string passed in.\n  var startsAt = text.search(VALID_FORMATTED_PHONE_NUMBER_PART);\n  if (startsAt < 0) {\n    return;\n  } // Trim everything to the left of the phone number.\n\n  text = text.slice(startsAt); // Trim the `+`.\n\n  var hasPlus;\n  if (text[0] === '+') {\n    hasPlus = true;\n    text = text.slice('+'.length);\n  } // Trim everything to the right of the phone number.\n\n  text = text.replace(AFTER_PHONE_NUMBER_DIGITS_END_PATTERN, ''); // Re-add the previously trimmed `+`.\n\n  if (hasPlus) {\n    text = '+' + text;\n  }\n  return text;\n}\n/**\r\n * Extracts formatted phone number digits (and a `+`) from text (if there're any).\r\n * @param  {string} text\r\n * @return {any[]}\r\n */\n\nfunction _extractFormattedDigitsAndPlus(text) {\n  // Extract a formatted phone number part from text.\n  var extractedNumber = extractFormattedPhoneNumber(text) || ''; // Trim a `+`.\n\n  if (extractedNumber[0] === '+') {\n    return [extractedNumber.slice('+'.length), true];\n  }\n  return [extractedNumber];\n}\n/**\r\n * Extracts formatted phone number digits (and a `+`) from text (if there're any).\r\n * @param  {string} text\r\n * @return {any[]}\r\n */\n\nexport function extractFormattedDigitsAndPlus(text) {\n  var _extractFormattedDigi3 = _extractFormattedDigitsAndPlus(text),\n    _extractFormattedDigi4 = _slicedToArray(_extractFormattedDigi3, 2),\n    formattedDigits = _extractFormattedDigi4[0],\n    hasPlus = _extractFormattedDigi4[1]; // If the extracted phone number part\n  // can possibly be a part of some valid phone number\n  // then parse phone number characters from a formatted phone number.\n\n  if (!VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART_PATTERN.test(formattedDigits)) {\n    formattedDigits = '';\n  }\n  return [formattedDigits, hasPlus];\n}", "map": {"version": 3, "names": ["_extractCountryCallingCode", "extractCountryCallingCodeFromInternationalNumberWithoutPlusSign", "extractNationalNumberFromPossiblyIncompleteNumber", "stripIddPrefix", "parseDigits", "VALID_DIGITS", "VALID_PUNCTUATION", "PLUS_CHARS", "VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART", "VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART_PATTERN", "RegExp", "VALID_FORMATTED_PHONE_NUMBER_PART", "AFTER_PHONE_NUMBER_DIGITS_END_PATTERN", "COMPLEX_NATIONAL_PREFIX", "AsYouType<PERSON><PERSON><PERSON>", "_ref", "defaultCountry", "defaultCallingCode", "metadata", "onNationalSignificantNumberChange", "_classCallCheck", "input", "text", "state", "_extractFormattedDigi", "extractFormattedDigitsAndPlus", "_extractFormattedDigi2", "_slicedToArray", "formattedDigits", "hasPlus", "digits", "justLeadingPlus", "startInternationalNumber", "inputDigits", "nextDigits", "hasReceivedThreeLeadingDigits", "length", "appendDigits", "extractIddPrefix", "isWaitingForCountryCallingCode", "extractCountryCallingCode", "appendNationalSignificantNumberDigits", "international", "hasExtractedNationalSignificantNumber", "extractNationalSignificantNumber", "getNationalDigits", "stateUpdate", "update", "_ref2", "callingCode", "_extractCountryCallin", "getDigitsWithoutInternationalPrefix", "countryCallingCode", "number", "setCallingCode", "nationalSignificantNumber", "reset", "numberingPlan", "hasSelectedNumberingPlan", "nationalPrefixForParsing", "_nationalPrefixForParsing", "couldPossiblyExtractAnotherNationalSignificantNumber", "test", "undefined", "nationalDigits", "setState", "_extractNationalNumbe", "nationalPrefix", "nationalNumber", "carrierCode", "onExtractedNationalNumber", "extractAnotherNationalSignificantNumber", "prevNationalSignificantNumber", "_extractNationalNumbe2", "complexPrefixBeforeNationalSignificantNumber", "nationalSignificantNumberMatchesInput", "nationalSignificantNumberIndex", "lastIndexOf", "prefixBeforeNationalNumber", "slice", "reExtractNationalSignificantNumber", "extractCallingCodeAndNationalSignificantNumber", "fixMissingPlus", "IDDPrefix", "numberWithoutIDD", "country", "_extractCountryCallin2", "newCallingCode", "missingPlus", "_ref3", "resetNationalSignificantNumber", "extractFormattedPhoneNumber", "startsAt", "search", "replace", "_extractFormattedDigitsAndPlus", "extractedNumber", "_extractFormattedDigi3", "_extractFormattedDigi4"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\AsYouTypeParser.js"], "sourcesContent": ["import extractCountryCallingCode from './helpers/extractCountryCallingCode.js'\r\nimport extractCountryCallingCodeFromInternationalNumberWithoutPlusSign from './helpers/extractCountryCallingCodeFromInternationalNumberWithoutPlusSign.js'\r\nimport extractNationalNumberFromPossiblyIncompleteNumber from './helpers/extractNationalNumberFromPossiblyIncompleteNumber.js'\r\nimport stripIddPrefix from './helpers/stripIddPrefix.js'\r\nimport parseDigits from './helpers/parseDigits.js'\r\n\r\nimport {\r\n\tVALID_DIGITS,\r\n\tVALID_PUNCTUATION,\r\n\tPLUS_CHARS\r\n} from './constants.js'\r\n\r\nconst VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART =\r\n\t'[' +\r\n\t\tVALID_PUNCTUATION +\r\n\t\tVALID_DIGITS +\r\n\t']+'\r\n\r\nconst VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART_PATTERN = new RegExp('^' + VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART + '$', 'i')\r\n\r\nconst VALID_FORMATTED_PHONE_NUMBER_PART =\r\n\t'(?:' +\r\n\t\t'[' + PLUS_CHARS + ']' +\r\n\t\t'[' +\r\n\t\t\tVALID_PUNCTUATION +\r\n\t\t\tVALID_DIGITS +\r\n\t\t']*' +\r\n\t\t'|' +\r\n\t\t'[' +\r\n\t\t\tVALID_PUNCTUATION +\r\n\t\t\tVALID_DIGITS +\r\n\t\t']+' +\r\n\t')'\r\n\r\nconst AFTER_PHONE_NUMBER_DIGITS_END_PATTERN = new RegExp(\r\n\t'[^' +\r\n\t\tVALID_PUNCTUATION +\r\n\t\tVALID_DIGITS +\r\n\t']+' +\r\n\t'.*' +\r\n\t'$'\r\n)\r\n\r\n// Tests whether `national_prefix_for_parsing` could match\r\n// different national prefixes.\r\n// Matches anything that's not a digit or a square bracket.\r\nconst COMPLEX_NATIONAL_PREFIX = /[^\\d\\[\\]]/\r\n\r\nexport default class AsYouTypeParser {\r\n\tconstructor({\r\n\t\tdefaultCountry,\r\n\t\tdefaultCallingCode,\r\n\t\tmetadata,\r\n\t\tonNationalSignificantNumberChange\r\n\t}) {\r\n\t\tthis.defaultCountry = defaultCountry\r\n\t\tthis.defaultCallingCode = defaultCallingCode\r\n\t\tthis.metadata = metadata\r\n\t\tthis.onNationalSignificantNumberChange = onNationalSignificantNumberChange\r\n\t}\r\n\r\n\tinput(text, state) {\r\n\t\tconst [formattedDigits, hasPlus] = extractFormattedDigitsAndPlus(text)\r\n\t\tconst digits = parseDigits(formattedDigits)\r\n\t\t// Checks for a special case: just a leading `+` has been entered.\r\n\t\tlet justLeadingPlus\r\n\t\tif (hasPlus) {\r\n\t\t\tif (!state.digits) {\r\n\t\t\t\tstate.startInternationalNumber()\r\n\t\t\t\tif (!digits) {\r\n\t\t\t\t\tjustLeadingPlus = true\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tif (digits) {\r\n\t\t\tthis.inputDigits(digits, state)\r\n\t\t}\r\n\t\treturn {\r\n\t\t\tdigits,\r\n\t\t\tjustLeadingPlus\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Inputs \"next\" phone number digits.\r\n\t * @param  {string} digits\r\n\t * @return {string} [formattedNumber] Formatted national phone number (if it can be formatted at this stage). Returning `undefined` means \"don't format the national phone number at this stage\".\r\n\t */\r\n\tinputDigits(nextDigits, state) {\r\n\t\tconst { digits } = state\r\n\t\tconst hasReceivedThreeLeadingDigits = digits.length < 3 && digits.length + nextDigits.length >= 3\r\n\r\n\t\t// Append phone number digits.\r\n\t\tstate.appendDigits(nextDigits)\r\n\r\n\t\t// Attempt to extract IDD prefix:\r\n\t\t// Some users input their phone number in international format,\r\n\t\t// but in an \"out-of-country\" dialing format instead of using the leading `+`.\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/185\r\n\t\t// Detect such numbers as soon as there're at least 3 digits.\r\n\t\t// Google's library attempts to extract IDD prefix at 3 digits,\r\n\t\t// so this library just copies that behavior.\r\n\t\t// I guess that's because the most commot IDD prefixes are\r\n\t\t// `00` (Europe) and `011` (US).\r\n\t\t// There exist really long IDD prefixes too:\r\n\t\t// for example, in Australia the default IDD prefix is `0011`,\r\n\t\t// and it could even be as long as `14880011`.\r\n\t\t// An IDD prefix is extracted here, and then every time when\r\n\t\t// there's a new digit and the number couldn't be formatted.\r\n\t\tif (hasReceivedThreeLeadingDigits) {\r\n\t\t\tthis.extractIddPrefix(state)\r\n\t\t}\r\n\r\n\t\tif (this.isWaitingForCountryCallingCode(state)) {\r\n\t\t\tif (!this.extractCountryCallingCode(state)) {\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tstate.appendNationalSignificantNumberDigits(nextDigits)\r\n\t\t}\r\n\r\n\t\t// If a phone number is being input in international format,\r\n\t\t// then it's not valid for it to have a national prefix.\r\n\t\t// Still, some people incorrectly input such numbers with a national prefix.\r\n\t\t// In such cases, only attempt to strip a national prefix if the number becomes too long.\r\n\t\t// (but that is done later, not here)\r\n\t\tif (!state.international) {\r\n\t\t\tif (!this.hasExtractedNationalSignificantNumber) {\r\n\t\t\t\tthis.extractNationalSignificantNumber(\r\n\t\t\t\t\tstate.getNationalDigits(),\r\n\t\t\t\t\t(stateUpdate) => state.update(stateUpdate)\r\n\t\t\t\t)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tisWaitingForCountryCallingCode({ international, callingCode }) {\r\n\t\treturn international && !callingCode\r\n\t}\r\n\r\n\t// Extracts a country calling code from a number\r\n\t// being entered in internatonal format.\r\n\textractCountryCallingCode(state) {\r\n\t\tconst { countryCallingCode, number } = extractCountryCallingCode(\r\n\t\t\t'+' + state.getDigitsWithoutInternationalPrefix(),\r\n\t\t\tthis.defaultCountry,\r\n\t\t\tthis.defaultCallingCode,\r\n\t\t\tthis.metadata.metadata\r\n\t\t)\r\n\t\tif (countryCallingCode) {\r\n\t\t\tstate.setCallingCode(countryCallingCode)\r\n\t\t\tstate.update({\r\n\t\t\t\tnationalSignificantNumber: number\r\n\t\t\t})\r\n\t\t\treturn true\r\n\t\t}\r\n\t}\r\n\r\n\treset(numberingPlan) {\r\n\t\tif (numberingPlan) {\r\n\t\t\tthis.hasSelectedNumberingPlan = true\r\n\t\t\tconst nationalPrefixForParsing = numberingPlan._nationalPrefixForParsing()\r\n\t\t\tthis.couldPossiblyExtractAnotherNationalSignificantNumber = nationalPrefixForParsing && COMPLEX_NATIONAL_PREFIX.test(nationalPrefixForParsing)\r\n\t\t} else {\r\n\t\t\tthis.hasSelectedNumberingPlan = undefined\r\n\t\t\tthis.couldPossiblyExtractAnotherNationalSignificantNumber = undefined\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * Extracts a national (significant) number from user input.\r\n\t * Google's library is different in that it only applies `national_prefix_for_parsing`\r\n\t * and doesn't apply `national_prefix_transform_rule` after that.\r\n\t * https://github.com/google/libphonenumber/blob/a3d70b0487875475e6ad659af404943211d26456/java/libphonenumber/src/com/google/i18n/phonenumbers/AsYouTypeFormatter.java#L539\r\n\t * @return {boolean} [extracted]\r\n\t */\r\n\textractNationalSignificantNumber(nationalDigits, setState) {\r\n\t\tif (!this.hasSelectedNumberingPlan) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\tconst {\r\n\t\t\tnationalPrefix,\r\n\t\t\tnationalNumber,\r\n\t\t\tcarrierCode\r\n\t\t} = extractNationalNumberFromPossiblyIncompleteNumber(\r\n\t\t\tnationalDigits,\r\n\t\t\tthis.metadata\r\n\t\t)\r\n\t\tif (nationalNumber === nationalDigits) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\tthis.onExtractedNationalNumber(\r\n\t\t\tnationalPrefix,\r\n\t\t\tcarrierCode,\r\n\t\t\tnationalNumber,\r\n\t\t\tnationalDigits,\r\n\t\t\tsetState\r\n\t\t)\r\n\t\treturn true\r\n\t}\r\n\r\n\t/**\r\n\t * In Google's code this function is called \"attempt to extract longer NDD\".\r\n\t * \"Some national prefixes are a substring of others\", they say.\r\n\t * @return {boolean} [result] — Returns `true` if extracting a national prefix produced different results from what they were.\r\n\t */\r\n\textractAnotherNationalSignificantNumber(nationalDigits, prevNationalSignificantNumber, setState) {\r\n\t\tif (!this.hasExtractedNationalSignificantNumber) {\r\n\t\t\treturn this.extractNationalSignificantNumber(nationalDigits, setState)\r\n\t\t}\r\n\t\tif (!this.couldPossiblyExtractAnotherNationalSignificantNumber) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\tconst {\r\n\t\t\tnationalPrefix,\r\n\t\t\tnationalNumber,\r\n\t\t\tcarrierCode\r\n\t\t} = extractNationalNumberFromPossiblyIncompleteNumber(\r\n\t\t\tnationalDigits,\r\n\t\t\tthis.metadata\r\n\t\t)\r\n\t\t// If a national prefix has been extracted previously,\r\n\t\t// then it's always extracted as additional digits are added.\r\n\t\t// That's assuming `extractNationalNumberFromPossiblyIncompleteNumber()`\r\n\t\t// doesn't do anything different from what it currently does.\r\n\t\t// So, just in case, here's this check, though it doesn't occur.\r\n\t\t/* istanbul ignore if */\r\n\t\tif (nationalNumber === prevNationalSignificantNumber) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\tthis.onExtractedNationalNumber(\r\n\t\t\tnationalPrefix,\r\n\t\t\tcarrierCode,\r\n\t\t\tnationalNumber,\r\n\t\t\tnationalDigits,\r\n\t\t\tsetState\r\n\t\t)\r\n\t\treturn true\r\n\t}\r\n\r\n\tonExtractedNationalNumber(\r\n\t\tnationalPrefix,\r\n\t\tcarrierCode,\r\n\t\tnationalSignificantNumber,\r\n\t\tnationalDigits,\r\n\t\tsetState\r\n\t) {\r\n\t\tlet complexPrefixBeforeNationalSignificantNumber\r\n\t\tlet nationalSignificantNumberMatchesInput\r\n\t\t// This check also works with empty `this.nationalSignificantNumber`.\r\n\t\tconst nationalSignificantNumberIndex = nationalDigits.lastIndexOf(nationalSignificantNumber)\r\n\t\t// If the extracted national (significant) number is the\r\n\t\t// last substring of the `digits`, then it means that it hasn't been altered:\r\n\t\t// no digits have been removed from the national (significant) number\r\n\t\t// while applying `national_prefix_transform_rule`.\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/blob/master/METADATA.md#national_prefix_for_parsing--national_prefix_transform_rule\r\n\t\tif (nationalSignificantNumberIndex >= 0 &&\r\n\t\t\tnationalSignificantNumberIndex === nationalDigits.length - nationalSignificantNumber.length) {\r\n\t\t\tnationalSignificantNumberMatchesInput = true\r\n\t\t\t// If a prefix of a national (significant) number is not as simple\r\n\t\t\t// as just a basic national prefix, then such prefix is stored in\r\n\t\t\t// `this.complexPrefixBeforeNationalSignificantNumber` property and will be\r\n\t\t\t// prepended \"as is\" to the national (significant) number to produce\r\n\t\t\t// a formatted result.\r\n\t\t\tconst prefixBeforeNationalNumber = nationalDigits.slice(0, nationalSignificantNumberIndex)\r\n\t\t\t// `prefixBeforeNationalNumber` is always non-empty,\r\n\t\t\t// because `onExtractedNationalNumber()` isn't called\r\n\t\t\t// when a national (significant) number hasn't been actually \"extracted\":\r\n\t\t\t// when a national (significant) number is equal to the national part of `digits`,\r\n\t\t\t// then `onExtractedNationalNumber()` doesn't get called.\r\n\t\t\tif (prefixBeforeNationalNumber !== nationalPrefix) {\r\n\t\t\t\tcomplexPrefixBeforeNationalSignificantNumber = prefixBeforeNationalNumber\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetState({\r\n\t\t\tnationalPrefix,\r\n\t\t\tcarrierCode,\r\n\t\t\tnationalSignificantNumber,\r\n\t\t\tnationalSignificantNumberMatchesInput,\r\n\t\t\tcomplexPrefixBeforeNationalSignificantNumber\r\n\t\t})\r\n\t\t// `onExtractedNationalNumber()` is only called when\r\n\t\t// the national (significant) number actually did change.\r\n\t\tthis.hasExtractedNationalSignificantNumber = true\r\n\t\tthis.onNationalSignificantNumberChange()\r\n\t}\r\n\r\n\treExtractNationalSignificantNumber(state) {\r\n\t\t// Attempt to extract a national prefix.\r\n\t\t//\r\n\t\t// Some people incorrectly input national prefix\r\n\t\t// in an international phone number.\r\n\t\t// For example, some people write British phone numbers as `+44(0)...`.\r\n\t\t//\r\n\t\t// Also, in some rare cases, it is valid for a national prefix\r\n\t\t// to be a part of an international phone number.\r\n\t\t// For example, mobile phone numbers in Mexico are supposed to be\r\n\t\t// dialled internationally using a `1` national prefix,\r\n\t\t// so the national prefix will be part of an international number.\r\n\t\t//\r\n\t\t// Quote from:\r\n\t\t// https://www.mexperience.com/dialing-cell-phones-in-mexico/\r\n\t\t//\r\n\t\t// \"Dialing a Mexican cell phone from abroad\r\n\t\t// When you are calling a cell phone number in Mexico from outside Mexico,\r\n\t\t// it’s necessary to dial an additional “1” after Mexico’s country code\r\n\t\t// (which is “52”) and before the area code.\r\n\t\t// You also ignore the 045, and simply dial the area code and the\r\n\t\t// cell phone’s number.\r\n\t\t//\r\n\t\t// If you don’t add the “1”, you’ll receive a recorded announcement\r\n\t\t// asking you to redial using it.\r\n\t\t//\r\n\t\t// For example, if you are calling from the USA to a cell phone\r\n\t\t// in Mexico City, you would dial +52 – 1 – 55 – 1234 5678.\r\n\t\t// (Note that this is different to calling a land line in Mexico City\r\n\t\t// from abroad, where the number dialed would be +52 – 55 – 1234 5678)\".\r\n\t\t//\r\n\t\t// Google's demo output:\r\n\t\t// https://libphonenumber.appspot.com/phonenumberparser?number=%2b5215512345678&country=MX\r\n\t\t//\r\n\t\tif (this.extractAnotherNationalSignificantNumber(\r\n\t\t\tstate.getNationalDigits(),\r\n\t\t\tstate.nationalSignificantNumber,\r\n\t\t\t(stateUpdate) => state.update(stateUpdate)\r\n\t\t)) {\r\n\t\t\treturn true\r\n\t\t}\r\n\t\t// If no format matches the phone number, then it could be\r\n\t\t// \"a really long IDD\" (quote from a comment in Google's library).\r\n\t\t// An IDD prefix is first extracted when the user has entered at least 3 digits,\r\n\t\t// and then here — every time when there's a new digit and the number\r\n\t\t// couldn't be formatted.\r\n\t\t// For example, in Australia the default IDD prefix is `0011`,\r\n\t\t// and it could even be as long as `14880011`.\r\n\t\t//\r\n\t\t// Could also check `!hasReceivedThreeLeadingDigits` here\r\n\t\t// to filter out the case when this check duplicates the one\r\n\t\t// already performed when there're 3 leading digits,\r\n\t\t// but it's not a big deal, and in most cases there\r\n\t\t// will be a suitable `format` when there're 3 leading digits.\r\n\t\t//\r\n\t\tif (this.extractIddPrefix(state)) {\r\n\t\t\tthis.extractCallingCodeAndNationalSignificantNumber(state)\r\n\t\t\treturn true\r\n\t\t}\r\n\t\t// Google's AsYouType formatter supports sort of an \"autocorrection\" feature\r\n\t\t// when it \"autocorrects\" numbers that have been input for a country\r\n\t\t// with that country's calling code.\r\n\t\t// Such \"autocorrection\" feature looks weird, but different people have been requesting it:\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/376\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/375\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/316\r\n\t\tif (this.fixMissingPlus(state)) {\r\n\t\t\tthis.extractCallingCodeAndNationalSignificantNumber(state)\r\n\t\t\treturn true\r\n\t\t}\r\n\t}\r\n\r\n\textractIddPrefix(state) {\r\n\t\t// An IDD prefix can't be present in a number written with a `+`.\r\n\t\t// Also, don't re-extract an IDD prefix if has already been extracted.\r\n\t\tconst {\r\n\t\t\tinternational,\r\n\t\t\tIDDPrefix,\r\n\t\t\tdigits,\r\n\t\t\tnationalSignificantNumber\r\n\t\t} = state\r\n\t\tif (international || IDDPrefix) {\r\n\t\t\treturn\r\n\t\t}\r\n\t\t// Some users input their phone number in \"out-of-country\"\r\n\t\t// dialing format instead of using the leading `+`.\r\n\t\t// https://github.com/catamphetamine/libphonenumber-js/issues/185\r\n\t\t// Detect such numbers.\r\n\t\tconst numberWithoutIDD = stripIddPrefix(\r\n\t\t\tdigits,\r\n\t\t\tthis.defaultCountry,\r\n\t\t\tthis.defaultCallingCode,\r\n\t\t\tthis.metadata.metadata\r\n\t\t)\r\n\t\tif (numberWithoutIDD !== undefined && numberWithoutIDD !== digits) {\r\n\t\t\t// If an IDD prefix was stripped then convert the IDD-prefixed number\r\n\t\t\t// to international number for subsequent parsing.\r\n\t\t\tstate.update({\r\n\t\t\t\tIDDPrefix: digits.slice(0, digits.length - numberWithoutIDD.length)\r\n\t\t\t})\r\n\t\t\tthis.startInternationalNumber(state, {\r\n\t\t\t\tcountry: undefined,\r\n\t\t\t\tcallingCode: undefined\r\n\t\t\t})\r\n\t\t\treturn true\r\n\t\t}\r\n\t}\r\n\r\n\tfixMissingPlus(state) {\r\n\t\tif (!state.international) {\r\n\t\t\tconst {\r\n\t\t\t\tcountryCallingCode: newCallingCode,\r\n\t\t\t\tnumber\r\n\t\t\t} = extractCountryCallingCodeFromInternationalNumberWithoutPlusSign(\r\n\t\t\t\tstate.digits,\r\n\t\t\t\tthis.defaultCountry,\r\n\t\t\t\tthis.defaultCallingCode,\r\n\t\t\t\tthis.metadata.metadata\r\n\t\t\t)\r\n\t\t\tif (newCallingCode) {\r\n\t\t\t\tstate.update({\r\n\t\t\t\t\tmissingPlus: true\r\n\t\t\t\t})\r\n\t\t\t\tthis.startInternationalNumber(state, {\r\n\t\t\t\t\tcountry: state.country,\r\n\t\t\t\t\tcallingCode: newCallingCode\r\n\t\t\t\t})\r\n\t\t\t\treturn true\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tstartInternationalNumber(state, { country, callingCode }) {\r\n\t\tstate.startInternationalNumber(country, callingCode)\r\n\t\t// If a national (significant) number has been extracted before, reset it.\r\n\t\tif (state.nationalSignificantNumber) {\r\n\t\t\tstate.resetNationalSignificantNumber()\r\n\t\t\tthis.onNationalSignificantNumberChange()\r\n\t\t\tthis.hasExtractedNationalSignificantNumber = undefined\r\n\t\t}\r\n\t}\r\n\r\n\textractCallingCodeAndNationalSignificantNumber(state) {\r\n\t\tif (this.extractCountryCallingCode(state)) {\r\n\t\t\t// `this.extractCallingCode()` is currently called when the number\r\n\t\t\t// couldn't be formatted during the standard procedure.\r\n\t\t\t// Normally, the national prefix would be re-extracted\r\n\t\t\t// for an international number if such number couldn't be formatted,\r\n\t\t\t// but since it's already not able to be formatted,\r\n\t\t\t// there won't be yet another retry, so also extract national prefix here.\r\n\t\t\tthis.extractNationalSignificantNumber(\r\n\t\t\t\tstate.getNationalDigits(),\r\n\t\t\t\t(stateUpdate) => state.update(stateUpdate)\r\n\t\t\t)\r\n\t\t}\r\n\t}\r\n}\r\n\r\n/**\r\n * Extracts formatted phone number from text (if there's any).\r\n * @param  {string} text\r\n * @return {string} [formattedPhoneNumber]\r\n */\r\nfunction extractFormattedPhoneNumber(text) {\r\n\t// Attempt to extract a possible number from the string passed in.\r\n\tconst startsAt = text.search(VALID_FORMATTED_PHONE_NUMBER_PART)\r\n\tif (startsAt < 0) {\r\n\t\treturn\r\n\t}\r\n\t// Trim everything to the left of the phone number.\r\n\ttext = text.slice(startsAt)\r\n\t// Trim the `+`.\r\n\tlet hasPlus\r\n\tif (text[0] === '+') {\r\n\t\thasPlus = true\r\n\t\ttext = text.slice('+'.length)\r\n\t}\r\n\t// Trim everything to the right of the phone number.\r\n\ttext = text.replace(AFTER_PHONE_NUMBER_DIGITS_END_PATTERN, '')\r\n\t// Re-add the previously trimmed `+`.\r\n\tif (hasPlus) {\r\n\t\ttext = '+' + text\r\n\t}\r\n\treturn text\r\n}\r\n\r\n/**\r\n * Extracts formatted phone number digits (and a `+`) from text (if there're any).\r\n * @param  {string} text\r\n * @return {any[]}\r\n */\r\nfunction _extractFormattedDigitsAndPlus(text) {\r\n\t// Extract a formatted phone number part from text.\r\n\tconst extractedNumber = extractFormattedPhoneNumber(text) || ''\r\n\t// Trim a `+`.\r\n\tif (extractedNumber[0] === '+') {\r\n\t\treturn [extractedNumber.slice('+'.length), true]\r\n\t}\r\n\treturn [extractedNumber]\r\n}\r\n\r\n/**\r\n * Extracts formatted phone number digits (and a `+`) from text (if there're any).\r\n * @param  {string} text\r\n * @return {any[]}\r\n */\r\nexport function extractFormattedDigitsAndPlus(text) {\r\n\tlet [formattedDigits, hasPlus] = _extractFormattedDigitsAndPlus(text)\r\n\t// If the extracted phone number part\r\n\t// can possibly be a part of some valid phone number\r\n\t// then parse phone number characters from a formatted phone number.\r\n\tif (!VALID_FORMATTED_PHONE_NUMBER_DIGITS_PART_PATTERN.test(formattedDigits)) {\r\n\t\tformattedDigits = ''\r\n\t}\r\n\treturn [formattedDigits, hasPlus]\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,0BAAP,MAAsC,wCAAtC;AACA,OAAOC,+DAAP,MAA4E,8EAA5E;AACA,OAAOC,iDAAP,MAA8D,gEAA9D;AACA,OAAOC,cAAP,MAA2B,6BAA3B;AACA,OAAOC,WAAP,MAAwB,0BAAxB;AAEA,SACCC,YADD,EAECC,iBAFD,EAGCC,UAHD,QAIO,gBAJP;AAMA,IAAMC,wCAAwC,GAC7C,MACCF,iBADD,GAECD,YAFD,GAGA,IAJD;AAMA,IAAMI,gDAAgD,GAAG,IAAIC,MAAJ,CAAW,MAAMF,wCAAN,GAAiD,GAA5D,EAAiE,GAAjE,CAAzD;AAEA,IAAMG,iCAAiC,GACtC,QACC,GADD,GACOJ,UADP,GACoB,GADpB,GAEC,GAFD,GAGED,iBAHF,GAIED,YAJF,GAKC,IALD,GAMC,GAND,GAOC,GAPD,GAQEC,iBARF,GASED,YATF,GAUC,IAVD,GAWA,GAZD;AAcA,IAAMO,qCAAqC,GAAG,IAAIF,MAAJ,CAC7C,OACCJ,iBADD,GAECD,YAFD,GAGA,IAHA,GAIA,IAJA,GAKA,GAN6C,CAA9C,C,CASA;AACA;AACA;;AACA,IAAMQ,uBAAuB,GAAG,WAAhC;IAEqBC,e;EACpB,SAAAA,gBAAAC,IAAA,EAKG;IAAA,IAJFC,cAIE,GAAAD,IAAA,CAJFC,cAIE;MAHFC,kBAGE,GAAAF,IAAA,CAHFE,kBAGE;MAFFC,QAEE,GAAAH,IAAA,CAFFG,QAEE;MADFC,iCACE,GAAAJ,IAAA,CADFI,iCACE;IAAAC,eAAA,OAAAN,eAAA;IACF,KAAKE,cAAL,GAAsBA,cAAtB;IACA,KAAKC,kBAAL,GAA0BA,kBAA1B;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IACA,KAAKC,iCAAL,GAAyCA,iCAAzC;EACA;;;WAED,SAAAE,MAAMC,IAAN,EAAYC,KAAZ,EAAmB;MAClB,IAAAC,qBAAA,GAAmCC,6BAA6B,CAACH,IAAD,CAAhE;QAAAI,sBAAA,GAAAC,cAAA,CAAAH,qBAAA;QAAOI,eAAP,GAAAF,sBAAA;QAAwBG,OAAxB,GAAAH,sBAAA;MACA,IAAMI,MAAM,GAAG1B,WAAW,CAACwB,eAAD,CAA1B,CAFkB,CAGlB;;MACA,IAAIG,eAAJ;MACA,IAAIF,OAAJ,EAAa;QACZ,IAAI,CAACN,KAAK,CAACO,MAAX,EAAmB;UAClBP,KAAK,CAACS,wBAAN;UACA,IAAI,CAACF,MAAL,EAAa;YACZC,eAAe,GAAG,IAAlB;UACA;QACD;MACD;MACD,IAAID,MAAJ,EAAY;QACX,KAAKG,WAAL,CAAiBH,MAAjB,EAAyBP,KAAzB;MACA;MACD,OAAO;QACNO,MAAM,EAANA,MADM;QAENC,eAAe,EAAfA;MAFM,CAAP;IAIA;IAED;AACD;AACA;AACA;AACA;;;WACC,SAAAE,YAAYC,UAAZ,EAAwBX,KAAxB,EAA+B;MAC9B,IAAQO,MAAR,GAAmBP,KAAnB,CAAQO,MAAR;MACA,IAAMK,6BAA6B,GAAGL,MAAM,CAACM,MAAP,GAAgB,CAAhB,IAAqBN,MAAM,CAACM,MAAP,GAAgBF,UAAU,CAACE,MAA3B,IAAqC,CAAhG,CAF8B,CAI9B;;MACAb,KAAK,CAACc,YAAN,CAAmBH,UAAnB,EAL8B,CAO9B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MACA,IAAIC,6BAAJ,EAAmC;QAClC,KAAKG,gBAAL,CAAsBf,KAAtB;MACA;MAED,IAAI,KAAKgB,8BAAL,CAAoChB,KAApC,CAAJ,EAAgD;QAC/C,IAAI,CAAC,KAAKiB,yBAAL,CAA+BjB,KAA/B,CAAL,EAA4C;UAC3C;QACA;MACD,CAJD,MAIO;QACNA,KAAK,CAACkB,qCAAN,CAA4CP,UAA5C;MACA,CA/B6B,CAiC9B;MACA;MACA;MACA;MACA;;MACA,IAAI,CAACX,KAAK,CAACmB,aAAX,EAA0B;QACzB,IAAI,CAAC,KAAKC,qCAAV,EAAiD;UAChD,KAAKC,gCAAL,CACCrB,KAAK,CAACsB,iBAAN,EADD,EAEC,UAACC,WAAD;YAAA,OAAiBvB,KAAK,CAACwB,MAAN,CAAaD,WAAb,CAAjB;UAAA,CAFD;QAIA;MACD;IACD;;;WAED,SAAAP,+BAAAS,KAAA,EAA+D;MAAA,IAA9BN,aAA8B,GAAAM,KAAA,CAA9BN,aAA8B;QAAfO,WAAe,GAAAD,KAAA,CAAfC,WAAe;MAC9D,OAAOP,aAAa,IAAI,CAACO,WAAzB;IACA,C,CAED;IACA;;;WACA,SAAAT,0BAA0BjB,KAA1B,EAAiC;MAChC,IAAA2B,qBAAA,GAAuClD,0BAAyB,CAC/D,MAAMuB,KAAK,CAAC4B,mCAAN,EADyD,EAE/D,KAAKnC,cAF0D,EAG/D,KAAKC,kBAH0D,EAI/D,KAAKC,QAAL,CAAcA,QAJiD,CAAhE;QAAQkC,kBAAR,GAAAF,qBAAA,CAAQE,kBAAR;QAA4BC,MAA5B,GAAAH,qBAAA,CAA4BG,MAA5B;MAMA,IAAID,kBAAJ,EAAwB;QACvB7B,KAAK,CAAC+B,cAAN,CAAqBF,kBAArB;QACA7B,KAAK,CAACwB,MAAN,CAAa;UACZQ,yBAAyB,EAAEF;QADf,CAAb;QAGA,OAAO,IAAP;MACA;IACD;;;WAED,SAAAG,MAAMC,aAAN,EAAqB;MACpB,IAAIA,aAAJ,EAAmB;QAClB,KAAKC,wBAAL,GAAgC,IAAhC;QACA,IAAMC,wBAAwB,GAAGF,aAAa,CAACG,yBAAd,EAAjC;QACA,KAAKC,oDAAL,GAA4DF,wBAAwB,IAAI9C,uBAAuB,CAACiD,IAAxB,CAA6BH,wBAA7B,CAAxF;MACA,CAJD,MAIO;QACN,KAAKD,wBAAL,GAAgCK,SAAhC;QACA,KAAKF,oDAAL,GAA4DE,SAA5D;MACA;IACD;IAED;AACD;AACA;AACA;AACA;AACA;AACA;;;WACC,SAAAnB,iCAAiCoB,cAAjC,EAAiDC,QAAjD,EAA2D;MAC1D,IAAI,CAAC,KAAKP,wBAAV,EAAoC;QACnC;MACA;MACD,IAAAQ,qBAAA,GAIIhE,iDAAiD,CACpD8D,cADoD,EAEpD,KAAK9C,QAF+C,CAJrD;QACCiD,cADD,GAAAD,qBAAA,CACCC,cADD;QAECC,cAFD,GAAAF,qBAAA,CAECE,cAFD;QAGCC,WAHD,GAAAH,qBAAA,CAGCG,WAHD;MAQA,IAAID,cAAc,KAAKJ,cAAvB,EAAuC;QACtC;MACA;MACD,KAAKM,yBAAL,CACCH,cADD,EAECE,WAFD,EAGCD,cAHD,EAICJ,cAJD,EAKCC,QALD;MAOA,OAAO,IAAP;IACA;IAED;AACD;AACA;AACA;AACA;;;WACC,SAAAM,wCAAwCP,cAAxC,EAAwDQ,6BAAxD,EAAuFP,QAAvF,EAAiG;MAChG,IAAI,CAAC,KAAKtB,qCAAV,EAAiD;QAChD,OAAO,KAAKC,gCAAL,CAAsCoB,cAAtC,EAAsDC,QAAtD,CAAP;MACA;MACD,IAAI,CAAC,KAAKJ,oDAAV,EAAgE;QAC/D;MACA;MACD,IAAAY,sBAAA,GAIIvE,iDAAiD,CACpD8D,cADoD,EAEpD,KAAK9C,QAF+C,CAJrD;QACCiD,cADD,GAAAM,sBAAA,CACCN,cADD;QAECC,cAFD,GAAAK,sBAAA,CAECL,cAFD;QAGCC,WAHD,GAAAI,sBAAA,CAGCJ,WAHD,CAPgG,CAehG;MACA;MACA;MACA;MACA;;MACA;;MACA,IAAID,cAAc,KAAKI,6BAAvB,EAAsD;QACrD;MACA;MACD,KAAKF,yBAAL,CACCH,cADD,EAECE,WAFD,EAGCD,cAHD,EAICJ,cAJD,EAKCC,QALD;MAOA,OAAO,IAAP;IACA;;;WAED,SAAAK,0BACCH,cADD,EAECE,WAFD,EAGCd,yBAHD,EAICS,cAJD,EAKCC,QALD,EAME;MACD,IAAIS,4CAAJ;MACA,IAAIC,qCAAJ,CAFC,CAGD;;MACA,IAAMC,8BAA8B,GAAGZ,cAAc,CAACa,WAAf,CAA2BtB,yBAA3B,CAAvC,CAJC,CAKD;MACA;MACA;MACA;MACA;;MACA,IAAIqB,8BAA8B,IAAI,CAAlC,IACHA,8BAA8B,KAAKZ,cAAc,CAAC5B,MAAf,GAAwBmB,yBAAyB,CAACnB,MADtF,EAC8F;QAC7FuC,qCAAqC,GAAG,IAAxC,CAD6F,CAE7F;QACA;QACA;QACA;QACA;;QACA,IAAMG,0BAA0B,GAAGd,cAAc,CAACe,KAAf,CAAqB,CAArB,EAAwBH,8BAAxB,CAAnC,CAP6F,CAQ7F;QACA;QACA;QACA;QACA;;QACA,IAAIE,0BAA0B,KAAKX,cAAnC,EAAmD;UAClDO,4CAA4C,GAAGI,0BAA/C;QACA;MACD;MACDb,QAAQ,CAAC;QACRE,cAAc,EAAdA,cADQ;QAERE,WAAW,EAAXA,WAFQ;QAGRd,yBAAyB,EAAzBA,yBAHQ;QAIRoB,qCAAqC,EAArCA,qCAJQ;QAKRD,4CAA4C,EAA5CA;MALQ,CAAD,CAAR,CA5BC,CAmCD;MACA;;MACA,KAAK/B,qCAAL,GAA6C,IAA7C;MACA,KAAKxB,iCAAL;IACA;;;WAED,SAAA6D,mCAAmCzD,KAAnC,EAA0C;MACzC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,KAAKgD,uCAAL,CACHhD,KAAK,CAACsB,iBAAN,EADG,EAEHtB,KAAK,CAACgC,yBAFH,EAGH,UAACT,WAAD;QAAA,OAAiBvB,KAAK,CAACwB,MAAN,CAAaD,WAAb,CAAjB;MAAA,CAHG,CAAJ,EAIG;QACF,OAAO,IAAP;MACA,CAxCwC,CAyCzC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MACA,IAAI,KAAKR,gBAAL,CAAsBf,KAAtB,CAAJ,EAAkC;QACjC,KAAK0D,8CAAL,CAAoD1D,KAApD;QACA,OAAO,IAAP;MACA,CA1DwC,CA2DzC;MACA;MACA;MACA;MACA;MACA;MACA;;MACA,IAAI,KAAK2D,cAAL,CAAoB3D,KAApB,CAAJ,EAAgC;QAC/B,KAAK0D,8CAAL,CAAoD1D,KAApD;QACA,OAAO,IAAP;MACA;IACD;;;WAED,SAAAe,iBAAiBf,KAAjB,EAAwB;MACvB;MACA;MACA,IACCmB,aADD,GAKInB,KALJ,CACCmB,aADD;QAECyC,SAFD,GAKI5D,KALJ,CAEC4D,SAFD;QAGCrD,MAHD,GAKIP,KALJ,CAGCO,MAHD;QAICyB,yBAJD,GAKIhC,KALJ,CAICgC,yBAJD;MAMA,IAAIb,aAAa,IAAIyC,SAArB,EAAgC;QAC/B;MACA,CAXsB,CAYvB;MACA;MACA;MACA;;MACA,IAAMC,gBAAgB,GAAGjF,cAAc,CACtC2B,MADsC,EAEtC,KAAKd,cAFiC,EAGtC,KAAKC,kBAHiC,EAItC,KAAKC,QAAL,CAAcA,QAJwB,CAAvC;MAMA,IAAIkE,gBAAgB,KAAKrB,SAArB,IAAkCqB,gBAAgB,KAAKtD,MAA3D,EAAmE;QAClE;QACA;QACAP,KAAK,CAACwB,MAAN,CAAa;UACZoC,SAAS,EAAErD,MAAM,CAACiD,KAAP,CAAa,CAAb,EAAgBjD,MAAM,CAACM,MAAP,GAAgBgD,gBAAgB,CAAChD,MAAjD;QADC,CAAb;QAGA,KAAKJ,wBAAL,CAA8BT,KAA9B,EAAqC;UACpC8D,OAAO,EAAEtB,SAD2B;UAEpCd,WAAW,EAAEc;QAFuB,CAArC;QAIA,OAAO,IAAP;MACA;IACD;;;WAED,SAAAmB,eAAe3D,KAAf,EAAsB;MACrB,IAAI,CAACA,KAAK,CAACmB,aAAX,EAA0B;QACzB,IAAA4C,sBAAA,GAGIrF,+DAA+D,CAClEsB,KAAK,CAACO,MAD4D,EAElE,KAAKd,cAF6D,EAGlE,KAAKC,kBAH6D,EAIlE,KAAKC,QAAL,CAAcA,QAJoD,CAHnE;UACqBqE,cADrB,GAAAD,sBAAA,CACClC,kBADD;UAECC,MAFD,GAAAiC,sBAAA,CAECjC,MAFD;QASA,IAAIkC,cAAJ,EAAoB;UACnBhE,KAAK,CAACwB,MAAN,CAAa;YACZyC,WAAW,EAAE;UADD,CAAb;UAGA,KAAKxD,wBAAL,CAA8BT,KAA9B,EAAqC;YACpC8D,OAAO,EAAE9D,KAAK,CAAC8D,OADqB;YAEpCpC,WAAW,EAAEsC;UAFuB,CAArC;UAIA,OAAO,IAAP;QACA;MACD;IACD;;;WAED,SAAAvD,yBAAyBT,KAAzB,EAAAkE,KAAA,EAA0D;MAAA,IAAxBJ,OAAwB,GAAAI,KAAA,CAAxBJ,OAAwB;QAAfpC,WAAe,GAAAwC,KAAA,CAAfxC,WAAe;MACzD1B,KAAK,CAACS,wBAAN,CAA+BqD,OAA/B,EAAwCpC,WAAxC,EADyD,CAEzD;;MACA,IAAI1B,KAAK,CAACgC,yBAAV,EAAqC;QACpChC,KAAK,CAACmE,8BAAN;QACA,KAAKvE,iCAAL;QACA,KAAKwB,qCAAL,GAA6CoB,SAA7C;MACA;IACD;;;WAED,SAAAkB,+CAA+C1D,KAA/C,EAAsD;MACrD,IAAI,KAAKiB,yBAAL,CAA+BjB,KAA/B,CAAJ,EAA2C;QAC1C;QACA;QACA;QACA;QACA;QACA;QACA,KAAKqB,gCAAL,CACCrB,KAAK,CAACsB,iBAAN,EADD,EAEC,UAACC,WAAD;UAAA,OAAiBvB,KAAK,CAACwB,MAAN,CAAaD,WAAb,CAAjB;QAAA,CAFD;MAIA;IACD;;;;AAGF;AACA;AACA;AACA;AACA;;SAjZqBhC,e;AAkZrB,SAAS6E,2BAATA,CAAqCrE,IAArC,EAA2C;EAC1C;EACA,IAAMsE,QAAQ,GAAGtE,IAAI,CAACuE,MAAL,CAAYlF,iCAAZ,CAAjB;EACA,IAAIiF,QAAQ,GAAG,CAAf,EAAkB;IACjB;EACA,CALyC,CAM1C;;EACAtE,IAAI,GAAGA,IAAI,CAACyD,KAAL,CAAWa,QAAX,CAAP,CAP0C,CAQ1C;;EACA,IAAI/D,OAAJ;EACA,IAAIP,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAhB,EAAqB;IACpBO,OAAO,GAAG,IAAV;IACAP,IAAI,GAAGA,IAAI,CAACyD,KAAL,CAAW,IAAI3C,MAAf,CAAP;EACA,CAbyC,CAc1C;;EACAd,IAAI,GAAGA,IAAI,CAACwE,OAAL,CAAalF,qCAAb,EAAoD,EAApD,CAAP,CAf0C,CAgB1C;;EACA,IAAIiB,OAAJ,EAAa;IACZP,IAAI,GAAG,MAAMA,IAAb;EACA;EACD,OAAOA,IAAP;AACA;AAED;AACA;AACA;AACA;AACA;;AACA,SAASyE,8BAATA,CAAwCzE,IAAxC,EAA8C;EAC7C;EACA,IAAM0E,eAAe,GAAGL,2BAA2B,CAACrE,IAAD,CAA3B,IAAqC,EAA7D,CAF6C,CAG7C;;EACA,IAAI0E,eAAe,CAAC,CAAD,CAAf,KAAuB,GAA3B,EAAgC;IAC/B,OAAO,CAACA,eAAe,CAACjB,KAAhB,CAAsB,IAAI3C,MAA1B,CAAD,EAAoC,IAApC,CAAP;EACA;EACD,OAAO,CAAC4D,eAAD,CAAP;AACA;AAED;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASvE,6BAATA,CAAuCH,IAAvC,EAA6C;EACnD,IAAA2E,sBAAA,GAAiCF,8BAA8B,CAACzE,IAAD,CAA/D;IAAA4E,sBAAA,GAAAvE,cAAA,CAAAsE,sBAAA;IAAKrE,eAAL,GAAAsE,sBAAA;IAAsBrE,OAAtB,GAAAqE,sBAAA,IADmD,CAEnD;EACA;EACA;;EACA,IAAI,CAACzF,gDAAgD,CAACqD,IAAjD,CAAsDlC,eAAtD,CAAL,EAA6E;IAC5EA,eAAe,GAAG,EAAlB;EACA;EACD,OAAO,CAACA,eAAD,EAAkBC,OAAlB,CAAP;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}