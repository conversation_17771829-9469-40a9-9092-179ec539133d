{"ast": null, "code": "import { exhaustMap } from './exhaustMap';\nimport { identity } from '../util/identity';\nexport function exhaustAll() {\n  return exhaustMap(identity);\n}", "map": {"version": 3, "names": ["exhaustMap", "identity", "exhaustAll"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\exhaustAll.ts"], "sourcesContent": ["import { OperatorFunction, ObservableInput, ObservedValueOf } from '../types';\nimport { exhaustMap } from './exhaustMap';\nimport { identity } from '../util/identity';\n\n/**\n * Converts a higher-order Observable into a first-order Observable by dropping\n * inner Observables while the previous inner Observable has not yet completed.\n *\n * <span class=\"informal\">Flattens an Observable-of-Observables by dropping the\n * next inner Observables while the current inner is still executing.</span>\n *\n * ![](exhaustAll.svg)\n *\n * `exhaustAll` subscribes to an Observable that emits Observables, also known as a\n * higher-order Observable. Each time it observes one of these emitted inner\n * Observables, the output Observable begins emitting the items emitted by that\n * inner Observable. So far, it behaves like {@link mergeAll}. However,\n * `exhaustAll` ignores every new inner Observable if the previous Observable has\n * not yet completed. Once that one completes, it will accept and flatten the\n * next inner Observable and repeat this process.\n *\n * ## Example\n *\n * Run a finite timer for each click, only if there is no currently active timer\n *\n * ```ts\n * import { fromEvent, map, interval, take, exhaustAll } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const higherOrder = clicks.pipe(\n *   map(() => interval(1000).pipe(take(5)))\n * );\n * const result = higherOrder.pipe(exhaustAll());\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link combineLatestAll}\n * @see {@link concatAll}\n * @see {@link switchAll}\n * @see {@link switchMap}\n * @see {@link mergeAll}\n * @see {@link exhaustMap}\n * @see {@link zipAll}\n *\n * @return A function that returns an Observable that takes a source of\n * Observables and propagates the first Observable exclusively until it\n * completes before subscribing to the next.\n */\nexport function exhaustAll<O extends ObservableInput<any>>(): OperatorFunction<O, ObservedValueOf<O>> {\n  return exhaustMap(identity);\n}\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,cAAc;AACzC,SAASC,QAAQ,QAAQ,kBAAkB;AA8C3C,OAAM,SAAUC,UAAUA,CAAA;EACxB,OAAOF,UAAU,CAACC,QAAQ,CAAC;AAC7B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}