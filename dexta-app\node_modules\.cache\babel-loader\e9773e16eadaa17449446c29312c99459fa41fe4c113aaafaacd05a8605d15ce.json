{"ast": null, "code": "import Hash from './_Hash.js';\nimport ListCache from './_ListCache.js';\nimport Map from './_Map.js';\n\n/**\n * Removes all key-value entries from the map.\n *\n * @private\n * @name clear\n * @memberOf MapCache\n */\nfunction mapCacheClear() {\n  this.size = 0;\n  this.__data__ = {\n    'hash': new Hash(),\n    'map': new (Map || ListCache)(),\n    'string': new Hash()\n  };\n}\nexport default mapCacheClear;", "map": {"version": 3, "names": ["Hash", "ListCache", "Map", "mapCacheClear", "size", "__data__"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/lodash-es/_mapCacheClear.js"], "sourcesContent": ["import Hash from './_Hash.js';\nimport ListCache from './_ListCache.js';\nimport Map from './_Map.js';\n\n/**\n * Removes all key-value entries from the map.\n *\n * @private\n * @name clear\n * @memberOf MapCache\n */\nfunction mapCacheClear() {\n  this.size = 0;\n  this.__data__ = {\n    'hash': new Hash,\n    'map': new (Map || ListCache),\n    'string': new Hash\n  };\n}\n\nexport default mapCacheClear;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,YAAY;AAC7B,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,GAAG,MAAM,WAAW;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAAA,EAAG;EACvB,IAAI,CAACC,IAAI,GAAG,CAAC;EACb,IAAI,CAACC,QAAQ,GAAG;IACd,MAAM,EAAE,IAAIL,IAAI,CAAD,CAAC;IAChB,KAAK,EAAE,KAAKE,GAAG,IAAID,SAAS,GAAC;IAC7B,QAAQ,EAAE,IAAID,IAAI,CAAD;EACnB,CAAC;AACH;AAEA,eAAeG,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}