{"ast": null, "code": "import { invariant } from '@react-dnd/invariant';\nlet isCallingCanDrop = false;\nexport class DropTargetMonitorImpl {\n  receiveHandlerId(targetId) {\n    this.targetId = targetId;\n  }\n  getHandlerId() {\n    return this.targetId;\n  }\n  subscribeToStateChange(listener, options) {\n    return this.internalMonitor.subscribeToStateChange(listener, options);\n  }\n  canDrop() {\n    // Cut out early if the target id has not been set. This should prevent errors\n    // where the user has an older version of dnd-core like in\n    // https://github.com/react-dnd/react-dnd/issues/1310\n    if (!this.targetId) {\n      return false;\n    }\n    invariant(!isCallingCanDrop, 'You may not call monitor.canDrop() inside your canDrop() implementation. ' + 'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor');\n    try {\n      isCallingCanDrop = true;\n      return this.internalMonitor.canDropOnTarget(this.targetId);\n    } finally {\n      isCallingCanDrop = false;\n    }\n  }\n  isOver(options) {\n    if (!this.targetId) {\n      return false;\n    }\n    return this.internalMonitor.isOverTarget(this.targetId, options);\n  }\n  getItemType() {\n    return this.internalMonitor.getItemType();\n  }\n  getItem() {\n    return this.internalMonitor.getItem();\n  }\n  getDropResult() {\n    return this.internalMonitor.getDropResult();\n  }\n  didDrop() {\n    return this.internalMonitor.didDrop();\n  }\n  getInitialClientOffset() {\n    return this.internalMonitor.getInitialClientOffset();\n  }\n  getInitialSourceClientOffset() {\n    return this.internalMonitor.getInitialSourceClientOffset();\n  }\n  getSourceClientOffset() {\n    return this.internalMonitor.getSourceClientOffset();\n  }\n  getClientOffset() {\n    return this.internalMonitor.getClientOffset();\n  }\n  getDifferenceFromInitialOffset() {\n    return this.internalMonitor.getDifferenceFromInitialOffset();\n  }\n  constructor(manager) {\n    this.targetId = null;\n    this.internalMonitor = manager.getMonitor();\n  }\n}", "map": {"version": 3, "names": ["invariant", "isCallingCanDrop", "DropTargetMonitorImpl", "receiveHandlerId", "targetId", "getHandlerId", "subscribeToStateChange", "listener", "options", "internalMonitor", "canDrop", "canDropOnTarget", "isOver", "isOverTarget", "getItemType", "getItem", "getDropResult", "didDrop", "getInitialClientOffset", "getInitialSourceClientOffset", "getSourceClientOffset", "getClientOffset", "getDifferenceFromInitialOffset", "constructor", "manager", "getMonitor"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\react-dnd\\src\\internals\\DropTargetMonitorImpl.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\nimport type {\n\t<PERSON><PERSON><PERSON><PERSON><PERSON>ana<PERSON>,\n\tDragDropMonitor,\n\tIdentifier,\n\tListener,\n\tUnsubscribe,\n\tXYCoord,\n} from 'dnd-core'\n\nimport type { DropTargetMonitor } from '../types/index.js'\n\nlet isCallingCanDrop = false\n\nexport class DropTargetMonitorImpl implements DropTargetMonitor {\n\tprivate internalMonitor: DragDropMonitor\n\tprivate targetId: Identifier | null = null\n\n\tpublic constructor(manager: DragDropManager) {\n\t\tthis.internalMonitor = manager.getMonitor()\n\t}\n\n\tpublic receiveHandlerId(targetId: Identifier | null): void {\n\t\tthis.targetId = targetId\n\t}\n\n\tpublic getHandlerId(): Identifier | null {\n\t\treturn this.targetId\n\t}\n\n\tpublic subscribeToStateChange(\n\t\tlistener: Listener,\n\t\toptions?: { handlerIds?: Identifier[] },\n\t): Unsubscribe {\n\t\treturn this.internalMonitor.subscribeToStateChange(listener, options)\n\t}\n\n\tpublic canDrop(): boolean {\n\t\t// Cut out early if the target id has not been set. This should prevent errors\n\t\t// where the user has an older version of dnd-core like in\n\t\t// https://github.com/react-dnd/react-dnd/issues/1310\n\t\tif (!this.targetId) {\n\t\t\treturn false\n\t\t}\n\t\tinvariant(\n\t\t\t!isCallingCanDrop,\n\t\t\t'You may not call monitor.canDrop() inside your canDrop() implementation. ' +\n\t\t\t\t'Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor',\n\t\t)\n\n\t\ttry {\n\t\t\tisCallingCanDrop = true\n\t\t\treturn this.internalMonitor.canDropOnTarget(this.targetId)\n\t\t} finally {\n\t\t\tisCallingCanDrop = false\n\t\t}\n\t}\n\n\tpublic isOver(options?: { shallow?: boolean }): boolean {\n\t\tif (!this.targetId) {\n\t\t\treturn false\n\t\t}\n\t\treturn this.internalMonitor.isOverTarget(this.targetId, options)\n\t}\n\n\tpublic getItemType(): Identifier | null {\n\t\treturn this.internalMonitor.getItemType()\n\t}\n\n\tpublic getItem(): any {\n\t\treturn this.internalMonitor.getItem()\n\t}\n\n\tpublic getDropResult(): any {\n\t\treturn this.internalMonitor.getDropResult()\n\t}\n\n\tpublic didDrop(): boolean {\n\t\treturn this.internalMonitor.didDrop()\n\t}\n\n\tpublic getInitialClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getInitialClientOffset()\n\t}\n\n\tpublic getInitialSourceClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getInitialSourceClientOffset()\n\t}\n\n\tpublic getSourceClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getSourceClientOffset()\n\t}\n\n\tpublic getClientOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getClientOffset()\n\t}\n\n\tpublic getDifferenceFromInitialOffset(): XYCoord | null {\n\t\treturn this.internalMonitor.getDifferenceFromInitialOffset()\n\t}\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB;AAYhD,IAAIC,gBAAgB,GAAG,KAAK;AAE5B,OAAO,MAAMC,qBAAqB;EAQjCC,gBAAuBA,CAACC,QAA2B,EAAQ;IAC1D,IAAI,CAACA,QAAQ,GAAGA,QAAQ;;EAGzBC,YAAmBA,CAAA,EAAsB;IACxC,OAAO,IAAI,CAACD,QAAQ;;EAGrBE,sBAA6BA,CAC5BC,QAAkB,EAClBC,OAAuC,EACzB;IACd,OAAO,IAAI,CAACC,eAAe,CAACH,sBAAsB,CAACC,QAAQ,EAAEC,OAAO,CAAC;;EAGtEE,OAAcA,CAAA,EAAY;IACzB;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACN,QAAQ,EAAE;MACnB,OAAO,KAAK;;IAEbJ,SAAS,CACR,CAACC,gBAAgB,EACjB,2EAA2E,GAC1E,8EAA8E,CAC/E;IAED,IAAI;MACHA,gBAAgB,GAAG,IAAI;MACvB,OAAO,IAAI,CAACQ,eAAe,CAACE,eAAe,CAAC,IAAI,CAACP,QAAQ,CAAC;KAC1D,SAAS;MACTH,gBAAgB,GAAG,KAAK;;;EAI1BW,MAAaA,CAACJ,OAA+B,EAAW;IACvD,IAAI,CAAC,IAAI,CAACJ,QAAQ,EAAE;MACnB,OAAO,KAAK;;IAEb,OAAO,IAAI,CAACK,eAAe,CAACI,YAAY,CAAC,IAAI,CAACT,QAAQ,EAAEI,OAAO,CAAC;;EAGjEM,WAAkBA,CAAA,EAAsB;IACvC,OAAO,IAAI,CAACL,eAAe,CAACK,WAAW,EAAE;;EAG1CC,OAAcA,CAAA,EAAQ;IACrB,OAAO,IAAI,CAACN,eAAe,CAACM,OAAO,EAAE;;EAGtCC,aAAoBA,CAAA,EAAQ;IAC3B,OAAO,IAAI,CAACP,eAAe,CAACO,aAAa,EAAE;;EAG5CC,OAAcA,CAAA,EAAY;IACzB,OAAO,IAAI,CAACR,eAAe,CAACQ,OAAO,EAAE;;EAGtCC,sBAA6BA,CAAA,EAAmB;IAC/C,OAAO,IAAI,CAACT,eAAe,CAACS,sBAAsB,EAAE;;EAGrDC,4BAAmCA,CAAA,EAAmB;IACrD,OAAO,IAAI,CAACV,eAAe,CAACU,4BAA4B,EAAE;;EAG3DC,qBAA4BA,CAAA,EAAmB;IAC9C,OAAO,IAAI,CAACX,eAAe,CAACW,qBAAqB,EAAE;;EAGpDC,eAAsBA,CAAA,EAAmB;IACxC,OAAO,IAAI,CAACZ,eAAe,CAACY,eAAe,EAAE;;EAG9CC,8BAAqCA,CAAA,EAAmB;IACvD,OAAO,IAAI,CAACb,eAAe,CAACa,8BAA8B,EAAE;;EAhF7DC,YAAmBC,OAAwB,EAAE;IAF7C,KAAQpB,QAAQ,GAAsB,IAAI;IAGzC,IAAI,CAACK,eAAe,GAAGe,OAAO,CAACC,UAAU,EAAE"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}