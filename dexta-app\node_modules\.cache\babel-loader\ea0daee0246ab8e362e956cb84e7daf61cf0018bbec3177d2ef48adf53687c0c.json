{"ast": null, "code": "'use strict';\n\nvar global = require('../internals/global');\nvar defineGlobalProperty = require('../internals/define-global-property');\nvar SHARED = '__core-js_shared__';\nvar store = global[SHARED] || defineGlobalProperty(SHARED, {});\nmodule.exports = store;", "map": {"version": 3, "names": ["global", "require", "defineGlobalProperty", "SHARED", "store", "module", "exports"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/core-js-pure/internals/shared-store.js"], "sourcesContent": ["'use strict';\nvar global = require('../internals/global');\nvar defineGlobalProperty = require('../internals/define-global-property');\n\nvar SHARED = '__core-js_shared__';\nvar store = global[SHARED] || defineGlobalProperty(SHARED, {});\n\nmodule.exports = store;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAC3C,IAAIC,oBAAoB,GAAGD,OAAO,CAAC,qCAAqC,CAAC;AAEzE,IAAIE,MAAM,GAAG,oBAAoB;AACjC,IAAIC,KAAK,GAAGJ,MAAM,CAACG,MAAM,CAAC,IAAID,oBAAoB,CAACC,MAAM,EAAE,CAAC,CAAC,CAAC;AAE9DE,MAAM,CAACC,OAAO,GAAGF,KAAK"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}