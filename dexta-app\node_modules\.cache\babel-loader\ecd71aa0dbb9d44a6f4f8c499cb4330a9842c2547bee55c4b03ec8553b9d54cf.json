{"ast": null, "code": "function _createForOfIteratorHelperLoose(o, allowArrayLike) {\n  var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n  if (it) return (it = it.call(o)).next.bind(it);\n  if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n    if (it) o = it;\n    var i = 0;\n    return function () {\n      if (i >= o.length) return {\n        done: true\n      };\n      return {\n        done: false,\n        value: o[i++]\n      };\n    };\n  }\n  throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\n\n// Counts all occurences of a symbol in a string\nexport function count_occurences(symbol, string) {\n  var count = 0; // Using `.split('')` here instead of normal `for ... of`\n  // because the importing application doesn't neccessarily include an ES6 polyfill.\n  // The `.split('')` approach discards \"exotic\" UTF-8 characters\n  // (the ones consisting of four bytes)\n  // but template placeholder characters don't fall into that range\n  // so skipping such miscellaneous \"exotic\" characters\n  // won't matter here for just counting placeholder character occurrences.\n\n  for (var _iterator = _createForOfIteratorHelperLoose(string.split('')), _step; !(_step = _iterator()).done;) {\n    var character = _step.value;\n    if (character === symbol) {\n      count++;\n    }\n  }\n  return count;\n}", "map": {"version": 3, "names": ["count_occurences", "symbol", "string", "count", "_iterator", "_createForOfIteratorHelperLoose", "split", "_step", "done", "character", "value"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\input-format\\source\\helpers.js"], "sourcesContent": ["// Counts all occurences of a symbol in a string\r\nexport function count_occurences(symbol, string) {\r\n\tlet count = 0\r\n\t// Using `.split('')` here instead of normal `for ... of`\r\n\t// because the importing application doesn't neccessarily include an ES6 polyfill.\r\n\t// The `.split('')` approach discards \"exotic\" UTF-8 characters\r\n\t// (the ones consisting of four bytes)\r\n\t// but template placeholder characters don't fall into that range\r\n\t// so skipping such miscellaneous \"exotic\" characters\r\n\t// won't matter here for just counting placeholder character occurrences.\r\n\tfor (const character of string.split('')) {\r\n\t\tif (character === symbol) {\r\n\t\t\tcount++\r\n\t\t}\r\n\t}\r\n\treturn count\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA,OAAO,SAASA,gBAATA,CAA0BC,MAA1B,EAAkCC,MAAlC,EAA0C;EAChD,IAAIC,KAAK,GAAG,CAAZ,CADgD,CAEhD;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,SAAAC,SAAA,GAAAC,+BAAA,CAAwBH,MAAM,CAACI,KAAP,CAAa,EAAb,CAAxB,GAAAC,KAAA,IAAAA,KAAA,GAAAH,SAAA,IAAAI,IAAA,GAA0C;IAAA,IAA/BC,SAA+B,GAAAF,KAAA,CAAAG,KAAA;IACzC,IAAID,SAAS,KAAKR,MAAlB,EAA0B;MACzBE,KAAK;IACL;EACD;EACD,OAAOA,KAAP;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}