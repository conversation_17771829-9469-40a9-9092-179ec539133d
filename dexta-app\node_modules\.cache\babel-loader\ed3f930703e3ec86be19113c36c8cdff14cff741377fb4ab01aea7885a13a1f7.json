{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = isValidTagOrAttributeName;\nvar VALID_TAG_REGEX = /^[a-zA-Z][a-zA-Z:_\\.\\-\\d]*$/;\nvar nameCache = {};\nfunction isValidTagOrAttributeName(tagName) {\n  if (!nameCache.hasOwnProperty(tagName)) {\n    nameCache[tagName] = VALID_TAG_REGEX.test(tagName);\n  }\n  return nameCache[tagName];\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "default", "isValidTagOrAttributeName", "VALID_TAG_REGEX", "name<PERSON>ache", "tagName", "hasOwnProperty", "test"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/react-html-parser/lib/utils/isValidTagOrAttributeName.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = isValidTagOrAttributeName;\nvar VALID_TAG_REGEX = /^[a-zA-Z][a-zA-Z:_\\.\\-\\d]*$/;\n\nvar nameCache = {};\n\nfunction isValidTagOrAttributeName(tagName) {\n  if (!nameCache.hasOwnProperty(tagName)) {\n    nameCache[tagName] = VALID_TAG_REGEX.test(tagName);\n  }\n  return nameCache[tagName];\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,OAAO,GAAGC,yBAAyB;AAC3C,IAAIC,eAAe,GAAG,6BAA6B;AAEnD,IAAIC,SAAS,GAAG,CAAC,CAAC;AAElB,SAASF,yBAAyBA,CAACG,OAAO,EAAE;EAC1C,IAAI,CAACD,SAAS,CAACE,cAAc,CAACD,OAAO,CAAC,EAAE;IACtCD,SAAS,CAACC,OAAO,CAAC,GAAGF,eAAe,CAACI,IAAI,CAACF,OAAO,CAAC;EACpD;EACA,OAAOD,SAAS,CAACC,OAAO,CAAC;AAC3B"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}