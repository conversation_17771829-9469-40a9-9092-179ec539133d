{"ast": null, "code": "import { switchMap } from './switchMap';\nimport { isFunction } from '../util/isFunction';\nexport function switchMapTo(innerObservable, resultSelector) {\n  return isFunction(resultSelector) ? switchMap(function () {\n    return innerObservable;\n  }, resultSelector) : switchMap(function () {\n    return innerObservable;\n  });\n}", "map": {"version": 3, "names": ["switchMap", "isFunction", "switchMapTo", "innerObservable", "resultSelector"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\switchMapTo.ts"], "sourcesContent": ["import { switchMap } from './switchMap';\nimport { ObservableInput, OperatorFunction, ObservedValueOf } from '../types';\nimport { isFunction } from '../util/isFunction';\n\n/** @deprecated Will be removed in v9. Use {@link switchMap} instead: `switchMap(() => result)` */\nexport function switchMapTo<O extends ObservableInput<unknown>>(observable: O): OperatorFunction<unknown, ObservedValueOf<O>>;\n/** @deprecated The `resultSelector` parameter will be removed in v8. Use an inner `map` instead. Details: https://rxjs.dev/deprecations/resultSelector */\nexport function switchMapTo<O extends ObservableInput<unknown>>(\n  observable: O,\n  resultSelector: undefined\n): OperatorFunction<unknown, ObservedValueOf<O>>;\n/** @deprecated The `resultSelector` parameter will be removed in v8. Use an inner `map` instead. Details: https://rxjs.dev/deprecations/resultSelector */\nexport function switchMapTo<T, R, O extends ObservableInput<unknown>>(\n  observable: O,\n  resultSelector: (outerValue: T, innerValue: ObservedValueOf<O>, outerIndex: number, innerIndex: number) => R\n): OperatorFunction<T, R>;\n\n/**\n * Projects each source value to the same Observable which is flattened multiple\n * times with {@link switchMap} in the output Observable.\n *\n * <span class=\"informal\">It's like {@link switchMap}, but maps each value\n * always to the same inner Observable.</span>\n *\n * ![](switchMapTo.png)\n *\n * Maps each source value to the given Observable `innerObservable` regardless\n * of the source value, and then flattens those resulting Observables into one\n * single Observable, which is the output Observable. The output Observables\n * emits values only from the most recently emitted instance of\n * `innerObservable`.\n *\n * ## Example\n *\n * Restart an interval Observable on every click event\n *\n * ```ts\n * import { fromEvent, switchMapTo, interval } from 'rxjs';\n *\n * const clicks = fromEvent(document, 'click');\n * const result = clicks.pipe(switchMapTo(interval(1000)));\n * result.subscribe(x => console.log(x));\n * ```\n *\n * @see {@link concatMapTo}\n * @see {@link switchAll}\n * @see {@link switchMap}\n * @see {@link mergeMapTo}\n *\n * @param innerObservable An `ObservableInput` to replace each value from the\n * source Observable.\n * @return A function that returns an Observable that emits items from the\n * given `innerObservable` (and optionally transformed through the deprecated\n * `resultSelector`) every time a value is emitted on the source Observable,\n * and taking only the values from the most recently projected inner\n * Observable.\n * @deprecated Will be removed in v9. Use {@link switchMap} instead: `switchMap(() => result)`\n */\nexport function switchMapTo<T, R, O extends ObservableInput<unknown>>(\n  innerObservable: O,\n  resultSelector?: (outerValue: T, innerValue: ObservedValueOf<O>, outerIndex: number, innerIndex: number) => R\n): OperatorFunction<T, ObservedValueOf<O> | R> {\n  return isFunction(resultSelector) ? switchMap(() => innerObservable, resultSelector) : switchMap(() => innerObservable);\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AAEvC,SAASC,UAAU,QAAQ,oBAAoB;AAwD/C,OAAM,SAAUC,WAAWA,CACzBC,eAAkB,EAClBC,cAA6G;EAE7G,OAAOH,UAAU,CAACG,cAAc,CAAC,GAAGJ,SAAS,CAAC;IAAM,OAAAG,eAAe;EAAf,CAAe,EAAEC,cAAc,CAAC,GAAGJ,SAAS,CAAC;IAAM,OAAAG,eAAe;EAAf,CAAe,CAAC;AACzH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}