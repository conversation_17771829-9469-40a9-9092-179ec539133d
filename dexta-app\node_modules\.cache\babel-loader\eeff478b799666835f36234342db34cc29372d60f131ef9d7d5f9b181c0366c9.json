{"ast": null, "code": "var _excluded = [\"value\", \"parse\", \"format\", \"inputComponent\", \"onChange\", \"onKeyDown\"];\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\n\n// This is just `./ReactInput.js` rewritten in Hooks.\nimport React, { useCallback, useRef } from 'react';\nimport PropTypes from 'prop-types';\nimport { onChange as onInputChange, onKeyDown as onInputKeyDown } from '../inputControl.js'; // Usage:\n//\n// <ReactInput\n// \tvalue={this.state.phone}\n// \tonChange={phone => this.setState({ phone })}\n// \tparse={character => character}\n// \tformat={value => ({ text: value, template: 'xxxxxxxx' })}/>\n//\n\nfunction Input(_ref, ref) {\n  var value = _ref.value,\n    parse = _ref.parse,\n    format = _ref.format,\n    InputComponent = _ref.inputComponent,\n    onChange = _ref.onChange,\n    onKeyDown = _ref.onKeyDown,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var internalRef = useRef();\n  var setRef = useCallback(function (instance) {\n    internalRef.current = instance;\n    if (ref) {\n      if (typeof ref === 'function') {\n        ref(instance);\n      } else {\n        ref.current = instance;\n      }\n    }\n  }, [ref]);\n  var _onChange = useCallback(function (event) {\n    return onInputChange(event, internalRef.current, parse, format, onChange);\n  }, [internalRef, parse, format, onChange]);\n  var _onKeyDown = useCallback(function (event) {\n    if (onKeyDown) {\n      onKeyDown(event);\n    }\n    return onInputKeyDown(event, internalRef.current, parse, format, onChange);\n  }, [internalRef, parse, format, onChange, onKeyDown]);\n  return /*#__PURE__*/React.createElement(InputComponent, _extends({}, rest, {\n    ref: setRef,\n    value: format(isEmptyValue(value) ? '' : value).text,\n    onKeyDown: _onKeyDown,\n    onChange: _onChange\n  }));\n}\nInput = /*#__PURE__*/React.forwardRef(Input);\nInput.propTypes = {\n  // Parses a single characher of `<input/>` text.\n  parse: PropTypes.func.isRequired,\n  // Formats `value` into `<input/>` text.\n  format: PropTypes.func.isRequired,\n  // Renders `<input/>` by default.\n  inputComponent: PropTypes.elementType.isRequired,\n  // `<input/>` `type` attribute.\n  type: PropTypes.string.isRequired,\n  // Is parsed from <input/> text.\n  value: PropTypes.string,\n  // This handler is called each time `<input/>` text is changed.\n  onChange: PropTypes.func.isRequired,\n  // Passthrough\n  onKeyDown: PropTypes.func,\n  onCut: PropTypes.func,\n  onPaste: PropTypes.func\n};\nInput.defaultProps = {\n  // Renders `<input/>` by default.\n  inputComponent: 'input',\n  // `<input/>` `type` attribute.\n  type: 'text'\n};\nexport default Input;\nfunction isEmptyValue(value) {\n  return value === undefined || value === null;\n}", "map": {"version": 3, "names": ["React", "useCallback", "useRef", "PropTypes", "onChange", "onInputChange", "onKeyDown", "onInputKeyDown", "Input", "_ref", "ref", "value", "parse", "format", "InputComponent", "inputComponent", "rest", "_objectWithoutProperties", "_excluded", "internalRef", "setRef", "instance", "current", "_onChange", "event", "_onKeyDown", "createElement", "_extends", "isEmptyValue", "text", "forwardRef", "propTypes", "func", "isRequired", "elementType", "type", "string", "onCut", "onPaste", "defaultProps", "undefined"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\input-format\\source\\react\\Input.js"], "sourcesContent": ["// This is just `./ReactInput.js` rewritten in Hooks.\r\n\r\nimport React, { useCallback, useRef } from 'react'\r\nimport PropTypes from 'prop-types'\r\n\r\nimport {\r\n\tonChange as onInputChange,\r\n\tonKeyDown as onInputKeyDown\r\n} from '../inputControl.js'\r\n\r\n// Usage:\r\n//\r\n// <ReactInput\r\n// \tvalue={this.state.phone}\r\n// \tonChange={phone => this.setState({ phone })}\r\n// \tparse={character => character}\r\n// \tformat={value => ({ text: value, template: 'xxxxxxxx' })}/>\r\n//\r\nfunction Input({\r\n\tvalue,\r\n\tparse,\r\n\tformat,\r\n\tinputComponent: InputComponent,\r\n\tonChange,\r\n\tonKeyDown,\r\n\t...rest\r\n}, ref) {\r\n\tconst internalRef = useRef();\r\n\tconst setRef = useCallback((instance) => {\r\n\t\tinternalRef.current = instance;\r\n\t\tif (ref) {\r\n\t\t\tif (typeof ref === 'function') {\r\n\t\t\t\tref(instance)\r\n\t\t\t} else {\r\n\t\t\t\tref.current = instance\r\n\t\t\t}\r\n\t\t}\r\n\t}, [ref]);\r\n\tconst _onChange = useCallback((event) => {\r\n\t\treturn onInputChange(\r\n\t\t\tevent,\r\n\t\t\tinternalRef.current,\r\n\t\t\tparse,\r\n\t\t\tformat,\r\n\t\t\tonChange\r\n\t\t)\r\n\t}, [internalRef, parse, format, onChange])\r\n\r\n\tconst _onKeyDown = useCallback((event) => {\r\n\t\tif (onKeyDown) {\r\n\t\t\tonKeyDown(event)\r\n\t\t}\r\n\t\treturn onInputKeyDown(\r\n\t\t\tevent,\r\n\t\t\tinternalRef.current,\r\n\t\t\tparse,\r\n\t\t\tformat,\r\n\t\t\tonChange\r\n\t\t)\r\n\t}, [internalRef, parse, format, onChange, onKeyDown])\r\n\r\n\treturn (\r\n\t\t<InputComponent\r\n\t\t\t{...rest}\r\n\t\t\tref={setRef}\r\n\t\t\tvalue={format(isEmptyValue(value) ? '' : value).text}\r\n\t\t\tonKeyDown={_onKeyDown}\r\n\t\t\tonChange={_onChange}/>\r\n\t)\r\n}\r\n\r\nInput = React.forwardRef(Input)\r\n\r\nInput.propTypes = {\r\n\t// Parses a single characher of `<input/>` text.\r\n\tparse: PropTypes.func.isRequired,\r\n\r\n\t// Formats `value` into `<input/>` text.\r\n\tformat: PropTypes.func.isRequired,\r\n\r\n\t// Renders `<input/>` by default.\r\n\tinputComponent: PropTypes.elementType.isRequired,\r\n\r\n\t// `<input/>` `type` attribute.\r\n\ttype: PropTypes.string.isRequired,\r\n\r\n\t// Is parsed from <input/> text.\r\n\tvalue: PropTypes.string,\r\n\r\n\t// This handler is called each time `<input/>` text is changed.\r\n\tonChange: PropTypes.func.isRequired,\r\n\r\n\t// Passthrough\r\n\tonKeyDown: PropTypes.func,\r\n\tonCut: PropTypes.func,\r\n\tonPaste: PropTypes.func\r\n}\r\n\r\nInput.defaultProps = {\r\n\t// Renders `<input/>` by default.\r\n\tinputComponent: 'input',\r\n\r\n\t// `<input/>` `type` attribute.\r\n\ttype: 'text'\r\n}\r\n\r\nexport default Input\r\n\r\nfunction isEmptyValue(value) {\r\n\treturn value === undefined || value === null\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAEA,OAAOA,KAAP,IAAgBC,WAAhB,EAA6BC,MAA7B,QAA2C,OAA3C;AACA,OAAOC,SAAP,MAAsB,YAAtB;AAEA,SACCC,QAAQ,IAAIC,aADb,EAECC,SAAS,IAAIC,cAFd,QAGO,oBAHP,C,CAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASC,KAATA,CAAAC,IAAA,EAQGC,GARH,EAQQ;EAAA,IAPPC,KAOO,GAAAF,IAAA,CAPPE,KAOO;IANPC,KAMO,GAAAH,IAAA,CANPG,KAMO;IALPC,MAKO,GAAAJ,IAAA,CALPI,MAKO;IAJSC,cAIT,GAAAL,IAAA,CAJPM,cAIO;IAHPX,QAGO,GAAAK,IAAA,CAHPL,QAGO;IAFPE,SAEO,GAAAG,IAAA,CAFPH,SAEO;IADJU,IACI,GAAAC,wBAAA,CAAAR,IAAA,EAAAS,SAAA;EACP,IAAMC,WAAW,GAAGjB,MAAM,EAA1B;EACA,IAAMkB,MAAM,GAAGnB,WAAW,CAAC,UAACoB,QAAD,EAAc;IACxCF,WAAW,CAACG,OAAZ,GAAsBD,QAAtB;IACA,IAAIX,GAAJ,EAAS;MACR,IAAI,OAAOA,GAAP,KAAe,UAAnB,EAA+B;QAC9BA,GAAG,CAACW,QAAD,CAAH;MACA,CAFD,MAEO;QACNX,GAAG,CAACY,OAAJ,GAAcD,QAAd;MACA;IACD;EACD,CATyB,EASvB,CAACX,GAAD,CATuB,CAA1B;EAUA,IAAMa,SAAS,GAAGtB,WAAW,CAAC,UAACuB,KAAD,EAAW;IACxC,OAAOnB,aAAa,CACnBmB,KADmB,EAEnBL,WAAW,CAACG,OAFO,EAGnBV,KAHmB,EAInBC,MAJmB,EAKnBT,QALmB,CAApB;EAOA,CAR4B,EAQ1B,CAACe,WAAD,EAAcP,KAAd,EAAqBC,MAArB,EAA6BT,QAA7B,CAR0B,CAA7B;EAUA,IAAMqB,UAAU,GAAGxB,WAAW,CAAC,UAACuB,KAAD,EAAW;IACzC,IAAIlB,SAAJ,EAAe;MACdA,SAAS,CAACkB,KAAD,CAAT;IACA;IACD,OAAOjB,cAAc,CACpBiB,KADoB,EAEpBL,WAAW,CAACG,OAFQ,EAGpBV,KAHoB,EAIpBC,MAJoB,EAKpBT,QALoB,CAArB;EAOA,CAX6B,EAW3B,CAACe,WAAD,EAAcP,KAAd,EAAqBC,MAArB,EAA6BT,QAA7B,EAAuCE,SAAvC,CAX2B,CAA9B;EAaA,oBACCN,KAAA,CAAA0B,aAAA,CAACZ,cAAD,EAAAa,QAAA,KACKX,IADL;IAECN,GAAG,EAAEU,MAFN;IAGCT,KAAK,EAAEE,MAAM,CAACe,YAAY,CAACjB,KAAD,CAAZ,GAAsB,EAAtB,GAA2BA,KAA5B,CAAN,CAAyCkB,IAHjD;IAICvB,SAAS,EAAEmB,UAJZ;IAKCrB,QAAQ,EAAEmB;EALX,GADD;AAQA;AAEDf,KAAK,gBAAGR,KAAK,CAAC8B,UAAN,CAAiBtB,KAAjB,CAAR;AAEAA,KAAK,CAACuB,SAAN,GAAkB;EACjB;EACAnB,KAAK,EAAET,SAAS,CAAC6B,IAAV,CAAeC,UAFL;EAIjB;EACApB,MAAM,EAAEV,SAAS,CAAC6B,IAAV,CAAeC,UALN;EAOjB;EACAlB,cAAc,EAAEZ,SAAS,CAAC+B,WAAV,CAAsBD,UARrB;EAUjB;EACAE,IAAI,EAAEhC,SAAS,CAACiC,MAAV,CAAiBH,UAXN;EAajB;EACAtB,KAAK,EAAER,SAAS,CAACiC,MAdA;EAgBjB;EACAhC,QAAQ,EAAED,SAAS,CAAC6B,IAAV,CAAeC,UAjBR;EAmBjB;EACA3B,SAAS,EAAEH,SAAS,CAAC6B,IApBJ;EAqBjBK,KAAK,EAAElC,SAAS,CAAC6B,IArBA;EAsBjBM,OAAO,EAAEnC,SAAS,CAAC6B;AAtBF,CAAlB;AAyBAxB,KAAK,CAAC+B,YAAN,GAAqB;EACpB;EACAxB,cAAc,EAAE,OAFI;EAIpB;EACAoB,IAAI,EAAE;AALc,CAArB;AAQA,eAAe3B,KAAf;AAEA,SAASoB,YAATA,CAAsBjB,KAAtB,EAA6B;EAC5B,OAAOA,KAAK,KAAK6B,SAAV,IAAuB7B,KAAK,KAAK,IAAxC;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}