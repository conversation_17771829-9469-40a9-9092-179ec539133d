{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { operate } from '../util/lift';\nimport { mergeAll } from './mergeAll';\nimport { popNumber, popScheduler } from '../util/args';\nimport { from } from '../observable/from';\nexport function merge() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var scheduler = popScheduler(args);\n  var concurrent = popNumber(args, Infinity);\n  return operate(function (source, subscriber) {\n    mergeAll(concurrent)(from(__spreadArray([source], __read(args)), scheduler)).subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["operate", "mergeAll", "popNumber", "popScheduler", "from", "merge", "args", "_i", "arguments", "length", "scheduler", "concurrent", "Infinity", "source", "subscriber", "__spread<PERSON><PERSON>y", "__read", "subscribe"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\operators\\merge.ts"], "sourcesContent": ["import { ObservableInput, ObservableInputTuple, OperatorFunction, SchedulerLike } from '../types';\nimport { operate } from '../util/lift';\nimport { mergeAll } from './mergeAll';\nimport { popNumber, popScheduler } from '../util/args';\nimport { from } from '../observable/from';\n\n/** @deprecated Replaced with {@link mergeWith}. Will be removed in v8. */\nexport function merge<T, A extends readonly unknown[]>(...sources: [...ObservableInputTuple<A>]): OperatorFunction<T, T | A[number]>;\n/** @deprecated Replaced with {@link mergeWith}. Will be removed in v8. */\nexport function merge<T, A extends readonly unknown[]>(\n  ...sourcesAndConcurrency: [...ObservableInputTuple<A>, number]\n): OperatorFunction<T, T | A[number]>;\n/** @deprecated Replaced with {@link mergeWith}. Will be removed in v8. */\nexport function merge<T, A extends readonly unknown[]>(\n  ...sourcesAndScheduler: [...ObservableInputTuple<A>, SchedulerLike]\n): OperatorFunction<T, T | A[number]>;\n/** @deprecated Replaced with {@link mergeWith}. Will be removed in v8. */\nexport function merge<T, A extends readonly unknown[]>(\n  ...sourcesAndConcurrencyAndScheduler: [...ObservableInputTuple<A>, number, SchedulerLike]\n): OperatorFunction<T, T | A[number]>;\n\nexport function merge<T>(...args: unknown[]): OperatorFunction<T, unknown> {\n  const scheduler = popScheduler(args);\n  const concurrent = popNumber(args, Infinity);\n\n  return operate((source, subscriber) => {\n    mergeAll(concurrent)(from([source, ...(args as ObservableInput<T>[])], scheduler)).subscribe(subscriber);\n  });\n}\n"], "mappings": ";AACA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,SAASC,IAAI,QAAQ,oBAAoB;AAiBzC,OAAM,SAAUC,KAAKA,CAAA;EAAI,IAAAC,IAAA;OAAA,IAAAC,EAAA,IAAkB,EAAlBA,EAAA,GAAAC,SAAA,CAAAC,MAAkB,EAAlBF,EAAA,EAAkB;IAAlBD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EACvB,IAAMG,SAAS,GAAGP,YAAY,CAACG,IAAI,CAAC;EACpC,IAAMK,UAAU,GAAGT,SAAS,CAACI,IAAI,EAAEM,QAAQ,CAAC;EAE5C,OAAOZ,OAAO,CAAC,UAACa,MAAM,EAAEC,UAAU;IAChCb,QAAQ,CAACU,UAAU,CAAC,CAACP,IAAI,CAAAW,aAAA,EAAEF,MAAM,GAAAG,MAAA,CAAMV,IAA6B,IAAGI,SAAS,CAAC,CAAC,CAACO,SAAS,CAACH,UAAU,CAAC;EAC1G,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}