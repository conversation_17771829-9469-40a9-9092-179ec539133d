{"ast": null, "code": "function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\n\n// This \"state\" object simply holds the state of the \"AsYouType\" parser:\n//\n// * `country?: string`\n// * `callingCode?: string`\n// * `digits: string`\n// * `international: boolean`\n// * `missingPlus: boolean`\n// * `IDDPrefix?: string`\n// * `carrierCode?: string`\n// * `nationalPrefix?: string`\n// * `nationalSignificantNumber?: string`\n// * `nationalSignificantNumberMatchesInput: boolean`\n// * `complexPrefixBeforeNationalSignificantNumber?: string`\n//\n// `state.country` and `state.callingCode` aren't required to be in sync.\n// For example, `state.country` could be `\"AR\"` and `state.callingCode` could be `undefined`.\n// So `state.country` and `state.callingCode` are totally independent.\n//\nvar AsYouTypeState = /*#__PURE__*/function () {\n  function AsYouTypeState(_ref) {\n    var onCountryChange = _ref.onCountryChange,\n      onCallingCodeChange = _ref.onCallingCodeChange;\n    _classCallCheck(this, AsYouTypeState);\n    this.onCountryChange = onCountryChange;\n    this.onCallingCodeChange = onCallingCodeChange;\n  }\n  _createClass(AsYouTypeState, [{\n    key: \"reset\",\n    value: function reset(_ref2) {\n      var country = _ref2.country,\n        callingCode = _ref2.callingCode;\n      this.international = false;\n      this.missingPlus = false;\n      this.IDDPrefix = undefined;\n      this.callingCode = undefined;\n      this.digits = '';\n      this.resetNationalSignificantNumber();\n      this.initCountryAndCallingCode(country, callingCode);\n    }\n  }, {\n    key: \"resetNationalSignificantNumber\",\n    value: function resetNationalSignificantNumber() {\n      this.nationalSignificantNumber = this.getNationalDigits();\n      this.nationalSignificantNumberMatchesInput = true;\n      this.nationalPrefix = undefined;\n      this.carrierCode = undefined;\n      this.complexPrefixBeforeNationalSignificantNumber = undefined;\n    }\n  }, {\n    key: \"update\",\n    value: function update(properties) {\n      for (var _i = 0, _Object$keys = Object.keys(properties); _i < _Object$keys.length; _i++) {\n        var key = _Object$keys[_i];\n        this[key] = properties[key];\n      }\n    }\n  }, {\n    key: \"initCountryAndCallingCode\",\n    value: function initCountryAndCallingCode(country, callingCode) {\n      this.setCountry(country);\n      this.setCallingCode(callingCode);\n    }\n  }, {\n    key: \"setCountry\",\n    value: function setCountry(country) {\n      this.country = country;\n      this.onCountryChange(country);\n    }\n  }, {\n    key: \"setCallingCode\",\n    value: function setCallingCode(callingCode) {\n      this.callingCode = callingCode;\n      this.onCallingCodeChange(callingCode, this.country);\n    }\n  }, {\n    key: \"startInternationalNumber\",\n    value: function startInternationalNumber(country, callingCode) {\n      // Prepend the `+` to parsed input.\n      this.international = true; // If a default country was set then reset it\n      // because an explicitly international phone\n      // number is being entered.\n\n      this.initCountryAndCallingCode(country, callingCode);\n    }\n  }, {\n    key: \"appendDigits\",\n    value: function appendDigits(nextDigits) {\n      this.digits += nextDigits;\n    }\n  }, {\n    key: \"appendNationalSignificantNumberDigits\",\n    value: function appendNationalSignificantNumberDigits(nextDigits) {\n      this.nationalSignificantNumber += nextDigits;\n    }\n    /**\r\n     * Returns the part of `this.digits` that corresponds to the national number.\r\n     * Basically, all digits that have been input by the user, except for the\r\n     * international prefix and the country calling code part\r\n     * (if the number is an international one).\r\n     * @return {string}\r\n     */\n  }, {\n    key: \"getNationalDigits\",\n    value: function getNationalDigits() {\n      if (this.international) {\n        return this.digits.slice((this.IDDPrefix ? this.IDDPrefix.length : 0) + (this.callingCode ? this.callingCode.length : 0));\n      }\n      return this.digits;\n    }\n  }, {\n    key: \"getDigitsWithoutInternationalPrefix\",\n    value: function getDigitsWithoutInternationalPrefix() {\n      if (this.international) {\n        if (this.IDDPrefix) {\n          return this.digits.slice(this.IDDPrefix.length);\n        }\n      }\n      return this.digits;\n    }\n  }]);\n  return AsYouTypeState;\n}();\nexport { AsYouTypeState as default };", "map": {"version": 3, "names": ["AsYouTypeState", "_ref", "onCountryChange", "onCallingCodeChange", "_classCallCheck", "reset", "_ref2", "country", "callingCode", "international", "missingPlus", "IDDPrefix", "undefined", "digits", "resetNationalSignificantNumber", "initCountryAndCallingCode", "nationalSignificantNumber", "getNationalDigits", "nationalSignificantNumberMatchesInput", "nationalPrefix", "carrierCode", "complexPrefixBeforeNationalSignificantNumber", "update", "properties", "_i", "_Object$keys", "Object", "keys", "length", "key", "setCountry", "setCallingCode", "startInternationalNumber", "appendDigits", "nextDigits", "appendNationalSignificantNumberDigits", "slice", "getDigitsWithoutInternationalPrefix"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\AsYouTypeState.js"], "sourcesContent": ["// This \"state\" object simply holds the state of the \"AsYouType\" parser:\r\n//\r\n// * `country?: string`\r\n// * `callingCode?: string`\r\n// * `digits: string`\r\n// * `international: boolean`\r\n// * `missingPlus: boolean`\r\n// * `IDDPrefix?: string`\r\n// * `carrierCode?: string`\r\n// * `nationalPrefix?: string`\r\n// * `nationalSignificantNumber?: string`\r\n// * `nationalSignificantNumberMatchesInput: boolean`\r\n// * `complexPrefixBeforeNationalSignificantNumber?: string`\r\n//\r\n// `state.country` and `state.callingCode` aren't required to be in sync.\r\n// For example, `state.country` could be `\"AR\"` and `state.callingCode` could be `undefined`.\r\n// So `state.country` and `state.callingCode` are totally independent.\r\n//\r\nexport default class AsYouTypeState {\r\n\tconstructor({ onCountryChange, onCallingCodeChange }) {\r\n\t\tthis.onCountryChange = onCountryChange\r\n\t\tthis.onCallingCodeChange = onCallingCodeChange\r\n\t}\r\n\r\n\treset({ country, callingCode }) {\r\n\t\tthis.international = false\r\n\t\tthis.missingPlus = false\r\n\t\tthis.IDDPrefix = undefined\r\n\t\tthis.callingCode = undefined\r\n\t\tthis.digits = ''\r\n\t\tthis.resetNationalSignificantNumber()\r\n\t\tthis.initCountryAndCallingCode(country, callingCode)\r\n\t}\r\n\r\n\tresetNationalSignificantNumber() {\r\n\t\tthis.nationalSignificantNumber = this.getNationalDigits()\r\n\t\tthis.nationalSignificantNumberMatchesInput = true\r\n\t\tthis.nationalPrefix = undefined\r\n\t\tthis.carrierCode = undefined\r\n\t\tthis.complexPrefixBeforeNationalSignificantNumber = undefined\r\n\t}\r\n\r\n\tupdate(properties) {\r\n\t\tfor (const key of Object.keys(properties)) {\r\n\t\t\tthis[key] = properties[key]\r\n\t\t}\r\n\t}\r\n\r\n\tinitCountryAndCallingCode(country, callingCode) {\r\n\t\tthis.setCountry(country)\r\n\t\tthis.setCallingCode(callingCode)\r\n\t}\r\n\r\n\tsetCountry(country) {\r\n\t\tthis.country = country\r\n\t\tthis.onCountryChange(country)\r\n\t}\r\n\r\n\tsetCallingCode(callingCode) {\r\n\t\tthis.callingCode = callingCode\r\n\t\tthis.onCallingCodeChange(callingCode, this.country)\r\n\t}\r\n\r\n\tstartInternationalNumber(country, callingCode) {\r\n\t\t// Prepend the `+` to parsed input.\r\n\t\tthis.international = true\r\n\t\t// If a default country was set then reset it\r\n\t\t// because an explicitly international phone\r\n\t\t// number is being entered.\r\n\t\tthis.initCountryAndCallingCode(country, callingCode)\r\n\t}\r\n\r\n\tappendDigits(nextDigits) {\r\n\t\tthis.digits += nextDigits\r\n\t}\r\n\r\n\tappendNationalSignificantNumberDigits(nextDigits) {\r\n\t\tthis.nationalSignificantNumber += nextDigits\r\n\t}\r\n\r\n\t/**\r\n\t * Returns the part of `this.digits` that corresponds to the national number.\r\n\t * Basically, all digits that have been input by the user, except for the\r\n\t * international prefix and the country calling code part\r\n\t * (if the number is an international one).\r\n\t * @return {string}\r\n\t */\r\n\tgetNationalDigits() {\r\n\t\tif (this.international) {\r\n\t\t\treturn this.digits.slice(\r\n\t\t\t\t(this.IDDPrefix ? this.IDDPrefix.length : 0) +\r\n\t\t\t\t(this.callingCode ? this.callingCode.length : 0)\r\n\t\t\t)\r\n\t\t}\r\n\t\treturn this.digits\r\n\t}\r\n\r\n\tgetDigitsWithoutInternationalPrefix() {\r\n\t\tif (this.international) {\r\n\t\t\tif (this.IDDPrefix) {\r\n\t\t\t\treturn this.digits.slice(this.IDDPrefix.length)\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn this.digits\r\n\t}\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACqBA,c;EACpB,SAAAA,eAAAC,IAAA,EAAsD;IAAA,IAAxCC,eAAwC,GAAAD,IAAA,CAAxCC,eAAwC;MAAvBC,mBAAuB,GAAAF,IAAA,CAAvBE,mBAAuB;IAAAC,eAAA,OAAAJ,cAAA;IACrD,KAAKE,eAAL,GAAuBA,eAAvB;IACA,KAAKC,mBAAL,GAA2BA,mBAA3B;EACA;;;WAED,SAAAE,MAAAC,KAAA,EAAgC;MAAA,IAAxBC,OAAwB,GAAAD,KAAA,CAAxBC,OAAwB;QAAfC,WAAe,GAAAF,KAAA,CAAfE,WAAe;MAC/B,KAAKC,aAAL,GAAqB,KAArB;MACA,KAAKC,WAAL,GAAmB,KAAnB;MACA,KAAKC,SAAL,GAAiBC,SAAjB;MACA,KAAKJ,WAAL,GAAmBI,SAAnB;MACA,KAAKC,MAAL,GAAc,EAAd;MACA,KAAKC,8BAAL;MACA,KAAKC,yBAAL,CAA+BR,OAA/B,EAAwCC,WAAxC;IACA;;;WAED,SAAAM,+BAAA,EAAiC;MAChC,KAAKE,yBAAL,GAAiC,KAAKC,iBAAL,EAAjC;MACA,KAAKC,qCAAL,GAA6C,IAA7C;MACA,KAAKC,cAAL,GAAsBP,SAAtB;MACA,KAAKQ,WAAL,GAAmBR,SAAnB;MACA,KAAKS,4CAAL,GAAoDT,SAApD;IACA;;;WAED,SAAAU,OAAOC,UAAP,EAAmB;MAClB,SAAAC,EAAA,MAAAC,YAAA,GAAkBC,MAAM,CAACC,IAAP,CAAYJ,UAAZ,CAAlB,EAAAC,EAAA,GAAAC,YAAA,CAAAG,MAAA,EAAAJ,EAAA,IAA2C;QAAtC,IAAMK,GAAG,GAAAJ,YAAA,CAAAD,EAAA,CAAT;QACJ,KAAKK,GAAL,IAAYN,UAAU,CAACM,GAAD,CAAtB;MACA;IACD;;;WAED,SAAAd,0BAA0BR,OAA1B,EAAmCC,WAAnC,EAAgD;MAC/C,KAAKsB,UAAL,CAAgBvB,OAAhB;MACA,KAAKwB,cAAL,CAAoBvB,WAApB;IACA;;;WAED,SAAAsB,WAAWvB,OAAX,EAAoB;MACnB,KAAKA,OAAL,GAAeA,OAAf;MACA,KAAKL,eAAL,CAAqBK,OAArB;IACA;;;WAED,SAAAwB,eAAevB,WAAf,EAA4B;MAC3B,KAAKA,WAAL,GAAmBA,WAAnB;MACA,KAAKL,mBAAL,CAAyBK,WAAzB,EAAsC,KAAKD,OAA3C;IACA;;;WAED,SAAAyB,yBAAyBzB,OAAzB,EAAkCC,WAAlC,EAA+C;MAC9C;MACA,KAAKC,aAAL,GAAqB,IAArB,CAF8C,CAG9C;MACA;MACA;;MACA,KAAKM,yBAAL,CAA+BR,OAA/B,EAAwCC,WAAxC;IACA;;;WAED,SAAAyB,aAAaC,UAAb,EAAyB;MACxB,KAAKrB,MAAL,IAAeqB,UAAf;IACA;;;WAED,SAAAC,sCAAsCD,UAAtC,EAAkD;MACjD,KAAKlB,yBAAL,IAAkCkB,UAAlC;IACA;IAED;AACD;AACA;AACA;AACA;AACA;AACA;;;WACC,SAAAjB,kBAAA,EAAoB;MACnB,IAAI,KAAKR,aAAT,EAAwB;QACvB,OAAO,KAAKI,MAAL,CAAYuB,KAAZ,CACN,CAAC,KAAKzB,SAAL,GAAiB,KAAKA,SAAL,CAAeiB,MAAhC,GAAyC,CAA1C,KACC,KAAKpB,WAAL,GAAmB,KAAKA,WAAL,CAAiBoB,MAApC,GAA6C,CAD9C,CADM,CAAP;MAIA;MACD,OAAO,KAAKf,MAAZ;IACA;;;WAED,SAAAwB,oCAAA,EAAsC;MACrC,IAAI,KAAK5B,aAAT,EAAwB;QACvB,IAAI,KAAKE,SAAT,EAAoB;UACnB,OAAO,KAAKE,MAAL,CAAYuB,KAAZ,CAAkB,KAAKzB,SAAL,CAAeiB,MAAjC,CAAP;QACA;MACD;MACD,OAAO,KAAKf,MAAZ;IACA;;;;SAtFmBb,c"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}