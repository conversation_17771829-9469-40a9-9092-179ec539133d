{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { observeOn } from '../operators/observeOn';\nimport { subscribeOn } from '../operators/subscribeOn';\nexport function scheduleObservable(input, scheduler) {\n  return innerFrom(input).pipe(subscribeOn(scheduler), observeOn(scheduler));\n}", "map": {"version": 3, "names": ["innerFrom", "observeOn", "subscribeOn", "scheduleObservable", "input", "scheduler", "pipe"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\scheduled\\scheduleObservable.ts"], "sourcesContent": ["import { innerFrom } from '../observable/innerFrom';\nimport { observeOn } from '../operators/observeOn';\nimport { subscribeOn } from '../operators/subscribeOn';\nimport { InteropObservable, SchedulerLike } from '../types';\n\nexport function scheduleObservable<T>(input: InteropObservable<T>, scheduler: SchedulerLike) {\n  return innerFrom(input).pipe(subscribeOn(scheduler), observeOn(scheduler));\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,yBAAyB;AACnD,SAASC,SAAS,QAAQ,wBAAwB;AAClD,SAASC,WAAW,QAAQ,0BAA0B;AAGtD,OAAM,SAAUC,kBAAkBA,CAAIC,KAA2B,EAAEC,SAAwB;EACzF,OAAOL,SAAS,CAACI,KAAK,CAAC,CAACE,IAAI,CAACJ,WAAW,CAACG,SAAS,CAAC,EAAEJ,SAAS,CAACI,SAAS,CAAC,CAAC;AAC5E"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}