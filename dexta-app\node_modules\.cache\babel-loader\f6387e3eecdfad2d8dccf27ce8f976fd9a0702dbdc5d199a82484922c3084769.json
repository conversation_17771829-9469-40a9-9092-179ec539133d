{"ast": null, "code": "import { scheduleObservable } from './scheduleObservable';\nimport { schedulePromise } from './schedulePromise';\nimport { scheduleArray } from './scheduleArray';\nimport { scheduleIterable } from './scheduleIterable';\nimport { scheduleAsyncIterable } from './scheduleAsyncIterable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isPromise } from '../util/isPromise';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isIterable } from '../util/isIterable';\nimport { isAsyncIterable } from '../util/isAsyncIterable';\nimport { createInvalidObservableTypeError } from '../util/throwUnobservableError';\nimport { isReadableStreamLike } from '../util/isReadableStreamLike';\nimport { scheduleReadableStreamLike } from './scheduleReadableStreamLike';\nexport function scheduled(input, scheduler) {\n  if (input != null) {\n    if (isInteropObservable(input)) {\n      return scheduleObservable(input, scheduler);\n    }\n    if (isArrayLike(input)) {\n      return scheduleArray(input, scheduler);\n    }\n    if (isPromise(input)) {\n      return schedulePromise(input, scheduler);\n    }\n    if (isAsyncIterable(input)) {\n      return scheduleAsyncIterable(input, scheduler);\n    }\n    if (isIterable(input)) {\n      return scheduleIterable(input, scheduler);\n    }\n    if (isReadableStreamLike(input)) {\n      return scheduleReadableStreamLike(input, scheduler);\n    }\n  }\n  throw createInvalidObservableTypeError(input);\n}", "map": {"version": 3, "names": ["scheduleObservable", "schedulePromise", "scheduleArray", "scheduleIterable", "scheduleAsyncIterable", "isInteropObservable", "isPromise", "isArrayLike", "isIterable", "isAsyncIterable", "createInvalidObservableTypeError", "isReadableStreamLike", "scheduleReadableStreamLike", "scheduled", "input", "scheduler"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\rxjs\\src\\internal\\scheduled\\scheduled.ts"], "sourcesContent": ["import { scheduleObservable } from './scheduleObservable';\nimport { schedulePromise } from './schedulePromise';\nimport { scheduleArray } from './scheduleArray';\nimport { scheduleIterable } from './scheduleIterable';\nimport { scheduleAsyncIterable } from './scheduleAsyncIterable';\nimport { isInteropObservable } from '../util/isInteropObservable';\nimport { isPromise } from '../util/isPromise';\nimport { isArrayLike } from '../util/isArrayLike';\nimport { isIterable } from '../util/isIterable';\nimport { ObservableInput, SchedulerLike } from '../types';\nimport { Observable } from '../Observable';\nimport { isAsyncIterable } from '../util/isAsyncIterable';\nimport { createInvalidObservableTypeError } from '../util/throwUnobservableError';\nimport { isReadableStreamLike } from '../util/isReadableStreamLike';\nimport { scheduleReadableStreamLike } from './scheduleReadableStreamLike';\n\n/**\n * Converts from a common {@link ObservableInput} type to an observable where subscription and emissions\n * are scheduled on the provided scheduler.\n *\n * @see {@link from}\n * @see {@link of}\n *\n * @param input The observable, array, promise, iterable, etc you would like to schedule\n * @param scheduler The scheduler to use to schedule the subscription and emissions from\n * the returned observable.\n */\nexport function scheduled<T>(input: ObservableInput<T>, scheduler: SchedulerLike): Observable<T> {\n  if (input != null) {\n    if (isInteropObservable(input)) {\n      return scheduleObservable(input, scheduler);\n    }\n    if (isArrayLike(input)) {\n      return scheduleArray(input, scheduler);\n    }\n    if (isPromise(input)) {\n      return schedulePromise(input, scheduler);\n    }\n    if (isAsyncIterable(input)) {\n      return scheduleAsyncIterable(input, scheduler);\n    }\n    if (isIterable(input)) {\n      return scheduleIterable(input, scheduler);\n    }\n    if (isReadableStreamLike(input)) {\n      return scheduleReadableStreamLike(input, scheduler);\n    }\n  }\n  throw createInvalidObservableTypeError(input);\n}\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,mBAAmB,QAAQ,6BAA6B;AACjE,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,UAAU,QAAQ,oBAAoB;AAG/C,SAASC,eAAe,QAAQ,yBAAyB;AACzD,SAASC,gCAAgC,QAAQ,gCAAgC;AACjF,SAASC,oBAAoB,QAAQ,8BAA8B;AACnE,SAASC,0BAA0B,QAAQ,8BAA8B;AAazE,OAAM,SAAUC,SAASA,CAAIC,KAAyB,EAAEC,SAAwB;EAC9E,IAAID,KAAK,IAAI,IAAI,EAAE;IACjB,IAAIT,mBAAmB,CAACS,KAAK,CAAC,EAAE;MAC9B,OAAOd,kBAAkB,CAACc,KAAK,EAAEC,SAAS,CAAC;;IAE7C,IAAIR,WAAW,CAACO,KAAK,CAAC,EAAE;MACtB,OAAOZ,aAAa,CAACY,KAAK,EAAEC,SAAS,CAAC;;IAExC,IAAIT,SAAS,CAACQ,KAAK,CAAC,EAAE;MACpB,OAAOb,eAAe,CAACa,KAAK,EAAEC,SAAS,CAAC;;IAE1C,IAAIN,eAAe,CAACK,KAAK,CAAC,EAAE;MAC1B,OAAOV,qBAAqB,CAACU,KAAK,EAAEC,SAAS,CAAC;;IAEhD,IAAIP,UAAU,CAACM,KAAK,CAAC,EAAE;MACrB,OAAOX,gBAAgB,CAACW,KAAK,EAAEC,SAAS,CAAC;;IAE3C,IAAIJ,oBAAoB,CAACG,KAAK,CAAC,EAAE;MAC/B,OAAOF,0BAA0B,CAACE,KAAK,EAAEC,SAAS,CAAC;;;EAGvD,MAAML,gCAAgC,CAACI,KAAK,CAAC;AAC/C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}