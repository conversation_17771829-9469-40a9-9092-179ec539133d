{"ast": null, "code": "import * as React from 'react';\n\n/**\n * @ignore - internal component.\n */\nconst Context = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== 'production') {\n  Context.displayName = 'TabsContext';\n}\n/**\n * @returns {unknown}\n */\n\nexport function useTabContext() {\n  return React.useContext(Context);\n}\nexport function getPanelId(context, value) {\n  const {\n    idPrefix\n  } = context;\n  if (idPrefix === null) {\n    return null;\n  }\n  return `${context.idPrefix}-P-${value}`;\n}\nexport function getTabId(context, value) {\n  const {\n    idPrefix\n  } = context;\n  if (idPrefix === null) {\n    return null;\n  }\n  return `${context.idPrefix}-T-${value}`;\n}\nexport default Context;", "map": {"version": 3, "names": ["React", "Context", "createContext", "process", "env", "NODE_ENV", "displayName", "useTabContext", "useContext", "getPanelId", "context", "value", "idPrefix", "getTabId"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/@mui/base/TabsUnstyled/TabsContext.js"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * @ignore - internal component.\n */\nconst Context = /*#__PURE__*/React.createContext(null);\n\nif (process.env.NODE_ENV !== 'production') {\n  Context.displayName = 'TabsContext';\n}\n/**\n * @returns {unknown}\n */\n\n\nexport function useTabContext() {\n  return React.useContext(Context);\n}\nexport function getPanelId(context, value) {\n  const {\n    idPrefix\n  } = context;\n\n  if (idPrefix === null) {\n    return null;\n  }\n\n  return `${context.idPrefix}-P-${value}`;\n}\nexport function getTabId(context, value) {\n  const {\n    idPrefix\n  } = context;\n\n  if (idPrefix === null) {\n    return null;\n  }\n\n  return `${context.idPrefix}-T-${value}`;\n}\nexport default Context;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA,MAAMC,OAAO,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAEtD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,aAAa;AACrC;AACA;AACA;AACA;;AAGA,OAAO,SAASC,aAAaA,CAAA,EAAG;EAC9B,OAAOP,KAAK,CAACQ,UAAU,CAACP,OAAO,CAAC;AAClC;AACA,OAAO,SAASQ,UAAUA,CAACC,OAAO,EAAEC,KAAK,EAAE;EACzC,MAAM;IACJC;EACF,CAAC,GAAGF,OAAO;EAEX,IAAIE,QAAQ,KAAK,IAAI,EAAE;IACrB,OAAO,IAAI;EACb;EAEA,OAAQ,GAAEF,OAAO,CAACE,QAAS,MAAKD,KAAM,EAAC;AACzC;AACA,OAAO,SAASE,QAAQA,CAACH,OAAO,EAAEC,KAAK,EAAE;EACvC,MAAM;IACJC;EACF,CAAC,GAAGF,OAAO;EAEX,IAAIE,QAAQ,KAAK,IAAI,EAAE;IACrB,OAAO,IAAI;EACb;EAEA,OAAQ,GAAEF,OAAO,CAACE,QAAS,MAAKD,KAAM,EAAC;AACzC;AACA,eAAeV,OAAO"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}