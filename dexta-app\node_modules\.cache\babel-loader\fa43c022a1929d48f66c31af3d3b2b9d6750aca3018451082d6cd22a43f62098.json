{"ast": null, "code": "var ListCache = require('./_ListCache');\n\n/**\n * Removes all key-value entries from the stack.\n *\n * @private\n * @name clear\n * @memberOf Stack\n */\nfunction stackClear() {\n  this.__data__ = new ListCache();\n  this.size = 0;\n}\nmodule.exports = stackClear;", "map": {"version": 3, "names": ["ListCache", "require", "stackClear", "__data__", "size", "module", "exports"], "sources": ["D:/CodeFreaks/dexta-ksa/dexta-app/node_modules/lodash/_stackClear.js"], "sourcesContent": ["var ListCache = require('./_ListCache');\n\n/**\n * Removes all key-value entries from the stack.\n *\n * @private\n * @name clear\n * @memberOf Stack\n */\nfunction stackClear() {\n  this.__data__ = new ListCache;\n  this.size = 0;\n}\n\nmodule.exports = stackClear;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAAA,EAAG;EACpB,IAAI,CAACC,QAAQ,GAAG,IAAIH,SAAS,CAAD,CAAC;EAC7B,IAAI,CAACI,IAAI,GAAG,CAAC;AACf;AAEAC,MAAM,CAACC,OAAO,GAAGJ,UAAU"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}