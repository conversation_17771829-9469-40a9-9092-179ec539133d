{"ast": null, "code": "import { Injector, RediError, Quantity } from '@wendellhu/redi';\nimport * as React from 'react';\nimport React__default, { useState, useEffect, useRef, useMemo, createContext, useCallback, useContext } from 'react';\nimport { BehaviorSubject } from 'rxjs';\nvar __REDI_CONTEXT_LOCK__ = 'REDI_CONTEXT_LOCK';\n// eslint-disable-next-line node/prefer-global/process\nvar isNode = typeof process !== 'undefined' && process.versions != null && process.versions.node != null;\nvar globalObject = typeof globalThis !== 'undefined' && globalThis || typeof window !== 'undefined' && window\n// eslint-disable-next-line no-restricted-globals\n|| typeof global !== 'undefined' && global;\nif (!globalObject[__REDI_CONTEXT_LOCK__]) {\n  globalObject[__REDI_CONTEXT_LOCK__] = true;\n} else if (!isNode) {\n  console.error('[redi]: \"RediContext\" is already created. You may import \"RediContext\" from different paths. Use \"import { RediContext } from \\'@wendellhu/redi/react-bindings\\'; instead.\"');\n}\nvar RediContext = React.createContext({\n  injector: null\n});\nRediContext.displayName = 'RediContext';\nvar RediProvider = RediContext.Provider;\nvar RediConsumer = RediContext.Consumer;\nvar __assign = undefined && undefined.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nfunction RediInjector(props) {\n  var children = props.children,\n    dependencies = props.dependencies;\n  var childInjectorRef = React.useRef(null);\n  // dispose the injector when the container Injector unmounts\n  React.useEffect(function () {\n    return function () {\n      var _a;\n      return (_a = childInjectorRef.current) === null || _a === void 0 ? void 0 : _a.dispose();\n    };\n  }, []);\n  return React.createElement(RediConsumer, null, function (context) {\n    var childInjector;\n    if (childInjectorRef.current) {\n      childInjector = childInjectorRef.current;\n    } else {\n      childInjector = context.injector ? context.injector.createChild(dependencies) : new Injector(dependencies);\n      childInjectorRef.current = childInjector;\n    }\n    return React.createElement(RediProvider, {\n      value: {\n        injector: childInjector\n      }\n    }, children);\n  });\n}\n/**\n * @param Comp\n * @param injector\n * @returns A component type that can be rendered.\n */\nfunction connectInjector(Comp, injector) {\n  return function ComponentWithInjector(props) {\n    return React.createElement(RediProvider, {\n      value: {\n        injector: injector\n      }\n    }, React.createElement(Comp, __assign({}, props)));\n  };\n}\nfunction connectDependencies(Comp, dependencies) {\n  return function ComponentWithInjector(props) {\n    return React.createElement(RediInjector, {\n      dependencies: dependencies\n    }, React.createElement(Comp, __assign({}, props)));\n  };\n}\nvar __extends$1 = undefined && undefined.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nvar ClassComponentNotInRediContextError = /** @class */function (_super) {\n  __extends$1(ClassComponentNotInRediContextError, _super);\n  function ClassComponentNotInRediContextError(component) {\n    return _super.call(this, \"You should make \\\"RediContext\\\" as \".concat(component.constructor.name, \"'s default context type. \") + 'If you want to use multiple context, please check this on React doc site. ' + 'https://reactjs.org/docs/context.html#classcontexttype') || this;\n  }\n  return ClassComponentNotInRediContextError;\n}(RediError);\nfunction WithDependency(id, quantity, lookUp) {\n  return function () {\n    return {\n      get: function () {\n        var thisComponent = this;\n        var context = thisComponent.context;\n        if (!context || !context.injector) {\n          throw new ClassComponentNotInRediContextError(thisComponent);\n        }\n        var injector = context.injector;\n        var thing = injector.get(id, quantity || Quantity.REQUIRED, lookUp);\n        return thing;\n      }\n    };\n  };\n}\nvar __extends = undefined && undefined.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nvar HooksNotInRediContextError = /** @class */function (_super) {\n  __extends(HooksNotInRediContextError, _super);\n  function HooksNotInRediContextError() {\n    return _super.call(this, 'Using dependency injection outside of a RediContext.') || this;\n  }\n  return HooksNotInRediContextError;\n}(RediError);\nfunction useInjector() {\n  var injectionContext = React.useContext(RediContext);\n  if (!injectionContext.injector) {\n    throw new HooksNotInRediContextError();\n  }\n  return injectionContext.injector;\n}\nfunction useDependency(id, quantityOrLookUp, lookUp) {\n  var injector = useInjector();\n  return React.useMemo(function () {\n    return injector.get(id, quantityOrLookUp, lookUp);\n  }, [id, quantityOrLookUp, lookUp]);\n}\nvar __spreadArray = undefined && undefined.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\n/**\n * unwrap an observable value, return it to the component for rendering, and\n * trigger re-render when value changes\n *\n * **IMPORTANT**. Parent and child components better not subscribe to the same\n * observable, otherwise unnecessary re-render would be triggered. Instead, the\n * top-most component should subscribe and pass value of the observable to\n * its offspring, by props or context. Please consider using `useDependencyContext` and\n * `useDependencyContextValue` in this case.\n *\n * @deprecated Please use `useObservable` instead.\n */\nfunction useDependencyValue(depValue$, defaultValue) {\n  var firstValue = depValue$ instanceof BehaviorSubject && typeof defaultValue === 'undefined' ? depValue$.getValue() : defaultValue;\n  var _a = useState(firstValue),\n    value = _a[0],\n    setValue = _a[1];\n  useEffect(function () {\n    var subscription = depValue$.subscribe(function (val) {\n      return setValue(val);\n    });\n    return function () {\n      return subscription.unsubscribe();\n    };\n  }, [depValue$]);\n  return value;\n}\nfunction unwrap(o) {\n  if (typeof o === 'function') {\n    return o();\n  }\n  return o;\n}\n/**\n * Subscribe to an observable and return its value. The component will re-render when the observable emits a new value.\n *\n * @param observable An observable or a function that returns an observable\n * @param defaultValue The default value of the observable. It the `observable` can omit an initial value, this value will be neglected.\n * @param shouldHaveSyncValue If the observable should have a sync value. If it does not have a sync value, an error will be thrown.\n * @param deps A dependency array to decide if we should re-subscribe when the `observable` is a function.\n * @returns Value or null.\n */\nfunction useObservable(observable, defaultValue, shouldHaveSyncValue, deps) {\n  if (typeof observable === 'function' && !deps) {\n    throw new RediError('Expected deps to be provided when observable is a function!');\n  }\n  var observableRef = useRef(null);\n  var initializedRef = useRef(false);\n  var destObservable = useMemo(function () {\n    return observable;\n  }, __spreadArray([], typeof deps !== 'undefined' ? deps : [observable], true));\n  // This state is only for trigger React to re-render. We do not use `setValue` directly because it may cause\n  // memory leaking.\n  var _a = useState(0),\n    _ = _a[0],\n    setRenderCounter = _a[1];\n  var valueRef = useRef(function () {\n    var innerDefaultValue;\n    if (destObservable) {\n      var sub = unwrap(destObservable).subscribe(function (value) {\n        initializedRef.current = true;\n        innerDefaultValue = value;\n      });\n      sub.unsubscribe();\n    }\n    return innerDefaultValue !== null && innerDefaultValue !== void 0 ? innerDefaultValue : defaultValue;\n  }());\n  useEffect(function () {\n    var subscription = null;\n    if (destObservable) {\n      observableRef.current = unwrap(destObservable);\n      subscription = observableRef.current.subscribe(function (value) {\n        valueRef.current = value;\n        setRenderCounter(function (prev) {\n          return prev + 1;\n        });\n      });\n    }\n    return function () {\n      return subscription === null || subscription === void 0 ? void 0 : subscription.unsubscribe();\n    };\n  }, [destObservable]);\n  if (shouldHaveSyncValue && !initializedRef.current) {\n    throw new Error('Expect `shouldHaveSyncValue` but not getting a sync value!');\n  }\n  return valueRef.current;\n}\n/**\n * subscribe to a signal that emits whenever data updates and re-render\n *\n * @param update$ a signal that the data the functional component depends has updated\n */\nfunction useUpdateBinder(update$) {\n  var _a = useState(0),\n    dumpSet = _a[1];\n  useEffect(function () {\n    var subscription = update$.subscribe(function () {\n      return dumpSet(function (prev) {\n        return prev + 1;\n      });\n    });\n    return function () {\n      return subscription.unsubscribe();\n    };\n  }, []);\n}\nvar DepValueMapProvider = new WeakMap();\n/**\n * subscribe to an observable value from a service, creating a context for it so\n * it child component won't have to subscribe again and cause unnecessary\n */\nfunction useDependencyContext(depValue$, defaultValue) {\n  var depRef = useRef(undefined);\n  var value = useDependencyValue(depValue$, defaultValue);\n  var Context = useMemo(function () {\n    return createContext(value);\n  }, [depValue$]);\n  var Provider = useCallback(function (props) {\n    return React__default.createElement(Context.Provider, {\n      value: value\n    }, props.children);\n  }, [depValue$, value]);\n  if (depRef.current !== depValue$) {\n    if (depRef.current) {\n      DepValueMapProvider.delete(depRef.current);\n    }\n    depRef.current = depValue$;\n    DepValueMapProvider.set(depValue$, Context);\n  }\n  return {\n    Provider: Provider,\n    value: value\n  };\n}\nfunction useDependencyContextValue(depValue$) {\n  var context = DepValueMapProvider.get(depValue$);\n  if (!context) {\n    throw new RediError(\"try to read context value but no ancestor component subscribed it.\");\n  }\n  return useContext(context);\n}\nexport { RediConsumer, RediContext, RediProvider, WithDependency, connectDependencies, connectInjector, useDependency, useDependencyContext, useDependencyContextValue, useDependencyValue, useInjector, useObservable, useUpdateBinder };", "map": {"version": 3, "names": ["__REDI_CONTEXT_LOCK__", "isNode", "process", "versions", "node", "globalObject", "globalThis", "window", "global", "console", "error", "RediContext", "React", "createContext", "injector", "displayName", "RediProvider", "Provider", "RediConsumer", "Consumer", "__assign", "undefined", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "RediInjector", "props", "children", "dependencies", "childInjectorRef", "useRef", "useEffect", "_a", "current", "dispose", "createElement", "context", "childInjector", "create<PERSON><PERSON>d", "Injector", "value", "connectInjector", "Comp", "ComponentWithInjector", "connectDependencies", "__extends$1", "__extends", "extendStatics", "d", "b", "setPrototypeOf", "__proto__", "Array", "TypeError", "String", "__", "constructor", "create", "ClassComponentNotInRediContextError", "_super", "component", "concat", "name", "RediError", "WithDependency", "id", "quantity", "lookUp", "get", "thisComponent", "thing", "Quantity", "REQUIRED", "HooksNotInRediContextError", "useInjector", "injectionContext", "useContext", "useDependency", "quantityOrLookUp", "useMemo", "__spread<PERSON><PERSON>y", "to", "from", "pack", "l", "ar", "slice", "useDependencyValue", "depValue$", "defaultValue", "firstValue", "BehaviorSubject", "getValue", "useState", "setValue", "subscription", "subscribe", "val", "unsubscribe", "unwrap", "o", "useObservable", "observable", "shouldHaveSyncValue", "deps", "observableRef", "initializedRef", "destObservable", "_", "set<PERSON><PERSON><PERSON>ou<PERSON>", "valueRef", "innerDefaultValue", "sub", "prev", "Error", "useUpdateBinder", "update$", "dumpSet", "DepValueMapProvider", "WeakMap", "useDependencyContext", "depRef", "Context", "useCallback", "React__default", "delete", "set", "useDependencyContextValue"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\@wendellhu\\redi\\esm\\react-bindings\\reactContext.js", "D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\@wendellhu\\redi\\esm\\react-bindings\\reactComponent.js", "D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\@wendellhu\\redi\\esm\\react-bindings\\reactDecorators.js", "D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\@wendellhu\\redi\\esm\\react-bindings\\reactHooks.js", "D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\@wendellhu\\redi\\esm\\react-bindings\\reactRx.js"], "sourcesContent": ["import * as React from 'react';\nvar __REDI_CONTEXT_LOCK__ = 'REDI_CONTEXT_LOCK';\n// eslint-disable-next-line node/prefer-global/process\nvar isNode = typeof process !== 'undefined' && process.versions != null && process.versions.node != null;\nvar globalObject = (typeof globalThis !== 'undefined' && globalThis)\n    || (typeof window !== 'undefined' && window)\n    // eslint-disable-next-line no-restricted-globals\n    || (typeof global !== 'undefined' && global);\nif (!globalObject[__REDI_CONTEXT_LOCK__]) {\n    globalObject[__REDI_CONTEXT_LOCK__] = true;\n}\nelse if (!isNode) {\n    console.error('[redi]: \"RediContext\" is already created. You may import \"RediContext\" from different paths. Use \"import { RediContext } from \\'@wendellhu/redi/react-bindings\\'; instead.\"');\n}\nexport var RediContext = React.createContext({\n    injector: null,\n});\nRediContext.displayName = 'RediContext';\nexport var RediProvider = RediContext.Provider;\nexport var RediConsumer = RediContext.Consumer;\n//# sourceMappingURL=reactContext.js.map", "var __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nimport { Injector } from '@wendellhu/redi';\nimport * as React from 'react';\nimport { RediConsumer, RediProvider } from './reactContext';\nfunction RediInjector(props) {\n    var children = props.children, dependencies = props.dependencies;\n    var childInjectorRef = React.useRef(null);\n    // dispose the injector when the container Injector unmounts\n    React.useEffect(function () { return function () { var _a; return (_a = childInjectorRef.current) === null || _a === void 0 ? void 0 : _a.dispose(); }; }, []);\n    return (React.createElement(RediConsumer, null, function (context) {\n        var childInjector;\n        if (childInjectorRef.current) {\n            childInjector = childInjectorRef.current;\n        }\n        else {\n            childInjector = context.injector\n                ? context.injector.createChild(dependencies)\n                : new Injector(dependencies);\n            childInjectorRef.current = childInjector;\n        }\n        return (React.createElement(RediProvider, { value: { injector: childInjector } }, children));\n    }));\n}\n/**\n * @param Comp\n * @param injector\n * @returns A component type that can be rendered.\n */\nexport function connectInjector(Comp, injector) {\n    return function ComponentWithInjector(props) {\n        return (React.createElement(RediProvider, { value: { injector: injector } },\n            React.createElement(Comp, __assign({}, props))));\n    };\n}\nexport function connectDependencies(Comp, dependencies) {\n    return function ComponentWithInjector(props) {\n        return (React.createElement(RediInjector, { dependencies: dependencies },\n            React.createElement(Comp, __assign({}, props))));\n    };\n}\n//# sourceMappingURL=reactComponent.js.map", "var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { Quantity, RediError, } from '@wendellhu/redi';\nvar ClassComponentNotInRediContextError = /** @class */ (function (_super) {\n    __extends(ClassComponentNotInRediContextError, _super);\n    function ClassComponentNotInRediContextError(component) {\n        return _super.call(this, \"You should make \\\"RediContext\\\" as \".concat(component.constructor.name, \"'s default context type. \")\n            + 'If you want to use multiple context, please check this on React doc site. '\n            + 'https://reactjs.org/docs/context.html#classcontexttype') || this;\n    }\n    return ClassComponentNotInRediContextError;\n}(RediError));\nexport function WithDependency(id, quantity, lookUp) {\n    return function () {\n        return {\n            get: function () {\n                var thisComponent = this;\n                var context = thisComponent.context;\n                if (!context || !context.injector) {\n                    throw new ClassComponentNotInRediContextError(thisComponent);\n                }\n                var injector = context.injector;\n                var thing = injector.get(id, quantity || Quantity.REQUIRED, lookUp);\n                return thing;\n            },\n        };\n    };\n}\n//# sourceMappingURL=reactDecorators.js.map", "var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { RediError, } from '@wendellhu/redi';\nimport * as React from 'react';\nimport { RediContext } from './reactContext';\nvar HooksNotInRediContextError = /** @class */ (function (_super) {\n    __extends(HooksNotInRediContextError, _super);\n    function HooksNotInRediContextError() {\n        return _super.call(this, 'Using dependency injection outside of a RediContext.') || this;\n    }\n    return HooksNotInRediContextError;\n}(RediError));\nexport function useInjector() {\n    var injectionContext = React.useContext(RediContext);\n    if (!injectionContext.injector) {\n        throw new HooksNotInRediContextError();\n    }\n    return injectionContext.injector;\n}\nexport function useDependency(id, quantityOrLookUp, lookUp) {\n    var injector = useInjector();\n    return React.useMemo(function () { return injector.get(id, quantityOrLookUp, lookUp); }, [id, quantityOrLookUp, lookUp]);\n}\n//# sourceMappingURL=reactHooks.js.map", "var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport { RediError } from '@wendellhu/redi';\nimport React, { createContext, useCallback, useContext, useEffect, useMemo, useRef, useState, } from 'react';\nimport { BehaviorSubject } from 'rxjs';\n/**\n * unwrap an observable value, return it to the component for rendering, and\n * trigger re-render when value changes\n *\n * **IMPORTANT**. Parent and child components better not subscribe to the same\n * observable, otherwise unnecessary re-render would be triggered. Instead, the\n * top-most component should subscribe and pass value of the observable to\n * its offspring, by props or context. Please consider using `useDependencyContext` and\n * `useDependencyContextValue` in this case.\n *\n * @deprecated Please use `useObservable` instead.\n */\nexport function useDependencyValue(depValue$, defaultValue) {\n    var firstValue = depValue$ instanceof BehaviorSubject && typeof defaultValue === 'undefined'\n        ? depValue$.getValue()\n        : defaultValue;\n    var _a = useState(firstValue), value = _a[0], setValue = _a[1];\n    useEffect(function () {\n        var subscription = depValue$.subscribe(function (val) { return setValue(val); });\n        return function () { return subscription.unsubscribe(); };\n    }, [depValue$]);\n    return value;\n}\nfunction unwrap(o) {\n    if (typeof o === 'function') {\n        return o();\n    }\n    return o;\n}\n/**\n * Subscribe to an observable and return its value. The component will re-render when the observable emits a new value.\n *\n * @param observable An observable or a function that returns an observable\n * @param defaultValue The default value of the observable. It the `observable` can omit an initial value, this value will be neglected.\n * @param shouldHaveSyncValue If the observable should have a sync value. If it does not have a sync value, an error will be thrown.\n * @param deps A dependency array to decide if we should re-subscribe when the `observable` is a function.\n * @returns Value or null.\n */\nexport function useObservable(observable, defaultValue, shouldHaveSyncValue, deps) {\n    if (typeof observable === 'function' && !deps) {\n        throw new RediError('Expected deps to be provided when observable is a function!');\n    }\n    var observableRef = useRef(null);\n    var initializedRef = useRef(false);\n    var destObservable = useMemo(function () { return observable; }, __spreadArray([], (typeof deps !== 'undefined' ? deps : [observable]), true));\n    // This state is only for trigger React to re-render. We do not use `setValue` directly because it may cause\n    // memory leaking.\n    var _a = useState(0), _ = _a[0], setRenderCounter = _a[1];\n    var valueRef = useRef((function () {\n        var innerDefaultValue;\n        if (destObservable) {\n            var sub = unwrap(destObservable).subscribe(function (value) {\n                initializedRef.current = true;\n                innerDefaultValue = value;\n            });\n            sub.unsubscribe();\n        }\n        return innerDefaultValue !== null && innerDefaultValue !== void 0 ? innerDefaultValue : defaultValue;\n    })());\n    useEffect(function () {\n        var subscription = null;\n        if (destObservable) {\n            observableRef.current = unwrap(destObservable);\n            subscription = observableRef.current.subscribe(function (value) {\n                valueRef.current = value;\n                setRenderCounter(function (prev) { return prev + 1; });\n            });\n        }\n        return function () { return subscription === null || subscription === void 0 ? void 0 : subscription.unsubscribe(); };\n    }, [destObservable]);\n    if (shouldHaveSyncValue && !initializedRef.current) {\n        throw new Error('Expect `shouldHaveSyncValue` but not getting a sync value!');\n    }\n    return valueRef.current;\n}\n/**\n * subscribe to a signal that emits whenever data updates and re-render\n *\n * @param update$ a signal that the data the functional component depends has updated\n */\nexport function useUpdateBinder(update$) {\n    var _a = useState(0), dumpSet = _a[1];\n    useEffect(function () {\n        var subscription = update$.subscribe(function () { return dumpSet(function (prev) { return prev + 1; }); });\n        return function () { return subscription.unsubscribe(); };\n    }, []);\n}\nvar DepValueMapProvider = new WeakMap();\n/**\n * subscribe to an observable value from a service, creating a context for it so\n * it child component won't have to subscribe again and cause unnecessary\n */\nexport function useDependencyContext(depValue$, defaultValue) {\n    var depRef = useRef(undefined);\n    var value = useDependencyValue(depValue$, defaultValue);\n    var Context = useMemo(function () {\n        return createContext(value);\n    }, [depValue$]);\n    var Provider = useCallback(function (props) {\n        return React.createElement(Context.Provider, { value: value }, props.children);\n    }, [depValue$, value]);\n    if (depRef.current !== depValue$) {\n        if (depRef.current) {\n            DepValueMapProvider.delete(depRef.current);\n        }\n        depRef.current = depValue$;\n        DepValueMapProvider.set(depValue$, Context);\n    }\n    return {\n        Provider: Provider,\n        value: value,\n    };\n}\nexport function useDependencyContextValue(depValue$) {\n    var context = DepValueMapProvider.get(depValue$);\n    if (!context) {\n        throw new RediError(\"try to read context value but no ancestor component subscribed it.\");\n    }\n    return useContext(context);\n}\n//# sourceMappingURL=reactRx.js.map"], "mappings": ";;;;AACA,IAAIA,qBAAqB,GAAG,mBAAmB;AAC/C;AACA,IAAIC,MAAM,GAAG,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACC,QAAQ,IAAI,IAAI,IAAID,OAAO,CAACC,QAAQ,CAACC,IAAI,IAAI,IAAI;AACxG,IAAIC,YAAY,GAAI,OAAOC,UAAU,KAAK,WAAW,IAAIA,UAAU,IAC3D,OAAOC,MAAM,KAAK,WAAW,IAAIA;AACzC;AAAA,GACQ,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAO;AAChD,IAAI,CAACH,YAAY,CAACL,qBAAqB,CAAC,EAAE;EACtCK,YAAY,CAACL,qBAAqB,CAAC,GAAG,IAAI;AAC9C,CAAC,MACI,IAAI,CAACC,MAAM,EAAE;EACdQ,OAAO,CAACC,KAAK,CAAC,6KAA6K,CAAC;AAChM;AACU,IAACC,WAAW,GAAGC,KAAK,CAACC,aAAa,CAAC;EACzCC,QAAQ,EAAE;AACd,CAAC;AACDH,WAAW,CAACI,WAAW,GAAG,aAAa;AAC7B,IAACC,YAAY,GAAGL,WAAW,CAACM,QAAA;AAC5B,IAACC,YAAY,GAAGP,WAAW,CAACQ,QAAA;ACnBtC,IAAIC,QAAQ,GAAIC,SAAI,IAAIA,SAAI,CAACD,QAAQ,IAAK,YAAY;EAClDA,QAAQ,GAAGE,MAAM,CAACC,MAAM,IAAI,UAASC,CAAC,EAAE;IACpC,KAAK,IAAIC,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEH,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACjDD,CAAC,GAAGG,SAAS,CAACF,CAAC,CAAC;MAChB,KAAK,IAAII,CAAC,IAAIL,CAAC,EAAE,IAAIH,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,EAC3DN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAC3B;IACQ,OAAON,CAAC;EAChB,CAAK;EACD,OAAOJ,QAAQ,CAACc,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AAC1C,CAAC;AAID,SAASO,YAAYA,CAACC,KAAK,EAAE;EACzB,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;IAAEC,YAAY,GAAGF,KAAK,CAACE,YAAY;EAChE,IAAIC,gBAAgB,GAAG3B,KAAK,CAAC4B,MAAM,CAAC,IAAI,CAAC;EAC7C;EACI5B,KAAK,CAAC6B,SAAS,CAAC,YAAY;IAAE,OAAO,YAAY;MAAE,IAAIC,EAAE;MAAE,OAAO,CAACA,EAAE,GAAGH,gBAAgB,CAACI,OAAO,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,OAAO,EAAE;IAAC,CAAE;EAAC,CAAE,EAAE,EAAE,CAAC;EAC9J,OAAQhC,KAAK,CAACiC,aAAa,CAAC3B,YAAY,EAAE,IAAI,EAAE,UAAU4B,OAAO,EAAE;IAC/D,IAAIC,aAAa;IACjB,IAAIR,gBAAgB,CAACI,OAAO,EAAE;MAC1BI,aAAa,GAAGR,gBAAgB,CAACI,OAAO;IACpD,CAAS,MACI;MACDI,aAAa,GAAGD,OAAO,CAAChC,QAAQ,GAC1BgC,OAAO,CAAChC,QAAQ,CAACkC,WAAW,CAACV,YAAY,CAAC,GAC1C,IAAIW,QAAQ,CAACX,YAAY,CAAC;MAChCC,gBAAgB,CAACI,OAAO,GAAGI,aAAa;IACpD;IACQ,OAAQnC,KAAK,CAACiC,aAAa,CAAC7B,YAAY,EAAE;MAAEkC,KAAK,EAAE;QAAEpC,QAAQ,EAAEiC;MAAa;IAAE,CAAE,EAAEV,QAAQ,CAAC;EACnG,CAAK,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACO,SAASc,eAAeA,CAACC,IAAI,EAAEtC,QAAQ,EAAE;EAC5C,OAAO,SAASuC,qBAAqBA,CAACjB,KAAK,EAAE;IACzC,OAAQxB,KAAK,CAACiC,aAAa,CAAC7B,YAAY,EAAE;MAAEkC,KAAK,EAAE;QAAEpC,QAAQ,EAAEA;MAAQ;IAAE,CAAE,EACvEF,KAAK,CAACiC,aAAa,CAACO,IAAI,EAAEhC,QAAQ,CAAC,EAAE,EAAEgB,KAAK,CAAC,CAAC,CAAC;EAC3D,CAAK;AACL;AACO,SAASkB,mBAAmBA,CAACF,IAAI,EAAEd,YAAY,EAAE;EACpD,OAAO,SAASe,qBAAqBA,CAACjB,KAAK,EAAE;IACzC,OAAQxB,KAAK,CAACiC,aAAa,CAACV,YAAY,EAAE;MAAEG,YAAY,EAAEA;IAAY,CAAE,EACpE1B,KAAK,CAACiC,aAAa,CAACO,IAAI,EAAEhC,QAAQ,CAAC,EAAE,EAAEgB,KAAK,CAAC,CAAC,CAAC;EAC3D,CAAK;AACL;ACjDA,IAAImB,WAAS,GAAIlC,SAAI,IAAIA,SAAI,CAACmC,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGnC,MAAM,CAACsC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAE,CAAE,YAAYC,KAAK,IAAI,UAAUJ,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACG,SAAS,GAAGF,CAAC;IAAC,CAAG,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAI7B,CAAC,IAAI6B,CAAC,EAAE,IAAIrC,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC0B,CAAC,EAAE7B,CAAC,CAAC,EAAE4B,CAAC,CAAC5B,CAAC,CAAC,GAAG6B,CAAC,CAAC7B,CAAC,CAAC;IAAC,CAAE;IACrG,OAAO2B,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAClC,CAAK;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnB,IAAI,OAAOA,CAAC,KAAK,UAAU,IAAIA,CAAC,KAAK,IAAI,EACrC,MAAM,IAAII,SAAS,CAAC,sBAAsB,GAAGC,MAAM,CAACL,CAAC,CAAC,GAAG,+BAA+B,CAAC;IAC7FF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASM,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGR,CAAC;IAAC;IACrCA,CAAC,CAAC3B,SAAS,GAAG4B,CAAC,KAAK,IAAI,GAAGrC,MAAM,CAAC6C,MAAM,CAACR,CAAC,CAAC,IAAIM,EAAE,CAAClC,SAAS,GAAG4B,CAAC,CAAC5B,SAAS,EAAE,IAAIkC,EAAE,EAAE,CAAC;EAC5F,CAAK;AACL,CAAC,EAAG;AAEJ,IAAIG,mCAAmC,gBAAkB,UAAUC,MAAM,EAAE;EACvEd,WAAS,CAACa,mCAAmC,EAAEC,MAAM,CAAC;EACtD,SAASD,mCAAmCA,CAACE,SAAS,EAAE;IACpD,OAAOD,MAAM,CAACpC,IAAI,CAAC,IAAI,EAAE,qCAAqC,CAACsC,MAAM,CAACD,SAAS,CAACJ,WAAW,CAACM,IAAI,EAAE,2BAA2B,CAAC,GACxH,4EAA4E,GAC5E,wDAAwD,CAAC,IAAI,IAAI;EAC/E;EACI,OAAOJ,mCAAmC;AAC9C,CAAC,CAACK,SAAS,CAAE;AACN,SAASC,cAAcA,CAACC,EAAE,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EACjD,OAAO,YAAY;IACf,OAAO;MACHC,GAAG,EAAE,SAAAA,CAAA,EAAY;QACb,IAAIC,aAAa,GAAG,IAAI;QACxB,IAAIjC,OAAO,GAAGiC,aAAa,CAACjC,OAAO;QACnC,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAAChC,QAAQ,EAAE;UAC/B,MAAM,IAAIsD,mCAAmC,CAACW,aAAa,CAAC;QAChF;QACgB,IAAIjE,QAAQ,GAAGgC,OAAO,CAAChC,QAAQ;QAC/B,IAAIkE,KAAK,GAAGlE,QAAQ,CAACgE,GAAG,CAACH,EAAE,EAAEC,QAAQ,IAAIK,QAAQ,CAACC,QAAQ,EAAEL,MAAM,CAAC;QACnE,OAAOG,KAAK;MAC5B;IACA,CAAS;EACT,CAAK;AACL;ACxCA,IAAIxB,SAAS,GAAInC,SAAI,IAAIA,SAAI,CAACmC,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGnC,MAAM,CAACsC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAE,CAAE,YAAYC,KAAK,IAAI,UAAUJ,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACG,SAAS,GAAGF,CAAC;IAAC,CAAG,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAI7B,CAAC,IAAI6B,CAAC,EAAE,IAAIrC,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC0B,CAAC,EAAE7B,CAAC,CAAC,EAAE4B,CAAC,CAAC5B,CAAC,CAAC,GAAG6B,CAAC,CAAC7B,CAAC,CAAC;IAAC,CAAE;IACrG,OAAO2B,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAClC,CAAK;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnB,IAAI,OAAOA,CAAC,KAAK,UAAU,IAAIA,CAAC,KAAK,IAAI,EACrC,MAAM,IAAII,SAAS,CAAC,sBAAsB,GAAGC,MAAM,CAACL,CAAC,CAAC,GAAG,+BAA+B,CAAC;IAC7FF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASM,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGR,CAAC;IAAC;IACrCA,CAAC,CAAC3B,SAAS,GAAG4B,CAAC,KAAK,IAAI,GAAGrC,MAAM,CAAC6C,MAAM,CAACR,CAAC,CAAC,IAAIM,EAAE,CAAClC,SAAS,GAAG4B,CAAC,CAAC5B,SAAS,EAAE,IAAIkC,EAAE,EAAE,CAAC;EAC5F,CAAK;AACL,CAAC,EAAG;AAIJ,IAAIkB,0BAA0B,gBAAkB,UAAUd,MAAM,EAAE;EAC9Db,SAAS,CAAC2B,0BAA0B,EAAEd,MAAM,CAAC;EAC7C,SAASc,0BAA0BA,CAAA,EAAG;IAClC,OAAOd,MAAM,CAACpC,IAAI,CAAC,IAAI,EAAE,sDAAsD,CAAC,IAAI,IAAI;EAChG;EACI,OAAOkD,0BAA0B;AACrC,CAAC,CAACV,SAAS,CAAE;AACN,SAASW,WAAWA,CAAA,EAAG;EAC1B,IAAIC,gBAAgB,GAAGzE,KAAK,CAAC0E,UAAU,CAAC3E,WAAW,CAAC;EACpD,IAAI,CAAC0E,gBAAgB,CAACvE,QAAQ,EAAE;IAC5B,MAAM,IAAIqE,0BAA0B,EAAE;EAC9C;EACI,OAAOE,gBAAgB,CAACvE,QAAQ;AACpC;AACO,SAASyE,aAAaA,CAACZ,EAAE,EAAEa,gBAAgB,EAAEX,MAAM,EAAE;EACxD,IAAI/D,QAAQ,GAAGsE,WAAW,EAAE;EAC5B,OAAOxE,KAAK,CAAC6E,OAAO,CAAC,YAAY;IAAE,OAAO3E,QAAQ,CAACgE,GAAG,CAACH,EAAE,EAAEa,gBAAgB,EAAEX,MAAM,CAAC;EAAC,CAAE,EAAE,CAACF,EAAE,EAAEa,gBAAgB,EAAEX,MAAM,CAAC,CAAC;AAC5H;ACnCA,IAAIa,aAAa,GAAIrE,SAAI,IAAIA,SAAI,CAACqE,aAAa,IAAK,UAAUC,EAAE,EAAEC,IAAI,EAAEC,IAAI,EAAE;EAC1E,IAAIA,IAAI,IAAIjE,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEoE,CAAC,GAAGF,IAAI,CAAC/D,MAAM,EAAEkE,EAAE,EAAErE,CAAC,GAAGoE,CAAC,EAAEpE,CAAC,EAAE,EAAE;IACjF,IAAIqE,EAAE,IAAI,EAAErE,CAAC,IAAIkE,IAAI,CAAC,EAAE;MACpB,IAAI,CAACG,EAAE,EAAEA,EAAE,GAAGjC,KAAK,CAAC/B,SAAS,CAACiE,KAAK,CAAC/D,IAAI,CAAC2D,IAAI,EAAE,CAAC,EAAElE,CAAC,CAAC;MACpDqE,EAAE,CAACrE,CAAC,CAAC,GAAGkE,IAAI,CAAClE,CAAC,CAAC;IAC3B;EACA;EACI,OAAOiE,EAAE,CAACpB,MAAM,CAACwB,EAAE,IAAIjC,KAAK,CAAC/B,SAAS,CAACiE,KAAK,CAAC/D,IAAI,CAAC2D,IAAI,CAAC,CAAC;AAC5D,CAAC;AAID;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASK,kBAAkBA,CAACC,SAAS,EAAEC,YAAY,EAAE;EACxD,IAAIC,UAAU,GAAGF,SAAS,YAAYG,eAAe,IAAI,OAAOF,YAAY,KAAK,WAAW,GACtFD,SAAS,CAACI,QAAQ,EAAE,GACpBH,YAAY;EAClB,IAAIzD,EAAE,GAAG6D,QAAQ,CAACH,UAAU,CAAC;IAAElD,KAAK,GAAGR,EAAE,CAAC,CAAC,CAAC;IAAE8D,QAAQ,GAAG9D,EAAE,CAAC,CAAC,CAAC;EAC9DD,SAAS,CAAC,YAAY;IAClB,IAAIgE,YAAY,GAAGP,SAAS,CAACQ,SAAS,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOH,QAAQ,CAACG,GAAG,CAAC;IAAC,CAAE,CAAC;IAChF,OAAO,YAAY;MAAE,OAAOF,YAAY,CAACG,WAAW,EAAE;IAAC,CAAE;EACjE,CAAK,EAAE,CAACV,SAAS,CAAC,CAAC;EACf,OAAOhD,KAAK;AAChB;AACA,SAAS2D,MAAMA,CAACC,CAAC,EAAE;EACf,IAAI,OAAOA,CAAC,KAAK,UAAU,EAAE;IACzB,OAAOA,CAAC,EAAE;EAClB;EACI,OAAOA,CAAC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,aAAaA,CAACC,UAAU,EAAEb,YAAY,EAAEc,mBAAmB,EAAEC,IAAI,EAAE;EAC/E,IAAI,OAAOF,UAAU,KAAK,UAAU,IAAI,CAACE,IAAI,EAAE;IAC3C,MAAM,IAAIzC,SAAS,CAAC,6DAA6D,CAAC;EAC1F;EACI,IAAI0C,aAAa,GAAG3E,MAAM,CAAC,IAAI,CAAC;EAChC,IAAI4E,cAAc,GAAG5E,MAAM,CAAC,KAAK,CAAC;EAClC,IAAI6E,cAAc,GAAG5B,OAAO,CAAC,YAAY;IAAE,OAAOuB,UAAU;EAAC,CAAE,EAAEtB,aAAa,CAAC,EAAE,EAAG,OAAOwB,IAAI,KAAK,WAAW,GAAGA,IAAI,GAAG,CAACF,UAAU,CAAC,EAAG,IAAI,CAAC,CAAC;EAClJ;EACA;EACI,IAAItE,EAAE,GAAG6D,QAAQ,CAAC,CAAC,CAAC;IAAEe,CAAC,GAAG5E,EAAE,CAAC,CAAC,CAAC;IAAE6E,gBAAgB,GAAG7E,EAAE,CAAC,CAAC,CAAC;EACzD,IAAI8E,QAAQ,GAAGhF,MAAM,CAAE,YAAY;IAC/B,IAAIiF,iBAAiB;IACrB,IAAIJ,cAAc,EAAE;MAChB,IAAIK,GAAG,GAAGb,MAAM,CAACQ,cAAc,CAAC,CAACX,SAAS,CAAC,UAAUxD,KAAK,EAAE;QACxDkE,cAAc,CAACzE,OAAO,GAAG,IAAI;QAC7B8E,iBAAiB,GAAGvE,KAAK;MACzC,CAAa,CAAC;MACFwE,GAAG,CAACd,WAAW,EAAE;IAC7B;IACQ,OAAOa,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAGtB,YAAY;EAC5G,CAAK,EAAG,CAAC;EACL1D,SAAS,CAAC,YAAY;IAClB,IAAIgE,YAAY,GAAG,IAAI;IACvB,IAAIY,cAAc,EAAE;MAChBF,aAAa,CAACxE,OAAO,GAAGkE,MAAM,CAACQ,cAAc,CAAC;MAC9CZ,YAAY,GAAGU,aAAa,CAACxE,OAAO,CAAC+D,SAAS,CAAC,UAAUxD,KAAK,EAAE;QAC5DsE,QAAQ,CAAC7E,OAAO,GAAGO,KAAK;QACxBqE,gBAAgB,CAAC,UAAUI,IAAI,EAAE;UAAE,OAAOA,IAAI,GAAG,CAAC;QAAC,CAAE,CAAC;MACtE,CAAa,CAAC;IACd;IACQ,OAAO,YAAY;MAAE,OAAOlB,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACG,WAAW,EAAE;IAAC,CAAE;EAC7H,CAAK,EAAE,CAACS,cAAc,CAAC,CAAC;EACpB,IAAIJ,mBAAmB,IAAI,CAACG,cAAc,CAACzE,OAAO,EAAE;IAChD,MAAM,IAAIiF,KAAK,CAAC,4DAA4D,CAAC;EACrF;EACI,OAAOJ,QAAQ,CAAC7E,OAAO;AAC3B;AACA;AACA;AACA;AACA;AACA;AACO,SAASkF,eAAeA,CAACC,OAAO,EAAE;EACrC,IAAIpF,EAAE,GAAG6D,QAAQ,CAAC,CAAC,CAAC;IAAEwB,OAAO,GAAGrF,EAAE,CAAC,CAAC,CAAC;EACrCD,SAAS,CAAC,YAAY;IAClB,IAAIgE,YAAY,GAAGqB,OAAO,CAACpB,SAAS,CAAC,YAAY;MAAE,OAAOqB,OAAO,CAAC,UAAUJ,IAAI,EAAE;QAAE,OAAOA,IAAI,GAAG,CAAC;MAAC,CAAE,CAAC;IAAC,CAAE,CAAC;IAC3G,OAAO,YAAY;MAAE,OAAOlB,YAAY,CAACG,WAAW,EAAE;IAAC,CAAE;EACjE,CAAK,EAAE,EAAE,CAAC;AACV;AACA,IAAIoB,mBAAmB,GAAG,IAAIC,OAAO,EAAE;AACvC;AACA;AACA;AACA;AACO,SAASC,oBAAoBA,CAAChC,SAAS,EAAEC,YAAY,EAAE;EAC1D,IAAIgC,MAAM,GAAG3F,MAAM,CAACnB,SAAS,CAAC;EAC9B,IAAI6B,KAAK,GAAG+C,kBAAkB,CAACC,SAAS,EAAEC,YAAY,CAAC;EACvD,IAAIiC,OAAO,GAAG3C,OAAO,CAAC,YAAY;IAC9B,OAAO5E,aAAa,CAACqC,KAAK,CAAC;EACnC,CAAK,EAAE,CAACgD,SAAS,CAAC,CAAC;EACf,IAAIjF,QAAQ,GAAGoH,WAAW,CAAC,UAAUjG,KAAK,EAAE;IACxC,OAAOkG,cAAK,CAACzF,aAAa,CAACuF,OAAO,CAACnH,QAAQ,EAAE;MAAEiC,KAAK,EAAEA;IAAK,CAAE,EAAEd,KAAK,CAACC,QAAQ,CAAC;EACtF,CAAK,EAAE,CAAC6D,SAAS,EAAEhD,KAAK,CAAC,CAAC;EACtB,IAAIiF,MAAM,CAACxF,OAAO,KAAKuD,SAAS,EAAE;IAC9B,IAAIiC,MAAM,CAACxF,OAAO,EAAE;MAChBqF,mBAAmB,CAACO,MAAM,CAACJ,MAAM,CAACxF,OAAO,CAAC;IACtD;IACQwF,MAAM,CAACxF,OAAO,GAAGuD,SAAS;IAC1B8B,mBAAmB,CAACQ,GAAG,CAACtC,SAAS,EAAEkC,OAAO,CAAC;EACnD;EACI,OAAO;IACHnH,QAAQ,EAAEA,QAAQ;IAClBiC,KAAK,EAAEA;EACf,CAAK;AACL;AACO,SAASuF,yBAAyBA,CAACvC,SAAS,EAAE;EACjD,IAAIpD,OAAO,GAAGkF,mBAAmB,CAAClD,GAAG,CAACoB,SAAS,CAAC;EAChD,IAAI,CAACpD,OAAO,EAAE;IACV,MAAM,IAAI2B,SAAS,CAAC,oEAAoE,CAAC;EACjG;EACI,OAAOa,UAAU,CAACxC,OAAO,CAAC;AAC9B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}