{"ast": null, "code": "function ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    enumerableOnly && (symbols = symbols.filter(function (sym) {\n      return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n    })), keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = null != arguments[i] ? arguments[i] : {};\n    i % 2 ? ownKeys(Object(source), !0).forEach(function (key) {\n      _defineProperty(target, key, source[key]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) {\n      Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n    });\n  }\n  return target;\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) {\n    arr2[i] = arr[i];\n  }\n  return arr2;\n}\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"];\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _s, _e;\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n  return _arr;\n}\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\nimport isObject from './helpers/isObject.js'; // Extracts the following properties from function arguments:\n// * input `text`\n// * `options` object\n// * `metadata` JSON\n\nexport default function normalizeArguments(args) {\n  var _Array$prototype$slic = Array.prototype.slice.call(args),\n    _Array$prototype$slic2 = _slicedToArray(_Array$prototype$slic, 4),\n    arg_1 = _Array$prototype$slic2[0],\n    arg_2 = _Array$prototype$slic2[1],\n    arg_3 = _Array$prototype$slic2[2],\n    arg_4 = _Array$prototype$slic2[3];\n  var text;\n  var options;\n  var metadata; // If the phone number is passed as a string.\n  // `parsePhoneNumber('88005553535', ...)`.\n\n  if (typeof arg_1 === 'string') {\n    text = arg_1;\n  } else throw new TypeError('A text for parsing must be a string.'); // If \"default country\" argument is being passed then move it to `options`.\n  // `parsePhoneNumber('88005553535', 'RU', [options], metadata)`.\n\n  if (!arg_2 || typeof arg_2 === 'string') {\n    if (arg_4) {\n      options = arg_3;\n      metadata = arg_4;\n    } else {\n      options = undefined;\n      metadata = arg_3;\n    }\n    if (arg_2) {\n      options = _objectSpread({\n        defaultCountry: arg_2\n      }, options);\n    }\n  } // `defaultCountry` is not passed.\n  // Example: `parsePhoneNumber('+78005553535', [options], metadata)`.\n  else if (isObject(arg_2)) {\n    if (arg_3) {\n      options = arg_2;\n      metadata = arg_3;\n    } else {\n      metadata = arg_2;\n    }\n  } else throw new Error(\"Invalid second argument: \".concat(arg_2));\n  return {\n    text: text,\n    options: options,\n    metadata: metadata\n  };\n}", "map": {"version": 3, "names": ["isObject", "normalizeArguments", "args", "_Array$prototype$slic", "Array", "prototype", "slice", "call", "_Array$prototype$slic2", "_slicedToArray", "arg_1", "arg_2", "arg_3", "arg_4", "text", "options", "metadata", "TypeError", "undefined", "_objectSpread", "defaultCountry", "Error", "concat"], "sources": ["D:\\CodeFreaks\\dexta-ksa\\dexta-app\\node_modules\\libphonenumber-js\\source\\normalizeArguments.js"], "sourcesContent": ["import isObject from './helpers/isObject.js'\r\n\r\n// Extracts the following properties from function arguments:\r\n// * input `text`\r\n// * `options` object\r\n// * `metadata` JSON\r\nexport default function normalizeArguments(args) {\r\n\tconst [arg_1, arg_2, arg_3, arg_4] = Array.prototype.slice.call(args)\r\n\r\n\tlet text\r\n\tlet options\r\n\tlet metadata\r\n\r\n\t// If the phone number is passed as a string.\r\n\t// `parsePhoneNumber('88005553535', ...)`.\r\n\tif (typeof arg_1 === 'string') {\r\n\t\ttext = arg_1\r\n\t}\r\n\telse throw new TypeError('A text for parsing must be a string.')\r\n\r\n\t// If \"default country\" argument is being passed then move it to `options`.\r\n\t// `parsePhoneNumber('88005553535', 'RU', [options], metadata)`.\r\n\tif (!arg_2 || typeof arg_2 === 'string')\r\n\t{\r\n\t\tif (arg_4) {\r\n\t\t\toptions = arg_3\r\n\t\t\tmetadata = arg_4\r\n\t\t} else {\r\n\t\t\toptions = undefined\r\n\t\t\tmetadata = arg_3\r\n\t\t}\r\n\r\n\t\tif (arg_2) {\r\n\t\t\toptions = { defaultCountry: arg_2, ...options }\r\n\t\t}\r\n\t}\r\n\t// `defaultCountry` is not passed.\r\n\t// Example: `parsePhoneNumber('+78005553535', [options], metadata)`.\r\n\telse if (isObject(arg_2))\r\n\t{\r\n\t\tif (arg_3) {\r\n\t\t\toptions  = arg_2\r\n\t\t\tmetadata = arg_3\r\n\t\t} else {\r\n\t\t\tmetadata = arg_2\r\n\t\t}\r\n\t}\r\n\telse throw new Error(`Invalid second argument: ${arg_2}`)\r\n\r\n\treturn {\r\n\t\ttext,\r\n\t\toptions,\r\n\t\tmetadata\r\n\t}\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,QAAP,MAAqB,uBAArB,C,CAEA;AACA;AACA;AACA;;AACA,eAAe,SAASC,kBAATA,CAA4BC,IAA5B,EAAkC;EAChD,IAAAC,qBAAA,GAAqCC,KAAK,CAACC,SAAN,CAAgBC,KAAhB,CAAsBC,IAAtB,CAA2BL,IAA3B,CAArC;IAAAM,sBAAA,GAAAC,cAAA,CAAAN,qBAAA;IAAOO,KAAP,GAAAF,sBAAA;IAAcG,KAAd,GAAAH,sBAAA;IAAqBI,KAArB,GAAAJ,sBAAA;IAA4BK,KAA5B,GAAAL,sBAAA;EAEA,IAAIM,IAAJ;EACA,IAAIC,OAAJ;EACA,IAAIC,QAAJ,CALgD,CAOhD;EACA;;EACA,IAAI,OAAON,KAAP,KAAiB,QAArB,EAA+B;IAC9BI,IAAI,GAAGJ,KAAP;EACA,CAFD,MAGK,MAAM,IAAIO,SAAJ,CAAc,sCAAd,CAAN,CAZ2C,CAchD;EACA;;EACA,IAAI,CAACN,KAAD,IAAU,OAAOA,KAAP,KAAiB,QAA/B,EACA;IACC,IAAIE,KAAJ,EAAW;MACVE,OAAO,GAAGH,KAAV;MACAI,QAAQ,GAAGH,KAAX;IACA,CAHD,MAGO;MACNE,OAAO,GAAGG,SAAV;MACAF,QAAQ,GAAGJ,KAAX;IACA;IAED,IAAID,KAAJ,EAAW;MACVI,OAAO,GAAAI,aAAA;QAAKC,cAAc,EAAET;MAArB,GAA+BI,OAA/B,CAAP;IACA;EACD,CAbD,CAcA;EACA;EAAA,KACK,IAAIf,QAAQ,CAACW,KAAD,CAAZ,EACL;IACC,IAAIC,KAAJ,EAAW;MACVG,OAAO,GAAIJ,KAAX;MACAK,QAAQ,GAAGJ,KAAX;IACA,CAHD,MAGO;MACNI,QAAQ,GAAGL,KAAX;IACA;EACD,CARI,MASA,MAAM,IAAIU,KAAJ,6BAAAC,MAAA,CAAsCX,KAAtC,EAAN;EAEL,OAAO;IACNG,IAAI,EAAJA,IADM;IAENC,OAAO,EAAPA,OAFM;IAGNC,QAAQ,EAARA;EAHM,CAAP;AAKA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}